<?php

namespace App\Models;

use App\Enums\DocumentTypeEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Helpers\HandleMediaCollection;
use Khaleds\Shared\Models\BaseModel;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Property\app\Models\Property;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Spatie\MediaLibrary\InteractsWithMedia;


class Document extends BaseModel
{
    use SoftDeletes;
    use HandleMediaCollection , InteractsWithMedia;
    use BelongsToTenancy;
    protected $translatable = [];

    protected $casts = [
        'metadata' => 'array',
    ];

    protected $guarded = ['id'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('upload_contract');
    }
    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('accountTypeScope', function (Builder $builder) {
            if (request()->is('api/*')) {
                $account = auth()->user();

                if ($account) {
                    $defaultRole = $account->accountRoles()
                        ->where('is_default', true)
                        ->first();

                    if ($defaultRole) {
                        if ($defaultRole->role == AccountRolesEnum::OWNER_REPRESENTER) { //get documents for representer of property owner
                            $builder->where('morphable_type', Property::class)
                                ->whereHasMorph('morphable', [Property::class], function ($query) use ($account) {
                                    $query->whereHas('owners', function ($q) use ($account) {
                                        $q->where('ownerable_id', $account->id)->where('ownerable_type' , Account::class);
                                    });
                                })
                                ->whereHas('documentType', function ($query) {
                                    $query->where('key', DocumentTypeEnum::Ownership);
                                });

                        } elseif ($defaultRole->role == AccountRolesEnum::TENANT_REPRESENTER) { //get documents for representer of lease tenant representer
                            $builder->where('morphable_type', Lease::class)
                                ->whereHasMorph('morphable', [Lease::class], function ($query) use ($account) {
                                    $query->whereHas('leaseMembers', function ($q) use ($account) {
                                        $q->where('member_id', $account->id)
                                            ->where('member_role', LeaseMemberTypesEnum::TENANT_REPRESENTER);
                                    });
                                });
                        }
                    }

                }
            }
        });
    }

    public function morphable()
    {
        return $this->morphTo();
    }

    public function documentType()
    {
        return $this->belongsTo(DocumentType::class);
    }
}
