<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Carbon\Carbon;

class ActiveValScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $today = Carbon::today()->format('Y-m-d'); // Get today's date

        // Add your query conditions
        $builder->where('is_active', 1) // Only active Ads
                ->whereHas('val', function ($query) use ($today) {
                    $query->where('active', 1) // Only active vals
                          ->where('start_date', '<=', $today) // Where start_date is today or earlier
                          ->where('end_date', '>=', $today);  // Where end_date is today or later
                });
    }
}
