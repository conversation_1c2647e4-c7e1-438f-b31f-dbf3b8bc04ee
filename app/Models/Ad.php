<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Filament\Notifications\Notification;
use App\Shared\BaseModel;


class Ad extends BaseModel
{
    public $translatable = ['title', 'description'];
    protected $fillable = ['title', 'description', 'link', 'image', 'active', 'property_id'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $recipient = auth()->user();

            $recipient->notify(
                Notification::make()
                    ->title('Saved successfully')
                    ->toDatabase(),
            );
        });
    }

    public function vals()
    {
        return $this->morphMany(Val::class, 'morphable');
    }
}
