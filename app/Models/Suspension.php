<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Suspension extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    // Optionally, define relationships if necessary.
    // For example, a suspension can belong to the suspended user (polymorphic relationship)
    public function suspended()
    {
        return $this->morphTo();
    }

    // Relationship to the user who suspended
    public function suspendedBy()
    {
        return $this->morphTo();
    }
}
