<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\Traits\HasBankAccounts;
use Modules\Company\app\Models\Company;
use Modules\Company\app\Models\UserProfile;
use Modules\Rates\app\Traits\HasRates;
use Modules\Subscription\app\models\Subscription;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Modules\Ticket\app\Models\Ticket;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable;
    use HasRoles;
    use HasRates;
    use HasBankAccounts;
    use BelongsToTenancy;

    /**
     * The attributes that are not assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function companies()
    {
        return $this->belongsToMany(Company::class)->withTimestamps();
    } 

    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'user_id');
    }

    public function getFullName(): string
    {
        return $this->name;
    }


//    public function company()
//    {
//        return $this->belongsTo(Company::class);
//    }

    public function getTenants(Panel $panel): Collection
    {
        return $this->company ?? collect();
    }
    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    public function company()
    {
        return $this->hasOne(Company::class,'id','company_id');
    }

    public function currentSubscription(): MorphOne
    {
        return $this->morphOne(Subscription::class, 'subscriber')
            ->where('is_current', true);
    }

    // Keep the original subscriptions relationship for when you need all subscriptions
    public function subscriptions(): MorphMany
    {
        return $this->morphMany(Subscription::class, 'subscriber');
    }

    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class, 'subscriber_id')->where('is_current', 1);
    }

    public function markEmailAsVerified()
    {
        parent::markEmailAsVerified();
        //verified and active
        $this->update(['email_verified' => 1, 'active' => 1]);
        // make related account as verified
        $this->markUserAccountAsVerified();
        return $this;
    }

    public function markUserAccountAsVerified(): void
    {
        $user_account = UserAccountCredential::withoutGlobalScopes()->where(['user_id' => $this->id])->first();
        if ($user_account) {
            $account = Account::find($user_account->account_id);
            if ($account) {
                $account->is_active = true;
                $account->otp_activated_at = date('Y-m-d H:i:s', time());
                $account->save();
            }
        }
    }

    public function userCredential()
    {
        return $this->hasOne(UserAccountCredential::class, 'user_id');
    }
}
