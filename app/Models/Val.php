<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Shared\BaseModel;
use Modules\Tenancy\Traits\BelongsToTenancy;
use App\Shared\HasRelationshipChecks;

class Val extends BaseModel
{
    use HasFactory;
    use BelongsToTenancy;
    // use HasRelationshipChecks;


    protected $guarded=['id'];
    protected $fillable=[];

    // protected $relationsList = ["checkCurrentMorph"];

    public function checkCurrentMorph()
    {
        if($this->morphable_type && $this->morphable_id) {
            return $this->morphable();
        }

        return $this->belongsTo(static::class)->whereRaw('1 = 0');
    }

    public function morphable()
    {
        return $this->morphTo();
    }
}
