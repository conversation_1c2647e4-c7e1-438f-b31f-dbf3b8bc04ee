<?php

namespace App\Imports;

use App\Models\City;
use App\Models\District;
use App\Models\Region;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SaudiLocationsImport implements ToCollection, WithHeadingRow
{
    public function collection(Collection $collection): void
    {
        $currentRegion = null;
        $currentCity = null;

        foreach ($collection as $row) {
            if (!empty($row['lkregionid']) && is_integer($row['lkregionid'])) {
                $currentRegion = $this->handleRegionObject($row);
            }

            if (!empty($row['lkcityid']) && is_integer($row['lkcityid']) && $currentRegion) {
                $cityName = [
                    'en' => $row['lkcitynameen'],
                    'ar' => $row['lkcitynamear'] ?? null
                ];

                $currentCity = City::firstOrCreate(['id' => $row['lkcityid']], [
                    'id' => $row['lkcityid'],
                    'name' => $cityName,
                    'latitude' => $row['citylatitude'],
                    'longitude' => $row['citylongitude'],
                    'region_id' => $currentRegion->id,
                ]);
            }

            if (!empty($row['lkdistrictid']) && is_integer($row['lkdistrictid']) && $currentCity) {
                $districtName = [
                    'en' => $row['lkdistrictnameen'],
                    'ar' => $row['lkdistrictnamear'] ?? null
                ];

                District::firstOrCreate(['id' => $row['lkdistrictid']], [
                    'id' => $row['lkdistrictid'],
                    'name' => $districtName,
                    'latitude' => $row['districtlatitude'],
                    'longitude' => $row['districtlongitude'],
                    'city_id' => $currentCity->id,
                    'is_active' => !($row['isdeleted'] == 1),
                ]);
            }
        }
    }

    protected function handleRegionObject(mixed $row): Region
    {
        $regionId = $row['lkregionid'];
        $regionName = [
            'en' => $row['lkregionnameen'],
            'ar' => $row['lkregionnamear'] ?? null
        ];
        // Find existing region or create new one
        $region = Region::find($regionId);

        if ($region) {
            // Update empty fields in existing region
            $nameData = is_array($region->name) ? $region->name : [];

            // Only update empty name fields
            if (!empty($regionName['en'])) {
                if (empty($nameData['en']) || $nameData['en'] == 'null') {
                    $nameData['en'] = $regionName['en'];
                }
            }
            if (!empty($regionName['ar'])) {
                if (empty($nameData['ar']) || $nameData['ar'] == 'null') {
                    $nameData['ar'] = $regionName['ar'];
                }
            }
            $updates = [];
            if ($nameData !== $region->name) {
                $updates['name'] = $nameData;
            }

            if (empty($region->latitude) && !empty($row['regionlatitude'])) {
                $updates['latitude'] = $row['regionlatitude'];
            }

            if (empty($region->longitude) && !empty($row['regionlongitude'])) {
                $updates['longitude'] = $row['regionlongitude'];
            }

            if (!empty($updates)) {
                $region->update($updates);
            }

            $currentRegion = $region;
        } else {
            // Create new region
            $currentRegion = Region::create([
                'id' => $regionId,
                'name' => $regionName,
                'latitude' => $row['regionlatitude'] ?? null,
                'longitude' => $row['regionlongitude'] ?? null,
            ]);
        }

        return $currentRegion;
    }
}
