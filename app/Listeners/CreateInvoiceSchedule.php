<?php

namespace App\Listeners;

use App\Events\LeasePublished;
use Modules\Invoice\Services\InvoiceScheduleService;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Modules\Invoice\Services\InvoiceService;

class CreateInvoiceSchedule
{
    protected $invoiceScheduleService;
    protected $invoiceService;

    /**
     * Create the event listener.
     */
    public function __construct(InvoiceScheduleService $invoiceScheduleService)
    {
        $this->invoiceScheduleService = $invoiceScheduleService;
        $this->invoiceService = app(InvoiceService::class);
    }

    /**
     * Handle the event.
     */
    public function handle(LeasePublished $event)
    {
         // Log the start of the invoice schedule creation
        Log::info('Creating invoice schedule for lease ID: ' . $event->lease->id);

        // Create the invoice schedule
        $this->invoiceScheduleService->createInvoiceSchedule($event->lease);

        // Execute the Artisan command to create the invoice schedule
        $exitCode = $this->invoiceService->executeFunctionForLeasePublish($event->lease);

        // Log the execution of the Artisan command
        if ($exitCode === true) {
            Log::info('Artisan command "invoice:create-schedule" executed successfully.');
        } else {
            Log::error('Artisan command "invoice:create-schedule" failed with exit code: ' . $exitCode);
        }
    }
}
