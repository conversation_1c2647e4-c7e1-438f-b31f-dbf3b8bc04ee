<?php

namespace App\Listeners;

use App\Enums\SyncUserAccountSourceEnum;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Services\AccountRoleService;

class SyncUserAccount
{

    /**
     * Create the event listener.
     */
    public function __construct(
        protected AccountRoleService $accountRoleService,
        protected string|null $source = null,
        protected bool $isNewAccount = false,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        try {
            DB::beginTransaction();
            $user = $event->model;
            $this->source = $event->source;
            $account_data = $this->prepareUserAccountData($user);
            //find and sync
            $account = Account::where(['national_id' => $user->userProfile->national_id])->first();
            $this->isNewAccount = !$account;
            if ($account) {
                $this->updateExistingAccount($account, $account_data);
            } else {
                // Create related account
                $account = Account::create($account_data);
                //add default role
                $this->accountRoleService->create(['account_id' => $account->id, 'role' => AccountRolesEnum::TENANT, 'is_default' => true]);
            }
            //handle media
            $this->handleAccountAvatar($user->userProfile->avatar_url, $account);
            //notify user
            $this->notifyUser($user);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
    protected function handleAccountAvatar(string|null $avatar_url, Account $account): void
    {
        if ($avatar_url) {
            $collectionName = 'profile';
            $avatarPath = Storage::disk('public')->path($avatar_url);
            $account->clearMediaCollection($collectionName);
            $account->addMedia($avatarPath)->preservingOriginal()->toMediaCollection($collectionName);
        }
    }
    protected function prepareUserAccountData($user): array
    {
        $account_data = [
            'name' => $user->name,
            'first_name' => $user->userProfile->first_name,
            'second_name' => $user->userProfile->second_name ?? null,
            'third_name' => $user->userProfile->third_name ?? null,
            'last_name' => $user->userProfile->last_name ?? null,
            'email' => $user->email,
            'phone' => $user->userProfile->phone_number,
            'lang' => $user->lang ?? app()->getLocale(),
            'password' => $user->unhashed_password,
            'national_id' => $user->userProfile->national_id,
            'birth_date' => $user->userProfile->birth_date,
            'id_type' => $user->userProfile->id_type ?? 'national_id',
            'country_of_issue' => $user->userProfile->country_of_issue ?? null,
        ];
        //check account verification
        if ($this->source == SyncUserAccountSourceEnum::REGISTER->value) {
            $account_data['is_active'] = false;
            $account_data['otp_activated_at'] = null;
        } else {
            $account_data['is_active'] = true;
            $account_data['otp_activated_at'] = date('Y-m-d H:i:s', time());
        }
        //handle birthdate
        $account_data['birth_date'] = $account_data['birth_date'] instanceof \DateTime
            ? $account_data['birth_date']->format('Y-m-d')
            : $account_data['birth_date'];

        return $account_data;
    }
    protected function updateExistingAccount(Account $account, array $data): void
    {
        $acc_data = [];
        if ($this->source == SyncUserAccountSourceEnum::REGISTER->value) {
            if (!empty($data['password'])) {
                $acc_data['password'] = $data['password']; //set the new entered password in register and still not verified
            }
        } else {
            //same existing password and account is active
            $acc_data['is_active'] = true;
            $acc_data['otp_activated_at'] = date('Y-m-d H:i:s', time());
        }
        if (count($acc_data) > 0) {
            $account->update($acc_data);
        }
    }
    protected function notifyUser($user): void
    {
        if ($this->isNewAccount && $this->source != SyncUserAccountSourceEnum::REGISTER->value) {
            //notify new account
            $template = NotificationsTemplate::where(['key' => 'send_user_credential'])->first();
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($user->id)
                    ->findBody(['{username}', '{email}', '{password}'])
                    ->replaceBody([$user->name, $user->userProfile->national_id, $user->unhashed_password])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->lang($user->lang ?? app()->getLocale())
                    ->database(true)
                    ->fire();
            }
        } elseif (!$this->isNewAccount && $this->source != SyncUserAccountSourceEnum::REGISTER->value) {
            //notify existing account
            $template = NotificationsTemplate::where(['key' => 'new_account_access_add'])->first();
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($user->id)
                    ->findBody(['{username}'])
                    ->replaceBody([$user->name])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->lang($user->lang ?? app()->getLocale())
                    ->fire();
            }
        }
    }
}
