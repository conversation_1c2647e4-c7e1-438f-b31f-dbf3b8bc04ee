<?php

namespace App\Listeners;


use App\Models\User;
use App\Models\UserAccountCredential;
use Modules\Account\app\Models\Account;

class SyncPasswords
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        $class = $event->class;
        $id = $event->id;
        $password = $event->password;

        $user_acc_credentials = UserAccountCredential::query();
        if ($class == User::class) {
            $user_acc_credentials = $user_acc_credentials->withoutGlobalScopes()->where(['user_id' => $id])->first();
            if ($user_acc_credentials) {
                $account = Account::find($user_acc_credentials->account_id);
                if ($account) {
                    $account->update(['password' => $password]);
                }
            }
        } elseif ($class == Account::class) {
            $user_acc_credentials = $user_acc_credentials->withoutGlobalScopes()->where(['account_id' => $id])->first();
            if ($user_acc_credentials) {
                $user = User::find($user_acc_credentials->user_id);
                if ($user) {
                    $user->update(['password' => $password]);
                }
            }
        }
    }

}
