<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\NafathLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use App\Models\User;

class NafathAPIController extends Controller
{
    private $app_key;
    private $app_id;
    private $base_url;
    private $headers;
    private $user;

    public function __construct()
    {
        $environment = config('services.nafath.stage');
        $this->app_key  = $environment == 'live' ? config('services.nafath.live_app_key')   : config('services.nafath.app_key');
        $this->app_id   = $environment == 'live' ? config('services.nafath.live_app_id')    : config('services.nafath.app_id');
        $this->base_url = $environment == 'live' ? config('services.nafath.production_url') : config('services.nafath.staing_url');
        $this->user = auth()->user();
        $this->headers = [
            'APP-KEY' => $this->app_key,
            'APP-ID' => $this->app_id,
        ];
    }

    public function send()
    {
        $endpoint = '/api/v1/mfa/request';
        $uuid = (string) Str::uuid();
        $query = [
            'local' => 'ar',
            'requestId' => $uuid,
        ];

        $payload = [
            'nationalId' => $this->user->userProfile->national_id,
            'service' => 'DigitalServiceEnrollmentWithoutBio',
        ];

        try {
            $response = Http::withHeaders($this->headers)
                ->accept('application/json')
                ->post($this->base_url . $endpoint . '?' . http_build_query($query), $payload);
    
            if ($response->successful()) {
                $responseData = $response->json();
                session()->flash('responseData', $responseData);
                return redirect()->route('nafath.verify.confirm');
            }

            return response()->json([
                'status' => 'error',
                'message' => $response->json()['detail'] ?? 'Unknown error',
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function verifyConfirm(Request $request)
    {
        $encodedData = $request->query('data');
        $responseData = $encodedData ? json_decode(base64_decode($encodedData), true) : null;
        
        // Check the last request for this national_id
        $lastRequest = null;
        $remainingSeconds = 0;
        $showCountdown = false;
        $showResendButton = false;
        
        if ($responseData && isset($responseData['national_id'])) {
            $lastRequest = NafathLog::where('national_id', $responseData['national_id'])
                ->orderBy('created_at', 'desc')
                ->first();
            
            if ($lastRequest) {
                $createdAt = $lastRequest->created_at;
                $currentTime = now();
                $timeDifference = $createdAt->diffInSeconds($currentTime);
                
                if ($timeDifference < 180) {
                    // Less than 180 seconds - show countdown
                    $remainingSeconds = (int) floor(180 - $timeDifference);
                    $showCountdown = true;
                    $showResendButton = false;
                } else {
                    // More than 180 seconds - show resend button
                    $remainingSeconds = 0;
                    $showCountdown = false;
                    $showResendButton = true;
                }
            } else {
                // No previous request found - show resend button
                $showResendButton = true;
            }
        }
        
        return view('vendor.filament-panels.pages.auth.nafath-sent-confirm', compact(
            'responseData', 
            'remainingSeconds', 
            'showCountdown', 
            'showResendButton'
        ));
    }

    public function resend($national_id)
    {
        $endpoint = '/api/v1/mfa/request';
        $uuid = (string) Str::uuid();
        $query = [
            'local' => 'ar',
            'requestId' => $uuid,
        ];

        $payload = [
            'nationalId' => $national_id,
            'service' => config('services.nafath.title', 'DigitalServiceEnrollmentWithoutBio'),
        ];

        try {
            $response = Http::withHeaders($this->headers)
                ->accept('application/json')
                ->post($this->base_url . $endpoint . '?' . http_build_query($query), $payload);
    
            // Log the request
            NafathLog::create([
                'request_id' => $uuid,
                'national_id' => $national_id,
                'status' => 'pending'
            ]);
    
            if ($response->successful()) {
                $responseData = $response->json();
                
                return redirect()->route('nafath.verify.confirm', [
                    'data' => base64_encode(json_encode([
                        'random' => $responseData['random'],
                        'national_id' => $national_id,
                        'transId' => $responseData['transId']
                    ]))
                ]);
            }

            return response()->json([
                'status' => 'error',
                'message' => $response->json()['detail'] ?? 'Unknown error',
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function retriveStatus(Request $request)
    {
        $endpoint = "/api/v1/mfa/request/status";
        $nid = $request->national_id;
        $random = $request->random;
        $transId = $request->transId;
    
        try {
            // Prepare the body data
            $bodyData = [
                'nationalId' => $nid,
                'random' => $random,
                'transId' => $transId,
            ];
    
            // Send the POST request with JSON data in the body
            $response = Http::withHeaders($this->headers)
                ->accept('application/json')   // Specify the response format you expect
                ->post($this->base_url . $endpoint, $bodyData);  // Use post method
    
            // dd($response);
            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();  // Get the response data as an array
    
                // If the status is "COMPLETED", update the user's nafath_verified field
                if (isset($data['status']) && $data['status'] === 'COMPLETED') {
                    // Retrieve the user based on national ID (nid)
                    $user = User::whereHas('userProfile', function ($query) use ($nid) {
                        $query->where('national_id', $nid);
                    })->first();
    
                    if ($user) {
                        // Update the user's nafath_verified field to 1
                        $user->nafath_verified_at = now();
                        $user->save();
                    }
                }
    
                // If the status is not COMPLETED, return the response data
                return response()->json($data);
            }
    
            // Return error message if the request was not successful
            return response()->json([
                'status' => 'error',
                'message' => $response->json()['detail'] ?? 'Unknown error',
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function receive(Request $request)
    {
        $data = $request->all();

        // يمكنك تنفيذ أي منطق خاص هنا
        return response()->json(['status' => 'success', 'data' => $data]);
    }

    public function retriveJwt()
    {
        $endpoint = "/api/v1/mfa/jwt";

        try {
            $response = Http::withHeaders($this->headers)
                ->accept('application/json')
                ->post($this->base_url . $endpoint);

            if ($response->successful()) {
                return response()->json($response->json());
            }

            return response()->json([
                'status' => 'error',
                'message' => $response->json()['detail'] ?? 'Unknown error',
            ], $response->status());
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function callback(Request $request)
    {
        $token = $request->input('token');
        $transId = $request->input('transId');
        $requestId = $request->input('requestId');

        if (!$token || !$requestId) {
            return response()->json(['error' => 'Missing token or requestId'], 400);
        }

        // Decode token payload
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return response()->json(['error' => 'Invalid token format'], 400);
        }

        $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);

        // Find the log by trans_id
        $log = NafathLog::where('request_id', $requestId)->first();

        if ($log) {
            $log->update([
                'token' => $token,
                'request_id' => $requestId,
                'decoded_payload' => $payload,
                'status' => 'completed',
                'trans_id' => $transId,
            ]);
        } else {
            // You can choose to create it if it doesn't exist
            NafathLog::create([
                'trans_id' => $transId,
                'request_id' => $requestId,
                'token' => $token,
                'status' => 'completed',
                'decoded_payload' => $payload,
            ]);
        }
    }
}
