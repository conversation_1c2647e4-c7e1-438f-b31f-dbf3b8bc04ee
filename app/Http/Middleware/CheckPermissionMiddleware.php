<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CheckPermissionMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {

        if (auth()->check()) {
            $roleCache = Cache::get('spatie.permission.cache')['roles'] ?? [];
            $userRoleIds = auth()->user()->roles->pluck('id')->toArray();

            $allRolesExist = collect($roleCache)
                    ->whereIn('a', $userRoleIds)
                    ->count() === count($userRoleIds);

            if(!$allRolesExist) {
                Artisan::call('permission:cache-reset');
            }

        }
        return $next($request);
    }
}
