<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetApiLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->header('Accept-Language', 'en');

        // You can also validate against allowed languages
        $allowedLocales = ['en', 'ar']; //Supported languages

        if (!in_array($locale, $allowedLocales)) {
            $locale = 'en'; // Default to English if invalid language
        }

        // Set the application locale
        app()->setLocale($locale);
        return $next($request);
    }
}
