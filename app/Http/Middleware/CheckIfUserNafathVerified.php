<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckIfUserNafathVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    { 
        // Get the currently authenticated user
        $user = auth()->user();
        
        if ($user && $user->nafath_verified_at == null) {
            // Redirect to the nafath verify page
            return redirect('/nafath-verify');
        }

        // If the user is not suspended, proceed to the next middleware
        return $next($request);
    }
}