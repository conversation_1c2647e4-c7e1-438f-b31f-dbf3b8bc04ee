<?php

namespace App\Console\Commands;

use Modules\Ad\app\Models\Ad;
use App\Models\User;
use App\Models\Val;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\Helpers\AccountHelper;
use Modules\Company\app\Models\Company;
use Modules\Property\app\Models\Property;

class ExpireFinishedValsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finished-values';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::today()->toDateString();

        DB::beginTransaction();

        try {
            Val::where('end_date', '<=', $today)
                ->where('active', 1)
                ->chunkById(10, function ($expiredVals) {
                    foreach ($expiredVals as $val) {
                        $val->active = 0;
                        $val->save();

                        if ($val->morphable) {
                            $members = [];
                            switch($val->morphable_type)
                            {
                                case Property::class:
                                    $property = $val->morphable; // Access the Property instance

                                    if ($property) {
                                        $brokersIds = $property->parent->brokers->pluck('id')->toArray();
                                        $ownerId = $property->company->user->id ?? null;
                                        $members [] = $ownerId;
                                        $members = array_merge($members, $brokersIds);
                                        $this->notifyAfterExpire($members);
                                    }
                                    break;
                                case Ad::class:
                                    $ad = $val->morphable;
                                    if($ad)
                                    {
                                        if($ad->property_id !== null)
                                        {
                                            $property = Property::find($ad->property_id);
                                            $brokersIds = $property->parent->brokers->pluck('id')->toArray();
                                            $ownerId = $property->company->user->id ?? null;
                                            $members [] = $ownerId;
                                            $members = array_merge($members, $brokersIds);
                                        }else{
                                            $admins = AccountHelper::AdminUsers()->pluck('id'); // This returns a collection of IDs
                                            $members = array_merge($members, $admins->toArray());
                                        }
                                        $this->notifyAfterExpire($members);
                                    }
                                    break;
                                case Company::class:
                                        $company = $val->morphable;
                                        if($company)
                                        {
                                            
                                            $ownerId = $company->user->id ?? null;
                                            $members = array_merge($members, array_filter([$ownerId]));
                                            $this->notifyAfterExpire($members);
                                        }
                                    break;
                            }                          
                        }

                        $this->info("Val ID {$val->id} has been deactivated and notification sent.");
                    }
                });

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
        }
    }

    protected function notifyAfterExpire(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'val_lisence_expired'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-web','email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }
}
