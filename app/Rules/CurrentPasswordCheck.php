<?php
namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class CurrentPasswordCheck implements Rule
{
    public function passes($attribute, $value): bool
    {
        $credentials = DB::table('user_account_credentials')
            ->where('user_id', auth()->user()->id)
            ->first();

        return $credentials && Hash::check($value, $credentials->password);
    }

    public function message(): string
    {
        return __('The provided password does not match your current password.');
    }
}
