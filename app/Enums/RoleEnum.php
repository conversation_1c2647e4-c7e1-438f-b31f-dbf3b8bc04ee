<?php

namespace App\Enums;

enum RoleEnum : string
{
    // case NAMEINAPP = 'name-in-database';

    case BROKER = 'Broker';
    case OWNER = 'Owner';
    case ADMIN = 'super_admin';
    case ACCOUNTANT = 'Accountant';

    public function label(): string
    {
        return match ($this) {
            static::BROKER => __('Broker'),
            static::OWNER => __('Owner'),
            static::ADMIN => __('Super Admin'),
            static::ACCOUNTANT => __('Accountant')
        };
    }

    /**
     * Static method to get label from roles
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }
}
