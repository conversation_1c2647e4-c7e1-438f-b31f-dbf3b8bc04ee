<?php

namespace App\Enums;

enum UsabilitiesEnum : string
{
    // case NAMEINAPP = 'name-in-database';

    case COMMERCIAL = 'Commercial';
    case RESIDENTIAL = 'Residential';

    public function label(): string
    {
        return match ($this) {
            static::COMMERCIAL => 'Commercial',
            static::RESIDENTIAL => 'Residential',
        };
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }
}
