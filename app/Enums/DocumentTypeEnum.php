<?php

namespace App\Enums;

enum DocumentTypeEnum: string
{
    case Ownership = 'ownership';
    case OWNERSHIP_DOCUMENT = 'ownership_documents';
    case Representative = 'representative';

    public static function getDocumentTypeValues(): array
    {
        return [
            self::Ownership->value,
            self::Representative->value,
        ];
    }

    public function label(): string
    {
        return match($this) {
            self::Ownership => __('Ownership'),
            self::Representative => __('Representative'),
        };
    }

    /**
     * Static method to get label from status
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }
}
