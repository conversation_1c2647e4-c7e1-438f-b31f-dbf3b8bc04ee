<?php

namespace App\Enums;

enum PropertyTypeEnum: string
{
    case Property = 'property';
    case Unit = 'unit';

    public function color(): string
    {
        return match($this) {
            self::Property => 'blue',
            self::Unit => 'green',
        };
    }


    public function label(): string
    {
        return match($this) {
            self::Property => __('Property'),
            self::Unit => __('Unit'),
        };
    }

    /**
     * Static method to get label from status
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }
}
