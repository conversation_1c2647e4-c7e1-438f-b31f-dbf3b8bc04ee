<?php

namespace App\Enums;

use Illuminate\Support\Str;
use Modules\Ad\app\Models\Ad;
use Modules\Company\app\Models\Company;
use Modules\Property\app\Models\Property;

enum ValMorphTypeEnum: string
{

    // key should be the name of model permission for ex "view_ad" , so the case will named "case AD"
    case PROPERTY = Property::class;
    case AD = Ad::class;
    case COMPANY = Company::class;

    public function label(): string
    {
        return match($this) {
            self::PROPERTY => __('Property'),
            self::AD => __('Ad'),
            self::COMPANY => __('Company'),
        };
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }

    public function permissions(): array
    {
        $base = Str::lower($this->name);
        // you can add more permissions needs
        return [
            "view_{$base}"
        ];
    }

    private function hasPermissions(): bool
    {
        $user = auth()->user();
        return $user && collect($this->permissions())
                ->every(fn ($permission) => $user->can($permission));
    }
    public static function options(): array
    {
        $options = [];

        foreach (self::cases() as $case) {
            if ($case->hasPermissions()) {
                $options[$case->value] = $case->label();
            }
        }

        return $options;
    }
}
