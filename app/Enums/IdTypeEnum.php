<?php

namespace App\Enums;

enum IdTypeEnum: string
{
    case NATIONAL_ID = 'national_id';
    case RESIDENCY_PERMIT = 'residency_permit';
    case PASSPORT = 'passport';
    case GCC_ID = 'gcc_id';
    case OTHER = 'other';

    /**
     * Get the label for the enum value
     */
    public function label(): string
    {
        return match($this) {
            self::NATIONAL_ID => __('National ID'),
            self::RESIDENCY_PERMIT => __('Residency Permit'),
            self::PASSPORT => __('Passport'),
            self::GCC_ID => __('GCC Citizen ID'),
            self::OTHER => __('Other'),
        };
    }

    /**
     * Get all enum values as an array for select fields
     */
    public static function options(): array
    {
        return [
            self::NATIONAL_ID->value => __(self::NATIONAL_ID->label()),
            self::RESIDENCY_PERMIT->value => __(self::RESIDENCY_PERMIT->label()),
            self::PASSPORT->value => __(self::PASSPORT->label()),
            self::GCC_ID->value => __(self::GCC_ID->label()),
            self::OTHER->value => __(self::OTHER->label()),
        ];
    }
}
