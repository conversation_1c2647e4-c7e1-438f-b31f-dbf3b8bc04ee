<?php

namespace App\Services;

use App\Models\Suspension;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class SuspensionService
{
    public function create(User $record, $auth, $reason, $actionTaken)
    {
        $record->update([
            'is_suspended' => $record->is_suspended ? 0 : 1,
        ]);

        DB::table('sessions')
            ->where('user_id', $record->id)
            ->delete();

        Suspension::create([
            'reason' => $reason,
            'suspended_type' => User::class,
            'suspended_id' => $record->id,
            'suspended_by_type' => $auth::class,
            'suspended_by_id' => $auth->id,
            'action_taken' => $actionTaken,
        ]);
    }
}
