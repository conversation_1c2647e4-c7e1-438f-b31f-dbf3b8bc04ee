<?php

namespace App\Services;

use App\Enums\RoleEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\app\Models\Lease;
use Modules\Property\app\Models\Property;

class RoleService
{
    public function applyUserRoleRestrictions(Builder $query): Builder
    {
        $user = auth()->user();

        if ($user && !$user->hasRole(RoleEnum::ADMIN)) {
            $query->whereNotIN('name', $user->getRoleNames());
        }
        return $query;
    }

    public function applyBrokerRoleRestrictions(Builder $query,$column = 'broker_id',$relation = null): Builder
    {
        if (auth()->user()->hasRole(RoleEnum::BROKER)) {
            if($relation){
                $query->whereHas($relation, function ($subQuery) use ($column) {
                    if(get_class($subQuery->getModel()) == Lease::class){
                        $subQuery->with('property',function($q) use ($column){
                            $q->where($column, auth()->id());
                        });
                    }else{
                        $subQuery->where($column, auth()->id());
                    }
                });
            }else{
               $query->where($column, auth()->id());
            }
        }
        return $query;
    }
}
