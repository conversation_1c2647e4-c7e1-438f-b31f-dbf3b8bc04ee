<?php

namespace App\Services;

use App\Models\NafathLog;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class NafathService
{
    protected string $base_url;
    protected array $headers;
    private $app_key;
    private $app_id;

    public function __construct()
    {
        $environment = config('services.nafath.stage');
        $this->app_key  = $environment == 'live'  ? config('services.nafath.live_app_key')   : config('services.nafath.stage_app_key');
        $this->app_id   = $environment == 'live'  ? config('services.nafath.live_app_id')    : config('services.nafath.stage_app_id');
        $this->base_url = $environment == 'live'  ? config('services.nafath.production_url') : config('services.nafath.staging_url');
        $this->headers = [
            'APP-KEY' => $this->app_key,
            'APP-ID' => $this->app_id, 
        ];
    }

    public function sendRequest($nationalId)
    {
        $endpoint = '/api/v1/mfa/request';
        $uuid = (string) Str::uuid();
        $query = [
            'local' => 'ar',
            'requestId' => $uuid,
        ];

        $payload = [
            'nationalId' => $nationalId,
            'service' => config('services.nafath.title'),
        ];



        $url = $this->base_url . $endpoint . '?' . http_build_query($query);

        try {
            $response = Http::withHeaders($this->headers)
                ->accept('application/json')
                ->post($url, $payload);
                if ($response->successful()) {
                    NafathLog::create(
                        [
                            'request_id' => $uuid,
                            'national_id' => $nationalId,
                            'status' => 'pending'
                        ]
                    );
                    return $response;
                }
                
                return $response;

        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), 500);
        }
    }
}
