<?php

namespace App\Helpers;

use App\Enums\SyncUserAccountSourceEnum;
use App\Models\UserAccountCredential;
use Filament\Forms\Set;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;

class AccountHelper
{

    public static function getAccountOptions(): array
    {
        return Account::select('id', 'name', 'national_id')
            ->where('is_active', true)
            ->get()
            ->mapWithKeys(function ($account) {
                return [$account->id => "{$account->name} - {$account->national_id}"];
            })
            ->toArray();
    }

    public static function getAccountOptionsWithOwnerRole(): array
    {
        return Account::query()
            ->whereHas('accountRoles', function ($query) {
                $query->where('role', AccountRolesEnum::OWNER);
            })
            ->get()
            ->mapWithKeys(fn (Account $account) => [
                $account->id => $account->name ?? $account->first_name . ' ' . $account->last_name
            ])
            ->toArray();
    }

    public static function getInitialSearchOptions(): array
    {
        //return [];
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account')
            ->with('account')
            ->get()
            ->mapWithKeys(function (UserAccountCredential $credential) {
                $account = $credential->account;

                return [
                    $credential->national_id => "{$account->first_name} {$account->second_name} {$account->third_name} {$account->last_name} ( {$credential->national_id} )"
                ];
            })->toArray();
    }

    public static function searchAccountDataByNationalId(string $national_id)
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account')
            ->where('national_id', $national_id)
            ->with('account')
            ->first();
    }

    public static function getSearchAccountResultsByNationalId(string $national_id): array
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account')
            ->where('national_id', $national_id)
            ->with('account')
            ->get()
            ->mapWithKeys(function (UserAccountCredential $credential) {
                $account = $credential->account;

                return [
                    $credential->national_id => "{$account->first_name} {$account->second_name} {$account->third_name} {$account->last_name} ( {$credential->national_id} )"
                ];
            })->toArray();
    }


    public static function searchAccountDataByResidencyPermit(string $permitNumber, string $country)
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account', function($query) use ($country) {
                $query->where('country_of_issue', $country);
            })
            ->where('residency_permit', $permitNumber)
            ->with('account')
            ->first();
    }

    public static function searchAccountDataByPassport(string $passportNumber, string $country)
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account', function($query) use ($country) {
                $query->where('country_of_issue', $country);
            })
            ->where('passport', $passportNumber)
            ->with('account')
            ->first();
    }


    public static function searchAccountDataByGccId(string $gccId, string $country)
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account', function($query) use ($country) {
                $query->where('country_of_issue', $country);
            })
            ->where('gcc_id', $gccId)
            ->with('account')
            ->first();
    }

    public static function searchAccountDataByOtherId(string $idNumber, string $country)
    {
        return UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account', function($query) use ($country) {
                $query->where('country_of_issue', $country);
            })
            ->where('other_id', $idNumber)
            ->with('account')
            ->first();
    }


    public static function searchAccountDataByIdType(string $idType, string $idNumber, ?string $country = null)
    {
        return match ($idType) {
            'national_id' => self::searchAccountDataByNationalId($idNumber),
            'residency_permit' => self::searchAccountDataByResidencyPermit($idNumber, $country),
            'passport' => self::searchAccountDataByPassport($idNumber, $country),
            'gcc_id' => self::searchAccountDataByGccId($idNumber, $country),
            'other' => self::searchAccountDataByOtherId($idNumber, $country),
            default => null,
        };
    }

    public static function getSearchResultsByIdType(string $idType, string $idNumber, ?string $country = null): array
    {
        $query = UserAccountCredential::query()
            ->withoutGlobalScopes()
            ->whereNull('user_id')
            ->whereHas('account');

        if ($idType === 'national_id') {
            $query->where('national_id', $idNumber);
        } else {
            if ($country) {
                $query->whereHas('account', function($q) use ($country) {
                    $q->where('country_of_issue', $country);
                });
            }

            $query->where($idType, $idNumber);
        }

        return $query->with('account')
            ->get()
            ->mapWithKeys(function (UserAccountCredential $credential) {
                $account = $credential->account;
                $idValue = $credential->national_id ?? $credential->residency_permit ??
                    $credential->passport ?? $credential->gcc_id ?? $credential->other_id;

                return [
                    $idValue => "{$account->first_name} {$account->second_name} {$account->third_name} {$account->last_name} ( {$idValue} )"
                ];
            })->toArray();
    }

    public static function fillFormFields(Set $set, $user, string $source): void
    {
        $prefixes = static::getFieldPrefixes($source);
        $set($prefixes['related'] . 'first_name', $user->first_name);
        $set($prefixes['related'] . 'second_name', $user->second_name);
        $set($prefixes['related'] . 'third_name', $user->third_name);
        $set($prefixes['related'] . 'last_name', $user->last_name);
        $set($prefixes['related'] . 'phone_number', trim($user->phone, '+966'));
        $set($prefixes['related'] . 'national_id', $user->national_id);
        $set($prefixes['related'] . 'country_of_issue', $user->country_of_issue);
        $set($prefixes['related'] . 'id_type', $user->id_type);
        $set($prefixes['related'] . 'birth_date', $user->birth_date);
        $set($prefixes['direct'] . 'email', $user->email);
        $set($prefixes['direct'] . 'name', "{$user->first_name} {$user->second_name} {$user->third_name} {$user->last_name}");
    }

    public static function fillNafathFormFields(Set $set, $user, string $source): void
    {
        $prefixes = static::getFieldPrefixes($source);
        $set($prefixes['related'] . 'first_name', $user->first_name);
        $set($prefixes['related'] . 'second_name', $user->second_name);
        $set($prefixes['related'] . 'third_name', $user->third_name);
        $set($prefixes['related'] . 'last_name', $user->last_name);
        $set($prefixes['related'] . 'phone_number', trim($user->phone, '+966'));
        $set($prefixes['related'] . 'national_id', $user->national_id);
        $set($prefixes['related'] . 'birth_date', $user->birth_date);
        $set($prefixes['direct'] . 'email', $user->email);
        $set($prefixes['direct'] . 'name', "{$user->first_name} {$user->second_name} {$user->third_name} {$user->last_name}");
    }

    protected static function getFieldPrefixes(string $source): array
    {
        return match ($source) {
            SyncUserAccountSourceEnum::ADD_USER_COMPANY->value => ['direct' => 'user.', 'related' => 'user.userProfile.'],
            SyncUserAccountSourceEnum::ADD_USER->value => ['direct' => '', 'related' => 'userProfile.'],
            default => ['direct' => '', 'related' => ''],
        };
    }
}
