<?php

namespace App\Helpers;

use App\Enums\RoleEnum;
use App\Models\User;
use App\Services\RoleService;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Illuminate\Support\HtmlString;
use Modules\Company\app\Models\Company;
use Spatie\Permission\Models\Role;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Database\Eloquent\Builder;

class FormHelper
{
    public static function brokerField()
    {
        $user = auth()->user();
        $isBroker = $user->hasRole(RoleEnum::BROKER);

        // If user is a broker, return hidden field with broker's ID
        if ($isBroker) {
            return Hidden::make('broker_ids')
                ->default([$user->id]);
        }

        // If user not has company, show companies with brokers
        if (is_null($user->company?->id) || is_null($user->company_id)) {
            return Grid::make()
                ->schema([
                    Select::make('company_id')
                        ->label(__('Company'))
                        ->options(Company::get()->pluck('name', 'id'))
                        ->searchable()
                        ->preload()
                        ->required()
                        ->disabled(fn (string $context) => 
                            $context === 'edit' && !auth()->user()->can('edit_broker_field_property')
                        )
                        ->dehydrated(fn (string $context) => 
                            $context !== 'edit' || auth()->user()->can('edit_broker_field_property')
                        )
                        ->helperText(fn (string $context) => 
                            $context === 'edit' && !auth()->user()->can('edit_broker_field_property') 
                                ? new \Illuminate\Support\HtmlString('<span style="color: red;">' . __('You do not have permission to edit broker assignments. Contact the admin or the company owner.') . '</span>')
                                : null)
                        ->afterStateUpdated(function ($get,$set,$state,$livewire){
                            $set('broker_ids', null);
                            $units = $get('units') ?? [];
                            foreach ($units as $key => $unit) {
                                $set("units.{$key}.company_id", $state);
                            }
                            $livewire->data['documentOwnership']['company_id'] = $state;
                            $livewire->data['document']['company_id'] = $state;
                        })
                        ->reactive()
                        ->validationMessages([
                            'required' => __('Select Company is required'),
                        ]),


                    Select::make('broker_ids')
                        ->label(__('Brokers'))
                        ->multiple()
                        ->relationship(
                            name: 'brokers',
                            titleAttribute: 'name',
                            modifyQueryUsing: fn (Builder $query, callable $get) => 
                                $query->role(RoleEnum::BROKER)
                                    ->where('company_id', $get('company_id') ?: 0)
                        )
                        ->searchable()
                        ->preload()
                        ->required()
                        ->reactive()
                        ->disabled(fn (string $context) => 
                            $context === 'edit' && !auth()->user()->can('edit_broker_field_property')
                        )
                        ->dehydrated(fn (string $context) => 
                            $context !== 'edit' || auth()->user()->can('edit_broker_field_property')
                        )
                        ->validationMessages([
                            'required' => __('Selecting at least one broker is required'),
                        ]),
                ]);
        }

        return Select::make('broker_ids')
            ->label(__('Broker'))
            ->relationship(name: 'brokers', titleAttribute: 'name')
            ->searchable(['name'])
            ->preload()
            ->multiple()
            ->required()
            ->reactive()
            ->validationMessages([
                'required' => __('Select Broker is required'),
            ]);
    }

    public static function getHtmlHintMessagesForPasswordRequirements(): HtmlString
    {
        $requirements = [
            __('Password requirements:'),
            '✗ ' . __('At least 8 characters'),
            '✗ ' . __('One uppercase letter'),
            '✗ ' . __('One lowercase letter'),
            '✗ ' . __('One number'),
            '✗ ' . __('One special character (@$!%*?&)'),
        ];
        return new HtmlString(implode('<br>', $requirements));
    }
    public static function getValidationRequirementsForPasswordState(string $state): array
    {
        return [
            'length' => ['✗ ' . __('At least 8 characters'), '✓ ' . __('At least 8 characters'), strlen($state) >= 8],
            'uppercase' => ['✗ ' . __('One uppercase letter'), '✓ ' . __('One uppercase letter'), preg_match('/[A-Z]/', $state)],
            'lowercase' => ['✗ ' . __('One lowercase letter'), '✓ ' . __('One lowercase letter'), preg_match('/[a-z]/', $state)],
            'number' => ['✗ ' . __('One number'), '✓ ' . __('One number'), preg_match('/\d/', $state)],
            'special' => ['✗ ' . __('One special character (@$!%*?&)'), '✓ ' . __('One special character (@$!%*?&)'), preg_match('/[@$!%*?&]/', $state)],
        ];
    }
    public static function getRolesOptionsForUserForm()
    {
        $query = Role::query();
        $query = app(RoleService::class)->applyUserRoleRestrictions($query)->whereNot('name',RoleEnum::OWNER)->get();
        return $query->mapWithKeys(fn (Role $role) => [$role->id => $role->translated_name ?? $role->name])
        ->toArray();
    }
}
