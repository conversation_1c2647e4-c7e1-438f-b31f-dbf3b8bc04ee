<?php

namespace App\Shared;

use Khaleds\FilamentTranslations\FilamentTranslationsPlugin;
use Khaleds\FilamentTranslations\FilamentTranslationsSwitcherPlugin;
use Filament\Navigation\NavigationGroup;

use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Awcodes\LightSwitch\LightSwitchPlugin;
use Awcodes\LightSwitch\Enums\Alignment;
use Modules\Ad\app\Filament\AdPlugin;
use Modules\Cms\app\Filament\CmsPlugin;
use Modules\Company\app\Filament\CompanyPlugin;
use Modules\EjarIntegration\app\Filament\EjarIntegrationPlugin;
use Modules\Invoice\app\Filament\InvoicePlugin;
use Modules\MagicReport\app\Filament\MagicReportPlugin;
use Modules\Service\app\Filament\ServicePlugin;
use Modules\Subscription\app\Filament\FeaturesPlugin;
use Modules\Subscription\app\Filament\PlanPlugin;
use Modules\Subscription\app\Filament\PrivatePermissionPlugin;
use Modules\Subscription\app\Filament\SubscriptionPlugin;
use Modules\Subscription\app\Http\Middleware\SubscriptionForAjax;
use Modules\Ticket\app\Filament\TicketPlugin;
use Modules\Account\app\Filament\AccountPlugin;
use Modules\Lease\app\Filament\LeasePlugin;
use Modules\Notification\app\Filament\NotificationPlugin;
use Modules\Payment\app\Filament\PaymentMethodPlugin;
use Modules\Request\app\Filament\RequestPlugin;
use Modules\Organization\app\Filament\OrganizationPlugin;

class Injection
{
    public static function getGroups(): array
    {
        return [
            NavigationGroup::make()
                ->label(fn() =>__('filament-shield::filament-shield.nav.group'))
                ->icon('heroicon-o-user-group')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Leases Management'))
                ->icon('heroicon-o-pencil')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Properties Management'))
                ->icon('heroicon-o-building-office')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Amenities Management'))
                ->icon('heroicon-o-shopping-cart')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() => __('Invoice'))
                ->icon('heroicon-o-receipt-percent')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() => __('Invoice'))
                ->icon('heroicon-o-bell') // Basic bell icon
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() => __('Notifications'))
                ->icon('heroicon-o-bell') // Basic bell icon
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Ejar Integration'))
                ->icon('heroicon-o-square-3-stack-3d')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Keraa App'))
                ->icon('heroicon-o-square-3-stack-3d')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Maintenance Management'))
                ->icon('heroicon-o-square-3-stack-3d')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Support Tickets'))
                ->icon('heroicon-o-ticket')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('Support Tickets'))
                ->icon('heroicon-o-ticket')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() =>__('CMS'))
                ->icon('heroicon-o-document-text')
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() => __('Notifications'))
                ->icon('heroicon-o-bell') // Basic bell icon
                ->collapsed(),
            NavigationGroup::make()
                ->label(fn() => __('Subscription Management'))
                ->icon('heroicon-o-cube')
                ->collapsed(),

            NavigationGroup::make()
                ->label(fn() =>__('filament-translations::translation.group'))
                ->icon('heroicon-o-cog-6-tooth')
                ->collapsed(),
            // Uncomment if needed:


        ];
    }

    public static function getMiddleware(): array
    {
        return [
            EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            AuthenticateSession::class,
            ShareErrorsFromSession::class,
            VerifyCsrfToken::class,
            SubstituteBindings::class,
            DisableBladeIconComponents::class,
            DispatchServingFilamentEvent::class,
            SubscriptionForAjax::class
        ];
    }

    public static function getPlugins(): array
    {
        return [
            \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make(),
            AccountPlugin::make(),
            ServicePlugin::make(),
            FilamentTranslationsPlugin::make(),
            FilamentTranslationsSwitcherPlugin::make(),
            FilamentTranslationsPlugin::make()->allowGoogleTranslateScan(),
            CompanyPlugin::make(),
            InvoicePlugin::make(),
            AdPlugin::make(),
            LeasePlugin::make(),
            TicketPlugin::make(),
            CmsPlugin::make(),
            NotificationPlugin::make(),
            PrivatePermissionPlugin::make(),
            PlanPlugin::make(),
            FeaturesPlugin::make(),
            SubscriptionPlugin::make(),
            PaymentMethodPlugin::make(),
            EjarIntegrationPlugin::make(),
            LightSwitchPlugin::make()
                ->position(Alignment::TopCenter)
                ->enabledOn(['login','register','password-reset','email-verification']),
            OrganizationPlugin::make(),
        ];
    }
}
