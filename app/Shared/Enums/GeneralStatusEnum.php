<?php

namespace App\Shared\Enums;

enum GeneralStatusEnum : string
{
    case Active = 'active';
    case Inactive = 'inactive';
    case Pending = 'pending';
    case Archived = 'archived';

    public function label(): string
    {
        return match($this) {
            self::Active => 'Active',
            self::Inactive => 'Inactive',
            self::Pending => 'Pending',
            self::Archived => 'Archived',
        };
    }

    public function value(): string
    {
        return $this->value;
    }

    public function isActive(): bool
    {
        return $this === self::Active;
    }

    public static function getOptions(): array
    {
        return [
            self::Active->value => self::Active->label(),
            self::Inactive->value => self::Inactive->label(),
            self::Pending->value => self::Pending->label(),
            self::Archived->value => self::Archived->label(),
        ];
    }

    public static function getActiveOptions(): array
    {
        return [
            self::Active->value => self::Active->label(),
            self::Inactive->value => self::Inactive->label(),
        ];
    }
}
