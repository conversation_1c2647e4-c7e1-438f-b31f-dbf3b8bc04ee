<?php

namespace App\Shared;

trait CalculatesDailyStats
{
    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    private function calculateDailyTotals($collectionData, $dateColumn, $sumColumn): array
    {
        // Prepare daily totals
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData) use ($sumColumn) {
            return $dayData->sum($sumColumn);
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map totals to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }
}