<?php

namespace App\Shared\Helpers;

use App\Enums\IdTypeEnum;
use App\Enums\SyncUserAccountSourceEnum;
use App\Forms\Components\NationalIdSearchSection;
use App\Helpers\AccountHelper;
use App\Helpers\FormHelper;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;
use Closure;
use Modules\Account\app\Models\Account;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;
use App\Forms\Components\HijriDatePicker;

class FormComponents
{
    public static function passwordInput(string $name, ?string $label = null, bool $withGenerator = false): TextInput
    {
        $passwordInput = TextInput::make($name)
            ->label($label ?? __('Password'))
            ->password()
            ->revealable()
            ->required()
            ->minLength(8)
            ->confirmed()
            ->placeholder(__('Enter your password'))
            ->live()
            ->hidden(fn (Get $get): bool => $get('is_data_fetched') === true || NationalIdSearchSection::isDataFetched())
            ->suffixIcon(fn ($state) =>
            strlen($state) >= 8 &&
            preg_match('/[A-Z]/', $state) &&
            preg_match('/[a-z]/', $state) &&
            preg_match('/\d/', $state) &&
            preg_match('/[@$!%*?&]/', $state)
                ? 'heroicon-o-check-circle'
                : 'heroicon-o-x-circle'
            )
            ->suffixIconColor(fn ($state) =>
            strlen($state) >= 8 &&
            preg_match('/[A-Z]/', $state) &&
            preg_match('/[a-z]/', $state) &&
            preg_match('/\d/', $state) &&
            preg_match('/[@$!%*?&]/', $state)
                ? 'success'
                : 'danger'
            )
            ->rules([
                'required',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
            ])
            ->validationMessages([
                'required' => __('Password is required'),
                'min' => __('Password must be at least 8 characters'),
                'confirmed' => __('Password confirmation does not match'),
                'regex' => __('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
            ])
            ->helperText(function ($state) {
                if (empty($state)) {
                    return FormHelper::getHtmlHintMessagesForPasswordRequirements();
                }
                $requirements = FormHelper::getValidationRequirementsForPasswordState($state);
                $message = __('Password requirements:') . '<br>';
                foreach ($requirements as $requirement) {
                    $message .= $requirement[2] ? $requirement[1] : $requirement[0];
                    $message .= '<br>';
                }

                return new HtmlString($message);
            });

        if ($withGenerator) {
            $passwordInput->suffixAction(
                \Filament\Forms\Components\Actions\Action::make('generatePassword')
                    ->icon('heroicon-o-sparkles')
                    ->tooltip(__('Generate Password'))
                    ->action(function ($set) use ($name) {
                        $password = static::generateStrongPassword();
                        $set($name, $password);
                        $set($name . '_confirmation', $password);
                    })
            );
        }

        return $passwordInput;
    }

    public static function passwordConfirmation(string $passwordField = 'password', ?string $label = null): TextInput
    {
        return TextInput::make($passwordField . '_confirmation')
            ->label($label ?? __('Password Confirmation'))
            ->password()
            ->revealable()
            ->required()
            ->placeholder(__('Confirm your password'))
            ->live()
            ->hidden(fn (Get $get): bool => $get('is_data_fetched') === true || NationalIdSearchSection::isDataFetched())
            ->same($passwordField)
            ->suffixIcon(fn ($state, $get) =>
            $state === $get($passwordField) && !empty($state)
                ? 'heroicon-o-check-circle'
                : 'heroicon-o-x-circle'
            )
            ->suffixIconColor(fn ($state, $get) =>
            $state === $get($passwordField) && !empty($state)
                ? 'success'
                : 'danger'
            )
            ->helperText(fn ($state, $get) =>
            new HtmlString(
                $state !== $get($passwordField)
                    ? '✗ ' . __('Passwords do not match')
                    : ($state
                    ? '✓ ' . __('Passwords match')
                    : ''
                )
            )
            )
            ->validationMessages([
                'required' => __('Password confirmation is required'),
                'same' => __('Passwords do not match'),
            ]);
    }

    private static function generateStrongPassword(int $length = 12): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $special = '@$!%*?&';

        $password = '';

        // Ensure at least one character from each required set
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $special[random_int(0, strlen($special) - 1)];

        // Fill the rest of the password
        $all = $uppercase . $lowercase . $numbers . $special;
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $all[random_int(0, strlen($all) - 1)];
        }

        // Shuffle the password to make it more random
        return str_shuffle($password);
    }


    ///////////////////////////////////////////////////////////////////////////////////////
    public static function nationalIdInput(string $name, ?string $label = null): TextInput
    {
        return TextInput::make($name)
            ->label($label ?? __('National ID'))
            ->placeholder(__('Enter national ID'))
            ->required()
            ->placeholder('1xxxxxxxxx or 2xxxxxxxxx')
            ->extraInputAttributes([
                'maxlength' => '10',
                'minlength' => '10',
                'pattern' => '[12][0-9]{9}',
                'style' => 'appearance: textfield; -webkit-appearance: textfield; -moz-appearance: textfield;',
                'oninput' => "
                this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                if (this.value.length > 0 && !['1','2'].includes(this.value[0])) {
                    this.value = '';
                }
                this.dispatchEvent(new Event('input'));
            "
            ])
            ->rules([
                'required',
                'numeric',
                'digits:10',
                'regex:/^[12]\d{9}$/',
            ])
            ->validationMessages([
                'required' => __('National ID is required'),
                'digits' => __('National ID must be exactly 10 digits'),
                'numeric' => __('National ID must contain only numbers'),
                'unique' => __('National ID is already exists'),
                'regex' => __('National ID must start with 1 for Saudi citizens or 2 for non-Saudi residents'),
            ]);
    }

    public static function countrySelect(string $name = 'country_of_issue', $gccOnly = false): Select
    {
        $country = Country::make($name)
            ->searchable()
            ->label(__('Country Of Issue'))
            ->required();

        // Check if $gccOnly is a closure or a boolean true
        if ($gccOnly === true || (is_callable($gccOnly) && $gccOnly instanceof \Closure)) {
            // Only GCC countries: UAE, Bahrain, Kuwait, Oman, Qatar, Saudi Arabia
            $country->only(['AE', 'BH', 'KW', 'OM', 'QA', 'SA']);
        }

        return $country;
    }

    public static function birthDateInput(?string $label = null,string $name = 'birth_date'): HijriDatePicker
    {
        return HijriDatePicker::make($name)
            ->label($label ?? __('Birth Date'))
            ->required()
            ->placeholder(__('Select your birth date'))
            ->maxDate(now()->subYears(18))
            ->showConvertedDate()
            ->validationMessages([
                'required' => __('Birth date is required'),
                'max_date' => __('You must be at least 18 years old'),
            ]);
    }


    public static function idFieldSet(
        string $fieldPrefix = '',
        string $table = 'accounts',
        bool $ignoreRecord = true,
        ?callable $readonlyCondition = null
    ): Fieldset {
        $idTypeField = $fieldPrefix . 'id_type';
        $nationalIdField = $fieldPrefix . 'national_id';
        $countryField = $fieldPrefix . 'country_of_issue';

        // Determine visibility conditions based on fieldPrefix
        $nonNationalIdTypes = [
            IdTypeEnum::RESIDENCY_PERMIT->value,
            IdTypeEnum::PASSPORT->value,
            IdTypeEnum::GCC_ID->value,
            IdTypeEnum::OTHER->value
        ];

        // Default readonly condition if none provided
        if ($readonlyCondition === null) {
            $readonlyCondition = fn(): bool => NationalIdSearchSection::isReadOnly();
        }

        return Fieldset::make('ID Information')
            ->label(__('ID Information'))
            ->schema([
                Select::make($idTypeField)
                    ->label(__('ID Type'))
                    ->options(IdTypeEnum::options())
                    ->default(IdTypeEnum::NATIONAL_ID->value)
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function (callable $set) use ($nationalIdField, $countryField) {
                        $set($nationalIdField, null);
                        $set($countryField, null);
                        $set('is_loading', false);
                        $set('is_filled', false);
                    }),

                // National ID input with enhanced functionality
                static::nationalIdInput($nationalIdField)
                    ->readonly($readonlyCondition)
                    ->unique(table: $table, column: 'national_Id', ignoreRecord: $ignoreRecord)
                    ->visible(fn (Get $get): bool => $get($idTypeField) === IdTypeEnum::NATIONAL_ID->value)
                    ->live(debounce: 500),

                // Rest of your fields...
                Group::make()
                    ->schema([
                        static::countrySelect($countryField)
                            ->searchable()
                            ->required(fn (Get $get): bool => $get($idTypeField) !== IdTypeEnum::RESIDENCY_PERMIT->value)
                            ->label(__('Country Of Issue'))
                            ->visible(fn (Get $get): bool => in_array($get($idTypeField), $nonNationalIdTypes)),
                    ])
                    ->visible(fn (Get $get): bool => in_array($get($idTypeField), [IdTypeEnum::RESIDENCY_PERMIT->value, IdTypeEnum::PASSPORT->value, IdTypeEnum::OTHER->value])),

                Group::make()
                    ->schema([
                        Select::make($countryField)
                            ->label(__('Country Of Issue'))
                            ->options([
                                'AE' => __('United Arab Emirates'),
                                'BH' => __('Bahrain'),
                                'KW' => __('Kuwait'),
                                'OM' => __('Oman'),
                                'QA' => __('Qatar'),
                                'SA' => __('Saudi Arabia'),
                            ])
                            ->default('SA')
                            ->required(),
                    ])
                    ->visible(fn (Get $get): bool => $get($idTypeField) === IdTypeEnum::GCC_ID->value),

                TextInput::make($nationalIdField)
                    ->dehydrated(fn (Get $get): bool => in_array($get($idTypeField), $nonNationalIdTypes))
                    ->label(fn (Get $get): string => match ($get($idTypeField)) {
                        IdTypeEnum::RESIDENCY_PERMIT->value => __('Residency Permit Number'),
                        IdTypeEnum::PASSPORT->value => __('Passport Number'),
                        IdTypeEnum::GCC_ID->value => __('GCC ID Number'),
                        IdTypeEnum::OTHER->value => __('ID Number'),
                        default => '',
                    })
                    ->placeholder(fn (Get $get): string => match ($get($idTypeField)) {
                        IdTypeEnum::RESIDENCY_PERMIT->value => __('Enter residency permit number'),
                        IdTypeEnum::PASSPORT->value => __('Enter passport number'),
                        IdTypeEnum::GCC_ID->value => __('Enter GCC ID number'),
                        IdTypeEnum::OTHER->value => __('Enter ID number'),
                        default => '',
                    })
                    ->visible(fn (Get $get): bool => in_array($get($idTypeField), $nonNationalIdTypes))
                    ->required(fn (Get $get): bool => in_array($get($idTypeField), $nonNationalIdTypes))
                    ->extraInputAttributes(fn (Get $get): array => match ($get($idTypeField)) {
                        IdTypeEnum::RESIDENCY_PERMIT->value => [
                            'maxlength' => '50',
                            'minlength' => '10',
                        ],
                        IdTypeEnum::PASSPORT->value => [
                            'maxlength' => '50',
                            'minlength' => '5',
                        ],
                        IdTypeEnum::GCC_ID->value => [
                            'maxlength' => '50',
                            'minlength' => '9',
                        ],
                        IdTypeEnum::OTHER->value => [
                            'maxlength' => '50',
                            'minlength' => '3',
                        ],
                        default => [],
                    })
                    ->rules(fn (Get $get): array => match ($get($idTypeField)) {
                        IdTypeEnum::RESIDENCY_PERMIT->value => [
                            "required_if:{$idTypeField}," . IdTypeEnum::RESIDENCY_PERMIT->value,
                            'string',
                            'min:5',
                            'max:50',
                        ],
                        IdTypeEnum::PASSPORT->value => [
                            "required_if:{$idTypeField}," . IdTypeEnum::PASSPORT->value,
                            'string',
                            'min:5',
                            'max:50',
                        ],
                        IdTypeEnum::GCC_ID->value => [
                            "required_if:{$idTypeField}," . IdTypeEnum::GCC_ID->value,
                            'string',
                            'min:9',
                            'max:50',
                        ],
                        IdTypeEnum::OTHER->value => [
                            "required_if:{$idTypeField}," . IdTypeEnum::OTHER->value,
                            'string',
                            'min:3',
                            'max:50',
                        ],
                        default => [],
                    })
                    ->validationMessages([
                        'required_if' => __('ID number is required'),
                        'numeric' => __('ID number must contain only numbers'),
                        'digits' => __('ID number must be exactly :digits digits'),
                        'min' => __('ID number must be at least :min characters'),
                        'max' => __('ID number must not exceed :max characters'),
                    ]),
            ]);
    }



    /////////////////////////////////////////////////////////////////////////////////////
    /**
     * Create an enhanced National ID input component with auto-fill functionality
     * This can be used standalone without a fieldset on registr
     */
    public static function nationalIdInputRegisterForm(
        string $name = 'national_id',
        string $table = 'user_profiles',
        bool $ignoreRecord = true,
        ?callable $readonlyCondition = null
    ): TextInput {
        if ($readonlyCondition === null) {
            $readonlyCondition = fn(): bool => NationalIdSearchSection::isReadOnly();
        }

        return TextInput::make($name)
            ->label(__('National ID'))
            ->required()
            ->unique(table: $table, column: 'national_Id', ignoreRecord: $ignoreRecord)
            ->placeholder('1xxxxxxxxx or 2xxxxxxxxx')
            ->readonly($readonlyCondition)
            ->live(debounce: 500)
            ->suffix(fn (Get $get) => $get('is_loading')
                ? new HtmlString(view('filament::components.icon', [
                    'icon' => 'heroicon-o-arrow-path',
                    'class' => 'h-4 w-4 animate-spin'
                ]))
                : ($get('is_filled')
                    ? new HtmlString(view('filament::components.icon', [
                        'icon' => 'heroicon-o-check-circle',
                        'class' => 'h-4 w-4'
                    ]))
                    : null
                )
            )
            ->hint(fn (Get $get) => $get('is_filled')
                ? new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                    .
                    view('filament::components.icon', [
                        'icon' => 'heroicon-o-check-circle',
                        'class' => 'h-4 w-4'
                    ]) . __('Data auto filled by national ID') . '</span>')
                : null
            )
            ->afterStateUpdated(function ($state, Set $set, Get $get) {
                // Reset states
                $set('is_loading', false);
                $set('is_filled', false);

                if (!$state || strlen($state) < 10) {
                    return;
                }
                // Set loading state
                $set('is_loading', true);
                try {
                    $account_data = AccountHelper::searchAccountDataByNationalId($state);
                    if (!$account_data) {
                        return;
                    }
                    $avatar_url = $account_data->account->getMedia('profile')->first() ? $account_data->account->getMedia('profile')->first()->getUrl() : null;
                    if ($avatar_url) {
                        $set('existing_avatar_url', $avatar_url);
                    }
                    AccountHelper::fillFormFields($set, $account_data->account, SyncUserAccountSourceEnum::REGISTER->value);
                    // Set success state
                    $set('is_filled', true);
                } finally {
                    // Ensure loading state is always turned off
                    $set('is_loading', false);
                }
            })
            ->extraInputAttributes([
                'maxlength' => '10',
                'minlength' => '10',
                'pattern' => '[12][0-9]{9}',
                'style' => 'appearance: textfield; -webkit-appearance: textfield; -moz-appearance: textfield;',
                'oninput' => "
                this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                if (this.value.length > 0 && !['1','2'].includes(this.value[0])) {
                    this.value = '';
                }
                this.dispatchEvent(new Event('input'));
            "
            ])
            ->rules([
                'required',
                'numeric',
                'digits:10',
                'regex:/^[12]\d{9}$/',
            ])
            ->validationMessages([
                'required' => __('National ID is required'),
                'digits' => __('National ID must be exactly 10 digits'),
                'unique' => __('National ID is already in exists'),
                'numeric' => __('National ID must contain only numbers'),
                'regex' => __('National ID must start with 1 for Saudi citizens or 2 for non-Saudi residents'),
            ]);
    }

    /**
     * Create an enhanced alternative ID input (residency permit, passport, etc.) with auto-fill functionality
     */
    public static function alternativeIdInputTypeId(
        string $name = 'national_id',
        string $idTypeField = 'id_type',
        string $countryField = 'country_of_issue'
    ): TextInput {
        return TextInput::make($name)
            ->dehydrated(fn (Get $get): bool => in_array($get($idTypeField), [
                IdTypeEnum::RESIDENCY_PERMIT->value,
                IdTypeEnum::PASSPORT->value,
                IdTypeEnum::GCC_ID->value,
                IdTypeEnum::OTHER->value
            ]))
            ->label(fn (Get $get): string => match ($get($idTypeField)) {
                IdTypeEnum::RESIDENCY_PERMIT->value => __('Residency Permit Number'),
                IdTypeEnum::PASSPORT->value => __('Passport Number'),
                IdTypeEnum::GCC_ID->value => __('GCC ID Number'),
                IdTypeEnum::OTHER->value => __('ID Number'),
                default => '',
            })
            ->placeholder(fn (Get $get): string => match ($get($idTypeField)) {
                IdTypeEnum::RESIDENCY_PERMIT->value => __('Enter residency permit number'),
                IdTypeEnum::PASSPORT->value => __('Enter passport number'),
                IdTypeEnum::GCC_ID->value => __('Enter GCC ID number'),
                IdTypeEnum::OTHER->value => __('Enter ID number'),
                default => '',
            })
            ->visible(fn (Get $get): bool => in_array($get($idTypeField), [
                IdTypeEnum::RESIDENCY_PERMIT->value,
                IdTypeEnum::PASSPORT->value,
                IdTypeEnum::GCC_ID->value,
                IdTypeEnum::OTHER->value
            ]))
            ->required(fn (Get $get): bool => in_array($get($idTypeField), [
                IdTypeEnum::RESIDENCY_PERMIT->value,
                IdTypeEnum::PASSPORT->value,
                IdTypeEnum::GCC_ID->value,
                IdTypeEnum::OTHER->value
            ]))
            ->live(debounce: 500)
            ->suffix(fn (Get $get) => $get('is_loading')
                ? new HtmlString(view('filament::components.icon', [
                    'icon' => 'heroicon-o-arrow-path',
                    'class' => 'h-4 w-4 animate-spin'
                ]))
                : ($get('is_filled')
                    ? new HtmlString(view('filament::components.icon', [
                        'icon' => 'heroicon-o-check-circle',
                        'class' => 'h-4 w-4'
                    ]))
                    : null
                )
            )
            ->hint(fn (Get $get) => $get('is_filled')
                ? new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                    .
                    view('filament::components.icon', [
                        'icon' => 'heroicon-o-check-circle',
                        'class' => 'h-4 w-4'
                    ]) . __('Data auto filled') . '</span>')
                : null
            )
            ->afterStateUpdated(function ($state, Set $set, Get $get) use ($idTypeField, $countryField) {
                // Reset states
                $set('is_loading', false);
                $set('is_filled', false);

                // Get the current ID type and country
                $idType = $get($idTypeField);
                $country = $get($countryField);

                // Validate minimum input length based on ID type
                $minLength = match($idType) {
                    IdTypeEnum::RESIDENCY_PERMIT->value => 10,
                    IdTypeEnum::PASSPORT->value => 5,
                    IdTypeEnum::GCC_ID->value => 9,
                    IdTypeEnum::OTHER->value => 3,
                    default => 0,
                };

                if (!$state || strlen($state) < $minLength || !$country) {
                    return;
                }

                // Set loading state
                $set('is_loading', true);
                try {
                    // Search using appropriate method based on ID type
                    $account_data = match($idType) {
                        IdTypeEnum::RESIDENCY_PERMIT->value => AccountHelper::searchAccountDataByResidencyPermit($state, $country),
                        IdTypeEnum::PASSPORT->value => AccountHelper::searchAccountDataByPassport($state, $country),
                        IdTypeEnum::GCC_ID->value => AccountHelper::searchAccountDataByGccId($state, $country),
                        IdTypeEnum::OTHER->value => AccountHelper::searchAccountDataByOtherId($state, $country),
                        default => null,
                    };

                    if (!$account_data) {
                        return;
                    }

                    $avatar_url = $account_data->account->getMedia('profile')->first() ? $account_data->account->getMedia('profile')->first()->getUrl() : null;
                    if ($avatar_url) {
                        $set('existing_avatar_url', $avatar_url);
                    }

                    AccountHelper::fillFormFields($set, $account_data->account, SyncUserAccountSourceEnum::REGISTER->value);
                    // Set success state
                    $set('is_filled', true);
                } finally {
                    // Ensure loading state is always turned off
                    $set('is_loading', false);
                }
            })
            ->extraInputAttributes(fn (Get $get): array => match ($get($idTypeField)) {
                IdTypeEnum::RESIDENCY_PERMIT->value => [
                    'maxlength' => '50',
                    'minlength' => '10',
                ],
                IdTypeEnum::PASSPORT->value => [
                    'maxlength' => '50',
                    'minlength' => '10',
                ],
                IdTypeEnum::GCC_ID->value => [
                    'maxlength' => '50',
                    'minlength' => '10',
                ],
                IdTypeEnum::OTHER->value => [
                    'maxlength' => '50',
                    'minlength' => '10',
                ],
                default => [],
            })
            ->rules(fn (Get $get): array => match ($get($idTypeField)) {
                IdTypeEnum::RESIDENCY_PERMIT->value => [
                    "required_if:{$idTypeField}," . IdTypeEnum::RESIDENCY_PERMIT->value,
                    'string',
                    'min:5',
                    'max:50',
                ],
                IdTypeEnum::PASSPORT->value => [
                    "required_if:{$idTypeField}," . IdTypeEnum::PASSPORT->value,
                    'string',
                    'min:5',
                    'max:50',
                ],
                IdTypeEnum::GCC_ID->value => [
                    "required_if:{$idTypeField}," . IdTypeEnum::GCC_ID->value,
                    'string',
                    'min:9',
                    'max:50',
                ],
                IdTypeEnum::OTHER->value => [
                    "required_if:{$idTypeField}," . IdTypeEnum::OTHER->value,
                    'string',
                    'min:3',
                    'max:50',
                ],
                default => [],
            })
            ->validationMessages([
                'required_if' => __('ID number is required'),
                'numeric' => __('ID number must contain only numbers'),
                'digits' => __('ID number must be exactly :digits digits'),
                'min' => __('ID number must be at least :min characters'),
                'max' => __('ID number must not exceed :max characters'),
            ]);
    }

    /**
     * Create an enhanced ID fieldset that uses both enhanced input types for auto-fill functionality on Register Form
     */
    public static function registerIdFieldSet(
        string $fieldPrefix = '',
        string $table = 'accounts',
        bool $ignoreRecord = true,
        ?callable $readonlyCondition = null
    ): Fieldset {
        $idTypeField = $fieldPrefix . 'id_type';
        $nationalIdField = $fieldPrefix . 'national_id';
        $countryField = $fieldPrefix . 'country_of_issue';

        // Determine visibility conditions based on fieldPrefix
        $nonNationalIdTypes = [
            IdTypeEnum::RESIDENCY_PERMIT->value,
            IdTypeEnum::PASSPORT->value,
            IdTypeEnum::GCC_ID->value,
            IdTypeEnum::OTHER->value
        ];

        // Default readonly condition if none provided
        if ($readonlyCondition === null) {
            $readonlyCondition = fn(): bool => NationalIdSearchSection::isReadOnly();
        }

        return Fieldset::make('ID Information')
            ->label(__('ID Information'))
            ->schema([
                Select::make($idTypeField)
                    ->label(__('ID Type'))
                    ->options(IdTypeEnum::options())
                    ->default(IdTypeEnum::NATIONAL_ID->value)
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function (callable $set) use ($nationalIdField, $countryField) {
                        $set($nationalIdField, null);
                        $set($countryField, null);
                        $set('is_loading', false);
                        $set('is_filled', false);
                    }),

                // Enhanced National ID input with auto-fill functionality
                static::nationalIdInputRegisterForm($nationalIdField, $table, $ignoreRecord, $readonlyCondition)
                    ->visible(fn (Get $get): bool => $get($idTypeField) === IdTypeEnum::NATIONAL_ID->value),

                // Country selector for non-national ID types - moved before alternative ID to establish country first
                Group::make()
                    ->schema([
                        static::countrySelect($countryField)
                            ->searchable()
                            ->required()
                            ->label(__('Country Of Issue'))
                            ->reactive()
                            ->afterStateUpdated(function (Set $set) {
                                $set('is_filled', false);
                            })
                    ])
                    ->visible(fn (Get $get): bool => in_array($get($idTypeField), ['residency_permit', 'passport', 'other'])),

                Group::make()
                    ->schema([
                        Select::make($countryField)
                            ->label(__('Country Of Issue'))
                            ->options([
                                'AE' => __('United Arab Emirates'),
                                'BH' => __('Bahrain'),
                                'KW' => __('Kuwait'),
                                'OM' => __('Oman'),
                                'QA' => __('Qatar'),
                                'SA' => __('Saudi Arabia'),
                            ])
                            ->default('SA')
                            ->required(),
                    ])
                    ->visible(fn (Get $get): bool => $get($idTypeField) === IdTypeEnum::GCC_ID->value),

                // Enhanced alternative ID input with auto-fill functionality
                static::alternativeIdInputTypeId($nationalIdField, $idTypeField, $countryField)
            ]);
    }



}
