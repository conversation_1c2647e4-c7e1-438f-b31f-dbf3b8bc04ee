<?php
namespace App\Shared;


use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

abstract class BaseModel extends Model
   implements HasMedia
{

    use HasTranslations,SoftDeletes
   ,InteractsWithMedia;

    protected $translatable=['name','description'];

    protected $guarded=['id'];


}
