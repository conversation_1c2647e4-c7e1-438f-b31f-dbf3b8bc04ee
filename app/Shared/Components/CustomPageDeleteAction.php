<?php

namespace App\Shared\Components;

use Filament\Actions\DeleteAction;

class CustomPageDeleteAction extends DeleteAction
{
    public array $relationsList = [];

    public function setRelationsList(array $relationsList): static
    {
        $this->relationsList = $relationsList;

        return $this;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->action(function ($record) {

            $hasRelations = false;

            foreach ($this->relationsList as $relation) {
                if ( !$record->$relation()->get()->isEmpty() ) {
                    $hasRelations = true;
                    if($hasRelations) break;
                }
            }
            if ($hasRelations) {
                \Filament\Notifications\Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete'))
                    ->body(__('This record has related records. Please remove all related records first.'))
                    ->send();

                return;
            }

            $record->delete();
            \Filament\Notifications\Notification::make()
                ->success()
                ->title(__('Deleted'))
                ->body(__('The record has been deleted.'))
                ->send();
        });
    }
}
