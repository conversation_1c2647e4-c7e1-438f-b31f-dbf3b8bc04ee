<?php
namespace app\Shared\Components;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Rupadana\FilamentSwiper\Infolists\Components\SwiperImageEntry;


class CustomSwiperImageEntry extends SwiperImageEntry{
    protected string $view = 'filament.infolists.components.swiper-image';

    protected bool $isParent = false;

    public function getState(): array
    {
        if ($this->getSpatie() == false) {
            return $this->getState();
        }

        $record = $this->getRecord();

        if (!$this->isParent) {
            $relationshipName = $this->getName();
        }else{
            $relationshipName = $this->getRelationshipName();
        }


        if (filled($relationshipName)) {
            $record = $record?->getRelationValue($relationshipName);
        }

        if (!$record) {
            return [];
        }

        $collection = $this->getCollection();

        return $record?->getRelationValue('media')
            ->filter(fn (Media $media): bool => blank($collection) || ($media->getAttributeValue('collection_name') === $collection))
            ->sortBy('order_column')
            ->map(fn (Media $media): array => [
                'uuid' => $media->uuid,
                'mime_type' => $media->mime_type,
                'url' => $media->getFullUrl(),
            ])
            ->values()
            ->all();
    }

    public function getImageUrl(?string $state = null): ?string
    {

        if ($this->getSpatie() == false) {
            return parent::getImageUrl($state);
        }

        $record = $this->getRecord();

        if (! $record) {
            return null;
        }

        if (!$this->isParent) {
            $relationshipName = $this->getName();
        }else{
            $relationshipName = $this->getRelationshipName();
        }

        if (filled($relationshipName)) {
            $record = $record->getRelationValue($relationshipName);
        }

        /** @var ?Media $media */
        $media = $record->media->first(fn (Media $media): bool => $media->uuid === $state);

        if (! $media) {
            return null;
        }

        $conversion = $this->getConversion();

        if ($this->getVisibility() === 'private') {
            try {
                return $media->getTemporaryUrl(
                    now()->addMinutes(5),
                    $conversion ?? '',
                );
            } catch (Throwable $exception) {
                // This driver does not support creating temporary URLs.
            }
        }

        return $media->getAvailableUrl(Arr::wrap($conversion));
    }

    public function setParent(bool $isParent = false){
        $this->isParent = $isParent;
        return $this;
    }

}
