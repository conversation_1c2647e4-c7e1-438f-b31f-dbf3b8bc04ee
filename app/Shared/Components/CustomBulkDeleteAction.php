<?php

namespace App\Shared\Components;

use Filament\Tables\Actions\DeleteBulkAction;
use Closure;

class CustomBulkDeleteAction extends DeleteBulkAction
{

    public array $relationsList = [];
    protected $beforeCallback = null;


    public function setRelationsList(array $relationsList): static
    {
        $this->relationsList = $relationsList;

        return $this;
    }

    public function before(?Closure $callback): static
    {
        $this->beforeCallback = $callback;
        return $this;
    }
    protected function setUp(): void
    {
        parent::setUp();

        $this->action(function ($records) {
            
            if ($this->beforeCallback) {
                $result = call_user_func($this->beforeCallback, $records);
                if ($result === false) {
                    return;
                }
            }

            foreach ($records as $record) {

                $hasRelations = false;

                foreach ($this->relationsList as $relation) {
                    if ( !$record->$relation()->get()->isEmpty() ) {
                        $hasRelations = true;
                        if($hasRelations) break;
                    }
                }

                if ($hasRelations) {

                    \Filament\Notifications\Notification::make()
                        ->danger()
                        ->title(__('Cannot Delete'))
                        ->body(__('This record has related records. Please remove all related records first.'))
                        ->send();

                    return;
                }

                $record->delete();
                \Filament\Notifications\Notification::make()
                    ->success()
                    ->title(__('Deleted'))
                    ->body(__('The records have been deleted.'))
                    ->send();
            }
        });
    }
}
