<?php

namespace App\Shared\Components;

use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class DateRangeFilter extends Filter
{
    protected string $column = 'created_at';
    protected string $fromLabel = 'From';
    protected string $untilLabel = 'Until';

    public static function make(?string $name = null): static
    {
        return (new static($name))->configure();
    }

    public function configure(): static
    {
        return $this
            ->form([
                DatePicker::make('from')
                    ->label($this->fromLabel),
                DatePicker::make('until')
                    ->label($this->untilLabel),
            ])
            ->query(function (Builder $query, array $data): Builder {
                return $query
                    ->when(
                        $data['from'],
                        fn(Builder $query, $date): Builder => $query->whereDate($this->column, '>=', $date)
                    )
                    ->when(
                        $data['until'],
                        fn(Builder $query, $date): Builder => $query->whereDate($this->column, '<=', $date)
                    );
            });
    }

    public function column(string $column): static
    {
        $this->column = $column;
        return $this;
    }

    public function labels(string $from, string $until): static
    {
        $this->fromLabel = $from;
        $this->untilLabel = $until;
        return $this->configure();
    }

    public function fromLabel(string $label): static
    {
        $this->fromLabel = $label;
        return $this->configure();
    }

    public function untilLabel(string $label): static
    {
        $this->untilLabel = $label;
        return $this->configure();
    }
}
