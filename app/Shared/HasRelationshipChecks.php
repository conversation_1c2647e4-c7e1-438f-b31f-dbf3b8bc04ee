<?php

namespace App\Shared;

use Filament\Notifications\Notification;
use ReflectionClass;


trait HasRelationshipChecks
{
    public function hasRelationships(): bool
    {
        $relatedCounts = [];
        $hasRelations = false;

        foreach ($this->relationsList as $relation) {
            if ( !$this->$relation()->get()->isEmpty() ) {
                $hasRelations = true;
                if($hasRelations) break;
            }
        }
        return $hasRelations;
    }

    protected static function bootHasRelationshipChecks()
    {
        static::deleting(function ($model) {
            $hasRelations = $model->hasRelationships();

            if ($hasRelations) {

                Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete'))
                    ->body(__('This record has related records. Please remove all related records first.'))
                    ->send();

                return false;
            }

            return true;
        });
    }
}
