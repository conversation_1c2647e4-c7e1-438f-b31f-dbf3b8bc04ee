<?php

namespace App\Providers;

use App\Events\PasswordUpdated;
use App\Events\SyncUserAccountEvent;
use App\Listeners\SyncPasswords;
use App\Listeners\SyncUserAccount;
use App\Models\Document;
use App\Observers\DocumentObserver;
use App\Providers\Filament\AdminPanelProvider;
use BezhanSalleh\FilamentShield\FilamentShieldServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use BezhanSalleh\FilamentLanguageSwitch\LanguageSwitch;
use Modules\Payment\app\Events\PaymentVerifiedEvent;
use Modules\Subscription\app\Events\UserRegistered;
use Modules\Subscription\app\Listeners\PaymentVerified;
use App\Providers\FilamentCacheServiceProvider;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        if(!$this->app->runningInConsole() || !$this->app->request->is('api/*')) {
            $this->app->register(AdminPanelProvider::class);
            $this->app->register(FilamentShieldServiceProvider::class);
            $this->app->register(FilamentCacheServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->loadViewsFrom(__DIR__.'/../../Modules/Invoice/resources/views/', 'invoice');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Lease/resources/views/', 'lease');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Property/resources/views/', 'property');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Request/resources/views/', 'Request');
        $this->loadViewsFrom(__DIR__.'/../../Modules/EjarIntegration/resources/views/', 'ejarintegration');
        LanguageSwitch::configureUsing(function (LanguageSwitch $switch) {
            $switch
                ->locales(['ar','en']) // also accepts a closure
                ->outsidePanelRoutes(['login','register','password-reset'])
                ->visible(outsidePanels: true, insidePanels: false);

        });



        Event::listen(
            PaymentVerifiedEvent::class,
            PaymentVerified::class,
        );

        Event::listen(SyncUserAccountEvent::class, SyncUserAccount::class);
        Event::listen(PasswordUpdated::class, SyncPasswords::class);

        Event::listen(
            UserRegistered::class,
            \Modules\Subscription\app\Listeners\SetDefaultSubscription::class,
        );

        Document::observe(DocumentObserver::class);
    }
}
