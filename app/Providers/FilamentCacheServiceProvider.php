<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Spatie\Permission\Events\RoleCreated;
use Spatie\Permission\Events\RoleDeleted;
use Spatie\Permission\Events\RoleUpdated;
use Spatie\Permission\Events\PermissionCreated;
use Spatie\Permission\Events\PermissionDeleted;
use Spatie\Permission\Events\PermissionUpdated;

class FilamentCacheServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        // Clear cache on role/permission changes
        Event::listen([
            RoleCreated::class,
            RoleUpdated::class,
            RoleDeleted::class,
            PermissionCreated::class,
            PermissionUpdated::class,
            PermissionDeleted::class,
            Login::class,
            Logout::class,
        ], function ($event) {
            $this->clearFilamentCache();
        });
    }

    protected function clearFilamentCache()
    {
        // Clear Laravel caches
        $this->app->make('cache')->forget('spatie.permission.cache');
        $this->app->make('cache')->forget('filament.user.menu');
        
        // Clear specific Filament caches
        if (file_exists(storage_path('framework/cache/filament'))) {
            array_map('unlink', glob(storage_path('framework/cache/filament/*')));
        }
        
        // Clear route cache
        if (file_exists(base_path('bootstrap/cache/routes-v7.php'))) {
            unlink(base_path('bootstrap/cache/routes-v7.php'));
        }
    }
}
