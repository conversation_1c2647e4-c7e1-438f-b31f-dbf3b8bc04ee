<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Spatie\Permission\Models\Role;

use Illuminate\Database\Eloquent\Builder;

class RoleScopeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Role::addGlobalScope(function (Builder $builder) {
            if (auth()->check() && !is_null(auth()->user()->company?->id)) {
                $builder->where('companies', true);
            }
        });
    }
}
