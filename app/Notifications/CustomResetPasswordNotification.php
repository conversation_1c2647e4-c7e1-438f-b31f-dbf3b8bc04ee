<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Auth\Notifications\ResetPassword as BaseResetPassword;


class CustomResetPasswordNotification extends BaseResetPassword
{
    public string $url;

    protected function resetUrl($notifiable): string
    {
        return $this->url;
    }

    protected function buildMailMessage($url)
    {
        return (new MailMessage)
            ->subject(__('Reset Your Password'))
            ->greeting(__("Hello!"))
            ->line(__('You are receiving this email because we received a password reset request for your account.'))
            ->action(__('Reset Password'), $url)
            ->line(__('This password reset link will expire in 60 minutes.'))
            ->line(__('If you did not request a password reset, no further action is required.'));
    }

}
