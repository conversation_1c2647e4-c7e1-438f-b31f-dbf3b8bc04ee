<?php 

namespace App\Filament\Actions;

use Filament\Forms\Components\Actions\Action;
use App\Services\NafathService;
use Filament\Notifications\Notification;
use Modules\Company\app\Models\UserProfile;

class VerifyWithNafathAction
{
    public static function make(): Action
    {
        return Action::make('verifyWithNafathModal')
            ->label(__('Verify With Nafath'))
            ->button()
            ->color('primary')
            ->disabled(fn ($get) => !preg_match('/^[12]\d{9}$/', $get('national_id') ?? ''))
            ->action(function ($get, $set, $livewire) {
                $nationalId = $get('national_id');

                if (!preg_match('/^[12]\d{9}$/', $nationalId)) {
                    Notification::make()
                        ->title(__('Invalid National ID'))
                        ->body(__('Please enter a valid 10-digit National ID starting with 1 or 2.'))
                        ->danger()
                        ->send();
                    return;
                }

                if (UserProfile::where('national_id', $nationalId)->exists()) {
                    Notification::make()
                        ->title(__('National ID Already Exists'))
                        ->body(__('This National ID is already registered with another account.'))
                        ->danger()
                        ->send();
                    return;
                }

                try {
                    $service = app(NafathService::class)->sendRequest($nationalId);
                    $response = json_decode($service->body());

                    if ($service->successful()) {
                        if (isset($response->random)) {
                            $set('nafath_response_random', $response->random);
                        }
                        // Redirect to the confirmation page with the encoded data in the query string
                        return redirect()->route('nafath.verify.confirm', ['data' => base64_encode(json_encode(['random' => $response->random , 'national_id' => $nationalId , 'transId' => $response->transId]))]);

                    } elseif ($service->failed()) {
                        if (($response->status ?? null) == 400) {
                            Notification::make()
                                ->title(__('Request Already Exists'))
                                ->body(__('Please try again after a while.'))
                                ->warning()
                                ->send();
                        } else {
                            Notification::make()
                                ->title(__('Issue with Request'))
                                ->body(__('There was an issue with the request.'))
                                ->danger()
                                ->send();
                        }
                    } else {
                        Notification::make()
                            ->title(__('Issue With Nafath'))
                            ->body(__('Please try again after a while.'))
                            ->warning()
                            ->send();
                    }
                } catch (\Exception $e) {
                    Notification::make()
                        ->title(__('Request Timeout'))
                        ->body(__('The request to Nafath failed. Please check your internet connection or try again later.'))
                        ->danger()
                        ->send();
                }
            });
    }
}
