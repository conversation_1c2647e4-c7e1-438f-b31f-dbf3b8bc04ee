<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;

class PropertyAnalyticsWidget extends Widget
{
    protected static ?string $heading = 'Property Analytics';

    protected int | string | array $columnSpan = 1;

    protected static ?int $sort = 4;

    protected static string $view = 'filament.widgets.property-analytics-widget';

    public ?string $filter = 'status';
    public string $chartType = 'doughnut';

    protected static ?string $pollingInterval = null;

    protected function getFilters(): ?array
    {
        return [
            'status' => 'By Status',
            'property_type' => 'By Property Type',
            'usability' => 'By Usability',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'status':
                return $this->getStatusData();
            case 'property_type':
                return $this->getPropertyTypeData();
            case 'usability':
                return $this->getUsabilityData();
            default:
                return $this->getStatusData();
        }
    }

    protected function getStatusData(): array
    {
        $properties = Property::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($properties as $property) {
            $labels[] = $this->getStatusLabel($property->status);
            $data[] = $property->count;
            $colors[] = $this->getStatusColor($property->status);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Properties by Status',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getPropertyTypeData(): array
    {
        $properties = Property::selectRaw('property_type, COUNT(*) as count')
            ->groupBy('property_type')
            ->get();

        $labels = [];
        $data = [];
        $colors = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

        foreach ($properties as $property) {
            $labels[] = $this->getPropertyTypeLabel($property->property_type);
            $data[] = $property->count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Properties by Type',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getUsabilityData(): array
    {
        $properties = Property::selectRaw('usability, COUNT(*) as count')
            ->groupBy('usability')
            ->get();

        $labels = [];
        $data = [];
        $colors = ['#10B981', '#F59E0B', '#EF4444', '#6B7280'];

        foreach ($properties as $property) {
            $labels[] = $this->getUsabilityLabel($property->usability);
            $data[] = $property->count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Properties by Usability',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getStatusLabel($status): string
    {
        // Use the enum's built-in label method
        if ($status instanceof PropertyStatus) {
            return PropertyStatus::getLabel($status);
        }

        // Handle string values by converting to enum
        if (is_string($status)) {
            $enumCase = PropertyStatus::tryFrom($status);
            if ($enumCase) {
                return PropertyStatus::getLabel($enumCase);
            }
        }

        return ucfirst($status);
    }

    protected function getPropertyTypeLabel(string $type): string
    {
        return match($type) {
            'residential' => 'Residential',
            'commercial' => 'Commercial',
            'industrial' => 'Industrial',
            'land' => 'Land',
            'mixed_use' => 'Mixed Use',
            default => ucfirst(str_replace('_', ' ', $type)),
        };
    }

    protected function getUsabilityLabel(string $usability): string
    {
        return match($usability) {
            'excellent' => 'Excellent',
            'good' => 'Good',
            'fair' => 'Fair',
            'poor' => 'Poor',
            'needs_renovation' => 'Needs Renovation',
            default => ucfirst(str_replace('_', ' ', $usability)),
        };
    }

    protected function getStatusColor($status): string
    {
        // Use the enum's built-in color method and convert to hex
        if ($status instanceof PropertyStatus) {
            return $this->convertFilamentColorToHex($status->getColor());
        }

        // Handle string values by converting to enum
        if (is_string($status)) {
            $enumCase = PropertyStatus::tryFrom($status);
            if ($enumCase) {
                return $this->convertFilamentColorToHex($enumCase->getColor());
            }
        }

        return '#9CA3AF'; // Default gray
    }

    protected function convertFilamentColorToHex(string $filamentColor): string
    {
        return match($filamentColor) {
            'gray' => '#6B7280',
            'success' => '#10B981',
            'warning' => '#F59E0B',
            'info' => '#3B82F6',
            'danger' => '#EF4444',
            default => '#9CA3AF',
        };
    }

    public function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'title' => [
                    'display' => true,
                    'text' => $this->getChartTitle(),
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->chartType === 'bar') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ];
        }

        return $baseOptions;
    }

    protected function getChartTitle(): string
    {
        return match($this->filter) {
            'status' => 'Properties Distribution by Status',
            'property_type' => 'Properties Distribution by Type',
            'usability' => 'Properties Distribution by Usability',
            default => 'Property Analytics',
        };
    }

    public function changeChartType(string $type): void
    {
        $this->chartType = $type;
        $this->dispatch('refreshPropertyChart');
    }

    public function updatedFilter(): void
    {
        $this->dispatch('refreshPropertyChart');
    }

    public function getCachedData(): array
    {
        return $this->getData();
    }

    public function getHeading(): string
    {
        return static::$heading ?? 'Property Analytics';
    }
}
