<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Modules\Invoice\app\Models\Invoice;

class InvoiceAnalyticsWidget extends Widget
{
    protected static ?string $heading = 'Invoice Analytics';

    protected int | string | array $columnSpan = 1;

    protected static ?int $sort = 3;

    protected static string $view = 'filament.widgets.invoice-analytics-widget';

    public string $chartType = 'pie';

    protected static ?string $pollingInterval = null;

    protected function getData(): array
    {
        return $this->getStatusData();
    }

    protected function getStatusData(): array
    {
        $invoices = Invoice::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($invoices as $invoice) {
            $labels[] = $this->getStatusLabel($invoice->status);
            $data[] = $invoice->count;
            $colors[] = $this->getStatusColor($invoice->status);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Invoices by Status',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getStatusLabel(string $status): string
    {
        return match($status) {
            'unpaid' => 'Unpaid',
            'partial_paid' => 'Partially Paid',
            'paid' => 'Paid',
            'settled' => 'Settled',
            default => ucfirst(str_replace('_', ' ', $status)),
        };
    }

    protected function getStatusColor(string $status): string
    {
        return match($status) {
            'unpaid' => '#EF4444',        // Red
            'partial_paid' => '#F59E0B',  // Yellow/Orange
            'paid' => '#10B981',          // Green
            'settled' => '#3B82F6',       // Blue
            default => '#9CA3AF',         // Gray
        };
    }

    public function getOptions(): array
    {
        $baseOptions = [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'title' => [
                    'display' => true,
                    'text' => 'Invoice Status Distribution',
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];

        if ($this->chartType === 'bar') {
            $baseOptions['scales'] = [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ];
        }

        return $baseOptions;
    }

    public function changeChartType(string $type): void
    {
        $this->chartType = $type;
        $this->dispatch('refreshInvoiceChart');
    }

    public function getCachedData(): array
    {
        return $this->getData();
    }

    public function getHeading(): string
    {
        return static::$heading ?? 'Invoice Analytics';
    }
}
