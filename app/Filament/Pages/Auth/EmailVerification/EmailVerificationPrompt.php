<?php

namespace App\Filament\Pages\Auth\EmailVerification;
use Filament\Pages\Auth\EmailVerification\EmailVerificationPrompt as BaseEmailVerificationPrompt;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;

class EmailVerificationPrompt extends BaseEmailVerificationPrompt
{
    public bool $canResend = true;
    public int $remainingTime = 0;
    public int $attemptCount = 0;

    public function mount(): void
    {
        parent::mount();
        $this->checkResendStatus();
    }

    public function checkResendStatus(): void
    {
        $key = "email_verification_" . auth()->id();
        $this->attemptCount = Cache::get("{$key}_attempts", 0);
        $lastAttemptTime = Cache::get("{$key}_last_attempt");
        $currentCooldown = Cache::get("{$key}_current_cooldown", 0);

        if (!$lastAttemptTime) {
            $this->canResend = true;
            $this->remainingTime = 0;
            return;
        }

        $cooldownDuration = $currentCooldown > 0 ? $currentCooldown : $this->getWaitTime($this->attemptCount);
        $this->remainingTime = max(0, ($lastAttemptTime + $cooldownDuration) - time());
        $this->canResend = $this->remainingTime <= 0 && $this->attemptCount < 4;
    }

    protected function getWaitTime(int $attempts): int
    {
        return match ($attempts) {
            1 => 60,    // 1 minute
            2 => 180,   // 3 minutes
            3 => 3600,  // 1 hour
            default => 86400, // 24 hours
        };
    }

    public function resendNotificationAction(): Action
    {
        return Action::make('resendNotification')
            ->link()
            ->label(__('filament-panels::pages/auth/email-verification/email-verification-prompt.actions.resend_notification.label'))
            ->action(function (): void {
                $key = "email_verification_" . auth()->id();
                $attempts = Cache::get("{$key}_attempts", 0);

                if ($attempts >= 4) {
                    Notification::make()
                        ->title(__('Too Many Attempts'))
                        ->body(__('You have reached the maximum number of attempts. Please try again after 24 hours.'))
                        ->danger()
                        ->send();
                    return;
                }

                // Get next cooldown duration
                $nextCooldown = $this->getWaitTime($attempts + 1);

                if ($lastAttempt = Cache::get("{$key}_last_attempt")) {
                    $currentCooldown = Cache::get("{$key}_current_cooldown", $this->getWaitTime($attempts));
                    $remainingTime = ($lastAttempt + $currentCooldown) - time();

                    if ($remainingTime > 0) {
                        $waitMessage = $this->getWaitMessage($remainingTime);
                        Notification::make()
                            ->title(__('Please Wait'))
                            ->body($waitMessage)
                            ->warning()
                            ->send();
                        return;
                    }
                }

                try {
                    // Store next cooldown duration
                    Cache::put("{$key}_current_cooldown", $nextCooldown, now()->addDay());

                    // Increment attempts and update last attempt time
                    Cache::put("{$key}_attempts", $attempts + 1, now()->addDay());
                    Cache::put("{$key}_last_attempt", time(), now()->addDay());

                    $this->sendEmailVerificationNotification($this->getVerifiable());

                    $this->checkResendStatus();

                    Notification::make()
                        ->title(__('filament-panels::pages/auth/email-verification/email-verification-prompt.notifications.notification_resent.title'))
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Notification::make()
                        ->title(__('Error'))
                        ->body(__('Failed to send verification email. Please try again later.'))
                        ->danger()
                        ->send();
                }
            });
    }
}
