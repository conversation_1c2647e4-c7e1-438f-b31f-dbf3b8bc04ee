<?php
namespace App\Filament\Pages;

use App\Events\PasswordUpdated;
use App\Models\User;
use App\Models\UserAccountCredential;
use App\Notifications\CustomResetPasswordNotification;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Exception;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Auth\ResetPassword as ResetPasswordNotification;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Support\Str;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Hash;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Filament\Pages\Auth\PasswordReset\RequestPasswordReset;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Computed;

class CustomResetPassword extends RequestPasswordReset
{
    public ?string $email = null;
    public bool $canResend = true;
    public int $remainingTime = 0;
    public int $attemptCount = 0;

    public function mount(): void
    {
        parent::mount();
        $this->email = Session::get('reset_password_email');
        $this->checkResendStatus();
    }

    public function checkResendStatus(): void
    {
        if (!$this->email) {
            return;
        }

        $key = "reset_password_{$this->email}";
        $this->attemptCount = Cache::get("{$key}_attempts", 0);
        $lastAttemptTime = Cache::get("{$key}_last_attempt");
        $currentCooldown = Cache::get("{$key}_current_cooldown", 0);

        if (!$lastAttemptTime) {
            $this->canResend = true;
            $this->remainingTime = 0;
            return;
        }
        $cooldownDuration = $currentCooldown > 0 ? $currentCooldown : $this->getCooldownDuration($this->attemptCount);
        $this->remainingTime = max(0, ($lastAttemptTime + $cooldownDuration) - time());
        $this->canResend = $this->remainingTime <= 0 && $this->attemptCount < 4;
    }

    protected function getCooldownDuration(int $attempts): int
    {
        return match($attempts) {
            1 => 60,      // 1 minute
            2 => 180,     // 3 minutes
            3 => 3600,    // 1 hour
            default => 86400,  // 24 hours
        };
    }

    public function request(): void
    {

        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return;
        }

        $data = $this->form->getState();
        $check_user = UserAccountCredential::withoutGlobalScopes()->where(['national_id' => $data['national_id']])->first();
        if (!$check_user) {
            Notification::make()
                ->title(__('Undefined user with this national ID.'))
                ->danger()
                ->send();
            return;
        }
        $user = User::withoutGlobalScopes()->where(['id' => $check_user->user_id])->first();
        if (!$user) {
            Notification::make()
                ->title(__('Undefined user.'))
                ->danger()
                ->send();
            return;
        }
        $this->email = $user->email;
        $data = ['email' => $this->email];
        $this->data = $data;
        Session::put('reset_password_email', $this->email);

        $key = "reset_password_{$this->email}";
        $attempts = Cache::get("{$key}_attempts", 0);
        // Check if max attempts reached
        if ($attempts >= 4) {
            Notification::make()
                ->title(__('Maximum attempts reached. Please try again in 24 hours.'))
                ->danger()
                ->send();
            return;
        }
        // Get next cooldown duration
        $nextCooldown = $this->getCooldownDuration($attempts + 1);
        // Check cooldown period
        if ($lastAttempt = Cache::get("{$key}_last_attempt")) {
            $currentCooldown = Cache::get("{$key}_current_cooldown", $this->getCooldownDuration($attempts));
            $remainingTime = ($lastAttempt + $currentCooldown) - time();
            if ($remainingTime > 0) {
                $minutes = ceil($remainingTime / 60);
                Notification::make()
                    ->title(__("Please wait {$minutes} minutes before requesting another reset."))
                    ->danger()
                    ->send();
                return;
            }
        }

        $status = Password::broker(Filament::getAuthPasswordBroker())->sendResetLink(
            $data,
            function (CanResetPassword $user, string $token): void {
                if (! method_exists($user, 'notify')) {
                    $userClass = $user::class;

                    throw new Exception("Model [{$userClass}] does not have a [notify()] method.");
                }

                $notification = new CustomResetPasswordNotification($token);
                $notification->url = Filament::getResetPasswordUrl($token, $user);

                $user->notify($notification);
            },
        );

        if ($status === Password::RESET_LINK_SENT) {
            // Store next cooldown duration
            Cache::put("{$key}_current_cooldown", $nextCooldown, now()->addDay());

            // Increment attempts and update last attempt time
            Cache::put("{$key}_attempts", $attempts + 1, now()->addDay());
            Cache::put("{$key}_last_attempt", time(), now()->addDay());

            $this->checkResendStatus();

            Notification::make()
                ->title(__('Reset password email sent successfully.'))
                ->success()
                ->send();

            $this->form->fill();
        } else {
            Notification::make()
                ->title(__($status))
                ->danger()
                ->send();
        }
    }

    public function dehydrate()
    {
        if (!$this->canResend && $this->remainingTime <= 0) {
            Session::forget('reset_password_email');
        }
    }

    public function getFormActions(): array
    {
        return [
            $this->getRequestFormAction(),
        ];
    }

    protected function getRequestFormAction(): Action
    {
        return Action::make('request')
            ->label(__('filament-panels::pages/auth/password-reset/request-password-reset.form.actions.request.label'))
            ->submit('request');
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getNationalIDFormComponent(),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected function getNationalIDFormComponent(): Component
    {
        return TextInput::make('national_id')
            ->label(__('National ID'))
            ->required()
            ->placeholder('1xxxxxxxxx or 2xxxxxxxxx')
            ->live()
            ->extraInputAttributes([
                'maxlength' => '10',
                'minlength' => '10',
                'pattern' => '[12][0-9]{9}',
                'style' => 'appearance: textfield; -webkit-appearance: textfield; -moz-appearance: textfield;',
                'oninput' => "
                        this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                        if (this.value.length > 0 && !['1','2'].includes(this.value[0])) {
                            this.value = '';
                        }
                        this.dispatchEvent(new Event('input'));",
                'tabindex' => 1,
            ])
            ->rules([
                'required',
                'numeric',
                'digits:10',
                'regex:/^[12]\d{9}$/',
            ])
            ->validationMessages([
                'required' => __('National ID is required'),
                'digits' => __('National ID must be exactly 10 digits'),
                'numeric' => __('National ID must contain only numbers'),
                'regex' => __('National ID must start with 1 for Saudi citizens or 2 for non-Saudi residents'),
            ]);
    }
}
