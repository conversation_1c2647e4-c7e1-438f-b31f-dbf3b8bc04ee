<?php


namespace App\Filament\Pages;

use App\Events\PasswordUpdated;
use App\Helpers\FormHelper;
use App\Models\User;
use DanH<PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Http\Responses\Auth\Contracts\PasswordResetResponse;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\PasswordReset\ResetPassword;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password as PasswordRule;

/**
 * @property Form $form
 */
class CustomResetPasswordFrom extends ResetPassword
{
    public ?string $national_id = null;

    public function mount(?string $email = null, ?string $token = null): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->token = $token ?? request()->query('token');
        $this->email = $token ?? request()->query('email');

        if (!$this->isValidToken($this->email, $this->token)) {
            abort(403, __('Token expired.'));
        }

        $user = $this->getUserByEmail(request()->query('email'));
        $this->form->fill(['national_id' => $user->userProfile->national_id,]);
    }

    public function resetPassword(): ?PasswordResetResponse
    {
        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return null;
        }

        $data = $this->form->getState();

        $data['email'] = $this->email;
        $data['token'] = $this->token;
        $status = Password::broker(Filament::getAuthPasswordBroker())->reset(
            $data,
            function (CanResetPassword | Model | Authenticatable $user) use ($data) {
                $user->forceFill([
                    'password' => Hash::make($data['password']),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
                event(new PasswordUpdated(User::class, $user->id, $data['password'])); //to unify the credentials methods
            },
        );

        if ($status === Password::PASSWORD_RESET) {
            Notification::make()
                ->title(__($status))
                ->success()
                ->send();

            return app(PasswordResetResponse::class);
        }

        Notification::make()
            ->title(__($status))
            ->danger()
            ->send();

        return null;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getNationalIDFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getPasswordConfirmationFormComponent(),
            ]);
    }

    protected function getNationalIDFormComponent(): Component
    {
        return TextInput::make('national_id')
            ->label(__('National ID'))
            ->disabled()
            ->autofocus();
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(__('filament-panels::pages/auth/password-reset/reset-password.form.password.label'))
            ->password()
            ->revealable()
            ->default('')
            ->required()
            ->minLength(8)
            ->live()
            ->placeholder(__('Enter your password'))
            ->same('passwordConfirmation')
            ->validationAttribute(__('filament-panels::pages/auth/password-reset/reset-password.form.password.validation_attribute'))
            ->suffixIcon(fn ($state) =>
            strlen($state) >= 8 &&
            preg_match('/[A-Z]/', $state) &&
            preg_match('/[a-z]/', $state) &&
            preg_match('/\d/', $state) &&
            preg_match('/[@$!%*?&]/', $state)
                ? 'heroicon-o-check-circle'
                : 'heroicon-o-x-circle'
            )
            ->suffixIconColor(fn ($state) =>
            strlen($state) >= 8 &&
            preg_match('/[A-Z]/', $state) &&
            preg_match('/[a-z]/', $state) &&
            preg_match('/\d/', $state) &&
            preg_match('/[@$!%*?&]/', $state)
                ? 'success'
                : 'danger'
            )
            ->rules([
                'required',
                //'confirmed',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
            ])
            ->validationMessages([
                'required' => __('Password is required'),
                'min' => __('Password must be at least 8 characters'),
                'regex' => __('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
            ])
            ->helperText(function ($state) {
                if (empty($state)) {
                    return FormHelper::getHtmlHintMessagesForPasswordRequirements();
                }
                $requirements = FormHelper::getValidationRequirementsForPasswordState($state);
                $message = __('Password requirements:') . '<br>';
                foreach ($requirements as $requirement) {
                    $message .= $requirement[2] ? $requirement[1] : $requirement[0];
                    $message .= '<br>';
                }

                return new HtmlString($message);
            });
    }

    protected function getPasswordConfirmationFormComponent(): Component
    {
        return TextInput::make('passwordConfirmation')
            ->label(__('filament-panels::pages/auth/password-reset/reset-password.form.password_confirmation.label'))
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->required()
            ->dehydrated(false)
            ->placeholder(__('Confirm your password'))
            ->live()
            ->same('password')
            ->suffixIcon(fn ($state, $get) =>
            $state === $get('password') && !empty($state)
                ? 'heroicon-o-check-circle'
                : 'heroicon-o-x-circle'
            )
            ->suffixIconColor(fn ($state, $get) =>
            $state === $get('password') && !empty($state)
                ? 'success'
                : 'danger'
            )
            ->helperText(fn ($state, $get) =>
            new HtmlString(
                $state !== $get('password')
                    ? '✗ ' . __('Passwords do not match')
                    : ($state
                    ? '✓ ' . __('Passwords match')
                    : ''
                )
            )
            )
            ->validationMessages([
                'required' => __('Password confirmation is required'),
                'same' => __('Passwords do not match'),
            ]);
    }

    protected function getUserByEmail(string $email) {
        $user = User::where(['email' => $email])->first();
        if (!$user) {
            throw new \Exception('User not found');
        }
        return $user;
    }

    protected function isValidToken(string $email, string $token): bool
    {
        $tokenRecord =  DB::table('password_reset_tokens')
            ->where('email', $email)
            ->where('created_at', '>', now()->subMinutes(
              config('auth.passwords.'.config('auth.defaults.passwords').'.expire', 60)
            )) //time < 60 min
            ->first();
        if ($tokenRecord && $tokenRecord->token) {
            return Hash::check($token, $tokenRecord->token);
        }
        return false;
    }
}
