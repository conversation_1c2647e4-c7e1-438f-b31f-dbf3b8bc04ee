<?php

namespace App\Filament\Pages;

use App\Enums\RoleEnum;
use App\Enums\SyncUserAccountSourceEnum;
use App\Events\SyncUserAccountEvent;
use App\Filament\Actions\VerifyWithNafathAction;
use App\Forms\Components\ViewImage;
use App\Forms\Components\UserCompanyInfoSection;
use App\Helpers\AccountHelper;
use App\Models\NafathLog;
use App\Models\User;
use App\Notifications\CustomVerifyEmailNotification;
use App\Shared\Helpers\FormComponents;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;
use Modules\Company\app\Models\Company;
use Modules\Company\app\Models\UserProfile;
use Modules\Subscription\app\Events\UserRegistered;

class Register extends BaseRegister
{

    protected static string $model = User::class;
    public function getMaxWidth(): string|null
    {
        return '6xl';
    }


    protected function getFormStatePath(): string
    {
        return 'data';
    }



    protected function handleRegistration(array $data): Model
    {
        // Construct the full name from individual components
        $fullName = trim(implode(' ', array_filter([
            $data['first_name'] ?? '',
            $data['second_name'] ?? '',
            $data['third_name'] ?? '',
            $data['last_name'] ?? ''
        ])));

        // Create user first
        $user = static::$model::create([
            'name' => $fullName,
            'password' => isset($data['password']) ? Hash::make($data['password']) : null,
            'email' => $data['email'],
            'lang' => app()->getLocale(),
        ]);

        $data['comp_unified_file'] = $data['comp_unified_file']?->store('company-files', 'public');
        $data['company_logo'] = $data['company_logo']?->store('company_logos', 'public');

        // Create company with all fields
        $company = Company::create([
            'name' => $data['company_name'],
            'user_id' => $user->id,
            'phone' => '+966' . $data['company_phone'],
            'email' => $data['company_email'],
            'address' => $data['company_address'],
            'comp_unified_file' => $data['comp_unified_file'],
            'comp_unified_number' => $data['comp_unified_number'],
            'comp_cr_number' => $data['comp_cr_number'],
            'website_url' => $data['website_url'],
            'logo' => $data['company_logo'],
        ]);

        // Update user with company information
        $user->update([
            'company_id' => $company->id,
        ]);
        //update user company cross table
        $company->usersWithPivot()->attach($user->id);
        if (isset($data['avatar_url'])){
            $data['avatar_url'] = $data['avatar_url']?->store('avatars', 'public');
        } else{
            $data['avatar_url'] = null;
        }

        // Create user profile
        $userProfile = new UserProfile([
            'user_id' => $user->id,
            'first_name' => $data['first_name'],
            'second_name' => $data['second_name'],
            'third_name' => $data['third_name'],
            'last_name' => $data['last_name'],
            'phone_number' => '+966' . $data['phone_number'],
            'national_id' => $data['national_id'],
            'birth_date' => $data['birth_date'],
//            'bank_account' => $data['bank_account'],
            'avatar_url' => $data['avatar_url'],
            'terms_accepted' => $data['terms_accepted'],
        ]);
        $userProfile->save();

        //confirm terms & conditions
        if (!isset($data['terms_accepted']) || !$data['terms_accepted']) {
            throw new \Exception(__('You must accept the terms and conditions'));
        }
        //clear nafath logs
        NafathLog::query()->where(['national_id' => $userProfile->national_id])->delete();
        // Assign role
        $user->assignRole(RoleEnum::OWNER);
        $userData = $user;
        $userData->unhashed_password = $data['password'] ?? null;
        event(new SyncUserAccountEvent($userData, SyncUserAccountSourceEnum::REGISTER->value));
        event(new UserRegistered($user));

        unset($user->unhashed_password);
        // Set email from user_company_email
        if (!empty($data['user_company_email'])) {
            $user->update(['email' => $data['user_company_email']]);
        }
        // Set phone_number in userProfile from user_company_phone
        if (isset($data['user_company_phone']) && $data['user_company_phone'] != '+966') {
            $userProfile->update(['phone_number' => $data['user_company_phone']]);
        }
        return $user;
    }
    protected function beforeRegister(): bool
    {
        if (!$this->data['terms_accepted']) {
            $this->addError('terms_accepted', __('You must accept the terms and conditions'));
            return false;
        }
        return true;
    }

    public function form(Form $form): Form
    {
        $nafathParam = request()->query('nafath');
        if($nafathParam !== null)
        {
            $national_id = json_decode($nafathParam , true)['national_id'];

            $nafathLog = NafathLog::where('national_id' , $national_id)->where('status' , 'completed')->firstOrFail();

            if($nafathLog)
            {
                $data = $nafathLog->decoded_payload;
                $date = \DateTime::createFromFormat('d-m-Y', $data['dateOfBirthG']);
                $formattedDate = $date ? $date->format('Y-m-d') : null;
            }
        }

        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make(__('National ID'))
                        ->icon('heroicon-o-identification')
                        ->schema([
                            Grid::make(1)
                                ->schema([
                                    TextInput::make('national_id')
                                        ->label(__('National ID'))
                                        ->required()
                                        ->unique(table: 'user_profiles',column: 'national_Id',ignoreRecord:true)
                                        ->placeholder('1xxxxxxxxx or 2xxxxxxxxx')
                                        ->live(debounce: 500)
                                        ->suffix(fn (Get $get) => $get('is_loading')
                                            ? new HtmlString(view('filament::components.icon', [
                                                'icon' => 'heroicon-o-arrow-path',
                                                'class' => 'h-4 w-4'
                                            ]))
                                            : ($get('is_filled')
                                                ? new HtmlString(view('filament::components.icon', [
                                                    'icon' => 'heroicon-o-check-circle',
                                                    'class' => 'h-4 w-4'
                                                ]))
                                                : null
                                            )
                                        )
                                        ->hint(fn (Get $get) => $get('is_filled')
                                            ? new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                                                .
                                                view('filament::components.icon', [
                                                    'icon' => 'heroicon-o-arrow-path',
                                                    'class' => 'h-4 w-4'
                                                ]) . __('Data auto filled by national ID') . '</span>')
                                            : null
                                        )
                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                            // Reset states
                                            $set('is_loading', false);
                                            $set('is_filled', false);

                                            if (!$state || strlen($state) < 10) {
                                                return;
                                            }
                                            // Set loading state
                                            $set('is_loading', true);
                                            try {
                                                $account_data = AccountHelper::searchAccountDataByNationalId($state);
                                                $nafathData = NafathLog::where('national_id' , $state)->first();

                                                if (!$account_data  && $nafathData && $nafathData['token'] == null) {
                                                    return;
                                                }

                                                if($account_data)
                                                {
                                                    $avatar_url = $account_data->account->getMedia('profile')->first() ? $account_data->account->getMedia('profile')->first()->getUrl() : null;
                                                    if ($avatar_url) {
                                                        //$avatar_url = "https://kera-laravel.test/storage/1714/01JF5BYHTNNPPH6KBZGSJ2WKEG.jpg";
                                                        $set('existing_avatar_url', $avatar_url);
                                                    }
                                                    AccountHelper::fillFormFields($set, $account_data->account, SyncUserAccountSourceEnum::REGISTER->value);
                                                    // Set success state
                                                    $set('is_filled', true);
                                                }elseif($nafathData && $nafathData['token'] !== null)
                                                {
                                                    AccountHelper::fillNafathFormFields($set, $nafathData->decoded_payload, SyncUserAccountSourceEnum::REGISTER->value);
                                                    // Set success state
                                                    $set('is_filled', true);
                                                }

                                            } finally {
                                                // Ensure loading state is always turned off
                                                $set('is_loading', false);
                                            }
                                        })
                                        ->extraInputAttributes([
                                            'maxlength' => '10',
                                            'minlength' => '10',
                                            'pattern' => '[12][0-9]{9}',
                                            'style' => 'appearance: textfield; -webkit-appearance: textfield; -moz-appearance: textfield;',
                                            'oninput' => "
                                                            this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                                            if (this.value.length > 0 && !['1','2'].includes(this.value[0])) {
                                                                this.value = '';
                                                            }
                                                            this.dispatchEvent(new Event('input'));
                                                        "
                                        ])
                                        ->rules([
                                            'required',
                                            'numeric',
                                            'digits:10',
                                            'regex:/^[12]\d{9}$/',
                                        ])
                                        ->validationMessages([
                                            'required' => __('National ID is required'),
                                            'digits' => __('National ID must be exactly 10 digits'),
                                            'unique' => __('National ID is already in exists'),
                                            'numeric' => __('National ID must contain only numbers'),
                                            'regex' => __('National ID must start with 1 for Saudi citizens or 2 for non-Saudi residents'),
                                        ])
                                        ->hint(fn (Get $get) => $get('nafath_response_random')
                                                ? new HtmlString('<span class="text-info-600 text-sm font-medium">Random Code: ' . $get('nafath_response_random') . '</span>')
                                                : ($get('is_filled')
                                                    ? new HtmlString('<span class="flex items-center gap-1 text-success-500 font-medium">'
                                                        . view('filament::components.icon', [
                                                            'icon' => 'heroicon-o-arrow-path',
                                                            'class' => 'h-4 w-4'
                                                        ]) . __('Data auto filled by national ID') . '</span>')
                                                    : null))
                                        ->default(fn () => $data['nin'] ?? null)
                                        ->disabled(fn () => !empty($data['nin']) ? true : false)
                                        ->suffixAction(fn () => empty($data)
                                            ? VerifyWithNafathAction::make()
                                            : null
                                        )
                                ]
                                        ),
                                    Hidden::make('is_loading')
                                        ->default(false)
                                        ->live(),
                                    Hidden::make('is_filled')
                                        ->default(false)
                                        ->live(),
                        ]),

                    Wizard\Step::make(__('Account Information'))
                        ->icon('heroicon-o-user')
                        ->schema([
                            TextInput::make('name')
                                ->hidden(),

                            TextInput::make('email')
                                ->label(__('Email'))
                                ->readOnly(fn (Get $get): bool => $get('is_filled'))
                                ->email()
                                ->required()
                                ->default(fn () => $data['email'] ?? null)
                                ->disabled(fn () => !empty($data['email']))
                                ->unique('users')
                                ->maxLength(255)
                                ->placeholder('<EMAIL>')
                                ->validationMessages([
                                    'required' => __('Email address is required'),
                                    'email' => __('Please enter a valid email address'),
                                    'unique' => __('This email is already registered'),
                                    'max' => __('Email must not exceed 255 characters'),
                                ]),

                            FormComponents::passwordInput('password', 'password', true)
                                ->hidden(fn (Get $get): bool => $get('is_filled')),
                            FormComponents::passwordConfirmation('password')
                                ->hidden(fn (Get $get): bool => $get('is_filled')),

                        ]),

                    Wizard\Step::make(__('Personal Information'))
                        ->icon('heroicon-o-identification')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('first_name')
                                        ->label(__('First Name'))
                                        ->required()
                                        ->default(fn () => $data['firstName'] ?? null)
                                        ->readOnly(function (Get $get, $livewire) {
                                            return $get('is_filled') || !empty($livewire->data['first_name']);
                                        })
                                        ->extraAttributes([
                                            'class' => 'bg-gray-100 text-gray-500 cursor-not-allowed',
                                        ])
                                        ->maxLength(25)
                                        ->placeholder(__('Enter your first name'))
                                        ->validationMessages([
                                            'required' => __('First name is required'),
                                            'max' => __('First name must not exceed 25 characters'),
                                        ]),

                                    TextInput::make('second_name')
                                        ->label(__('Second Name'))
                                        ->required()
                                        ->default(fn () => $data['fatherName'] ?? null)
                                        ->readOnly(function (Get $get, $livewire) {
                                            return $get('is_filled') || !empty($livewire->data['second_name']);
                                        })
                                        ->maxLength(25)
                                        ->placeholder(__('Enter your second name'))
                                        ->validationMessages([
                                            'required' => __('Second name is required'),
                                            'max' => __('Second name must not exceed 25 characters'),
                                        ]),

                                    TextInput::make('third_name')
                                        ->label(__('Third Name'))
                                        ->required()
                                        ->default(fn () => $data['grandFatherName'] ?? null)
                                        ->readOnly(function (Get $get, $livewire) {
                                            return $get('is_filled') || !empty($livewire->data['third_name']);
                                        })
                                        ->maxLength(25)
                                        ->placeholder(__('Enter your third name'))
                                        ->validationMessages([
                                            'required' => __('Third name is required'),
                                            'max' => __('Third name must not exceed 25 characters'),
                                        ]),

                                    TextInput::make('last_name')
                                        ->label(__('Last Name'))
                                        ->required()
                                        ->default(fn () => $data['familyName'] ?? null)
                                        ->readOnly(function (Get $get, $livewire) {
                                            return $get('is_filled') || !empty($livewire->data['last_name']);
                                        })
                                        ->maxLength(25)
                                        ->placeholder(__('Enter your last name'))
                                        ->validationMessages([
                                            'required' => __('Last name is required'),
                                            'max' => __('Last name must not exceed 25 characters'),
                                        ]),

                                    FormComponents::birthDateInput(__('Birth Date'))
                                        ->default(fn () => $formattedDate  ?? null)
                                        ->readOnly(function (Get $get, $livewire) {
                                            return $get('is_filled') || !empty($livewire->data['birth_date']);
                                        })
                                        ->helperText(__('Must be at least 18 years old')),

                                    TextInput::make('phone_number')
                                        ->label(__('Phone Number'))
                                        ->tel()
                                        ->required()
                                        ->readOnly(fn (Get $get): bool => $get('is_filled'))
                                        ->prefix('+966')
                                        ->placeholder(__('5xxxxxxxx'))
                                        ->inputMode('numeric')
                                        ->length(9)
                                        ->maxLength(9)
                                        ->rules(function (callable $get) {
                                            return [
                                                'required',
                                                'string',
                                                'regex:/^5[0-9]{8}$/',
                                                'size:9',
                                                function ($attribute, $value, $fail) {
                                                    $phoneNumber = '+966' . $value;
                                                    $exists = DB::table('user_profiles')
                                                        ->where('phone_number', $phoneNumber)
                                                        ->exists();

                                                    if ($exists) {
                                                        $fail(__('This phone number is already taken.'));
                                                    }
                                                },
                                            ];
                                        })
                                        ->validationMessages([
                                            'required' => __('Phone number is required'),
                                            'regex' => __('Phone number must start with 5 and be 9 digits'),
                                            'size' => __('Phone number must be exactly 9 digits'),
                                            'unique' => __('This phone number is already taken'),
                                        ])
                                        ->beforeStateDehydrated(fn ($state) => '+966' . $state)
                                        ->extraInputAttributes([
                                            'maxlength' => '9',
                                            'pattern' => '5[0-9]*',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                        ])
                                        ->telRegex('/^5[0-9]{8}$/'),
                                    FileUpload::make('avatar_url')
                                        ->label(__('Profile Picture'))
                                        ->image()
                                        ->directory('avatars')
                                        ->preserveFilenames()
                                        ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'])
                                        ->maxSize(5120) // 5MB
                                        ->disabled(fn (Get $get): bool => $get('is_filled'))
                                        ->default(fn (Get $get): ?string =>
                                        $get('is_filled') && $get('existing_avatar_url')
                                            ? $get('existing_avatar_url')
                                            : null
                                        )
                                        ->validationMessages([
                                            'image' => __('File must be an image'),
                                            'mimes' => __('File must be a type of: jpg, png, jpeg, webp, svg'),
                                            'max' => __('File must not be larger than 5MB'),
                                        ])->beforeStateDehydrated(function (){}),

                                    Hidden::make('existing_avatar_url')
                                        ->default(null)
                                        ->live(),
                                    Grid::make(2)
                                        ->schema([
                                            ViewImage::make('profile_preview')
                                                ->label(__('Profile Picture'))
                                                ->imageUrl(fn (Get $get) => $get('existing_avatar_url'))
                                                ->visible(fn (Get $get): bool => (bool) $get('existing_avatar_url') && $get('is_filled')),
                                        ]),

                                ]),
                        ]),

                    Wizard\Step::make(__('Company Information'))
                        ->icon('heroicon-o-building-office')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('company_name')
                                        ->label(__('Company Name'))
                                        ->required()
                                        ->maxLength(75)
                                        ->minLength(1)
                                        ->placeholder(__('Enter company name'))
                                        ->live()
                                        ->extraInputAttributes([
                                            'maxlength' => '75',
                                            'oninput' => "
                                                            this.value = this.value.substring(0, 75);
                                                            this.dispatchEvent(new Event('input'));"
                                        ])
                                        ->validationMessages([
                                            'required' => __('Company name is required'),
                                            'max' => __('Company name must not exceed 75 characters'),
                                            'min' => __('Company name is required'),
                                        ]),

                                    Textarea::make('company_address')
                                        ->label(__('Company Address'))
                                        ->required()
                                        ->maxLength(150)
                                        ->minLength(1)
                                        ->placeholder(__('Enter company address'))
                                        ->rows(1)
                                        ->live()
                                        ->extraInputAttributes([
                                            'maxlength' => '150',
                                            'oninput' => "
            this.value = this.value.substring(0, 150);
            this.dispatchEvent(new Event('input'));
        "
                                        ])
                                        ->validationMessages([
                                            'required' => __('Company address is required'),
                                            'max' => __('Company address must not exceed 150 characters'),
                                            'min' => __('Company address is required'),
                                        ]),
                                    TextInput::make('company_phone')
                                        ->label(__('Company Phone'))
                                        ->tel()
                                        ->required()
                                        ->prefix('+966')
                                        ->placeholder(__('5xxxxxxxx'))
                                        ->rules([
                                            'required',
                                            'string',
                                            'regex:/^5[0-9]{8}$/',
                                            'size:9'
                                        ])
                                        ->extraInputAttributes([
                                            'maxlength' => '9',
                                            'pattern' => '5[0-9]*',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                        ])
                                        ->validationMessages([
                                            'required' => __('Company phone is required'),
                                            'regex' => __('Phone number must start with 5 and be 9 digits'),
                                            'size' => __('Phone number must be exactly 9 digits'),
                                        ]),

                                    TextInput::make('company_email')
                                        ->label(__('Company Email'))
                                        ->email()
                                        ->required()
                                        ->placeholder('<EMAIL>')
                                        ->validationMessages([
                                            'required' => __('Company email is required'),
                                            'email' => __('Please enter a valid email address'),
                                        ]),

                                    TextInput::make('website_url')
                                        ->label(__('Website URL'))
                                        ->url()
                                        ->placeholder('https://www.example.com')
                                        ->nullable()
                                        ->validationMessages([
                                            'url' => __('Please enter a valid URL'),
                                        ]),


                                    TextInput::make('comp_unified_number')
                                        ->label(__('Unified Number'))
                                        ->required()
                                        ->numeric()
                                        ->placeholder(__('Just Start with 70xxxxxxxx'))
                                        ->live()
                                        ->extraInputAttributes([
                                            'maxlength' => '10',
                                            'minlength' => '10',
                                            'pattern' => '70[0-9]{8}',
                                            'oninput' => "
                                                this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                                if (this.value.length > 1 && !this.value.startsWith('70')) {
                                                    this.value = '70';
                                                }
                                                this.dispatchEvent(new Event('input'));
                                            "
                                        ])
                                        ->rules([
                                            'required',
                                            'numeric',
                                            'digits:10',
                                            'regex:/^70\d{8}$/',
                                        ])
                                        ->validationMessages([
                                            'required' => __('Unified Number is required'),
                                            'digits' => __('Unified Number must be exactly 10 digits'),
                                            'numeric' => __('Unified Number must contain only numbers'),
                                            'regex' => __('Unified Number must start with 70'),
                                        ]),

                                    TextInput::make('comp_cr_number')
                                        ->label(__('CR Number'))
                                        ->required()
                                        ->unique(table: 'companies',column: 'comp_cr_number', ignoreRecord: true)
                                        ->placeholder(__('Enter company CR Number'))
                                        ->live()
                                        ->rules([
                                            'required',
                                        ])
                                        ->validationMessages([
                                            'required' => __('CR Number is required'),
                                            'unique' => __('This CR Number is already taken'),
                                        ]),



                                    FileUpload::make('comp_unified_file')
                                        ->label(__('Company Unified File'))
                                        ->directory('company_files')
                                        ->preserveFilenames()
                                        ->required()
                                        ->acceptedFileTypes([
                                            'application/pdf',
                                            'application/msword',
                                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                        ])
                                        ->maxSize(10240) // 10MB
                                        ->validationMessages([
                                            'required' => __('Company unified file is required'),
                                            'mimes' => __('File must be a type of: PDF, DOC, DOCX'),
                                            'max' => __('File must not be larger than 10MB'),
                                        ])->beforeStateDehydrated(function (){}),

                                    FileUpload::make('company_logo')
                                        ->label(__('Company Logo'))
                                        ->image()
                                        ->directory('company_logos')
                                        ->preserveFilenames()
                                        ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'])
                                        ->maxSize(5120) // 5MB
                                        ->validationMessages([
                                            'image' => __('File must be an image'),
                                            'mimes' => __('File must be a type of: jpg, png, jpeg, webp, svg'),
                                            'max' => __('File must not be larger than 5MB'),

                                        ])->beforeStateDehydrated(function (){}),

                                    UserCompanyInfoSection::userInfoFields(),

                                    Checkbox::make('terms_accepted')
                                        ->label(new HtmlString(__('I accept the terms and conditions') . ' <a href="/cms/terms-conditions" target="_blank" class="inline-flex items-center text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200">' . ' <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg></a>'))
                                        ->required()
                                        ->required()
                                        ->columnSpanFull()
                                        ->rules(['required', 'accepted'])
                                        ->validationMessages([
                                            'required' => __('You must accept the terms and conditions'),
                                            'accepted' => __('You must accept the terms and conditions'),
                                        ])
                                        ->live(),

                                ]),
                        ]),
                ])

                    ->submitAction(new HtmlString(Blade::render(<<<BLADE

                    <x-filament::button
                            type="submit"
                            size="sm"
                            wire:submit="register"
                            wire:loading.attr="disabled"
                            wire:loading.class="opacity-70 cursor-wait"
                            x-data
                            x-on:click="setTimeout(() => \$el.disabled = true, 1)"
                            x-on:submit="setTimeout(() => \$el.disabled = false, 1000)"
                        >
                        <span wire:loading.remove>{{__('Submit')}}</span>
                        <span wire:loading>{{__('Submitting...')}}</span>
                    </x-filament::button>
                    BLADE))),

            ]);
    }

    protected function getFormActions(): array
    {
        return [];
    }


    private function updateFullName($state, callable $set, $get): void
    {
        $fullName = trim(implode(' ', array_filter([
            $get('first_name') ?? '',
            $get('second_name') ?? '',
            $get('third_name') ?? '',
            $get('last_name') ?? ''
        ])));

        $set('name', $fullName);
    }

    private function hasEmptyRequiredFields(Get $get): bool
    {
        $requiredFields = [
            'email', 'password', 'password_confirmation',
            'first_name', 'second_name', 'third_name', 'last_name',
            'phone_number', 'national_id', 'birth_date',
            'company_name', 'company_phone', 'company_email',
            'company_address', 'comp_unified_number', 'comp_unified_file',
            'comp_cr_number'
        ];

        foreach ($requiredFields as $field) {
            if (empty($get($field))) {
                return true;
            }
        }

        return false;
    }

    protected function sendEmailVerificationNotification(Model $user): void
    {
        if (! $user instanceof MustVerifyEmail) {
            return;
        }

        if ($user->hasVerifiedEmail()) {
            return;
        }

        if (! method_exists($user, 'notify')) {
            $userClass = $user::class;

            throw new \Exception("Model [{$userClass}] does not have a [notify()] method.");
        }

        $notification = app(CustomVerifyEmailNotification::class);
        $notification->url = Filament::getVerifyEmailUrl($user);

        $user->notify($notification);
    }
}

