<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MaintenanceRequestResource\Components\FormComponent;
use App\Filament\Resources\MaintenanceRequestResource\Components\TableComponent;
use App\Filament\Resources\MaintenanceRequestResource\Pages\CreateMaintenanceRequest;
use App\Filament\Resources\MaintenanceRequestResource\Pages\EditMaintenanceRequest;
use App\Filament\Resources\MaintenanceRequestResource\Pages\ListMaintenanceRequests;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;


class MaintenanceRequestResource extends Resource
{
    protected static ?string $model = MaintenanceRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    const SENDER_TYPE = [
        Account::class => 'Account',
        User::class => 'User'
    ];

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListMaintenanceRequests::route('/'),
            'create' =>CreateMaintenanceRequest::route('/create'),
//            'view' => ViewMaintenanceRequest::route('/{record}'),
            'edit' => EditMaintenanceRequest::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
    public static function getNavigationGroup(): string
    {
        return  __('Maintenance Management');
    }
    public static function getNavigationLabel(): string
    {
        return __("Maintenance Requests");
    }
    public static function getBreadcrumb() : string
    {
        return __('Maintenance Request');
    }
    public static function getModelLabel(): string
    {
        return __('Maintenance Request');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Maintenance Request');
    }
}
