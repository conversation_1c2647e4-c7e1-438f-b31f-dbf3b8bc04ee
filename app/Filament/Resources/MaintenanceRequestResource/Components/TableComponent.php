<?php

namespace App\Filament\Resources\MaintenanceRequestResource\Components;

use App\Models\User;
use App\Shared\Components\DateRangeFilter;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Account\app\Models\Account;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Actions\IssueInvoiceAction;

class TableComponent
{
    public static function getTable($table)
    {
        return $table
            ->columns([
                TextColumn::make('unit.name')
                    ->label(__('Unit Name'))
                    ->getStateUsing(fn (Model $record): string => $record->unit?->property_type?->name . ' - ' . $record->unit?->number ?? 'N/A')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('account.name')
                    ->label(__('Account'))
                    ->getStateUsing(fn (Model $record): string => $record->account?->getFullName() ?? 'N/A')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHasMorph(
                            'account',
                            [Account::class, User::class],
                            function (Builder $query, $type) use ($search) {
                                $type === User::class
                                    ? $query->where('name', 'like', "%{$search}%")
                                    : $query->where(fn ($q) =>
                                $q->where('first_name', 'like', "%{$search}%")
                                    ->orWhere('last_name', 'like', "%{$search}%")
                                );
                            }
                        );
                    }),

                BadgeColumn::make('service.name')
                    ->label(__('Service'))
                    ->getStateUsing(fn (Model $record): string => $record->service?->name ?? 'N/A')
                    ->sortable()
                    ->searchable(),

                BadgeColumn::make('account_type')
                    ->label(__('Account Type'))
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        Account::class => __('Account'),
                        User::class => __('User'),
                        default => __('Unknown'),
                    })
                    ->colors([
                        'primary' => 'Account/Entities/Account',
                        'success' => 'App/Models/User',
                    ]),

                TextColumn::make('account_id')
                    ->label(__('Account Id'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('description')
                    ->label(__('Description'))
                    ->limit(25)
                    ->tooltip(function (TextColumn $column): string {
                        return strip_tags($column->getState());
                    })
                    ->formatStateUsing(fn (string $state): string => Str::of(strip_tags($state))->limit(25))
                    ->searchable()
                    ->sortable(),

                TextColumn::make('status')
                    ->label(__('Status'))
                    ->formatStateUsing(fn (string $state): string => maintenanceRequestStatusEnum::trans($state))
                    ->sortable()
                    ->badge()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->sortable()
                    ->tooltip(fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s'))
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([


                Filter::make('account_name')
                    ->form([
                        TextInput::make('account_name')
                            ->label(__('Account Name'))
                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['account_name'],
                            fn ($query, $value) => $query->whereHasMorph(
                                'account',
                                [User::class, Account::class],
                                fn ($query, $type) => $type === User::class
                                    ? $query->where('name', 'like', "%{$value}%")
                                    : $query->where(fn ($q) =>
                                    $q->where('first_name', 'like', "%{$value}%")
                                        ->orWhere('last_name', 'like', "%{$value}%")
                                    )
                            )
                        );
                    }),

                SelectFilter::make('status')
                    ->options(maintenanceRequestStatusEnum::getMaintenanceRequestStatusOptions())
                    ->label(__('Status'))
                    ->multiple(),
                DateRangeFilter::make('created_at')
                    ->column('created_at')
                    ->fromLabel(__('Created From'))
                    ->untilLabel(__('Created Until')),
                DateRangeFilter::make('visit_time')
                    ->column('visit_time')
                    ->fromLabel(__('Visit From'))
                    ->untilLabel(__('Visit Until')),
                DateRangeFilter::make('service_time')
                    ->column('service_time')
                    ->fromLabel(__('Service Time From'))
                    ->untilLabel(__('Service Time Until')),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    DeleteAction::make(),
                    IssueInvoiceAction::make(),
                ])
                ->button()
                ->extraAttributes([
                    'class' => 'custom-action-btn',
                ])
                ->color('transparent')
                ->label(__('Commends')),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ;
    }
}
