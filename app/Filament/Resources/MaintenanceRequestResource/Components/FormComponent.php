<?php

namespace App\Filament\Resources\MaintenanceRequestResource\Components;

use App\Helpers\AccountHelper;
use App\Models\User;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseEnum;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;
use Modules\Property\app\Models\Property;
use Modules\Service\app\Models\Service;

class FormComponent
{

    public static function getForm(): array
    {
        return [
            Hidden::make('account_type')
                ->default(Account::class),
            Select::make('account_id')
                ->label(__('Account'))
                ->required()
                ->options(AccountHelper::getAccountOptions())
                ->searchable()
                ->placeholder(__('Select an Account'))
                ->reactive(),

            Select::make('lease_id')
                ->label(__('Lease'))
                ->options(function (Get $get) {
                    $accountId = $get('account_id');

                    if (!$accountId) {
                        return [];
                    }

                    $leases = LeaseMember::where('member_id', $accountId)
                        ->whereHas('lease', function ($query) {
                            $query->where('status', LeaseEnum::PUBLISHED);
                        })
                        ->pluck('lease_id', 'lease_id') // Use both as key and value
                        ->toArray();

                    return $leases;
                })
                ->required()
                ->searchable()
                ->live()
                ->afterStateUpdated(function (Set $set) {
                    $set('service_id', null);
                    $set('unit_id', null); // Reset unit_id when lease changes
                })
                ->preload()
                ->placeholder(__('Select a lease')),

            Select::make('unit_id')
                ->label(__('Unit Number'))
                ->options(function (Get $get) {
                    $leaseId = $get('lease_id');

                    if (!$leaseId) {
                        return [];
                    }

                    // If you have a relationship defined
                    return LeaseUnit::where('lease_id', $leaseId)
                        ->with('unit') // Make sure you have the unit relationship defined
                        ->get()
                        ->pluck('unit.number', 'unit_id')
                        ->toArray();
                })
                ->required()
                ->searchable()
                ->live()
                ->afterStateUpdated(function (Set $set) {
                    $set('service_id', null);
                })
                ->preload()
                ->placeholder(__('Select a unit')),

                Select::make('service_id')
                ->label(__('Service'))
                ->options(function (Get $get) {
                    $leaseId = $get('lease_id');
                    $unitId = $get('unit_id');
            
                    if (!$leaseId || !$unitId) {
                        return [];
                    }
            
                    return Service::where('is_fixed_lease', false)
                        ->whereHas('leaseUnitServices', function ($query) use ($leaseId, $unitId) {
                            $query->whereHas('leaseUnit', function ($q) use ($leaseId, $unitId) {
                                $q->where('lease_id', $leaseId)
                                    ->where('unit_id', $unitId);
                            });
                        })
                        ->pluck('name', 'id'); // Pluck name for display, ID for value
                })
                ->required()
                ->searchable()
                ->preload()
                ->placeholder(__('Select a service')),

            TextInput::make('reason')
                ->label(__('Reason'))
                ->maxLength(255)
                ->nullable()
                ->placeholder(__('Enter the reason for the maintenance request')),

            DateTimePicker::make('visit_time')
                ->label(__('Visit Time'))
                ->nullable()
                ->native(false)
                ->placeholder(__('Select visit date and time')),

            DateTimePicker::make('service_time')
                ->label(__('Service Time'))
                ->native(false)
                ->nullable()
                ->placeholder(__('Select service date and time')),

            Textarea::make('description')
                ->label(__('Description'))
                ->nullable(),

            Select::make('status')
                ->label(__('Status'))
                ->options(MaintenanceRequestStatusEnum::getMaintenanceRequestStatusOptions())
                ->required(),

        ];
    }
}
