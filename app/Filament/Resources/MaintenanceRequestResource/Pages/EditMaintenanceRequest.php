<?php

namespace App\Filament\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Resources\MaintenanceRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;

class EditMaintenanceRequest extends EditRecord
{
    protected static string $resource = MaintenanceRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $this->sendNotification($this->getRecord());
    }
    protected function sendNotification($record): void
    {
        // push notification
        //modify_maintenance_req notify
        $template = NotificationsTemplate::where(['key' => 'modify_maintenance_req'])->first();
        if ($template) {
            SendNotification::make(["fcm-api"])
                ->template($template->key)
                ->model(Account::class)
                ->id($record->account_id)
                ->findBody(['{status}', '{expected_date}'])
                ->replaceBody([maintenanceRequestStatusEnum::getLabel($record->status), $record->expected_date])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
    }
}
