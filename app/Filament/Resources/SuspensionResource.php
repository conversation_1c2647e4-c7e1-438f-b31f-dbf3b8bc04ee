<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\Suspension;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;

class SuspensionResource extends Resource
{
    protected static ?string $model = Suspension::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Filament Shield';


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('ID'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('reason')
                    ->label(__('Reason'))
                    ->sortable()
                    ->default('-'),
                Tables\Columns\TextColumn::make('action_taken')
                    ->label(__('Type'))
                    ->sortable()
                    ->color(fn ($state) => $state === 'Suspended' ? 'danger' : 'success'),
                Tables\Columns\TextColumn::make('suspended_type')
                    ->label(__('Suspended'))
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        $modelClass = $record->suspended_type;

                        if (class_exists($modelClass)) {
                            $relatedModel = $modelClass::find($record->suspended_id);
                            if ($relatedModel) {
                                return $relatedModel->name;
                            }
                        }

                        return __('Unknown');
                    }),
                Tables\Columns\TextColumn::make('suspended_by_type')
                    ->label(__('Suspended By'))
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        $modelClass = $record->suspended_by_type;

                        if (class_exists($modelClass)) {
                            $relatedModel = $modelClass::find($record->suspended_by_id);
                            if ($relatedModel) {
                                return $relatedModel->name;
                            }
                        }

                        return __('Unknown');
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->since(),
            ])
            ->filters([
                SelectFilter::make('action_taken')
                ->label(__('Type'))
                ->options([
                    'Suspended' => __('Suspended'),
                    'Unsuspended' => __('Unsuspended'),
                ])
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);

    }

    public static function getPages(): array
    {
        return [
            'index' => SuspensionResource\Pages\ListSuspension::route('/'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }

   public static function getNavigationLabel(): string
   {
       return __("Suspension Log");
   }
   public static function getNavigationGroup(): ?string
   {
       return __('filament-shield::filament-shield.nav.group');
   }

    public static function getBreadcrumb() : string
    {
        return __('suspensions');
    }
    public static function getModelLabel(): string
    {
        return __('suspension');
    }

    public static function getPluralModelLabel(): string
    {
        return __('suspensions');
    }
}
