<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use App\Shared\CalculatesDailyStats;

class InactiveLeaseCount extends BaseWidget
{
    use HasWidgetShield,CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Inactive Leases');
    }
    
    protected function getStats(): array
    {
        $NonActiveLeases = Lease::select('status','updated_at')
        ->where('status','!=', LeaseEnum::PUBLISHED)->get();

        $NonActiveLeasesChart = $this->calculateDailyStats($NonActiveLeases, 'updated_at');

        $totalInactiveLeases = $NonActiveLeases->count();
                return [
            Stat::make(__('Total Inactive Leases'), $totalInactiveLeases)
                ->color('danger')
                ->chart(array_reverse($NonActiveLeasesChart))
        ];
    }
}
