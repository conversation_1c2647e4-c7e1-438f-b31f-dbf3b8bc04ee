<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use App\Enums\RoleEnum;
use <PERSON><PERSON><PERSON><PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Modules\Subscription\app\models\Subscription;
use App\Shared\CalculatesDailyStats;

class ActiveSubscription extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Active Subscriptions');
    }
    
    protected function getStats(): array
    {
        $activeSubscriptions = Subscription::select('is_current', 'updated_at')
            ->where('is_current', true)
            ->get();
        
        $activeChart = $this->calculateDailyStats($activeSubscriptions, 'updated_at');
        $totalActiveSubscribtion = $activeSubscriptions->count();

        return [
            Stat::make(__('Total Active Subscriptions'), $totalActiveSubscribtion)
                ->color('success')
                ->chart(array_reverse($activeChart))
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole(RoleEnum::ADMIN);
    }
}