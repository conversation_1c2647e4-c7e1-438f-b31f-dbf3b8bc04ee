<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use App\Shared\CalculatesDailyStats;
use Illuminate\Support\HtmlString;

class TotalPaidPayments extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }
    
    protected function getHeading(): ?string
    {
        return __('Total Income Payments');
    }
    
    protected function getStats(): array
    {
        $paidInvoices = Invoice::select('status', 'paid', 'updated_at')
            ->whereIn('status', [InvoiceStatusEnum::PAID, InvoiceStatusEnum::PARTIAL_PAID])
            ->get();
        
        $paidChart = $this->calculateDailyTotals($paidInvoices, 'updated_at', 'paid');
        $totalPaid = $paidInvoices->sum('paid');

        return [
            Stat::make(__('Total Income Payments'), 
            new HtmlString(
                '<div class="text-3xl font-bold">' .
                number_format($totalPaid) .
                ' <span class="icon-saudi_riyal"></span>' .
                '</div>'
            ))
                ->color('success')
                ->chart(array_reverse($paidChart))
        ];
    }
}