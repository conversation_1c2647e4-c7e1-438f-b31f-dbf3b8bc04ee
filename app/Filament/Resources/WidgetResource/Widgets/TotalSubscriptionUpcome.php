<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use App\Enums\RoleEnum;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Modules\Subscription\app\models\Subscription;
use App\Shared\CalculatesDailyStats;
use Illuminate\Support\HtmlString;

class TotalSubscriptionUpcome extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Subscription Upcome');
    }
    
    protected function getStats(): array
    {
        $currentMonthStart = Carbon::now()->startOfMonth();
        $currentMonthEnd = Carbon::now()->endOfMonth();

        $upcomingSubscriptions = Subscription::select('is_current', 'expired_at', 'price', 'updated_at')
            ->where('is_current', true)
            ->whereBetween('expired_at', [$currentMonthStart, $currentMonthEnd])
            ->get();
        
        $upcomeChart = $this->calculateDailyTotals($upcomingSubscriptions, 'expired_at', 'price');
        $totalSubscriptionUpcomeCurrentMonth = $upcomingSubscriptions->sum('price');

        return [
            Stat::make(__('Total Subscription Upcome'),
                new HtmlString(
                    '<div class="text-3xl font-bold">' .
                    number_format($totalSubscriptionUpcomeCurrentMonth) .
                    ' <span class="icon-saudi_riyal"></span>' .
                    '</div>'
                ))
                ->color('warning')
                ->chart(array_reverse($upcomeChart))
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole(RoleEnum::ADMIN);
    }
}