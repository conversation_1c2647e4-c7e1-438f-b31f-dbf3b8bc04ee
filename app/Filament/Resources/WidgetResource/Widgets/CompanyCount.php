<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use App\Enums\RoleEnum;
use <PERSON><PERSON><PERSON><PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Modules\Company\app\Models\Company;
use App\Shared\CalculatesDailyStats;

class CompanyCount extends BaseWidget
{
    use HasWidgetShield,CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Companies');
    }
    
    protected function getStats(): array
    {
        $companies = Company::select('id', 'created_at')->get();
        if(auth()->user()->company_id == null)
        {
            $count =  $companies->count();
            $companyChart = $this->calculateDailyStats($companies, 'created_at');
        }else{
            $count =  1;
            $companyChart = [];
        }
        return [
            Stat::make(__('Total Companies'), $count)
                ->color('primary')
                ->chart(array_reverse($companyChart))
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole(RoleEnum::ADMIN);
    }
}
