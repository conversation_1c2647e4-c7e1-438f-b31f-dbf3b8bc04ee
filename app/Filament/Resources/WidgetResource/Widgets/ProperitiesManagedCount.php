<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;
use App\Shared\CalculatesDailyStats;

class ProperitiesManagedCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Managed Properities');
    }
    
    protected function getStats(): array
    {
        $managedProperties = Property::select('status', 'updated_at')
            ->where('status', '!=', PropertyStatus::DRAFT)
            ->get();
        
        $propertiesChart = $this->calculateDailyStats($managedProperties, 'updated_at');
        $totalProperities = $managedProperties->count();

        return [
            Stat::make(__('Total Managed Properities'), $totalProperities)
                ->color('warning')
                ->chart(array_reverse($propertiesChart))
        ];
    }
}