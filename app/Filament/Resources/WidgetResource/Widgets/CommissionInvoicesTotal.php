<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\HtmlString;
use Modules\Invoice\app\Models\Invoice;
use App\Shared\CalculatesDailyStats;

class CommissionInvoicesTotal extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Commission Invoices');
    }

    protected function getStats(): array
    {
        $commissionInvoices = Invoice::select('invoice_type', 'total', 'updated_at')
            ->where('invoice_type', 'commission')
            ->get();

        $commissionChart = $this->calculateDailyTotals($commissionInvoices, 'updated_at', 'total');
        $totalCommission = $commissionInvoices->sum('total');

        return [
            Stat::make(__('Total Commission Invoices'),
            new HtmlString(
                '<div class="text-3xl font-bold">' .
                number_format($totalCommission) .
                ' <span class="icon-saudi_riyal"></span>' .
                '</div>'
            )
            )
                ->color('success')
                ->chart(array_reverse($commissionChart))
        ];
    }
}
