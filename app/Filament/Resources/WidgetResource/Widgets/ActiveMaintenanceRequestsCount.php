<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;
use App\Shared\CalculatesDailyStats;

class ActiveMaintenanceRequestsCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Active Maintenance Requests');
    }
    
    protected function getStats(): array
    {
        $activeRequests = MaintenanceRequest::select('status', 'updated_at')
            ->where('status', maintenanceRequestStatusEnum::OPEN)
            ->get();
        
        $activeRequestsChart = $this->calculateDailyStats($activeRequests, 'updated_at');
        $totalActiveRequests = $activeRequests->count();

        return [
            Stat::make(__('Active Maintenance Requests'), $totalActiveRequests)
                ->color('success')
                ->chart(array_reverse($activeRequestsChart))
        ];
    }
}