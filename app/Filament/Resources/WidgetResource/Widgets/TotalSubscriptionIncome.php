<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use App\Enums\RoleEnum;
use Illuminate\Support\HtmlString;
use App\Shared\CalculatesDailyStats;
use Illuminate\Support\Facades\Auth;
use Modules\Payment\Enums\PaymentStatusEnum;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Payment\app\Models\KhaledsPayment;
use Modules\Subscription\app\models\Subscription;
use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class TotalSubscriptionIncome extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Subscription Income');
    }
    
    protected function getStats(): array
    {
        $subscriptionPayments = KhaledsPayment::select('order_table', 'payment_status', 'amount', 'updated_at')
            ->where('order_table', Subscription::class)
            ->where('payment_status', PaymentStatusEnum::PAID)
            ->get();
        
        $incomeChart = $this->calculateDailyTotals($subscriptionPayments, 'updated_at', 'amount');
        $totalSubscriptionIncome = $subscriptionPayments->sum('amount');

        return [
            Stat::make(__('Total Subscription Income'),
            new HtmlString(
                '<div class="text-3xl font-bold">' .
                number_format($totalSubscriptionIncome) .
                ' <span class="icon-saudi_riyal"></span>' .
                '</div>'
            ))
                ->color('success')
                ->chart(array_reverse($incomeChart))
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole(RoleEnum::ADMIN);
    }
}