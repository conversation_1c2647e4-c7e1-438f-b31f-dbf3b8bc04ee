<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseSyncStatus;
use App\Shared\CalculatesDailyStats;

class EjarRegisteredLeaseCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }
    
    protected function getHeading(): ?string
    {
        return __('Total Ejar Registered Leases');
    }
    protected function getStats(): array
    {
        $registeredLeases = Lease::select('ejar_uuid', 'updated_at')
            ->whereNotNull('ejar_uuid')
            ->get();

        $registeredLeasesChart = $this->calculateDailyStats($registeredLeases, 'updated_at');
        $totalRegisterdEjarLeases = $registeredLeases->count();

        return [
            Stat::make(__('Total Ejar Registered Leases'), $totalRegisterdEjarLeases)
                ->color('primary')
                ->chart(array_reverse($registeredLeasesChart))
        ];
    }
}