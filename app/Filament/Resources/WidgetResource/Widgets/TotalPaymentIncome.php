<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;
use Modules\Payment\app\Models\KhaledsPayment;
use Modules\Payment\Enums\PaymentStatusEnum;

class TotalPaymentIncome extends BaseWidget
{
    use HasWidgetShield;

    protected int | string | array $columnSpan = '1';

    protected function getHeading(): ?string
    {
        return __('Total Paid Payments');
    }

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }
    
    protected function getStats(): array
    {
        $totalIncomeGenerated = Invoice::where('status' , [InvoiceStatusEnum::UNPAID , InvoiceStatusEnum::PARTIAL_PAID])->sum('remaining');

        return [
            Stat::make('', $totalIncomeGenerated)
                ->color('primary')
        ];
    }
}
