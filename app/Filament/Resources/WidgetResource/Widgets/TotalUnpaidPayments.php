<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use App\Shared\CalculatesDailyStats;
use Illuminate\Support\HtmlString;

class TotalUnpaidPayments extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Unpaid Payments');
    }
    
    protected function getStats(): array
    {
        $unpaidInvoices = Invoice::select('status', 'remaining', 'updated_at')
            ->whereIn('status', [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID])
            ->get();
        
        $unpaidChart = $this->calculateDailyTotals($unpaidInvoices, 'updated_at', 'remaining');
        $totalUnpaid = $unpaidInvoices->sum('remaining');
        $activeSubscriptionUrl = url('/') . '/admin/invoices';

        return [
            Stat::make(__('Total Unpaid Payments'), 
            new HtmlString(
                '<div class="text-3xl font-bold">' .
                number_format($totalUnpaid) .
                ' <span class="icon-saudi_riyal"></span>' .
                '</div>'
            ))
                ->color('danger')
                ->chart(array_reverse($unpaidChart))
                ->url($activeSubscriptionUrl)
        ];
    }
}