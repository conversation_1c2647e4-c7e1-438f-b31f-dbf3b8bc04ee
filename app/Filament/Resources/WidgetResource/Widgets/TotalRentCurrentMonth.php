<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Illuminate\Support\HtmlString;

class TotalRentCurrentMonth extends BaseWidget
{
    use HasWidgetShield;

    protected int | string | array $columnSpan = '1';

    protected function getHeading(): ?string
    {
        return __('Total Rent Current Month');
    }

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }
    
    protected function getStats(): array
    {
        $leaseIds = Lease::where('status' , LeaseEnum::PUBLISHED)->pluck('id');
        
        $totalRent = InvoiceSchedule::whereIn('lease_id' , $leaseIds)->whereBetween('due_date', [

            Carbon::now()->startOfMonth(),

            Carbon::now()->endOfMonth(),

        ])->sum('total_amount');

        return [
            Stat::make(__('Total Rent Current Month'),
            new HtmlString(
                '<div class="text-3xl font-bold">' .
                number_format($totalRent) .
                ' <span class="icon-saudi_riyal"></span>' .
                '</div>'
            ))
                ->color('primary')
        ];
    }
}
