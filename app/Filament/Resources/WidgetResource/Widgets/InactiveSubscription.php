<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use App\Enums\RoleEnum;
use <PERSON><PERSON><PERSON><PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Modules\Subscription\app\models\Subscription;
use App\Shared\CalculatesDailyStats;

class InactiveSubscription extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('InActive Subscriptions');
    }
    
    protected function getStats(): array
    {
        $inactiveSubscriptions = Subscription::select('is_current', 'updated_at')
            ->where('is_current', false)
            ->get();
        
        $inactiveChart = $this->calculateDailyStats($inactiveSubscriptions, 'updated_at');
        $totalInactiveSubscribtion = $inactiveSubscriptions->count();

        return [
            Stat::make(__('InActive Subscriptions'), $totalInactiveSubscribtion)
                ->color('danger')
                ->chart(array_reverse($inactiveChart))
        ];
    }

    public static function canView(): bool
    {
        return Auth::user()?->hasRole(RoleEnum::ADMIN);
    }
}