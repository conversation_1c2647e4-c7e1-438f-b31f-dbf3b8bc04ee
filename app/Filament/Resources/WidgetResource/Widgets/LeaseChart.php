<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\ChartWidget;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseSyncStatus;

class LeaseChart extends ChartWidget
{
    use HasWidgetShield;

    protected static ?string $heading = 'Lease Chart';

    protected int | string | array $columnSpan = '1';
    
    public function __construct()
    {
        static::$heading = __('Lease Chart');
    }
    
    protected function getData(): array
    {
        $totalActiveLeases = Lease::where('status', LeaseEnum::PUBLISHED)->count();
        $totalDraftLeases = Lease::where('status', LeaseEnum::DRAFT)->count();
        $totalClosedLeases = Lease::where('status', LeaseEnum::CLOSED)->count();
        $totalTerminatedLeases = Lease::where('status', LeaseEnum::TERMINATED)->count();
        $totalRegisterdEjarLeases = Lease::where('ejar_sync_status',LeaseSyncStatus::COMPLETED->value)->count();

        return [
            'datasets' => [
                [
                    'label' => __('Leases Distribution'),
                    'data' => [
                        $totalActiveLeases,
                        $totalDraftLeases,
                        $totalClosedLeases,
                        $totalTerminatedLeases,
                        $totalRegisterdEjarLeases,
                    ],
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',   // Blue
                        'rgba(239, 68, 68, 0.8)',    // Red
                        'rgba(34, 197, 94, 0.8)',    // Green
                        'rgba(67, 67, 67, 0.8)',     // Gray
                        'rgba(60, 85, 238, 0.8)',    // Blue
                    ],
                    'borderColor' => [
                        'rgb(59, 130, 246)',
                        'rgb(239, 68, 68)',
                        'rgb(34, 197, 94)',
                        'rgb(67, 67, 67)',
                        'rgb(60, 85, 238)',
                    ],
                    'borderWidth' => 1
                ],
            ],
            'labels' => [
                __('Published Leases'),
                __('Draft Leases'),
                __('Closed Leases'),
                __('Terminated Leases'),
                __('Registered EJAR Leases'),
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
            ],
        ];
    }
}