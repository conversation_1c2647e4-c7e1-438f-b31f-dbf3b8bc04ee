<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use App\Shared\CalculatesDailyStats;

class ActiveLeaseCount extends BaseWidget
{
    use HasWidgetShield,CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    
    protected function getColumns(): int
    {
        return $this->columnSpan;
    }
    protected function getHeading(): ?string
    {
        return __('Active Lease Count');
    }
    
    protected function getStats(): array
    {
        $ActiveLeases = Lease::select('status','updated_at')
        ->where('status', LeaseEnum::PUBLISHED)->get();
        $activeSubscriptionUrl = url('/') .'/'.'admin'.'/'.'leases?tableFilters[status][value]=published';
        $activeLeasesChart = $this->calculateDailyStats($ActiveLeases, 'updated_at');

        $totalActiveLeases = $ActiveLeases->count();

        return [
            Stat::make( __('Active Lease Count'), $totalActiveLeases)
                ->color('success')
                ->url($activeSubscriptionUrl)
                ->chart(array_reverse($activeLeasesChart))
        ];
    }
}
