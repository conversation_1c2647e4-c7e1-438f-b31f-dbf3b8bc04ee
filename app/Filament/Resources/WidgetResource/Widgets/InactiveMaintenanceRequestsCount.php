<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;
use App\Shared\CalculatesDailyStats;

class InactiveMaintenanceRequestsCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Inactive Maintenance Requests');
    }
    
    protected function getStats(): array
    {
        $inactiveRequests = MaintenanceRequest::select('status', 'updated_at')
            ->where('status', '!=', maintenanceRequestStatusEnum::OPEN)
            ->get();
        
        $inactiveRequestsChart = $this->calculateDailyStats($inactiveRequests, 'updated_at');
        $totalInactiveRequests = $inactiveRequests->count();

        return [
            Stat::make(__('Total Inactive Maintenance Requests'), $totalInactiveRequests)
                ->color('danger')
                ->chart(array_reverse($inactiveRequestsChart))
        ];
    }
}