<?php

namespace App\Filament\Resources;

use App\Enums\DocumentTypeEnum;
use App\Services\RoleService;
use Filament\Forms;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Tables;
use App\Models\Document;
use Filament\Forms\Form;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use App\Models\DocumentType;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\DocumentResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\DocumentResource\RelationManagers;
use Filament\Resources\Pages\EditRecord;
use Modules\Property\Enums\OwnershipDocumentTypeEnum;
use Str;
use App\Forms\Components\HijriDatePicker;


class DocumentResource extends Resource
{
    protected static ?string $model = Document::class;

    protected static ?string $navigationIcon = 'heroicon-o-document';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Document Type & Name'))
                    ->label(__('Document Type & Name'))
                    ->schema([
                        Forms\Components\Select::make('document_type_id')
                            ->required()
                            ->label(__('Document Type'))
                            ->options(DocumentType::select('id','name')->get()->pluck('name', 'id'))
                            ->hidden(fn($livewire) => $livewire instanceof EditRecord)
                            ->searchable(['name'])
                            ->native(false)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('metadata.document_name_and_type')
                            ->required()
                            ->label(__('Document Name and Type'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('metadata.document_number')
                                ->required()
                                ->label(__('Document Number'))
                                ->maxLength(255),
                    ])->columns(2),
                Forms\Components\Section::make(__('Document Release Information'))
                    ->label(__('Document Release Information'))
                    ->schema([
                        HijriDatePicker::make('metadata.release_date')
                            ->required()
                            ->native(false)
                            ->syncWith('document_date')
                            ->showConvertedDate()
                            ->label(__('Release Date')),
                        HijriDatePicker::make('metadata.expiration_date')
                            ->required()
                            ->native(false)
                            ->syncWith('document_date')
                            ->showConvertedDate()
                            ->hideSwitcher()
                            ->label(__('Expiration Date')),
                        Forms\Components\TextInput::make('metadata.released_by')
                            ->required()
                            ->label(__('Released By'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('metadata.released_in')
                            ->required()
                            ->label(__('Released In'))
                            ->maxLength(255)
                    ])->columns(2),
                Forms\Components\Section::make(__('Upload Contract'))
                    ->label(__('Upload Contract'))
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('upload_contract')
                            ->label(__('Upload Contract Authorization'))
                            ->imageEditor()
                            ->downloadable()
                            ->panelLayout('grid')
                            ->reorderable()
                            ->multiple()
                            ->maxSize(5120)
                            ->collection(function (Forms\Get $get) {
                                $documentTypeId = $get('document_type_id');
                                $documentType = DocumentType::find($documentTypeId)->key;
                                return $documentType == DocumentTypeEnum::Ownership->value ? 'ownership_document' :'upload_contract';
                            })
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->columnSpanFull()
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('DocumentType.name')
                    ->label(__('Document Type'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('morphable_type')
                    ->label(__('Related To'))
                    ->formatStateUsing(function ($state) {
                        return class_basename($state);
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('morphable.id')
                    ->label(__('Related Item'))
                    ->url(fn ($record) => $record->morphable ? url("/admin/" . Str::plural(strtolower(class_basename($record->morphable_type))) . "/" . $record->morphable_id) : null)
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->datetime()->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ]);
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\DeleteBulkAction::make(),
            //         Tables\Actions\ForceDeleteBulkAction::make(),
            //         Tables\Actions\RestoreBulkAction::make(),
            //     ]),
            // ]);
    }

    public static function infolist(Infolist|\Filament\Infolists\Infolist $infolist): \Filament\Infolists\Infolist
    {
        return $infolist
            ->schema([
                Tabs::make('Document Information')
                    ->tabs([
                        Tabs\Tab::make(__('Document Details'))
                            ->schema([
                                Section::make(__('Document Type & Name'))
                                    ->schema([
                                        TextEntry::make('metadata.document_type')
                                                ->label(__('Document Type')),

                                        TextEntry::make('metadata.owner_id_number')
                                            ->label(__('Owner ID Number'))
                                            ->visible(fn ($record) => $record->metadata['document_type'] === OwnershipDocumentTypeEnum::Electronic->value),

                                        TextEntry::make('metadata.ownership_reference_no')
                                            ->label(__('Ownership Reference No.'))
                                            ->visible(fn ($record) => in_array($record->metadata['document_type'], [
                                                OwnershipDocumentTypeEnum::Electronic->value,
                                                OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                                                OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                                                OwnershipDocumentTypeEnum::Other->value,
                                            ])),

                                        TextEntry::make('metadata.real_estate_number')
                                            ->label(__('Real Estate Number'))
                                            ->visible(fn ($record) => $record->metadata['document_type'] === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value),

                                        TextEntry::make('metadata.legal_document_type_name')
                                            ->label(__('Legal Document Type Name'))
                                            ->visible(fn ($record) => $record->metadata['document_type'] === OwnershipDocumentTypeEnum::Other->value),

                                        TextEntry::make('metadata.issue_date')
                                            ->label(fn ($record) => $record->metadata['document_type'] === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value
                                                ? __('First Registration Date')
                                                : __('Issue Date'))
                                            ->visible(fn ($record) => in_array($record->metadata['document_type'], [
                                                OwnershipDocumentTypeEnum::Electronic->value,
                                                OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                                                OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                                                OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value,
                                                OwnershipDocumentTypeEnum::Other->value,
                                            ])),
                                    ])
                                    ->visible(fn ($record) => $record->DocumentType?->key === DocumentTypeEnum::Ownership->value)
                                    ->columns(2),
                                Section::make(__('Document Type & Name'))
                                    ->schema([
                                        TextEntry::make('DocumentType.name')
                                            ->label(__('Document Type')),
                                        TextEntry::make('metadata.document_name_and_type')
                                            ->label(__('Document Name and Type')),
                                        TextEntry::make('metadata.document_number')
                                            ->label(__('Document Number')),
                                    ])
                                    ->visible(fn ($record) => $record->DocumentType?->key === DocumentTypeEnum::Representative->value)
                                    ->columns(2),

                                Section::make(__('Document Release Information'))
                                    ->schema([
                                        TextEntry::make('metadata.release_date')
                                            ->label(__('Release Date'))
                                            ->date(),
                                        TextEntry::make('metadata.expiration_date')
                                            ->label(__('Expiration Date'))
                                            ->date(),
                                        TextEntry::make('metadata.released_by')
                                            ->label(__('Released By')),
                                        TextEntry::make('metadata.released_in')
                                            ->label(__('Released In')),
                                    ])
                                    ->visible(fn ($record) => $record->DocumentType?->key === DocumentTypeEnum::Representative->value)
                                    ->columns(2),
                            ]),

                        Tabs\Tab::make(__('Documents Attachments'))
                            ->schema([
                                Section::make('Uploaded Documents')
                                    ->schema([
                                        ViewEntry::make('documents')
                                            ->view('filament.resources.document-resource.components.document-viewer')
                                            ->viewData([
                                                'documentType' => $infolist->record->DocumentType,
                                            ])
                                            ->columnSpanFull(),
                                    ])
                                    ->collapsible()
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocuments::route('/'),
            // 'create' => Pages\CreateDocument::route('/create'),
            'view' => Pages\ViewDocument::route('/{record}'),
            'edit' => Pages\EditDocument::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        return $query
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Leases Management');
    }

    public static function getNavigationLabel(): string
    {
        return __("Documents");
    }

    public static function getBreadcrumb() : string
    {
        return __('Documents');
    }
    public static function getModelLabel(): string
    {
        return __('Document');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Documents');
    }
}
