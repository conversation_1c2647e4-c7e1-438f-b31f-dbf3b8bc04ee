<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Subscription\app\Enums\SubscriptionStatusEnum;
use Illuminate\Support\HtmlString;

class SubscriptionsRelationManager extends RelationManager
{
    protected static string $relationship = 'subscriptions';

    protected static ?string $title = 'Subscriptions';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Subscriptions');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Subscriptions');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->where('is_current', true))
            ->columns([
                TextColumn::make('plan.name')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('price')
                    ->formatStateUsing(function ($state) {
                       return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->sortable(),

                TextColumn::make('start_at')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('expired_at')
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => SubscriptionStatusEnum::tryFrom($state)?->color() ?? 'gray'),

                BooleanColumn::make('is_current')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->sortable(),
            ])
            ->defaultSort('start_at', 'desc');
    }
}
