<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Ticket\Enums\TicketStatusEnum;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Filament\Resources\RelationManagers\RelationManager;

class TicketsRelationManager extends RelationManager
{
    protected static string $relationship = 'tickets';

    protected static ?string $recordTitleAttribute = 'title';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('tickets');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('tickets');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('ticket_category_id')
                    ->relationship('ticketCategory', 'name')
                    ->label(__('Category'))
                    ->required(),

                Forms\Components\TextInput::make('title')
                    ->label(__('Title'))
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('priority')
                    ->label(__('Priority'))
                    ->options(TicketPriorityEnum::getTicketPriorityOptions())
                    ->required(),

                Forms\Components\Select::make('status')
                    ->label(__('Status'))
                    ->options(TicketStatusEnum::getTicketStatusOptions())
                    ->required(),

                Forms\Components\Textarea::make('message')
                    ->label(__('Message'))
                    ->required()
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('ticketCategory.name')
                    ->label(__('Category')),

                Tables\Columns\TextColumn::make('priority')
                    ->label(__('Priority'))
                    ->badge()
                    ->formatStateUsing(fn (string $state) => TicketPriorityEnum::getLabel($state))
                    ->color(fn (string $state): string => match ($state) {
                        TicketPriorityEnum::HIGH => 'danger',
                        TicketPriorityEnum::MEDIUM => 'warning',
                        TicketPriorityEnum::LOW => 'success',
                        default => 'secondary',
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->formatStateUsing(fn (string $state) => TicketStatusEnum::getLabel($state))
                    ->color(fn (string $state): string => match ($state) {
                        TicketStatusEnum::PENDING => 'danger',
                        TicketStatusEnum::OPEN => 'warning',
                        TicketStatusEnum::RESOLVED => 'success',
                        TicketStatusEnum::CLOSED => 'gray',
                        default => 'secondary',
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(TicketStatusEnum::getTicketStatusOptions()),

                Tables\Filters\SelectFilter::make('priority')
                    ->label(__('Priority'))
                    ->options(TicketPriorityEnum::getTicketPriorityOptions()),
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->form([
                        Forms\Components\TextInput::make('title')
                            ->label(__('Title'))
                            ->disabled(),
                        Forms\Components\Textarea::make('message')
                            ->label(__('Message'))
                            ->disabled()
                            ->columnSpanFull(),
                        Forms\Components\Select::make('status')
                            ->label(__('Status'))
                            ->options(TicketStatusEnum::getTicketStatusOptions())
                            ->disabled(),
                        Forms\Components\Select::make('priority')
                            ->label(__('Priority'))
                            ->options(TicketPriorityEnum::getTicketPriorityOptions())
                            ->disabled(),
                    ]),
//                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
//                ]),
            ])
            ;
    }
}
