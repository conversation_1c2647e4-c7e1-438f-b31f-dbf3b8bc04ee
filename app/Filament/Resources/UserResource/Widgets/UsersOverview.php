<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Enums\RoleEnum;
use App\Models\User;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\TranslatedRole;

class UsersOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        $users = User::select('id', 'is_suspended', 'active', 'email_verified_at', 'created_at', 'updated_at')->get();

        // Total Users
        $totalUsersChart = $this->calculateDailyStats($users, 'created_at');
        $userCount = $users->count();
        $this->cards[] = Stat::make(__('Total Users'), $userCount)
            ->color('primary')
            ->chart(array_reverse($totalUsersChart));

        // Suspended Users
        $suspendedUsersData = $users->where('is_suspended', 1);
        $suspendedUsersChart = $this->calculateDailyStats($suspendedUsersData, 'updated_at');
        $suspendedUsers = $suspendedUsersData->count();
        $this->cards[] = Stat::make(__('Suspended Users'), $suspendedUsers)
            ->color('danger')
            ->chart(array_reverse($suspendedUsersChart));
    
        // Inactive Users
        $inactiveUsersData = $users->where('active', 0)->filter(function($user) {
            return is_null($user->email_verified_at);
        });
        $inactiveUsersChart = $this->calculateDailyStats($inactiveUsersData, 'updated_at');
        $inactiveUsers = $inactiveUsersData->count();
        $this->cards[] = Stat::make(__('Inactive Users'), $inactiveUsers)
            ->color('danger')
            ->chart(array_reverse($inactiveUsersChart));

        // Role distribution
        $roles = TranslatedRole::with(['users' => function($query) {
            $query->select('users.id', 'users.created_at', 'users.updated_at');
        }])->get();

        foreach ($roles as $role) {
            $roleUsersChart = $this->calculateDailyStats($role->users, 'updated_at');
            $count = $role->users->count();
            $this->cards[] = Stat::make($role->translated_name ?? $role->name, $count)
                ->color('warning')
                ->chart(array_reverse($roleUsersChart));
        }

        // Users without role
        $usersWithoutRoleData = User::doesntHave('roles')
            ->select('id', 'created_at', 'updated_at')
            ->get();
        $noRoleChart = $this->calculateDailyStats($usersWithoutRoleData, 'created_at');
        $usersWithoutRole = $usersWithoutRoleData->count();
        
        if ($usersWithoutRole > 0) {
            $this->cards[] = Stat::make('No Role', $usersWithoutRole)
                ->color('warning')
                ->chart(array_reverse($noRoleChart));
        }

        return $this->cards;
    }
}
