<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\User;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Spatie\Permission\Models\Role;

class UsersChartOverview extends ChartWidget
{
    protected function getData(): array
    {
        $roleDistribution = Role::withCount('users')
            ->get()
            ->mapWithKeys(function ($role) {
                return [$role->name => $role->users_count];
            });

        // Add users without any role
        $usersWithoutRole = User::doesntHave('roles')->count();
        if ($usersWithoutRole > 0) {
            $roleDistribution->put('No Role', $usersWithoutRole);
        }

        return [
            'datasets' => [
                [
                    'data' => $roleDistribution->values()->toArray(),
                    'backgroundColor' => [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40',
                        '#8AC926',
                    ],
                ],
            ],
            'labels' => $roleDistribution->keys()->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
