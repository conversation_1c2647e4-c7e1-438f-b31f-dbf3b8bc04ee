<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Events\PasswordUpdated;
use App\Filament\Resources\UserResource;
use App\Forms\ChangePasswordForm;
use App\Models\User;
use App\Rules\CurrentPasswordCheck;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Hash;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    public function getTitle(): string
    {
        return __('Edit User');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('changePassword')
                ->label('Change Password')
                ->icon('heroicon-o-key')
                ->modalHeading('Change Password')
                ->form([
                    TextInput::make('current_password')
                        ->label(__('Current Password'))
                        ->password()
                        ->revealable()
                        ->required()
                        ->rule(new CurrentPasswordCheck()),

                    TextInput::make('new_password')
                        ->label(__('New Password'))
                        ->password()
                        ->revealable()
                        ->default('')
                        ->required()
                        ->minLength(8)
                        ->confirmed()
                        ->live()
                        ->placeholder(__('Enter your new password'))
                        ->suffixAction(
                            \Filament\Forms\Components\Actions\Action::make('generatePassword')
                                ->icon('heroicon-o-sparkles')
                                ->tooltip(__('Generate Password'))
                                ->action(function ($set) {
                                    $password = ChangePasswordForm::generateStrongPassword();
                                    $set('new_password', $password);
                                    $set('new_password_confirmation', $password);
                                })
                        )
                        ->suffixIcon(fn ($state) =>
                        strlen($state) >= 8 &&
                        preg_match('/[A-Z]/', $state) &&
                        preg_match('/[a-z]/', $state) &&
                        preg_match('/\d/', $state) &&
                        preg_match('/[@$!%*?&]/', $state)
                            ? 'heroicon-o-check-circle'
                            : 'heroicon-o-x-circle'
                        )
                        ->suffixIconColor(fn ($state) =>
                        strlen($state) >= 8 &&
                        preg_match('/[A-Z]/', $state) &&
                        preg_match('/[a-z]/', $state) &&
                        preg_match('/\d/', $state) &&
                        preg_match('/[@$!%*?&]/', $state)
                            ? 'success'
                            : 'danger'
                        )
                        ->rules([
                            'required',
                            'min:8',
                            'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
                        ])
                        ->validationMessages([
                            'required' => __('Password is required'),
                            'min' => __('Password must be at least 8 characters'),
                            'confirmed' => __('Password confirmation does not match'),
                            'regex' => __('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
                        ]),

                    TextInput::make('new_password_confirmation')
                        ->label(__('Confirm New Password'))
                        ->password()
                        ->revealable()
                        ->required()
                        ->placeholder(__('Confirm your new password'))
                        ->live()
                        ->same('new_password')
                        ->suffixIcon(fn ($state, $get) =>
                        $state === $get('new_password') && !empty($state)
                            ? 'heroicon-o-check-circle'
                            : 'heroicon-o-x-circle'
                        )
                        ->suffixIconColor(fn ($state, $get) =>
                        $state === $get('new_password') && !empty($state)
                            ? 'success'
                            : 'danger'
                        )
                        ->validationMessages([
                            'required' => __('Password confirmation is required'),
                            'same' => __('Passwords do not match'),
                        ]),
                ])
                ->action(function (array $data) {
                    // Update the password
                    $user = $this->getRecord();
                    $user->password = Hash::make($data['new_password']);
                    $user->save();

                    // Revoke all tokens if using Laravel Sanctum
                    if (method_exists($user, 'tokens')) {
                        $user->tokens()->delete();
                    }

                    // Show success notification
                    Notification::make()
                        ->success()
                        ->title(__('Password changed successfully'))
                        ->body(__('Please log in again with your new password.'))
                        ->send();

                    //sync user passwords
                    event(new PasswordUpdated(User::class, $user->id, $data['new_password']));
                    // Revoke all tokens if using Laravel Sanctum
                    if (method_exists($user, 'tokens')) {
                        $user->tokens()->delete();
                    }
                    Auth::logout();
                    // Clear session and logout
                    session()->flush();
                    // Show success notification
                    Notification::make()
                        ->success()
                        ->title(__('Password changed successfully'))
                        ->body(__('Please log in again with your new password.'))
                        ->persistent()
                        ->send();
                    // Return response that will trigger a full page reload
                    return redirect()->route('filament.admin.auth.login');
                })
                ->visible(fn () => $this->getRecord()->id === auth()->id()),
        ];
    }
}
