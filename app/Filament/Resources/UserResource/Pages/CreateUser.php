<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Enums\SyncUserAccountSourceEnum;
use App\Events\SyncUserAccountEvent;
use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;
    protected $userPassword = null;
    protected $userCompanyPhone = null;
    protected $userCompanyEmail = null;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set email from user_company_email
        if (isset($data['user_company_email']) && !is_null($data['user_company_email'])) {
            $this->userCompanyEmail = $data['user_company_email'];
        }

        // Set phone_number in userProfile from user_company_phone
        if (isset($data['user_company_phone']) && $data['user_company_phone'] != '+966') {
            $this->userCompanyPhone = $data['user_company_phone'];
        }
        $this->userPassword = $data['password'] ?? null;
        $data['active'] = 1;
        $data['email_verified'] = 1;

        return $data;
    }

    public function getTitle(): string
    {
        return __('§');
    }



    protected function afterCreate(): void
    {
        //sync user account
        $user = $this->record;
        $user->unhashed_password = $this->userPassword;
        event(new SyncUserAccountEvent($user, SyncUserAccountSourceEnum::ADD_USER->value));
        unset($user->unhashed_password);
        if (!is_null($this->userCompanyPhone)) {
            $user->userProfile()->update(['phone_number' => $this->userCompanyPhone]);
        }
        if (!is_null($this->userCompanyEmail)) {
            $user->update(['email' => $this->userCompanyEmail]);
        }
    }
}
