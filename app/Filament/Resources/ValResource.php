<?php

namespace App\Filament\Resources;

use App\Enums\ValMorphTypeEnum;
use App\Filament\Resources\ValResource\Pages;
use App\Filament\Resources\ValResource\RelationManagers;
use App\Models\Val;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use Modules\Ad\app\Models\Ad;
use Modules\Company\app\Models\Company;
use Modules\Property\app\Models\Property;
use App\Forms\Components\HijriDatePicker;

class ValResource extends Resource
{
    protected static ?string $model = Val::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('morphable_type')
                    ->label(__('Model Type'))
                    ->options(ValMorphTypeEnum::options())
                    ->native(false)
                    ->live(),
                Forms\Components\Select::make('morphable_id')
                    ->label(__('Model Id'))
                    ->options(function (callable $get) {
                        $type = $get('morphable_type');
                        if (!$type) return [];
                        if ($type == Property::class) {
                            return $type::with('parent')
                                ->whereNotNull('parent_id')
                                ->get()
                                ->map(function($unit) {
                                    return [
                                        'id' => $unit->id,
                                        'name' => $unit->parent->name . ' - Unit #' . $unit->id
                                    ];
                                })
                                ->pluck('name', 'id');
                        }elseif($type == Company::class)
                        {
                            if(auth()->user()->company_id == null)
                            {
                                return $type::all()->pluck('name', 'id');
                            }
                            return $type::where('id' , auth()->user()->company_id)->pluck('name', 'id');
                        }
                        return $type::all()->pluck('title', 'id');
                    })
                    ->searchable(),
                Forms\Components\TextInput::make('value')
                    ->label(__('License number'))
                    ->required()
                    ->maxLength(255),
                HijriDatePicker::make('start_date')
                    ->label(__('Start Date'))
                    ->syncWith('vals_date')
                    ->showConvertedDate()
                    ->default(now()),
                HijriDatePicker::make('end_date')
                    ->label(__('End Date'))
                    ->after('start_date')
                    ->syncWith('vals_date')
                    ->showConvertedDate()
                    ->hideSwitcher()
                    ->afterOrEqual(today())
                    ->default(now()->addYear()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('morphable_type')
                    ->label(__('Model Type'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('morphable_id')
                    ->label(__('Model Id'))
                    ->formatStateUsing(function ($state) {
                        return $state;
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('value')
                    ->label(__('Value'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label(__('Start Date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->label(__('End Date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted at'))
                    ->since()
                    ->sortable()
                    ->tooltip(tooltip: fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageVals::route('/'),
        ];
    }


    public static function getNavigationGroup(): ?string
    {
        return __('Keraa App');
    }
    public static function getNavigationLabel(): string
    {
        return __("Vals");
    }
    public static function getBreadcrumb() : string
    {
        return __('Val');
    }
    public static function getModelLabel(): string
    {
        return __('Val');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Vals');
    }
}
