<?php

namespace App\Filament\Resources\ValResource\Pages;

use App\Filament\Resources\ValResource;
use App\Models\Val; // Import the Val model
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageVals extends ManageRecords
{
    protected static string $resource = ValResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label(__('Add Val'))
                ->after(function ($record) {
                    // Update all related "Vals" except the newly created record
                    Val::where('morphable_id', $record->morphable_id)
                        ->where('morphable_type', $record->morphable_type)
                        ->where('id', '!=', $record->id) // Exclude the newly created record
                        ->update(['active' => 0]);
                }),
        ];
    }
}
