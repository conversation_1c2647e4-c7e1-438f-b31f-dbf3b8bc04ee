<?php

namespace App\Filament\Resources;

use App\Enums\RoleEnum;
use App\Enums\SyncUserAccountSourceEnum;
use App\Events\PasswordUpdated;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers\CompanyRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\SubscriptionsRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\TicketsRelationManager;
use App\Filament\Resources\UserResource\Widgets\UsersChartOverview;
use App\Filament\Resources\UserResource\Widgets\UsersOverview;
use App\Forms\ChangePasswordForm;
use App\Forms\Components\CustomeImpersonate;
use App\Forms\Components\NationalIdSearchSection;
use App\Forms\Components\UserCompanyInfoSection;
use App\Helpers\FormHelper;
use App\Shared\Helpers\FormComponents;
use App\Models\User;
use App\Services\RoleService;
use App\Services\SuspensionService;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Forms\Get;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use GPBMetadata\Google\Api\Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\Forms\BankAccountForm;
use Modules\Company\app\Filament\Resources\CompanyResource\Forms\CompanyForm;
use Spatie\Permission\Models\Role;
use STS\FilamentImpersonate\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Filament Shield';
    public bool $isReadOnly = false;


    public function mount()
    {
        $this->isReadOnly = NationalIdSearchSection::isReadOnly();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                NationalIdSearchSection::make('Search User', SyncUserAccountSourceEnum::ADD_USER->value , true)
                    ->icon('heroicon-o-identification'),
                Forms\Components\Section::make(__('Personal Information'))
                    ->description(__('Enter user personal details'))
                    ->relationship('userProfile')
                    ->schema([
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('first_name')
                                    ->label(__('First Name'))
                                    ->placeholder(__('Enter first name'))
                                    ->required()
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly()),
                                Forms\Components\TextInput::make('second_name')
                                    ->label(__('Second Name'))
                                    ->required()
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->required()
                                    ->placeholder(__('Enter second name')),
                                Forms\Components\TextInput::make('third_name')
                                    ->label(__('Third Name'))
                                    ->required()
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->placeholder(__('Enter third name')),
                                Forms\Components\TextInput::make('last_name')
                                    ->label(__('Last Name'))
                                    ->required()
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->placeholder(__('Enter last name')),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('phone_number')
                                    ->label(__('Phone Number'))
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->tel()
                                    ->required()
                                    ->prefix('+966')
                                    ->placeholder('5xxxxxxxx')
                                    ->rules(function (callable $get, $record) {
                                        $recordId = $record?->id; // Retrieve the current record ID
                                        return [
                                            'required',
                                            'string',
                                            'regex:/^5[0-9]{8}$/',
                                            'size:9',
                                            function ($attribute, $value, $fail) use ($recordId) {
                                                $phoneNumber = '+966' . $value;
                                                $query = DB::table('user_profiles')
                                                    ->where('phone_number', $phoneNumber);

                                                if ($recordId) {
                                                    $query->where('id', '!=', $recordId);
                                                }

                                                $exists = $query->exists();

                                                if ($exists) {
                                                    $fail(__('This phone number is already taken.'));
                                                }
                                            },
                                        ];
                                    })
                                    ->extraInputAttributes([
                                        'maxlength' => '9',
                                        'pattern' => '5[0-9]*',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)',
                                    ])
                                    ->visible(fn ($livewire) => $livewire instanceof Pages\CreateUser || $livewire instanceof Pages\EditUser)
                                    ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                                    ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                                    ->validationMessages([
                                        'required' => __('Phone is required'),
                                        'regex' => __('Phone number must start with 5 and be 9 digits'),
                                        'size' => __('Phone number must be exactly 9 digits'),
                                        'unique' => __('This phone number is already taken'),
                                    ]),

                                TextInput::make('account_phone')
                                    ->label(__('Phone Number'))
                                    ->tel()
                                    ->required()
                                    ->prefix('+966')
                                    ->afterStateHydrated(function ($component, $record) {
                                        $phone = $record?->userCredential()
                                            ->withoutGlobalScopes()
                                            ->first()?->account?->phone;
                                        // Remove +966 prefix if it exists
                                        $phone = str_replace('+966', '', $phone);
                                        $component->state($phone);
                                    })
                                    ->disabled()
                                    ->visible(fn ($livewire) => $livewire instanceof Pages\ViewUser)
                                    ->dehydrated(false),

                                Forms\Components\TextInput::make('account_email')
                                    ->label(__('Email'))
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->placeholder(__('Personal email address'))
                                    ->email()
                                    ->visible(fn ($livewire) => $livewire instanceof Pages\ViewUser)
                                    ->afterStateHydrated(function ($component, $record) {
                                        $email = $record?->userCredential()
                                            ->withoutGlobalScopes()
                                            ->first()?->account?->email;
                                        $component->state($email);
                                    }),

                                FormComponents::idFieldSet(
                                    fieldPrefix: '',
                                    table: 'user_profiles',
                                    ignoreRecord: true,
                                    readonlyCondition: fn(): bool => NationalIdSearchSection::isReadOnly()
                                ),

                                FormComponents::birthDateInput('birth')
                                ->label(__('Birth Date'))
                                    ->native(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->helperText(__('Must be at least 18 years old')),



                            ]),

                        Forms\Components\FileUpload::make('avatar_url')
                            ->label(__('Avatar'))
                            ->placeholder(__('Upload avatar'))
                            ->image()
                            ->directory('avatars')
                            ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set, $livewire) {
                            $nationalId = $livewire->data['search_national_id'];
                            if (!is_null($nationalId)) {
                                $set('is_data_fetched', true);
                                NationalIdSearchSection::makeReadOnly();
                            }
                        })
                            ->columnSpan('full'),
                    ]),

                // user company info section
                Section::make(__("User's Company Information"))
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('userProfile.phone_number')
                                    ->label(__('User Company Phone'))
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->tel()
                                    ->required()
                                    ->prefix('+966')
                                    ->placeholder('5xxxxxxxx')
                                    ->rules([
                                        'required',
                                        'string',
                                        'regex:/^5[0-9]{8}$/',
                                        'size:9'
                                    ])
                                    ->extraInputAttributes([
                                        'maxlength' => '9',
                                        'pattern' => '5[0-9]*',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                    ])
                                    ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                                    ->formatStateUsing(fn ($state) => str_replace('+966', '', $state)),

                                Forms\Components\TextInput::make('email')
                                    ->label(__('User Company Email'))
                                    ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                                    ->placeholder(__('Enter email address'))
                                    ->email()
                                    ->required()
                                    ->unique(table: 'users',column: 'email',ignoreRecord:true)
                                    ->maxLength(255)
                                    ->validationMessages([
                                        'required' => __('Email is required'),
                                        'email' => __('Please enter a valid email address'),
                                        'unique' => __('This email is already taken'),
                                    ]),
                            ])
                    ])
                    ->visible(fn ($livewire) => $livewire instanceof Pages\ViewUser),


                Forms\Components\Section::make(__('Account Information'))
                    ->schema([
                        Forms\Components\Hidden::make('name')
                            ->dehydrateStateUsing(function ($state, $get) {
                                $firstName = $get('userProfile.first_name') ?? '';
                                $secondName = $get('userProfile.second_name') ?? '';
                                $thirdName = $get('userProfile.third_name') ?? '';
                                $lastName = $get('userProfile.last_name') ?? '';

                                return trim("{$firstName} {$secondName} {$thirdName} {$lastName}");
                            }),

                        Forms\Components\TextInput::make('email')
                            ->label(__('Email Address'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->placeholder(__('Enter email address'))
                            ->email()
                            ->required()
                            ->visible(fn ($livewire) => $livewire instanceof Pages\CreateUser || $livewire instanceof Pages\EditUser)
                            ->unique(table: 'users',column: 'email',ignoreRecord:true)
                            ->maxLength(255)
                            ->validationMessages([
                                'required' => __('Email is required'),
                                'email' => __('Please enter a valid email address'),
                                'unique' => __('This email is already taken'),
                            ]),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label(__('Email Verified At'))
                            ->placeholder(__('Select verification date'))
                            ->default(now())
                            ->columnSpan('full'),
                        ...ChangePasswordForm::make($form->getOperation(), true),
                    ])
                    ->columns(2),

            Forms\Components\Select::make('roles')
                ->label(__('Roles'))
                ->placeholder(__('Select roles'))
                ->multiple()
                ->relationship('roles', 'translated_name', function ($query) {
                    $user = auth()->user();

                    if ($user->hasRole(RoleEnum::ADMIN->value)) {
                        // Do not apply any restrictions
                        return;
                    }

                    if ($user->hasRole(RoleEnum::OWNER->value)) {
                        // Exclude super_admin and owner roles
                        $query->whereNotIn('name', [RoleEnum::ADMIN->value, RoleEnum::OWNER->value]);
                    } else {
                        $query->whereNotIn('name', [RoleEnum::ADMIN->value, RoleEnum::OWNER->value]);
                        // Default behavior for other users
                        // app(RoleService::class)->applyUserRoleRestrictions($query)
                        //     ->whereNot('name', RoleEnum::OWNER->value);
                    }
                })
                ->live()
                ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                    if (!is_null($get('search_national_id'))) {
                        $set('is_data_fetched', true);
                        NationalIdSearchSection::makeReadOnly();
                    }

                    if (auth()->check() && auth()->user()->hasRole(RoleEnum::ADMIN->value)) {
                        if (empty($state)) {
                            $set('show_company_select', false);
                            return;
                        }

                        $roles = Role::whereIn('id', $state)->get(['id', 'companies']);
                        $hasCompanyAccess = $roles->contains('companies', true);
                        $set('show_company_select', $hasCompanyAccess);
                    }
                })
                ->preload()
                ->columnSpan('full'),

                UserCompanyInfoSection::make(__("User's Company Information"))
                    ->visible(fn ($livewire) => $livewire instanceof Pages\CreateUser && auth()->user()->hasRole(RoleEnum::OWNER)),

                Forms\Components\Section::make(__("User's Company Information"))
                    ->description(__('Please enter user company information here.'))
                    ->schema([
                        Forms\Components\Select::make('company_id')
                            ->label(__('Company'))
                            ->placeholder(__('Select Company'))
                            ->relationship('company', 'name')
                            ->required()
                            ->preload(),
                        UserCompanyInfoSection::userInfoFields()
                            ->visible(fn ($livewire) => $livewire instanceof Pages\CreateUser ),
                    ])
                    ->hidden(fn (Forms\Get $get): bool => !$get('show_company_select')),

                Forms\Components\Section::make(__('Bank Accounts'))
                    ->description(__('Manage user bank accounts'))
                    ->schema([
                        Forms\Components\Repeater::make('bankAccounts')
                            ->label(__('Bank Accounts'))
                            ->relationship('bankAccounts')
                            ->schema(BankAccountForm::make())
                            ->columnSpan('full'),
                    ])
                    ->visible(fn () => $form->getOperation() === 'edit'),
            ]);
    }
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.translated_name')
                    ->label(__('Roles'))
                    ->badge()
                    ->color("primary")
                    ->formatStateUsing(fn (string $state): string => RoleEnum::tryFrom($state)?->label() ?? $state)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('is_suspended')
                    ->label(__('Suspension Status'))
                    ->badge()
                    ->formatStateUsing(fn (string $state): string =>
                        $state == '1' ? __('Suspended') : __('Allowed')
                    )
                    ->color(fn (string $state): string =>
                        $state == '1' ? 'danger' : 'success'
                    )
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->since(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->since(),
            ])
            ->filters([
                SelectFilter::make('roles')
                    ->label(__('Roles'))
                    ->relationship('roles', 'name')
                    ->getOptionLabelFromRecordUsing(fn ($record) => RoleEnum::tryFrom($record->name)?->label() ?? $record->name),
                TernaryFilter::make('email_verified')
                    ->label(__('Email Verified'))
                    ->placeholder(__('All Users'))
                    ->trueLabel(__('Verified Users'))
                    ->falseLabel(__('Unverified Users'))
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('email_verified_at'),
                        false: fn (Builder $query) => $query->whereNull('email_verified_at'),
                    ),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')->label(__('Created From')),
                        Forms\Components\DatePicker::make('created_until')->label(__('Created Until'))->default(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\Action::make('sendNewPassword')
                        ->label(__('Send New Password'))
                        ->icon('heroicon-o-key')
                        ->modalHeading(__('Send New Password'))
                        ->requiresConfirmation()
                        ->modalButton(__('Send'))
                        ->action(function (User $record): void {
                            // Store current locale to save current lang
                            $currentLocale = app()->getLocale();

                            // Generate random password
                            $lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
                            $upperCaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                            $numbers = '0123456789';
                            $specialChars = '!@#$%^&*()._-';
                            $newPassword = substr(str_shuffle($lowerCaseLetters), 0, 4) .
                                substr(str_shuffle($upperCaseLetters), 0, 2) .
                                $specialChars[rand(0, strlen($specialChars) - 1)] .
                                substr(str_shuffle($numbers), 0, 5);
                            $newPassword = str_shuffle($newPassword);
                            $record->update([
                                'password' => Hash::make($newPassword),
                            ]);
                            //sync user passwords
                            event(new PasswordUpdated(User::class, $record->id, $newPassword));
                            $template = NotificationsTemplate::where(['key' => 'send_user_credential'])->first();
                            if ($template) {
                                SendNotification::make(['email'])
                                    ->template($template->key)
                                    ->model(User::class)
                                    ->id($record->id)
                                    ->findBody(['{username}', '{email}', '{password}'])
                                    ->replaceBody([$record->name, $record->userProfile->national_id, $newPassword])
                                    ->icon($template->icon)
                                    ->url(url($template->url))
                                    ->lang($record->lang)
                                    ->privacy('private')
                                    ->database(true)
                                    ->fire();
                            }

                            // Restore original locale for lang before fire action
                            app()->setLocale($currentLocale);
                        }),

                    Tables\Actions\Action::make('toggle_suspend')
                        ->label(fn (User $record): string => $record->is_suspended ? __('Unsuspend') : __('Suspend'))
                        ->icon(fn (User $record): string => $record->is_suspended ? 'heroicon-o-check-circle' : 'heroicon-o-lock-closed')
                        ->modalHeading(fn (User $record): string => $record->is_suspended ? __('Be aware that this user can login and do any action on the dashboard after the unsuspension') : __('Be aware that this user cannot login yet or do any action on the dashboard after the suspension'))
                        ->requiresConfirmation()
                        ->modalButton(fn (User $record): string => $record->is_suspended ? __('Unsuspend') : __('Suspend'))
                        ->form([
                            Forms\Components\TextInput::make('reason')
                                ->label(__('Reason'))
                                ->placeholder(__('Enter a reason'))
                                ->maxLength(255),
                        ])
                        ->action(function (User $record, array $data) {
                            // Use the reason input from the modal form
                            $reason = $data['reason']; // Default if no reason is provided

                            // Determine whether the action is to suspend or unsuspend
                            $actionTaken = $record->is_suspended ? 'Unsuspended' : 'Suspended';

                            // Call the SuspensionService to handle suspension status, session termination, and logging
                            $suspensionService = new SuspensionService();
                            $suspensionService->create($record, auth()->user(), $reason, $actionTaken);
                        })
                        ->after(function (User $record): void {
                            $message = $record->is_suspended
                                ? __('The user has been successfully suspended, and their session has been terminated.')
                                : __('The user has been successfully unsuspended.');

                            Notification::make()
                                ->title(__('Action Completed'))
                                ->body($message)
                                ->success()
                                ->send();
                        }),
                    CustomeImpersonate::make('impersonate'),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('verifyEmail')
                    ->action(function (Collection $records) {
                        $records->each(function ($record) {
                            $record->email_verified_at = now();
                            $record->save();
                        });
                    })
                    ->deselectRecordsAfterCompletion()
                    ->requiresConfirmation()
                    ->icon('heroicon-o-check')
                    ->label('Verify Email'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            TicketsRelationManager::class,
            CompanyRelationManager::class,
            SubscriptionsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'view' => Pages\ViewUser::route('/{record}'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'email'];
    }
    public static function getWidgets(): array
    {
        return [
            UsersOverview::class,
        ];
    }


    public static function getNavigationLabel(): string
    {
        return __("users"); // TODO: Change the autogenerated stub
    }
    public static function getNavigationGroup(): ?string
    {
        return __('filament-shield::filament-shield.nav.group');
    }

    public static function getBreadcrumb() : string
    {
        return __('users');
    }
    public static function getModelLabel(): string
    {
        return __('User');
    }

    public static function getPluralModelLabel(): string
    {
        return __('users');
    }
}
