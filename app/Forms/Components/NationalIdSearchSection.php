<?php
namespace App\Forms\Components;

use App\Helpers\AccountHelper;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Modules\Account\app\Models\Account;

class NationalIdSearchSection extends Component
{
    static string $source;
    static bool $isReadOnly = false;
    static bool $isDataFetched = false;

    public static function make(string $title, string $source = '', bool $isReadOnly = false): Section
    {
        static::$source = $source;
        static::$isReadOnly = false; // Always start with false
        static::$isDataFetched = false;

        return Section::make(__($title))
            ->description(__('Search by national ID and get user data'))
            ->visible(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord)
            ->schema([
                Hidden::make('is_data_fetched')
                    ->default(false)
                    ->live(),

                Grid::make(1)
                    ->schema([
                        Select::make('search_national_id')
                            ->label(__('Search by National ID'))
                            ->searchable()
                            ->getSearchResultsUsing(function ( string $search, Get $get) {
                                if (strlen($search) != 10) {
                                    return [];
                                }
                                $account_data = AccountHelper::getSearchAccountResultsByNationalId($search);
                                return $account_data;
                            })
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                if (!$state) {
                                    $set('is_data_fetched', false);
                                    static::$isDataFetched = false;
                                    static::$isReadOnly = false;
                                    return;
                                }

                                $account_data = Account::where(['national_id' => $state])->first();
                                if (!$account_data) {
                                    $set('is_data_fetched', false);
                                    static::$isDataFetched = false;
                                    static::$isReadOnly = false;
                                    return;
                                }

                                $set('is_data_fetched', true);
                                static::$isDataFetched = true;
                                static::$isReadOnly = true;
                                AccountHelper::fillFormFields($set, $account_data, static::$source);
                            })
                            ->placeholder(__('Search by National ID or Name')),
                    ]),
            ]);
    }

    public static function isReadOnly(): bool
    {
        return static::$isReadOnly;
    }

    public static function makeReadOnly(): void
    {
        static::$isReadOnly = true;
    }

    public static function isDataFetched(): bool
    {
        return static::$isDataFetched;
    }
}
