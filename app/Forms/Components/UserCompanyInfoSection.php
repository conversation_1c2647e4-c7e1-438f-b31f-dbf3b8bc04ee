<?php
namespace App\Forms\Components;

use Filament\Forms\Components\Component;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;

class UserCompanyInfoSection extends Component
{
    static string $source;
    public static function make(string $title, string $source = ''): Section
    {
        static::$source = $source;
        return Section::make(__($title))
            ->description(__('Please enter user company information here.'))
            ->visible(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord)
            ->schema(static::sectionSchema());
    }

    public static function sectionSchema(): array
    {
        return [
            static::userInfoFields(),
        ];
    }
    public static function userInfoFields()
    {
        return Grid::make(2)->schema([
                    TextInput::make('user_company_email')
                        ->label(__('User Company Email'))
                        ->placeholder(__('Enter email address'))
                        ->email()
                        ->unique(table: 'users',column: 'email',ignoreRecord:true)
                        ->maxLength(255)
                        ->validationMessages([
                            'email' => __('Please enter a valid email address'),
                            'unique' => __('This email is already taken'),
                        ]),

                    TextInput::make('user_company_phone')
                        ->label(__('User Company Phone'))
                        ->tel()
                        ->prefix('+966')
                        ->placeholder('5xxxxxxxx')
                        ->rules([
                            'string',
                            'regex:/^5[0-9]{8}$/',
                            'size:9'
                        ])
                        ->extraInputAttributes([
                            'maxlength' => '9',
                            'pattern' => '5[0-9]*',
                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                        ])
                        ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                        ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                        ->validationMessages([
                            'regex' => __('Phone number must start with 5 and be 9 digits'),
                            'size' => __('Phone number must be exactly 9 digits'),
                            'unique' => __('This phone number is already taken'),
                        ]),
                ]);
    }

}
