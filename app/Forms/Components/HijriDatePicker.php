<?php

namespace App\Forms\Components;

use Closure;

use Carbon\CarbonInterface;
use Illuminate\Support\Facades\App;
use Filament\Forms\Components\DatePicker;

class HijriDatePicker extends DatePicker
{
  protected string $view = 'forms.components.hijri-date-picker';
  
  protected bool $showSwitcher = true;
  protected ?string $syncGroup = null;
  protected bool $isHijri = false; 
  protected bool $showConvertedDate = false;

  protected function setUp(): void
  {
    parent::setUp();

    $this->afterStateHydrated(function (HijriDatePicker $component, $state) {
      if (blank($state)) {
        return;
      }

      // Format the date if needed
      // $component->state(Carbon::parse($state)->format('Y-m-d'));
    });
  }

  public function getMinDate(): ?string
  {
    $minDate = parent::getMinDate();
    
    if (App::has('livewire') && $this->getLivewire()) {
      
      $this->getLivewire()->dispatch('updateHijriDateConstraints', [
        'field' => $this->getStatePath(),
        'minDate' => $minDate,
      ]);
    }
    
    return $minDate;
  }
  
  public function getMaxDate(): ?string
  {
    $maxDate = parent::getMaxDate();
    
    // Only dispatch once per instance to avoid infinite loops
    if (App::has('livewire') && $this->getLivewire()) {
      
      // Use deferred dispatch to ensure it happens after the component is fully rendered
      $this->getLivewire()->dispatch('updateHijriDateConstraints', [
        'field' => $this->getStatePath(),
        'maxDate' => $maxDate,
      ]);
    }
    
    return $maxDate;
  }

  public function hideSwitcher(bool $hide = true): static
  {
    $this->showSwitcher = !$hide;
    return $this;
  }
  
  public function getShowSwitcher(): bool
  {
    return $this->showSwitcher;
  }
  
  public function syncWith(string $group): static
  {
    $this->syncGroup = $group;
    return $this;
  }
  
  public function getSyncGroup(): ?string
  {
    return $this->syncGroup;
  }
  
  public function isHijri(bool $isHijri = true): static
  {
    $this->isHijri = $isHijri;
    return $this;
  }
  
  public function getIsHijri(): bool
  {
    return $this->isHijri;
  }

  public function showConvertedDate( bool $showConvertedDate = true): static
  {
    $this->showConvertedDate = $showConvertedDate;
    return $this;
  }

  public function getShowConvertedDate(): bool
  {
    return $this->showConvertedDate;
  }

}