<?php
namespace App\Forms\Components;
use Closure;
use Filament\Forms\Components\Field;

class ViewImage extends Field
{
    protected string $view = 'forms.components.view-image';

    protected string|Closure|null $imageUrl = null;

    public function imageUrl(string|Closure|null $url): static
    {
        $this->imageUrl = $url;

        return $this;
    }

    public function getImageUrl(): string|null
    {
        return $this->evaluate($this->imageUrl);
    }
}
