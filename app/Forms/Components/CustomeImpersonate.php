<?php

namespace App\Forms\Components;

use STS\FilamentImpersonate\Tables\Actions\Impersonate;

use Filament\Tables\Actions\Action;
use STS\FilamentImpersonate\Concerns\Impersonates;

class CustomeImpersonate extends Impersonate
{
    use Impersonates;
    protected function setUp(): void
    {
        $this
            ->label(__('simulation'))
            ->icon('impersonate-icon')
            ->action(fn ($record) => $this->impersonate($record))
            ->hidden(fn ($record) => !$this->canBeImpersonated($record));

    }
}
