<?php

namespace App\Forms;

use App\Forms\Components\NationalIdSearchSection;
use App\Helpers\FormHelper;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\HtmlString;

class ChangePasswordForm
{
    public static function make($context = null, bool $withGenerator = false)
    {
        if ($context === 'edit' || $context === 'view') {
            return [];
        }

        $passwordInput = TextInput::make('password')
            ->label(__('Password'))
            ->password()
            ->revealable()
            ->default('')
            ->required()
            ->minLength(8)
            ->confirmed()
            ->live()
            ->placeholder(__('Enter your password'));

        if ($withGenerator) {
            $passwordInput->suffixAction(
                \Filament\Forms\Components\Actions\Action::make('generatePassword')
                    ->icon('heroicon-o-sparkles')
                    ->tooltip(__('Generate Password'))
                    ->action(function ($set) {
                        $password = static::generateStrongPassword();
                        $set('password', $password);
                        $set('password_confirmation', $password);
                    })
            );
        }

        // Wrap the password fields in a Section
        return [
            Section::make(__('Password'))
            ->schema([

                $passwordInput
                ->suffixIcon(fn ($state) =>
                strlen($state) >= 8 &&
                preg_match('/[A-Z]/', $state) &&
                preg_match('/[a-z]/', $state) &&
                preg_match('/\d/', $state) &&
                preg_match('/[@$!%*?&]/', $state)
                    ? 'heroicon-o-check-circle'
                    : 'heroicon-o-x-circle'
                )
                ->suffixIconColor(fn ($state) =>
                strlen($state) >= 8 &&
                preg_match('/[A-Z]/', $state) &&
                preg_match('/[a-z]/', $state) &&
                preg_match('/\d/', $state) &&
                preg_match('/[@$!%*?&]/', $state)
                    ? 'success'
                    : 'danger'
                )
                ->rules([
                    'required',
                    'min:8',
                    'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
                ])
                ->validationMessages([
                    'required' => __('Password is required'),
                    'min' => __('Password must be at least 8 characters'),
                    'confirmed' => __('Password confirmation does not match'),
                    'regex' => __('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
                ])
                ->helperText(function ($state) {
                    if (empty($state)) {
                        return FormHelper::getHtmlHintMessagesForPasswordRequirements();
                    }
                    $requirements = FormHelper::getValidationRequirementsForPasswordState($state);
                    $message = __('Password requirements:') . '<br>';
                    foreach ($requirements as $requirement) {
                        $message .= $requirement[2] ? $requirement[1] : $requirement[0];
                        $message .= '<br>';
                    }

                    return new HtmlString($message);
                }),

            TextInput::make('password_confirmation')
                ->label(__('Password Confirmation'))
                ->password()
                ->revealable()
                ->required()
                ->placeholder(__('Confirm your password'))
                ->live()
                ->same('password')
                ->suffixIcon(fn ($state, $get) =>
                $state === $get('password') && !empty($state)
                    ? 'heroicon-o-check-circle'
                    : 'heroicon-o-x-circle'
                )
                ->suffixIconColor(fn ($state, $get) =>
                $state === $get('password') && !empty($state)
                    ? 'success'
                    : 'danger'
                )
                ->helperText(fn ($state, $get) =>
                new HtmlString(
                    $state !== $get('password')
                        ? '✗ ' . __('Passwords do not match')
                        : ($state
                        ? '✓ ' . __('Passwords match')
                        : ''
                    )
                )
                )
                ->validationMessages([
                    'required' => __('Password confirmation is required'),
                    'same' => __('Passwords do not match'),
                ])
            ])
                ->hidden(fn(Forms\get $get): bool => !is_null($get('search_national_id')))
                //->hidden(fn(): bool => NationalIdSearchSection::isDataFetched())
];
    }

    public static function generateStrongPassword(int $length = 12): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $special = '@$!%*?&';

        $password = '';

        // Ensure at least one character from each required set
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $special[random_int(0, strlen($special) - 1)];

        // Fill the rest of the password
        $all = $uppercase . $lowercase . $numbers . $special;
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $all[random_int(0, strlen($all) - 1)];
        }

        // Shuffle the password to make it more random
        return str_shuffle($password);
    }
}
