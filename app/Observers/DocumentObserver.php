<?php

namespace App\Observers;

use App\Enums\DocumentTypeEnum;
use App\Models\Document;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class DocumentObserver
{
    /**
     * Handle the Document "created" event.
     */
    public function created(Document $document): void
    {
    }

    /**
     * Handle the Document "updated" event.
     */
    public function updated(Document $document): void
    {
        if ($document->isDirty(['metadata'])) {
            // Load the document type relationship if not already loaded
            if (!$document->relationLoaded('documentType')) {
                $document->load('documentType');
            }

            // Check if the document is related to a Property and has the 'ownership' document type
            if ($document->morphable_type === Property::class && $document->documentType && $document->documentType->key === DocumentTypeEnum::Ownership->value)
            {
                $oldMetadata = $document->getOriginal('metadata');
                $newMetadata = $document->metadata;
                if (is_array($oldMetadata) && is_array($newMetadata)) {
                    ksort($oldMetadata);
                    ksort($newMetadata);

                    if ($oldMetadata !== $newMetadata) {
                        $property = $document->morphable;
                        if ($property->ejar_sync_status == EjarSyncStatus::SYNCED || $property->ejar_sync_status == EjarSyncStatus::NEED_SYNC) {
                            // Update the property sync step status to NEED_SYNC
                            $property->syncSteps()
                                ->where('step', PropertySyncStep::OWNERSHIP_DOCS->value)
                                ->update(['status' => PropertySyncStatus::NEED_SYNC->value]);

                            // Update the property's ejar_sync_status to NEED_SYNC
                            $property->update(['ejar_sync_status' => EjarSyncStatus::NEED_SYNC->value]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Handle the Document "deleted" event.
     */
    public function deleted(Document $document): void
    {
        //
    }

    /**
     * Handle the Document "restored" event.
     */
    public function restored(Document $document): void
    {
        //
    }

    /**
     * Handle the Document "force deleted" event.
     */
    public function forceDeleted(Document $document): void
    {
        //
    }

}
