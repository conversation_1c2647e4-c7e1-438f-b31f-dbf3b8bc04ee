<x-filament-panels::page>
    {{-- Welcome Widget --}}
    <div class="mb-0">
        @livewire(\App\Filament\Widgets\WelcomeWidget::class)
    </div>

    <div class="grid grid-cols-12 gap-4 lg:gap-8 custom_widget_page">
        {{-- Left Column Content --}}
        <div class="col-span-12 lg:col-span-12 grid ">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                @can('widget_CompanyCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\CompanyCount::class)
                @endcan
                @can('widget_ActiveLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveLeaseCount::class)
                @endcan
                @can('widget_InactiveLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveLeaseCount::class)
                @endcan

                @can('widget_EjarRegisteredLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\EjarRegisteredLeaseCount::class)
                @endcan
                @can('widget_ActiveMaintenanceRequestsCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveMaintenanceRequestsCount::class)
                @endcan
                @can('widget_InactiveMaintenanceRequestsCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveMaintenanceRequestsCount::class)
                @endcan

                @can('widget_CommissionInvoicesTotal')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\CommissionInvoicesTotal::class)
                @endcan
                @can('widget_TotalPaidPayments')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalPaidPayments::class)
                @endcan
                @can('widget_TotalUnpaidPayments')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalUnpaidPayments::class)
                @endcan

                @can('widget_InactiveSubscription')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveSubscription::class)
                @endcan
                @can('widget_ActiveSubscription')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveSubscription::class)
                @endcan
                @can('widget_TotalSubscriptionIncome')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionIncome::class)
                @endcan

                @can('widget_TotalSubscriptionUpcome')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionUpcome::class)
                @endcan
                @can('widget_ProperitiesManagedCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ProperitiesManagedCount::class)
                @endcan

                @can('widget_TotalRentCurrentMonth')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalRentCurrentMonth::class)
                @endcan
            </div>
        </div>

        {{-- Charts Section --}}
        <div class="col-span-12 lg:col-span-12 grid">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {{-- Lease Analytics Chart --}}
                <div class="col-span-1">
                    @livewire(\App\Filament\Widgets\LeaseAnalyticsWidget::class)
                </div>

                {{-- Invoice Analytics Chart --}}
                <div class="col-span-1">
                    @livewire(\App\Filament\Widgets\InvoiceAnalyticsWidget::class)
                </div>

                {{-- Property Analytics Chart --}}
                <div class="col-span-1">
                    @livewire(\App\Filament\Widgets\PropertyAnalyticsWidget::class)
                </div>
            </div>
        </div>

        {{-- Table Widgets Section --}}
        <div class="col-span-12 mt-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {{-- Latest Leases Widget --}}
                <div class="col-span-1">
                    @livewire(\App\Filament\Widgets\LatestLeasesWidget::class)
                </div>

                {{-- Latest Invoices Widget --}}
                <div class="col-span-1">
                    @livewire(\App\Filament\Widgets\LatestInvoicesWidget::class)
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
