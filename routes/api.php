<?php

use App\Http\Controllers\NafathAPIController;
use App\Models\NafathLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Modules\Payment\app\Http\Controllers\PaymentController;
use Modules\Request\app\Http\Controllers\Api\RequestController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::get('/contract-status', [\Modules\General\app\Http\Controllers\Api\EjarController::class, 'contractStatus']);
Route::post('/create-ejar-contract', [\Modules\General\app\Http\Controllers\Api\EjarController::class, 'createEjarContract']);


Route::middleware(['api'])->prefix('payment')->group(static function () {
    Route::post('callback', [PaymentController::class, 'verify'])->name('payment.callback');
    Route::get('verify', [PaymentController::class, 'verify'])->name('payment.verify');
});

Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('changeStatus/{id}/{status}', [RequestController::class, 'changeStatus']);
    Route::get('request-details/{id}', [RequestController::class, 'requestDetails']);
    Route::get('settled-payments/{id}', [RequestController::class, 'getSettledPayments']);
});

Route::post('/NafathCallBack', [NafathAPIController::class , 'callback'])->name('nafath.callback');