<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Widgets;

use Modules\Account\app\Models\Account;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class AccountsOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        $accounts = Account::select('id', 'is_active', 'otp_activated_at', 'created_at', 'updated_at')->get();

        // Total Accounts
        $totalAccountsChart = $this->calculateDailyStats($accounts, 'created_at');
        $accountCount = $accounts->count();
        $this->cards[] = Stat::make(__('Total Accounts'), $accountCount)
            ->color('primary')
            ->chart(array_reverse($totalAccountsChart));

        // Active Accounts
        $activeAccountsData = $accounts->where('is_active', 1);
        $activeAccountsChart = $this->calculateDailyStats($activeAccountsData, 'updated_at');
        $activeAccounts = $activeAccountsData->count();
        $this->cards[] = Stat::make(__('Active Accounts'), $activeAccounts)
            ->color('primary')
            ->chart(array_reverse($activeAccountsChart));
    
        // Inactive Accounts
        $inactiveAccountsData = $accounts->where('is_active', 0);
        $inactiveAccountsChart = $this->calculateDailyStats($inactiveAccountsData, 'updated_at');
        $inactiveAccounts = $inactiveAccountsData->count();
        $this->cards[] = Stat::make(__('Inactive Accounts'), $inactiveAccounts)
            ->color('danger')
            ->chart(array_reverse($inactiveAccountsChart));
            
        return $this->cards;
    }
}