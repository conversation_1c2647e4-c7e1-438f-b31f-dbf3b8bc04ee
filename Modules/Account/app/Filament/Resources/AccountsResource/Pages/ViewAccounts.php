<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions;

class ViewAccounts extends ViewRecord
{
    protected static string $resource = AccountsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->delete();
                    $this->redirect(AccountsResource::getUrl('index'));
                }),
        ];
    }

    public function getTitle(): string
    {
        return __('View Account :') . " {$this->record->first_name} {$this->record->last_name}";
    }

    protected function getContentGrid(): array
    {
        return [
            'md' => 2,
            'lg' => 3,
        ];
    }

    public function hasFullWidthContent(): bool
    {
        return true;
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    protected function getFooterWidgets(): array
    {
        return [];
    }

    public function form(\Filament\Forms\Form $form): \Filament\Forms\Form
    {
        return $form
            ->schema([
                \Filament\Forms\Components\Section::make(__('Personal Information'))
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('first_name')
                            ->label(__('First Name'))
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('second_name')
                            ->label(__('Second Name'))
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('third_name')
                            ->label(__('Third Name'))
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('last_name')
                            ->label(__('Last Name'))
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('id_type')
                            ->label(__('ID Type'))
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'national_id' => __('National ID'),
                                'residency_permit' => __('Residency Permit'),
                                'passport' => __('Passport'),
                                'gcc_id' => __('GCC Citizen ID'),
                                'other' => __('Other'),
                                default => $state,
                            })
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('national_id')
                            ->label(fn ($record): string => match ($record->id_type) {
                                'national_id' => __('National ID'),
                                'residency_permit' => __('Residency Permit Number'),
                                'passport' => __('Passport Number'),
                                'gcc_id' => __('GCC ID Number'),
                                'other' => __('ID Number'),
                                default => __('ID Number'),
                            })
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('country_of_issue')
                            ->label(__('Country Of Issue'))
                            ->disabled()
                            ->visible(fn ($record): bool => $record->id_type !== 'national_id'),
                        \Filament\Forms\Components\DatePicker::make('birth_date')
                            ->label(__('Birth Date'))
                            ->disabled(),
                    ])
                    ->columns(2),

                \Filament\Forms\Components\Section::make(__('Contact Information'))
                    ->schema([
                        \Filament\Forms\Components\TextInput::make('email')
                            ->label(__('Email'))
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('phone')
                            ->label(__('Phone'))
                            ->disabled(),
                    ])
                    ->columns(2),

                \Filament\Forms\Components\Section::make(__('Account Settings'))
                    ->schema([
                        \Filament\Forms\Components\Select::make('lang')
                            ->label(__('Preferred Language'))
                            ->options([
                                'en' => __('English'),
                                'ar' => __('Arabic'),
                            ])
                            ->disabled(),
                        \Filament\Forms\Components\Toggle::make('is_active')
                            ->label(__('Active'))
                            ->disabled(),
                    ])
                    ->columns(2),

                \Filament\Forms\Components\Section::make(__('Timestamps'))
                    ->schema([
                        \Filament\Forms\Components\DateTimePicker::make('created_at')
                            ->label(__('Created'))
                            ->disabled(),
                        \Filament\Forms\Components\DateTimePicker::make('updated_at')
                            ->label(__('Updated'))
                            ->disabled(),
                        \Filament\Forms\Components\DateTimePicker::make('deleted_at')
                            ->label(__('Deleted'))
                            ->disabled()
                            ->visible(fn ($record) => $record->trashed()),
                    ])
                    ->columns(2),
            ]);
    }
}
