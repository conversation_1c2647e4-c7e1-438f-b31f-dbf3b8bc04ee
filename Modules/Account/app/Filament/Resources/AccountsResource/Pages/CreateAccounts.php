<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Pages;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Account\app\Models\Account;

class CreateAccounts extends CreateRecord
{
    protected static string $resource = AccountsResource::class;
    protected $userPassword = null;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $this->userPassword = $data['password'];

        return $data;
    }



    protected function afterCreate(): void
    {
        $template = NotificationsTemplate::where(['key' => 'send_user_credential'])->first();
        if ($template) {
            SendNotification::make(['email'])
                ->template($template->key)
                ->model(Account::class)
                ->id($this->record->id)
                ->findBody(['{username}', '{email}', '{password}'])
                ->replaceBody([$this->record->name, $this->record->national_id, $this->userPassword])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
