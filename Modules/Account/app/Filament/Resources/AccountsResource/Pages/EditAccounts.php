<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAccounts extends EditRecord
{
    protected static string $resource = AccountsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // If enable_vat is false, ensure vat_number is set to 0
        if (isset($data['enable_vat']) && $data['enable_vat'] === false) {
            $data['vat_number'] = 0;
        }
        
        return $data;
    }
}
