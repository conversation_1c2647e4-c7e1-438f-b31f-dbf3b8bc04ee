<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Account\app\Filament\Resources\AccountsResource\Widgets\AccountsOverview;

class ListAccounts extends ListRecords
{
    protected static string $resource = AccountsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Account')),
        ];
    }
    protected function getHeaderWidgets(): array
    {
        return [
            AccountsOverview::class,
        ];
    }
}
