<?php

namespace Modules\Account\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Account\app\Http\Requests\Api\SwitchRoleRequest;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Account\app\Models\Account;
use Modules\Account\Services\AccountCustomerService;
use Modules\Account\Services\AccountRoleService;
class AccountRoleController extends ControllerAbstract
{
    public function __construct(AccountRoleService $service)
    {
        parent::__construct($service);
    }

    public function switchRole(SwitchRoleRequest $switchRoleRequest)
    {
        $this->service->setAccountRoleDefault($switchRoleRequest->get('account_role_id'));
        return ApiResponse::data(new AccountCustomerResource(auth()->user()->load('accountRoles')), __('Data Retrieved Successfully'));
    }
}
