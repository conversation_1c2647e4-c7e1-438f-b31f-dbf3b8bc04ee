<?php

namespace Modules\Account\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\Account\app\Http\Requests\Api\ChangePasswordRequest;
use Modules\Account\app\Http\Requests\Api\LoginRequest;
use Modules\Account\app\Http\Requests\Api\OtpCheckByNationalIDRequest;
use Modules\Account\app\Http\Requests\Api\OtpCheckRequest;
use Modules\Account\app\Http\Requests\Api\RegisterRequest;
use Modules\Account\app\Http\Requests\Api\ResetPasswordRequest;
use Modules\Account\app\Http\Requests\Api\SendOtpByNationalIDRequest;
use Modules\Account\app\Http\Requests\Api\SendOtpRequest;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Account\app\Models\Account;
use Modules\Account\app\Http\Requests\Api\NationalIDLoginRequest;
use Modules\Account\app\Http\Requests\Api\ResetPasswordByNationalIDRequest;
use Modules\Account\Services\AccountCustomerService;
use Modules\Notification\Services\NotificationService;

//use Modules\Notification\Services\NotificationService; todo handle notification module

class AccountCustomerAuth
{
    public $model = Account::class;
    const EMAIL_NOTIFICATION_CLASS = "EmailOtp";


    public function __construct(
        private AccountCustomerService $accountCustomerService,
        public NotificationService $notificationService
    )
    {
    }


    public function register(RegisterRequest $request)
    {
        $result = $this->accountCustomerService->register(collect($request->validated()));

        if ($result) {

            //todo fire register event
            //event(new UserRegistered($this->accountCustomerService->model));
            return ApiResponse::data(
                [
                    'customer'=> new AccountCustomerResource($this->accountCustomerService->model->load('accountRoles')),
                ]
                , __('Data Retrieved Successfully'), 200);
        } else
            return ApiResponse::errors($this->accountCustomerService->message);
    }

    public function checkOtpAndActivate(OtpCheckRequest $request)
    {

        $result = $this->accountCustomerService->activateOtp(collect($request->validated()));

        if ($result) {

            return ApiResponse::data(
                [
                    'customer' => new AccountCustomerResource($this->accountCustomerService->model->load('accountRoles')),
                    'token' => $this->accountCustomerService->model->token

                ], __('Data Retrieved Successfully')
            );
        } else
            return ApiResponse::errors(__('This Otp Not Valid.'));


    }

    public function checkOtpAndActivateByNationalId(OtpCheckByNationalIdRequest $request)
    {

        $result = $this->accountCustomerService->activateOtpByNationalId(collect($request->validated()));

        if ($result) {

            return ApiResponse::data(
                [
                    'customer' => new AccountCustomerResource($this->accountCustomerService->model->load('accountRoles')),
                    'token' => $this->accountCustomerService->model->token

                ], __('Data Retrieved Successfully')
            );
        } else
            return ApiResponse::errors(__('This Otp Not Valid.'));


    }


    public function login(LoginRequest $request)
    {

        $result = $this->accountCustomerService->login(collect($request->validated()));
        if ($result) {
            //todo handle notification services
            //if ($request->has('device_token'))
                $this->notificationService->store($request->get('device_token'), $this->accountCustomerService->model->id);

            return ApiResponse::data(
                [
                    'customer' => new AccountCustomerResource($this->accountCustomerService->model->load('accountRoles')),
                    'token' => $this->accountCustomerService->model->token

                ], __('Data Retrieved Successfully')
            );
        } else
            return ApiResponse::errors(__('Phone & Password does not match with our record.'), 400);

    }

    public function loginByNationalID(NationalIDLoginRequest $request)
    {
        $result = $this->accountCustomerService->loginByNationalID(collect($request->validated()));
        if ($result) {
            //store device token
            $this->notificationService->store($request->get('device_token'), $this->accountCustomerService->model->id);

            return ApiResponse::data(
                [
                    'customer' => new AccountCustomerResource($this->accountCustomerService->model->load('accountRoles')),
                    'token' => $this->accountCustomerService->model->token

                ], __('Data Retrieved Successfully')
            );
        } else {
            return ApiResponse::errors(__('National ID & Password does not match with our record.'), 400);
        }
    }

    public function verifyAccount(Request $request)
    {

        $model = $request->user();

        if ($model) {
            $model->update([
                'activated' => 1
            ]);
        }
        return response()->json([
            'status' => 200,
            'message' => __('done')
        ]);

    }

    public function profile()
    {
        return ApiResponse::data(new AccountCustomerResource(auth()->user()->load('accountRoles')), __('Data Retrieved Successfully'));

    }

    public function sendOtp(SendOtpRequest $request)
    {

        $result = $this->accountCustomerService->updateOtp(collect($request->validated()));


        return ApiResponse::data(['otp_code' => $result], __('Data updated successfully'));
    }

    public function sendOtpByNationalID(SendOtpByNationalIDRequest $request)
    {

        $result = $this->accountCustomerService->updateOtpByNationalID(collect($request->validated()));


        return ApiResponse::data(['otp_code' => $result['otp'], 'customer' => new AccountCustomerResource($result['model'])], __('Data updated successfully'));
    }

    public function optCheck(OtpCheckRequest $request)
    {

        $result = $this->accountCustomerService->checkOtp(collect($request->validated()));

        if ($result)
            return ApiResponse::success(__('done'));
        else
            return ApiResponse::errors(__('This Otp Not Valid.'));

    }

    public function optCheckByNationalID(OtpCheckByNationalIDRequest $request)
    {

        $result = $this->accountCustomerService->checkOtpByNationalID(collect($request->validated()));

        if ($result)
            return ApiResponse::success(__('done'));
        else
            return ApiResponse::errors(__('This Otp Not Valid.'));

    }

    public function resetPasswordByPhone(ResetPasswordRequest $request)
    {

        $result = $this->accountCustomerService->resetPasswordByPhone(collect($request->validated()));


        if ($result)
            return ApiResponse::success(__('done'));
        else
            return ApiResponse::errors(__('This Otp Not Valid.'), 400);

    }

    public function resetPasswordByNationalID(ResetPasswordByNationalIDRequest $request)
    {

        $result = $this->accountCustomerService->resetPasswordByNationalIDe(collect($request->validated()));


        if ($result)
            return ApiResponse::success(__('done'));
        else
            return ApiResponse::errors(__('This Otp Not Valid.'), 400);

    }

    public function updateProfile(Request $request)
    {
        $model = $request->user();
        $rules = [
            "name" => "sometimes|nullable|max:255|string",
            "phone" => "sometimes|max:25|string|unique:accounts,phone,{$model->id}",
            'image' => 'sometimes|nullable|mimes:jpeg,jpg,png,gif|max:10000'
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 401);
        }
        $data = $request->only("name", 'phone');
        $model->update($data);
        if ($request->image && $request->image != null) {
            $model->clearMediaCollection('images');
            $model->addMediaFromRequest('image')->toMediaCollection('images');
        }

        return response()->json([
            'status' => 200,
            'message' => __('done'),
            "data" => new AccountCustomerResource($model)
        ]);

    }

    public function changePassword(ChangePasswordRequest $request)
    {
        $this->accountCustomerService->model = $request->user();


        $result = $this->accountCustomerService->changePassword($request->validated());

        if ($result)
            return ApiResponse::success(__('Data Successfully Updated'));


        return ApiResponse::errors(__('Your old password not correct'));

    }

    public function logout()
    {
        //todo handle notification service
        //$this->notificationService->delete(auth()->user()->id);
        auth()->user()->currentAccessToken()->delete();
        return ApiResponse::success(__('Logged out successfully'));
    }

    public function deleteProfileImage(Request $request)
    {
        $model = $request->user();
        $model->clearMediaCollection('images');
        return response()->json([
            'status' => 200,
            'message' => __('done'),
            "data" => new AccountCustomerResource($model)
        ]);
    }

    public function deleteAccount(Request $request)
    {
        $request->user()->delete();
        //todo handle notification service
        //$this->notificationService->delete($request->user()->id);

        return response()->json([
            'status' => 200,
            'message' => __('deleted .')
        ]);
    }

    public function deleteImage(Request $request)
    {
        auth()->user()->clearMediaCollection('profile');

        return ApiResponse::success(__('Image Deleted Successfully'));
    }


    public function freezeAccount(Request $request)
    {

        $rules = [
            "freeze" => [
                'required',
                Rule::in([0, 1]),
            ]
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 401);
        }

        $model = $request->user();

        $model->update([
            'is_freeze' => $request->freeze
        ]);

        return response()->json([
            'status' => 200,
            'message' => __('done')
        ]);
    }

    public function changeLanguage()
    {

        $user = auth()->user();
        $languages = ["ar" => "en", "en" => "ar"];
        $user->lang = $languages[$user->lang];
        $user->save();

        return ApiResponse::data(new AccountCustomerResource($user->load('accountRoles')), __('Data updated successfully'));


    }
}
