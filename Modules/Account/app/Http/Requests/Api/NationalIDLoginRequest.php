<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class NationalIDLoginRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "national_id" => "required",
            'password' => 'required|string',
            "device_token" => "required",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'national_id.required' => __('The national ID is required.'),
            'password.required' => __('The password is required.'),
            'password.string' => __('The password must be a string.'),
            'device_token.required' => __('The device token is required.')
        ];
    }

}
