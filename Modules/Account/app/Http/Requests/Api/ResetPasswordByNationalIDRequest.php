<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordByNationalIDRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => 'required|string|min:8|confirmed|regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$/',

            "national_id" => "required|exists:accounts,national_id",
            "otp_code"=> "required|max:255|exists:accounts,otp_code",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'password.required' => __('The password is required.'),
            'password.string' => __('The password must be a string.'),
            'password.min' => __('The password must be at least 8 characters.'),
            'password.confirmed' => __('The password confirmation does not match.'),
            'password.regex' => __('The password must contain at least one uppercase letter, one special character, one lowercase letter, and one number.'),
            'national_id.required' => __('The national ID is required.'),
            'national_id.exists' => __('The national ID is not registered in our system.'),
            'otp_code.required' => __('The OTP code is required.'),
            'otp_code.max' => __('The OTP code must not exceed 255 characters.'),
            'otp_code.exists' => __('The OTP code is invalid.')
        ];
    }

}
