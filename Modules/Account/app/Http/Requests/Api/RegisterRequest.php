<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RegisterRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "device_token" => "required",
            "first_name" => "required",
            "second_name" => "required",
            "third_name" => "required",
            "last_name" => "required",
            'id_type' => [
                "required",
                Rule::in(['national_id', 'residency_permit', 'passport', 'gcc_id', 'other'])
            ],
            'country_of_issue' => "required_if:id_type,passport,other,residency_permit,gcc_id",
            'national_id'      => [
                'required',
                Rule::unique('accounts')->whereNull('deleted_at'),
                function ($attribute, $value, $fail) {
                    $idType = $this->input('id_type');
                    if (!$this->isValidIdFormat($idType, $value)) {
                        $fail(__('validation.invalid_id_format'));
                    }
                }
            ],
            "email" => ["required", "unique:accounts"],
            "birth_date" => ["required"],
            "phone" => "required|max:25|unique:accounts,phone|string",
            'password' => 'required|string|min:8|confirmed|regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{6,}$/',

        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'device_token.required' => __('The device token is required.'),
            'first_name.required' => __('The first name is required.'),
            'second_name.required' => __('The second name is required.'),
            'third_name.required' => __('The third name is required.'),
            'last_name.required' => __('The last name is required.'),
            'birth_date.required' => __('The Birth date is required.'),
            'id_type.required' => __('The ID type is required.'),
            'id_type.in' => __('The selected ID type is invalid.'),
            'country_of_issue.required_if' => __('The country of issue is required for the selected ID type.'),
            'national_id.required' => __('The ID number is required.'),
            'national_id.unique' => __('This ID number is already registered.'),
            'email.required' => __('The email is required.'),
            'email.unique' => __('This email is already registered.'),
            'phone.required' => __('The phone number is required.'),
            'phone.max' => __('The phone number may not be greater than 25 characters.'),
            'phone.unique' => __('This phone number is already registered.'),
            'phone.string' => __('The phone number must be a string.'),
            'password.required' => __('The password is required.'),
            'password.string' => __('The password must be a string.'),
            'password.min' => __('The password must be at least 8 characters.'),
            'password.confirmed' => __('The password confirmation does not match.'),
            'password.regex' => __('The password must contain at least one uppercase letter, one lowercase letter, and one number.')
        ];
    }

    /**
     * Prepare the data for validation. If ID type is national_id, set country of issue to Saudi Arabia
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        if ($this->input('id_type') === 'national_id') {
            $this->merge([
                'country_of_issue' => 'SA'
            ]);
        }
    }

    // Valid format of id Types Values
    // Example passport: ABCDEFGHIJ, ABCDEFGHIJKLMNOPQRSTUVWXYZ, ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890
    private function isValidIdFormat(string $type , string $value): bool
    {
        return match ($type) {
            'national_id' => preg_match('/^[12]\d{9}$/', $value) && !in_array($value, ['0000000000', '1111111111']),
            'residency_permit', 'passport', 'gcc_id', 'other' => preg_match('/^[A-Za-z0-9]{5,15}$/', $value),
            default => false,
        };
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
