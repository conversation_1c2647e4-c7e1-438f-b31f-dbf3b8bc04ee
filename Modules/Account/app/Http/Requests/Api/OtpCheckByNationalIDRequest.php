<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class OtpCheckByNationalIDRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "otp_code" => "required|exists:accounts,otp_code",
            "national_id" => "required|exists:accounts,national_id",

        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'otp_code.required' => __('The OTP code is required.'),
            'otp_code.exists' => __('The OTP code is invalid.'),
            'national_id.required' => __('The national ID is required.'),
            'national_id.exists' => __('The national ID is not registered in our system.')
        ];
    }

}
