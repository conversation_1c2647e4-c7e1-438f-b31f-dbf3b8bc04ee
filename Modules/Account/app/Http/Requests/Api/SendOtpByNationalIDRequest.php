<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class SendOtpByNationalIDRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "national_id" => "required|exists:accounts,national_id",

        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'national_id.required' => __('The national ID is required.'),
            'national_id.exists' => __('The national ID is not registered in our system.')
        ];
    }

}
