<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "phone" => "required",
            'password' => 'required|string',
            "device_token" => "required",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'phone.required' => __('The phone number is required.'),
            'password.required' => __('The password is required.'),
            'password.string' => __('The password must be a string.'),
            'device_token.required' => __('The device token is required.')
        ];
    }

}
