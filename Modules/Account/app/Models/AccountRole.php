<?php

namespace Modules\Account\app\Models;

use Illuminate\Database\Eloquent\Model;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $account_id
 * @property string $role
 * @property boolean $is_default
 * @property string $created_at
 * @property string $updated_at
 * @property Account $account
 */
class AccountRole extends BaseModel
{

    /**
     * @var array
     */
    protected $guarded = ['id'];

    protected  $casts = [
        'is_default' =>'boolean'
        ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function account()
    {
        return $this->belongsTo('Modules\Account\app\Models\Account');
    }
}
