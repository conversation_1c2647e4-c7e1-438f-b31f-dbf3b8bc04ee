<?php

namespace Modules\Account\app\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Account\app\Models\Account;

class IsCustomerActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->user() && $request->user()->is_blocked) {
            return response()->json([
                'success' => false,
                'message' => __('You are Blocked'),
            ], 401);
        }else if($request->user() && !$request->user()->is_active){
            return response()->json([
                'success' => false,
                'message' => __('Your account not verified yet'),
            ], 401);
        }else if(!$request->user()){
            if ($request->has('national_id')) {
                $authPerson = Account::where('national_id', $request->national_id)->first();
                $data = [
                    'customer' => new AccountCustomerResource($authPerson)
                ];
            } else {
                $authPerson = Account::where('phone', $request->phone)->first();
                $data = [];
            }
            if (!is_null($authPerson) && $authPerson && !$authPerson->is_active)
                return response()->json([
                    'success' => false,
                    'message' => __('Your account not verified yet'),
                    'data' => $data,
                ], 401);
        }
        return $next($request);
    }
}
