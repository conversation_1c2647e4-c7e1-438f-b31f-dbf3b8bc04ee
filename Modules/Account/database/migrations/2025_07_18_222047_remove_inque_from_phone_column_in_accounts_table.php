<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Remove unique constraint from the 'phone' column
            $table->dropUnique(['phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Re-add the unique constraint on the 'phone' column if rolling back
        Schema::table('accounts', function (Blueprint $table) {
            $table->unique('phone');
        });
    }
};
