<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_roles', function (Blueprint $table) {
            $table->enum('role', \Modules\Account\Enums\AccountRolesEnum::getAccountRoleTypes())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_roles', function (Blueprint $table) {

        });
    }
};
