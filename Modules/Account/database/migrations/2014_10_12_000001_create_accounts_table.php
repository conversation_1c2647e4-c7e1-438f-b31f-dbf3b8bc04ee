<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            //Main Data
            $table->string('first_name');
            $table->string('second_name');
            $table->string('third_name');
            $table->string('last_name');
            $table->string('email')->unique()->index();
            $table->string('phone', 25)->unique()->nullable();
            $table->string('national_id')->nullable();
            $table->date('birth_date')->nullable();
            $table->string('lang',2)->default('ar');
            //Auth Data
            $table->string('password');
            $table->string('otp_code', 6)->nullable();
            $table->timestamp('otp_activated_at')->nullable();
            $table->boolean('is_active')->default(0)->nullable();
            $table->boolean('is_blocked')->default(0)->nullable();
            $table->string('block_reason')->nullable();
            //Soft Delete
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
