<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Change the column to enum
            $table->enum('id_type', [
                'national_id',
                'residency_permit',
                'passport',
                'gcc_id',
                'other'
            ])->change();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            // Revert back to varchar(50)
            $table->string('id_type', 50)->change();
        });
    }
};
