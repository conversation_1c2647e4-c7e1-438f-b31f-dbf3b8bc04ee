<?php

namespace Modules\Account\Enums;

enum AccountRolesEnum: string
{
    const TENANT = 'tenant';
    const OWNER = 'owner';
    const TENANT_REPRESENTER = 'tenant_representer';
    const OWNER_REPRESENTER = 'owner_representer';

    public static function getAccountRoleTypes(): array
    {
        return [
            self::TENANT,
            self::TENANT_REPRESENTER,
            self::OWNER,
            self::OWNER_REPRESENTER,
        ];
    }

    public static function labels(): array
    {
        return [
            self::TENANT => __('Tenant'),
            self::TENANT_REPRESENTER => __('Tenant Representer'),
            self::OWNER => __('Owner'),
            self::OWNER_REPRESENTER => __('Owner Representer'),
        ];
    }

    /**
     * Static method to get label from roles
     */
    public static function getLabel(string $role): string
    {
        if (!$role) {
            return '-';
        }

        return self::labels()[$role];
    }
}
