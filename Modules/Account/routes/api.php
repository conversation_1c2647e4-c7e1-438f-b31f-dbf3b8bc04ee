<?php

use Illuminate\Support\Facades\Route;
use Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth;
use Modules\Account\app\Http\Controllers\Api\ProfileController;

//use Accounts\Http\Controllers\Api\AccountApiController;
//use Accounts\Http\Controllers\Api\CustomerLocationApiController;
//use Accounts\Http\Controllers\Api\CustomerServiceApiController;
//use Accounts\Http\Controllers\Api\Vendor\AccountVendorRate;
//use Accounts\Http\Controllers\Api\Vendor\VendorAreaController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('customer/register', [AccountCustomerAuth::class, 'register'])->name('customer.register');

Route::post('customer/checkOtpAndActivate', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'checkOtpAndActivate'])->name('customer.checkOtpAndActivate');
Route::post('customer/checkOtpAndActivate-by-national-id', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'checkOtpAndActivateByNationalId'])->name('customer.checkOtpAndActivateByNationalId');
Route::middleware(['throttle:5,1'])->group(function () {
    Route::post('customer/sendOtp', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'sendOtp'])->name('customer.sendOtp');
    Route::post('customer/sendOtp-by-national-id', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'sendOtpByNationalID'])->name('customer.sendOtpByNationalID');
    Route::post('customer/checkOtp', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'optCheck'])->name('customer.checkOtp');
    Route::post('customer/checkOtp-by-national-id', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'optCheckByNationalID'])->name('customer.checkOtpByNationalID');
    Route::post('customer/resetPassword', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'resetPasswordByPhone'])->name('customer.resetPassword');
    Route::post('customer/resetPassword-by-national-id', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'resetPasswordByNationalID'])->name('customer.resetPasswordByNationalID');
});
Route::middleware(['throttle:5,1', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::post('customer/login', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'login'])->name('customer.login');
    Route::post('customer/login-by-national-id', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'loginByNationalID'])->name('customer.loginByNationalID');
});
Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::post('customer/changePassword', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'changePassword'])->name('customer.changePassword');
    Route::post('customer/logout', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'logout'])->name('customer.logout');

    Route::get('customer/profile', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'profile'])->name('customer.profile');
    Route::get('customer/switch-role', [\Modules\Account\app\Http\Controllers\Api\AccountRoleController::class, 'switchRole'])->name('customer.switchRole');
    Route::post('customer/delete', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'deleteAccount'])->name('customer.delete');
    Route::post('customer/delete-image', [AccountCustomerAuth::class, 'deleteImage']);
    Route::put('customer/changeLanguage', [\Modules\Account\app\Http\Controllers\Api\AccountCustomerAuth::class, 'changeLanguage'])->name('customer.changeLanguage');

    //profile
    Route::prefix('customer/profile')->group(function () {
        Route::get('/', [ProfileController::class, 'index'])->name('customer.profile.index');
        Route::post('update', [ProfileController::class, 'updateProfile'])->name('customer.profile.update');
        Route::post('changePhone', [ProfileController::class, 'changePhone'])->name('customer.profile.changePhone');
        Route::post('checkPassword', [ProfileController::class, 'checkPassword'])->name('customer.checkPassword');
        Route::post('deleteAccount', [ProfileController::class, 'deleteAccount'])->name('customer.deleteAccount');
    });
});
