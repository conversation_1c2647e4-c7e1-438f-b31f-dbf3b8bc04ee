<?php


namespace Modules\Account\Services;


use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Modules\Account\app\Models\Account;

class ProfileService
{

    public string $message;
    public Collection $data;

    public function __construct(public Account|null $model ,
    public OtpService $otpService,
    public AccountCustomerService $accountCustomerService,
    ){}


    public function storeCardsInfo(array $data,string $collectionName):bool{

    try {

        $this->model->clearMediaCollection($collectionName); // all media in the images collection will be deleted
        $this->model->addMediaFromRequest('front_image')->toMediaCollection($collectionName);
        $this->model->addMediaFromRequest('back_image')->toMediaCollection($collectionName);

        return $this->model->update($data);

    }catch (\Exception $e){
        $this->message=$e->getMessage();
        return false;
    }


}

public function updateProfile(array $data,string $collectionName){

    try {

        if(array_key_exists('image',$data)) {

            $this->model->clearMediaCollection($collectionName); // all media in the images collection will be deleted
            $this->model->addMediaFromRequest('image')->toMediaCollection($collectionName);
            unset($data['image']);
        }
        //update full name
        $data['name'] = $this->accountCustomerService->concatenateAccountName(
            $data['first_name'] ?? $this->model->first_name,
            $data['second_name'] ?? $this->model->second_name,
            $data['third_name'] ?? $this->model->third_name,
            $data['last_name'] ?? $this->model->last_name
        );
        //update model
        return $this->model->update($data);

    }catch (\Exception $e){
        $this->message=$e->getMessage();
        return false;
    }

}

    public function updatePhone(array $data){

        if(Auth::guard('accounts')->attempt(collect($data)->only(['phone', 'password'])->toArray())) {

            $this->model->is_active = 0;
            $this->model->otp_code = random_int('111111', '999999');
            $this->model->phone = $data['new_phone'];
            $this->otpService->send($this->model->otp_code,$this->model->phone);

            $this->model->save();
            return true ;

        }
        return false;
    }

}
