<?php

namespace Modules\Account\Services;

use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\Repositories\AccountRoleRepository;

class AccountRoleService extends ServiceAbstract
{
    public function __construct(AccountRoleRepository $repository)
    {
        parent::__construct($repository);
    }

    public function setAccountRoleDefault(int $id)
    {
        $user = auth()->user();

        DB::table('account_roles')
        ->where('account_id', $user->id)
        ->update([
            'is_default' => DB::raw("CASE WHEN id = {$id} THEN 1 ELSE 0 END")
        ]);
    }

    public function accountRoleExist(int $account_id, string $role):bool
    {
        $condition = ['account_id' => $account_id, 'role' => $role];
        if (empty($this->repository->getFirstBy($condition))){
            return false;
        }
        return true;
    }
}
