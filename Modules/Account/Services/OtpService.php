<?php
namespace Modules\Account\Services;

class OtpService
{

    const  MESSAGE = "Your OTP- ";
    const  SMS_URL = "https://el.cloud.unifonic.com/rest/SMS/messages";
    const  SMS_APPID = "48JD8TN7E1uGXArlMNGL9MnVQsndck";

    public function send(int $otp,string $phone) : bool {

        try {

            $response  = $this->formatSaudiPhoneNumber($phone);

            if(!$response)
            return false;
                $this->post(self::SMS_URL, [
                    "AppSid" => self::SMS_APPID,
                    "Recipient" => $response,
                    "Body" => self::MESSAGE . $otp,

                ]);

            return $this->response['status'];


        } catch (\Throwable $th) {
            return false;
        }


    }

    private function formatSaudiPhoneNumber($phone) {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Check if the number already starts with '966'
        if (strpos($phone, '966') === 0) {
            // If it starts with '966', ensure it's the correct length
            if (strlen($phone) !== 12) {
                return false; // Invalid length
            }
            return $phone;
        }

        // Check if the number starts with '05' (Saudi mobile prefix)
        if (strpos($phone, '05') === 0) {
            // Remove the leading '0' and add '966'
            return '966' . substr($phone, 1);
        }

        // Check if the number is 9 digits (without country code)
        if (strlen($phone) === 9) {
            return '966' . $phone;
        }

        // If none of the above conditions are met, it's not a valid Saudi number
        return false;
    }

    public function sendSmsNotification(string $content,string $phone) : bool {
        try {
            $response  = $this->formatSaudiPhoneNumber($phone);

            if(!$response)
            return false;
                $this->post(self::SMS_URL, [
                    "AppSid" => self::SMS_APPID,
                    "Recipient" => $response,
                    "Body" => $content,
                ]);

            return $this->response['status'];


        } catch (\Throwable $th) {
            return false;
        }


    }

}
