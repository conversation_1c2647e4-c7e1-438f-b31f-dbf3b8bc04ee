<?php


namespace Modules\Account\Services;


use App\Events\PasswordUpdated;
use App\Models\UserAccountCredential;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Lease\Repositories\LeaseMemberRepository;

class AccountCustomerService
{

    public string $message;
    public Collection $data;
    protected AccountRoleService $accountRoleService;
    protected LeaseMemberRepository $leaseMemberRepository;

    public function __construct(public Account|null $model,
                                public OtpService   $otpService,
                                AccountRoleService $accountRoleService,
                                LeaseMemberRepository $leaseMemberRepository
    )
    {
        $this->accountRoleService = $accountRoleService;
        $this->leaseMemberRepository = $leaseMemberRepository;
    }

    public function register(Collection $data): bool
    {

        try {
            $data->put('otp_code', random_int('111111', '999999'));
            DB::beginTransaction();
            $data = $data->toArray();
            $data['name'] = $this->concatenateAccountName($data['first_name'], $data['second_name'], $data['third_name'], $data['last_name']);
            $this->model = $this->model::create($data);
            $this->model->token = $this->model->createToken("API TOKEN")->plainTextToken;
            //create default role
            $this->accountRoleService->create(['account_id' => $this->model->id, 'role' => AccountRolesEnum::TENANT, 'is_default' => true]);
            //$this->otpService->send($data['otp_code'],$data['phone']);
            //verify_account_email notify
            $template = NotificationsTemplate::where(['key' => 'verify_account_email'])->first();
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($this->model->id)
                    ->findBody(['{username}', '{confirmation_code}'])
                    ->replaceBody([$this->model->name, $this->model->otp_code])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->message = $e->getMessage();
            return false;
        }

    }

    public function login(Collection $data): bool
    {

        if (Auth::guard('accounts')->attempt($data->only(['phone', 'password'])->toArray())) {
            $this->model = $this->model->where('phone', $data->only('phone'))->first();
            $this->model->token = $this->model->createToken("API TOKEN")->plainTextToken;
            return true;
        }

        return false;

    }
    public function loginByNationalID(Collection $data): bool
    {
        $credentials = UserAccountCredential::withoutGlobalScopes() //disable deleted_at check in user_account_credentials
        ->where('national_id', $data->get('national_id'))->first();

        if ($credentials && Hash::check($data->get('password'), $credentials->password)) {
            $account = Account::find($credentials->account_id);

            if ($account) {
                Auth::guard('accounts')->login($account);
                $this->model = $account;
                $this->model->token = $this->model->createToken("API TOKEN")->plainTextToken;
                return true;
            }
        }

        return false;
    }
    public function concatenateAccountName(?string $first = null, ?string $second = null, ?string $third = null, ?string $last = null): string
    {
        // Filter out null/empty values and trim whitespace
        $names = array_filter([$first, $second, $third, $last], function($name) {
            return !is_null($name) && trim($name) !== '';
        });

        // Remove duplicate names
        $names = array_unique($names);

        // Join names with single space and trim any extra spaces
        return trim(implode(' ', $names));
    }

    public function checkOtp(Collection $data): bool
    {
        $this->model = $this->model->where('phone', $data->only('phone'))
            ->where('otp_code', $data->only('otp_code'))
            ->first();

        if ($this->model)
            return true;

        return false;
    }
    public function checkOtpByNationalID(Collection $data): bool
    {
        $this->model = $this->model->where('national_id', $data->only('national_id'))
            ->where('otp_code', $data->only('otp_code'))
            ->first();

        if ($this->model)
            return true;

        return false;
    }

    public function activateOtp(Collection $data): bool
    {

        $this->checkOtp($data);

        if ($this->model) {
            $this->model->update([
                'otp_code' => null,
                'is_active' => 1
            ]);
            $this->model->token = $this->model->createToken("API TOKEN")->plainTextToken;
            return true;
        }


        return false;


    }

    public function activateOtpByNationalId(Collection $data): bool
    {

        $this->checkOtpByNationalID($data);

        if ($this->model) {
            $this->model->update([
                'otp_code' => null,
                'is_active' => 1
            ]);
            $this->model->token = $this->model->createToken("API TOKEN")->plainTextToken;
            return true;
        }


        return false;


    }

    public function updateOtp(Collection $data): string
    {

        $otp = random_int('111111', '999999');
        //$this->otpService->send($otp, $data['phone']);

        $this->model->where('phone', $data->only('phone'))
            ->update([
                'otp_code' => $otp
            ]);


        return $otp;
    }

    public function updateOtpByNationalID(Collection $data): array
    {

        $otp = random_int('111111', '999999');
        $this->model = $this->model->where('national_id', $data->only('national_id'))->first();
        $this->model->update([
            'otp_code' => $otp
        ]);

        return [ 'otp' => $otp, 'model' => $this->model];
    }

    public function resetPasswordByPhone(Collection $data): bool
    {

        $this->getModelByPhone($data);
        return $this->resetPassword($data);
    }
    public function resetPasswordByNationalIDe(Collection $data): bool
    {

        $this->getModelByNationalID($data);
        return $this->resetPassword($data);
    }

    public function resetPassword(Collection $data): bool
    {

        if ($this->model) {
            $data['otp_code'] = null;
            //sync user passwords
            event(new PasswordUpdated(Account::class, $this->model->id, $data->only('password')));
            return $this->model->update($data->only(['otp_code', 'password'])->toArray());
        }

        return false;
    }

    public function getModelByNationalID(Collection $data): void
    {
        $this->model = $this->model->where('national_id', $data->only('national_id'))->where('otp_code', $data->only('otp_code'))->first();
    }

    public function getModelByPhone(Collection $data): void
    {
        $this->model = $this->model->where('phone', $data->only('phone'))->where('otp_code', $data->only('otp_code'))->first();
    }

    public function changePassword(array $data)
    {
        if (!Hash::check($data['old_password'], $this->model->password))
            return false;

        $this->model->update([
            'password' => $data['password']
        ]);
        //sync user passwords
        event(new PasswordUpdated(Account::class, $this->model->id, $data['password']));
        return true;
    }

    public function deleteAccount($id):bool
    {
        $account = $this->model->find($id);

        $member = $this->leaseMemberRepository->getFirstBy(['member_id' => $account->id]);

        if ($member !== null) {
            return false;
        }

        $account->delete();
        return true;
    }
}
