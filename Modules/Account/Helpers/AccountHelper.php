<?php
namespace Modules\Account\Helpers;
use App\Enums\RoleEnum;
use App\Models\User;

class AccountHelper
{

    public static function CurrentRole()
    {
        $account = auth()->user();
        if ($account) {
            $default_role = $account->accountRoles()->where('is_default', true)->first();
            if ($default_role) {
                return $default_role->role;
            }
        }
        return null;
    }

    public static function AdminUsers()
    {
        return User::role(RoleEnum::ADMIN)->get();
    }
}
