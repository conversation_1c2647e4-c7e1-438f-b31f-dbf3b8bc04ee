<?php

namespace Modules\Payment\Enums;

class PaymentValidationEnum
{

    const PAY_VALIDATION =
        [
            "amount" => 'required|numeric',
            "notes" => 'nullable|max:500',

            // morphed columns to allow any table for payment (orders,services ,etc...)
            "invoice_id" => 'required|numeric',
        ];

    const VERIFY_VALIDATION =
        [
            "amount" => 'required|numeric',
            "transaction_code" => 'required|exists:khaleds_payments,transaction_code',
            "status" =>'required|string'

        ];

    const  FAWRY_PAY_VALIDATION=[
        "items*"=>'required',
        "items.*.itemId"=>'required|numeric',
        "items.*.price"=>'required|numeric',
        "items.*.quantity"=>'required|numeric',
    ];

    const  THAWANI_PAY_VALIDATION=[
        "items*"=>'required',
        "items.*.name"=>'required|string',
        "items.*.unit_amount"=>'required|numeric',
        "items.*.quantity"=>'required|numeric',
    ];

    const HyperPay_VALIDATION=[
        "source"=>'required'
    ];

    const Kashier_VALIDATION=[
        "source"=>'required'
    ];

    const OPAY_VALIDATION=[
        "items*"=>'required',
        "items.*.price"=>'required|numeric',
        "items.*.productId"=>'required',
        "items.*.quantity"=>'required|numeric',
        ];

}
