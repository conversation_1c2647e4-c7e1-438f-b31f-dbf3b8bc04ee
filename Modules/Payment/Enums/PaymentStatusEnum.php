<?php

namespace Modules\Payment\Enums;

class PaymentStatusEnum
{

    const PAID ='PAID';
    const UNPAID ='UNPAID';

    public static function labels(): array
    {
        return [
            self::PAID => __('Paid'),
            self::UNPAID => __('unpaid'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
}
