<?php

namespace Modules\Payment\Enums;

class PaymentMethodTypeEnum
{

    const ONLINE ='ONLINE';
    const OFFLINE ='OFFLINE';

    public static function labels(): array
    {
        return [
            self::ONLINE => __('Online'),
            self::OFFLINE => __('Offline'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getValues(): array // New method for values
    {
        return [
            self::ONLINE,
            self::OFFLINE,
        ];
    }
}
