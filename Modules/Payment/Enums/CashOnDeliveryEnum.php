<?php

namespace Modules\Payment\Enums;

class CashOnDeliveryEnum
{

    const PAY_VALIDATION =
        [
            "amount" => 'required|numeric',
            "notes" => 'nullable|max:500',
            "payment_method_id" => "required|exists:khaleds_payment_methods,id",
            "invoice_id" => "required|exists:invoices,id",
            "transaction_code" => 'required|max:100|unique:khaleds_payments,transaction_code',
        ];

    const VERIFY_VALIDATION =
        [
            "amount" => 'required|numeric',
            "transaction_code" => 'required|exists:khaleds_payments,transaction_code',
        ];

}
