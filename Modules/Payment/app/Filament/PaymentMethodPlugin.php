<?php

namespace Modules\Payment\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource;

class PaymentMethodPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Payment';
    }

    public function getId(): string
    {
        return 'paymentmethod';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                PaymentMethodResource::class,
            ]);
    }
}
