<?php

namespace Modules\Payment\app\Filament\Resources;


use Modules\Payment\app\Models\PaymentMethod;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Components\paymentFormComponent;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Components\paymentMethodFormComponent;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Components\paymentMethodListComponent;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Pages\CreatePaymentMethod;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Pages\EditPaymentMethod;
use Modules\Payment\app\Filament\Resources\PaymentMethodResource\Pages\ListPaymentMethods;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

class PaymentMethodResource extends Resource
{
    protected static ?string $model = PaymentMethod::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                paymentMethodFormComponent::make(),
            ]);
    }

    public static function table(Table $table): Table
    {
        // Get the list configuration
        $list = paymentMethodListComponent::getList();
    
        // Query to filter by method_type as 'offline'
        return $table
            ->query(PaymentMethod::query()->where('method_type', PaymentMethodTypeEnum::OFFLINE)) // Add the query to filter by 'method_type'
            ->columns($list['columns'])
            ->filters($list['filters'])
            ->actions($list['actions'])
            ->bulkActions($list['bulkActions']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPaymentMethods::route('/'),
            'create' => CreatePaymentMethod::route('/create'),
            'edit' => EditPaymentMethod::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('Payemnt Methods');
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Invoice');
    }
    public static function getBreadcrumb() : string
    {
        return __('Payment Methods');
    }
    public static function getModelLabel(): string
    {
        return __('Payment Methods');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Payment Methods');
    }
}
