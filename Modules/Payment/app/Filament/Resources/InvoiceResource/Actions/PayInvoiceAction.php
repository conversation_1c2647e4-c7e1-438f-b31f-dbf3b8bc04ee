<?php

namespace Modules\Payment\app\Filament\Resources\InvoiceResource\Actions;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Actions\Action;
use Modules\Payment\app\Models\PaymentMethod;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Closure;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Cancel;
use Filament\Support\Exceptions\Halt;
use Illuminate\Database\Eloquent\Model;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\PaymentMethodEnum;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

class PayInvoiceAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'payInvoice';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Receive Payment'))
            ->modalHeading(__('Pay Invoice'))
            ->modalSubmitActionLabel(__('Submit Payment'))
            ->color('success')
            ->icon('heroicon-o-currency-dollar')
            ->visible(function ($record) {
                    // Decode the JSON field to get the leaseId
                $leaseId = optional(json_decode($record->extra))->lease_id;
                if($leaseId)
                {
                        // Get the lease associated with the leaseId
                            $lease = Lease::find($leaseId);
                        }
                
                // Check if the lease exists and if the status is 'unpaid' or 'partial_paid'
                // Also, check if the payment method is Offline
                return ($record->status === 'unpaid' || $record->status === 'partial_paid') &&
                       ($leaseId == null || $leaseId !== null && $lease->payment_method == PaymentMethodEnum::OFFLINE);
            })
            ->form([
                TextInput::make('amount')
                    ->label(__('Payment Amount'))
                    ->required()
                    ->numeric()
                    ->default(fn ($record) => $record->remaining)
                    ->minValue(1)
                    ->rule(function ($livewire,$set,$get) {
                        return function (string $attribute, $value, Closure $fail) use ($livewire,$set,$get) {
                            $amount = (float)$value;
                            $remaining = $this->record->remaining;
                            if($amount <= 0 || $amount > $remaining)
                            {
                                $fail(__('max paid amount is') . $remaining);
                            }                      
                        };
                    })
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire, $component) {
                        $state = $state === '' || $state === null ? 0 : (float)$state;
                        $livewire->validateOnly($component->getStatePath());
                    })
                    ->hint(fn ($record) => (__('Remaining balance') . $record->remaining )),

                Select::make('payment_method_id')
                    ->label(__('Payment Method'))
                    ->options(PaymentMethod::where('is_active', true)->where('method_type' , PaymentMethodTypeEnum::OFFLINE)->pluck('name', 'id'))
                    ->required()
                    ->searchable(),

                DatePicker::make('payment_date')
                    ->label(__('Payment Date'))
                    ->default(now())
                    ->required(),

                Textarea::make('notes')
                    ->label(__('Payment Notes'))
                    ->rows(3),

                Textarea::make('transaction_code')
                    ->label(__('Transaction Code'))
                    ->rows(3),
            ])
            ->action(function (array $data): void {
                DB::beginTransaction();
                
                try {
                    // Validate the payment amount one more time
                    if ((float)$data['amount'] > $this->record->remaining) {
                        throw new Halt(__('Invalid payment amount'));
                    }

                    $paymentFactory = new \Modules\Payment\Factories\PaymentFactory();
                    
                    // Get payment method from database
                    $paymentMethod = PaymentMethod::findOrFail($data['payment_method_id']);
                    
                    // Get payment processor based on payment method
                    $payment = $paymentFactory->get('Cash');
                    
                    if (!$payment) {
                        throw new Halt(__('Invalid payment method'));
                    }

                    if($data['amount'] > $this->record->remaining || $data['amount'] < 0)
                    {
                        throw new Halt(__('Please Enter Amount greater than 0 and less than remainig amount'));
                    }

                    // Prepare payment data
                    $paymentData = [
                        'invoice_id' => $this->record->id,
                        'amount' => (float)$data['amount'],
                        'payment_method_id' => $data['payment_method_id'],
                        'payment_date' => $data['payment_date'],
                        'notes' => $data['notes'] ?? null,
                        'transaction_code' => $data['transaction_code'] ?? Str::uuid(),
                    ];

                    // Set payment request data
                    $payment->response->request = $paymentData;

                    // Process payment
                    $paymentResponse = $payment->pay($paymentData);

                    if ($paymentResponse->response->status !== true) {
                        throw new Halt(__('Payment processing failed'));
                    }

                    DB::commit();

                    Notification::make()
                        ->title(__('Payment successful'))
                        ->success()
                        ->send();

                } catch (\Exception $e) {
                    DB::rollBack();

                    Notification::make()
                        ->title(__('Payment failed'))
                        ->body($e->getMessage())
                        ->danger()
                        ->send();

                    throw new Halt();
                }
            });
    }

    public function getRecord(): ?Model
    {
        return $this->record;
    }
}