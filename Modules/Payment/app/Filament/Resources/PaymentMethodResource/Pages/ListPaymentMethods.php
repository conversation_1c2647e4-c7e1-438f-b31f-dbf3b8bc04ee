<?php

namespace Modules\Payment\app\Filament\Resources\PaymentMethodResource\Pages;

use Modules\Payment\app\Filament\Resources\PaymentMethodResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPaymentMethods extends ListRecords
{
    protected static string $resource = PaymentMethodResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add New')),
        ];
    }
}
