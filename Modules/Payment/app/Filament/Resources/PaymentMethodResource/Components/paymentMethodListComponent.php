<?php

namespace Modules\Payment\app\Filament\Resources\PaymentMethodResource\Components;

use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Notifications\Notification;
use Modules\Payment\app\Models\PaymentMethod;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

class paymentMethodListComponent
{
    public static function getList(): array
    {
        return [
            'columns' => [
                TextColumn::make('id')
                    ->label(__('id'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('name')
                    ->label(__('Name'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('description')
                    ->label(__('Description')),

                TextColumn::make('method_type')
                        ->label(__('Method Type'))
                        ->sortable()
                        ->getStateUsing(fn ($record) => PaymentMethodTypeEnum::getLabel($record->method_type)),

                ToggleColumn::make('is_active')
                    ->label(__('Active'))
                    ->sortable()
                    ->onColor('success')
                    ->offColor('danger'),
                TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->sortable()
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->sortable()
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),
            ],
            'filters' => [
                // Add any filters here if needed
            ],

            'actions' => [
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make(),
                    Action::make('toggle_active')
                        ->label(fn (PaymentMethod $record): string => $record->is_active ? __('Deactivate') : __('Activate'))
                        ->icon(fn (PaymentMethod $record): string => $record->is_active ? 'heroicon-o-lock-closed' : 'heroicon-o-check-circle')
                        ->modalHeading(fn (PaymentMethod $record): string => $record->is_active ? __('Are you sure you want to deactivate this payment method?') : __('Are you sure you want to activate this payment method?'))
                        ->requiresConfirmation()
                        ->modalButton(fn (PaymentMethod $record): string => $record->is_active ? __('Deactivate') : __('Activate'))
                        ->action(function (PaymentMethod $record): void {
                            $record->update([
                                'is_active' => !$record->is_active,
                            ]);
                        })
                        ->after(function (PaymentMethod $record): void {
                            $message = $record->is_active
                                ? __('The payment method has been successfully activated.')
                                : __('The payment method has been successfully deactivated.');

                            Notification::make()
                                ->title(__('Action Completed'))
                                ->body($message)
                                ->success()
                                ->send();
                        }),
                    DeleteAction::make()
                        ->visible(fn ($record) =>
                            !\DB::table('khaleds_payments')->where('payment_method_id', $record->id)->exists()
                        ),
                ])
                ->button()
                ->extraAttributes([
                    'class' => 'custom-action-btn',
                ])
                ->color('transparent')
                ->label(__('Commends')),
            ],
            'bulkActions' => [
                BulkActionGroup::make([]),
            ],
        ];
    }
}
