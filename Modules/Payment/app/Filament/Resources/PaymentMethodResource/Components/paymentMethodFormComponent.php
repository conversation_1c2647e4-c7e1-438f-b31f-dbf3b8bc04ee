<?php

namespace Modules\Payment\app\Filament\Resources\PaymentMethodResource\Components;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;

class paymentMethodFormComponent
{
    public static function make()
    {
        return Grid::make(2)
            ->schema([
                Section::make(__('Title & Description'))
                    ->schema([
                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        TextInput::make('name.ar')
                                            ->label(__('Name Arabic'))
                                            ->required()
                                            ->rules([
                                                function() {
                                                    return function($attribute, $value, $fail) {
                                                        $exists = \DB::table('khaleds_payment_methods')
                                                            ->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, "$.ar"))) = ?', [strtolower($value)])
                                                            ->where('id', '!=', request()->route('record'))
                                                            ->exists();
                                                        
                                                        if ($exists) {
                                                            $fail(__('This name is already taken.'));
                                                        }
                                                    };
                                                }
                                            ]),
                                        Textarea::make('description.ar')
                                            ->label(__('Description Arabic')),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        TextInput::make('name.en')
                                            ->label(__('Name English'))
                                            ->required()
                                            ->rules([
                                                function() {
                                                    return function($attribute, $value, $fail) {
                                                        $exists = \DB::table('khaleds_payment_methods')
                                                            ->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, "$.en"))) = ?', [strtolower($value)])
                                                            ->where('id', '!=', request()->route('record'))
                                                            ->exists();
                                                        
                                                        if ($exists) {
                                                            $fail(__('This name is already taken.'));
                                                        }
                                                    };
                                                }
                                            ])
                                            ->afterStateUpdated(fn ($state, callable $set) => $set('method', strtolower(str_replace(' ', '_', $state)))),
                                        Textarea::make('description.en')
                                            ->label(__('Description English')),
                                    ]),
                            ]),
                        Hidden::make('method'),
                    ]),
                Toggle::make('is_active')
                    ->label(__('Is Active'))
                    ->default(true),
            ]);
    }
}