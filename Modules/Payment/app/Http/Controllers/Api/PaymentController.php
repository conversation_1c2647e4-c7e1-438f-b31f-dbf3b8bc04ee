<?php

namespace Modules\Payment\app\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Modules\Invoice\app\Models\Invoice;

class PaymentController extends Controller
{
    public function pay(Request $request)
    {  
        $paymentFactory = new \Modules\Payment\Factories\PaymentFactory();
        $payment = $paymentFactory->get('ClickPay');
        
        $invoice = Invoice::find($request['invoice_id']);
        if($request['amount'] > $invoice->remaining)
        {
            return response()->json([
                'success' => false,
                'message' => __('amount you entered greater that remaining amount in the invoice'),
                'data' => null,
            ],422);
        }
        // Perform the payment and store the response
        $paymentResponse = $payment->pay($request['amount'] , null , $request['invoice_id'] , $request['notes']);
        $redirectUrl['redirect_url'] = $paymentResponse['html']['redirect_url'] ?? null;
        // Return the payment response as a JSON API response
        return response()->json([
            'success' => true,
            'data' => $redirectUrl,
        ]);
    }
}
