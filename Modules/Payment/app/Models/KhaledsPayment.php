<?php

namespace Modules\Payment\app\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Subscription\app\models\Subscription;

class KhaledsPayment extends Model
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $guarded = ['id'];


    public function order()
    {
        return $this->morphTo();
    }

    public function subscription()
    {
        if ($this->order_table == Subscription::class)
            return $this->belongsTo(Subscription::class,'order_id');

        return null;
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id', 'id');
    }
}
