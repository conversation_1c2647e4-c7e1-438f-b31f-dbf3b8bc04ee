<?php

namespace Modules\Payment\Classes;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

use Modules\Payment\Interfaces\PaymentInterface;
use Modules\Payment\Classes\BaseController;
use Modules\Payment\Enums\PaymentStatusEnum;
use Illuminate\Support\Str;
use Modules\Invoice\Traits\InvoiceUpdate;
use Modules\Payment\app\Models\PaymentMethod;
use Modules\Payment\Traits\PaymentSave;

class ClickPayPayment extends BaseController implements PaymentInterface
{
    use PaymentSave , InvoiceUpdate;
    public const PAYMENT_METHOD = "clickpay";
    public $clickpay_server_key;
    public $clickpay_profile_id;
    public $verify_route_name;
    public $payment_id; // Define the payment_id property
    public $uniqid; // Define the payment_id property



    public function __construct()
    {
        $this->clickpay_server_key = config('nafezly-payments.CLICKPAY_SERVER_KEY');
        $this->clickpay_profile_id = config('nafezly-payments.CLICKPAY_PROFILE_ID');
        $this->verify_route_name = 'payment.callback';
    }



    /**
     * @param $amount
     * @param null $user_id
     * @param null $user_first_name
     * @param null $user_last_name
     * @param null $user_email
     * @param null $user_phone
     * @param null $source
     * @return string[]
     * @throws MissingPaymentInfoException
     */
    public function pay($amount = null, $source = null , $invoice_id , $notes,$model=null): array
    {
        $user_first_name = auth()->user()->first_name;
        $user_last_name = auth()->user()->last_name;
        $user_email = auth()->user()->email;
        $user_phone = auth()->user()->phone;
        $user_id = auth()->user()->id;
        $paymentMethodId = PaymentMethod::where('method', 'clickpay')
            ->value('id');
        $data =
        [
            'amount' => $amount,
            'invoice_id' => $invoice_id,
            'notes' => $notes,
            'model' => $model,
            'payment_method_id' => $paymentMethodId,
            'transaction_code' => Str::uuid(),
        ];
        $payment  = $this->saveToPayment($data);
        $this->setPassedVariablesToGlobal($amount,$user_id,$user_first_name,$user_last_name,$user_email,$user_phone,$source);
        $required_fields = ['amount','user_first_name','user_last_name','user_email','user_phone'];
//        $this->checkRequiredFields($required_fields, 'CLICKPAY');


        if($this->payment_id==null)
            $uniqid = uniqid().rand(100000,999999);
        else
            $uniqid = $this->payment_id;

        $response = \Http::withHeaders([
            'authorization' => $this->clickpay_server_key
        ])->post('https://secure.clickpay.com.sa/payment/request', [
            "profile_id" => $this->clickpay_profile_id,
            "tran_type" => "sale",
            "tran_class" => "ecom",
            "cart_id" => "cart_" . $uniqid,
            "cart_currency" => $this->currency??"SAR",
            "cart_amount" => $this->amount,
            "cart_description" => "Credit",
            "paypage_lang" => "ar",
            "customer_details" => [
                "name" => $this->user_first_name.' '.$this->user_last_name,
                "email" => $this->user_email,
                "phone" => $this->user_phone,
                "street1" => "address street",
                "city" => "riyadh",
                "state" => "riyadh",
                "country" => "SA",
                "zip" => "12345"
            ],
            "shipping_details" => [
                "name" => $this->user_first_name.' '.$this->user_last_name,
                "email" => $this->user_email,
                "phone" => $this->user_phone,
                "street1" => "street2",
                "city" => "riyadh",
                "state" => "riyadh",
                "country" => "SA",
                "zip" => "54321"
            ],
            "framed"=> true,
            "framed_return_top"=> false,
            "framed_return_parent"=> false,
            "hide_shipping"=> true,
            "callback"=> route($this->verify_route_name,['payment'=>"clickpay",'payment_id'=>$uniqid]),
            "return" => route($this->verify_route_name,['payment'=>"clickpay",'payment_id'=>$uniqid , 'payment_details' => $payment]),

        ])->json();

        if(isset($response['tran_ref'])){
            cache(['clickpay_ref_code_'.$uniqid => $response['tran_ref'] ]);
            return [
                'payment_id'=>$response['tran_ref'],
                'html'=>$response,
                'redirect_url'=>$response['redirect_url']
            ];
        }
        return [
            'payment_id'=>$uniqid,
            'html'=>$response,
            'redirect_url'=>""
        ];
    }

    /**
     * @param Request $request
     * @return array|void
     */
    public function verify(Request $request)
    {
        $response = \Http::withHeaders([
            'authorization' => $this->clickpay_server_key
        ])->post('https://secure.clickpay.com.sa/payment/query', [
            'profile_id' => $this->clickpay_profile_id,
            'tran_ref' => cache('clickpay_ref_code_'.$request['payment_id'])
        ])->json();

        if(isset($response['payment_result']['response_status']) && $response['payment_result']['response_status'] == "A" ){
            $response['updated_payment'] = $this->updateToPayment(PaymentStatusEnum::PAID , $request['payment_details']);
             $this->updateInvoiceStatus($request['payment_details']);
            event(new \Modules\Payment\app\Events\PaymentVerifiedEvent($response));

            return [
                'success' => true,
                'payment_id'=>cache('clickpay_ref_code_'.$request['payment_id']),
                'message' => __('nafezly::messages.PAYMENT_DONE'),
                'process_data' => $request->all()
            ];
        }else{
            return [
                'success' => false,
                'payment_id'=>"",
                'message' => __('nafezly::messages.PAYMENT_FAILED'),
                'process_data' => $request->all()
            ];
        }
    }
}
