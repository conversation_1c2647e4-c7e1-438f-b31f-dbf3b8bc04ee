<?php

namespace Modules\Payment\Classes;

use Modules\Payment\Enums\CashOnDeliveryEnum;
use Modules\Payment\Enums\PaymentStatusEnum;
use Modules\Payment\Interfaces\IPaymentInterface;
use Modules\Payment\Interfaces\PaymentAbstract;
use Modules\Invoice\Traits\InvoiceUpdate;


class CashPayment extends PaymentAbstract implements IPaymentInterface
{
    use InvoiceUpdate;
    public const PAYMENT_METHOD = "CashPayment";
    protected $payment;

    public function pay(): self
    {
        $this->validations = CashOnDeliveryEnum::PAY_VALIDATION;

        try {

            $this->validate();

            $payment = $this->saveToPayment($this->response->request);
            
            $this->setAdditionalData($payment->toArray());
            $this->verify();
            $this->response->message = __("Paid Successfully");


        } catch (\Exception $e) {

            $this->response->message = $e->getMessage();
            $this->saveToLogs();

        }

        return $this;

    }

    public function setAdditionalData(array $payment): self
    {
        $this->payment = $payment;
        return $this;
    }

    public function getAdditionalData(): array
    {
        return $this->payment ?? [];
    }

    public function verify(): self
    {
        try {

            $this->validations = CashOnDeliveryEnum::VERIFY_VALIDATION;

            $this->validate();

            $this->updateToPayment(PaymentStatusEnum::PAID , $this->getAdditionalData()['id']);
            // dd($this->getAdditionalData());
            $this->updateInvoiceStatus($this->getAdditionalData()['id']);

        } catch (\Exception $e) {
            $this->response->message = $e->getMessage();
            $this->saveToLogs();
        }
        return $this;

    }
}
