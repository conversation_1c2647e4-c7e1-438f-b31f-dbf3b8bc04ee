<?php

namespace Modules\Payment\Interfaces;

use Illuminate\Database\Eloquent\Model;
use Modules\Payment\Services\PaymentResponse;
use Modules\Payment\Traits\PaymentSave;
use Modules\Payment\Traits\PaymentSaveToLogs;
use Modules\Payment\Traits\PaymentValidation;

abstract class PaymentAbstract
{

    use PaymentSave, PaymentSaveToLogs, PaymentValidation;
    protected array $data;
    protected array $attributes;
    protected array $validations;
    protected Model $buyer;
    public PaymentResponse $response;



    public function __construct()
    {
        $this->response = new PaymentResponse();
    }

    public function setRequest(array $attributes): IPaymentInterface
    {
        $this->response->request = $attributes;
        return $this;
    }

    public function setBuyerModel(Model $buyer): IPaymentInterface
    {
        $this->buyer = $buyer;
        return $this;
    }
}
