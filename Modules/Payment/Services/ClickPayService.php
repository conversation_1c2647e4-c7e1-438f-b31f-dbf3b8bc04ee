<?php

namespace Modules\App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use Khaleds\Payment\Classes\ClickPayPayment;

class ClickPayService
{
    public string $url;
    public string $callbackUrl;
    public string $server_key;

    public function __construct()
    {
        $this->url = Config::get('payment.clickpay.url');
        $this->server_key = Config::get('payment.clickpay.server_key');
        $this->callbackUrl = route('api.v1.payment.callback', ['method' => ClickPayPayment::PAYMENT_METHOD], true);
    }

    public function getPayload($cart_id, $amount, $redirect_url = null): array
    {
        if (!is_null($redirect_url)) {
            $this->callbackUrl = $this->callbackUrl . '?redirect=' . $redirect_url;
        }

        return [
            'profile_id' => Config::get('payment.clickpay.profile_id'),
            'tran_type' => 'sale',
            'tran_class' => 'ecom',
            'cart_id' => strval($cart_id),
            'cart_description' => 'Dummy Order 35925502061445345',
            'cart_currency' => Config::get('payment.clickpay.currency'),
            'cart_amount' => $amount,
            'framed' => true,
            'hide_shipping' => true,
            'customer_details' => [
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'street1' => '404, 11th st, void',
                'city' => 'Dubai',
                'state' => 'DU',
                'country' => 'AE',
                'ip' => '*************',
            ],
            'callback' => $this->callbackUrl,
            'return' => $this->callbackUrl,
        ];
    }

    public function curlPostRequest(array $payload)
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => $this->server_key,
        ])->post($this->url, $payload);

        return $response->json();
    }

    public function signatureVerified(array $requestData): bool
    {
        $requestSignature = $requestData['signature'];
        unset($requestData['signature']);
        
        // Ignore empty values fields
        $request_fields = array_filter($requestData);
        // Sort form fields
        ksort($request_fields);
        // Generate URL-encoded query string of Post fields except signature field
        $query = http_build_query($request_fields);
        $signature = hash_hmac('sha256', $query, $this->server_key);

        return hash_equals($signature, $requestSignature);
    }
}
