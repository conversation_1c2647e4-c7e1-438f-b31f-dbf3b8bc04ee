<?php

namespace Modules\Payment\Traits;

use Modules\Invoice\app\Models\Invoice;
use Modules\Payment\Enums\PaymentStatusEnum;
use Modules\Payment\app\Models\KhaledsPayment;

trait PaymentSave
{

    /**
     * @return $this
     */
    public function saveToPayment($data)
    {
        $payment = KhaledsPayment::create(
            [
                "model_id" => auth()->id(),
                "model_table" => auth()->user()::class,
                "order_id" => $data['invoice_id'],
                "order_table" => $data['model'] ?? Invoice::class,
                "payment_method" => static::PAYMENT_METHOD,
                "payment_status" => PaymentStatusEnum::UNPAID,
                "transaction_code" => $data['transaction_code'],
                "payment_method_id"  => $data['payment_method_id'] ?? null,
                "payment_date" => $data['payment_date'] ?? now()->toDateString(),
                "amount" => $data['amount'],
                "notes" => $data['notes'] ?? null
            ]
        );
        return $payment;
    }

    /**
     * @return $this
     */

    public function updateToPayment(string $status, $payment)
    {
        $payment = KhaledsPayment::where('id', $payment)
            ->firstOrFail();

        $payment->payment_status = $status;
        $payment->save();

        return $payment->fresh();
    }


}
