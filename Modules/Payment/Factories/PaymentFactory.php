<?php

namespace Modules\Payment\Factories;

use Modules\Payment\Interfaces\IPaymentInterface;
use Modules\Payment\Interfaces\PaymentInterface;
use Modules\Payment\Classes;


class PaymentFactory
{


    /**
     *
     * get the payment class that the user want
     * if not exist return ex
     * @param string $name
     * @return PaymentInterface|Exception|IPaymentInterface
     * @throws Exception
     */

    public function get(string $name): PaymentInterface|IPaymentInterface|Exception
    {

        $className = 'Modules\Payment\Classes\\' . $name . 'Payment';
        if (class_exists($className))
            return new $className();

        throw new \Exception("Invalid gateway");
    }


}
