<?php

use Illuminate\Support\Facades\Route;
use Modules\Payment\app\Http\Controllers\PaymentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::resource('payment', PaymentController::class)->names('payment');
});
Route::get('verify-payment', action: [PaymentController::class, 'verify'])->name('verify-payment');
Route::get('payment/status/{status}', action: [PaymentController::class, 'success'])->name('payment.success');