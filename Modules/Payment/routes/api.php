<?php

use Illuminate\Support\Facades\Route;
use Modules\Payment\app\Http\Controllers\Api\PaymentController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->prefix('v1')->group(function () {
    Route::apiResource('payment', PaymentController::class)->names('payment');
    Route::get('pay', [PaymentController::class, 'pay'])->name('pay');
});
