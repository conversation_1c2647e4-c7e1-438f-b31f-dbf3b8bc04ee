<?php 

namespace Modules\Payment\database\seeders;

use Illuminate\Database\Seeder;
use Modules\Payment\app\Models\PaymentMethod;
use Modules\Payment\Enums\PaymentMethodsEnum;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

class PaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $paymentMethods = [
            PaymentMethodsEnum::CLICKPAY => [
                'name' => [
                    'en' => 'Clickpay',
                    'ar' => 'كليك باي',
                ],
                'description' => [
                    'en' => 'Payment via clickpay.',
                    'ar' => 'الدفع عن طريق كليك باي .',
                ],
                'method_type'=> PaymentMethodTypeEnum::ONLINE,
                'is_active' => true,
            ],
            PaymentMethodsEnum::CASH => [
                'name' => [
                    'en' => 'Cash',
                    'ar' => 'نقدي',
                ],
                'description' => [
                    'en' => 'Payment via Cash.',
                    'ar' => 'الدفع عن طريق طرق غير متصلة.',
                ],
                'method_type'=> PaymentMethodTypeEnum::OFFLINE,
                'is_active' => true,
            ],
            PaymentMethodsEnum::BANK_ACCOUNT => [
                'name' => [
                    'en' => 'Bank Account',
                    'ar' => 'حساب بنكي',
                ],
                'description' => [
                    'en' => 'Payment via Bank Account',
                    'ar' => 'الدفع عن طريق طرق متصلة.',
                ],
                'method_type'=> PaymentMethodTypeEnum::OFFLINE,
                'is_active' => true,
            ],
            PaymentMethodsEnum::BANK_TRANSFER => [
                'name' => [
                    'en' => 'Bank Transfer',
                    'ar' => 'تحويل بنكي',
                ],
                'description' => [
                    'en' => 'Payment via Bank Transfer.',
                    'ar' => 'الدفع عن طريق التحويل البنكي.',
                ],
                'method_type'=> PaymentMethodTypeEnum::OFFLINE,
                'is_active' => true,
            ],
            PaymentMethodsEnum::EJAR => [
                'name' => [
                    'en' => 'Ejar',
                    'ar' => 'إيجار',
                ],
                'description' => [
                    'en' => 'Payment via Ejar.',
                    'ar' => 'الدفع عبر الإنترنت باستخدام إيجار.',
                ],
                'method_type'=> PaymentMethodTypeEnum::OFFLINE,
                'is_active' => true,
            ],
        ];

        foreach ($paymentMethods as $method => $data) {
            $existingMethod = PaymentMethod::where('method', $method)->first();

            if ($existingMethod) {
                $existingMethod->update($data);
            } else {
                PaymentMethod::create(array_merge($data, ['method' => $method]));
            }
        }
    }
}