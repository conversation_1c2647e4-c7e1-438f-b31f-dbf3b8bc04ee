<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('khaleds_payments', function (Blueprint $table) {
            $table->unsignedBigInteger('payment_method_id')->nullable()->after('notes'); // Add the column

            $table->foreign('payment_method_id') // Set the foreign key
                ->references('id')
                ->on('khaleds_payment_methods')
                ->onDelete('set null'); // Optional: Decide the behavior on deletion
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('khaleds_payments', function (Blueprint $table) {
            $table->dropForeign(['payment_method_id']); // Drop the foreign key
            $table->dropColumn('payment_method_id');    // Drop the column
        });
    }
};
