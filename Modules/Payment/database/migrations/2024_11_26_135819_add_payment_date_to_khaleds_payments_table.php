<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('khaleds_payments', function (Blueprint $table) {
            // Add the 'payment_date' column with default current date
            $table->date('payment_date')->default(now()); // 'now()' returns the current date
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('khaleds_payments', function (Blueprint $table) {
            // Drop the 'payment_date' column if the migration is rolled back
            $table->dropColumn('payment_date');
        });
    }
};
