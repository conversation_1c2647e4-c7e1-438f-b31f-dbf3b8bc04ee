<?php

namespace Modules\Ticket\Enums;

enum TicketPriorityEnum: string
{
    const LOW = 'low';
    const MEDIUM = 'medium';
    const HIGH = 'high';


    public static function labels(): array
    {
        return [
            self::LOW => __('Low'),
            self::MEDIUM => __('Medium'),
            self::HIGH => __('High'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getTicketPriorityOptions(): array
    {
        return [
            self::LOW => __('Low'),
            self::MEDIUM => __('Medium'),
            self::HIGH => __('High'),
        ];
    }

    public static function getTicketPriorityColors(): array
    {
        return [
            self::LOW => '#28a745', // Green for Low
            self::MEDIUM => '#ffc107', // Yellow for Medium
            self::HIGH => '#dc3545', // Red for High
        ];
    }

    public static function getTicketPriorityValues(): array // New method
    {
        return [
            self::LOW,
            self::MEDIUM,
            self::HIGH,
        ];
    }
}
