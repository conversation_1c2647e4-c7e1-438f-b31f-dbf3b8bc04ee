<?php

namespace Modules\Ticket\Enums;

enum TicketStatusEnum: string
{
    const PENDING = 'pending';
    const OPEN = 'open';
    const RESOLVED = 'resolved';
    const CLOSED = 'closed';


    public static function labels(): array
    {
        return [
            self::PENDING => __('Pending'),
            self::OPEN => __('Open'),
            self::RESOLVED => __('Resolved'),
            self::CLOSED => __('Closed'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
    public static function getTicketStatusOptions(): array
    {
        return self::labels();
    }
    public static function getTicketStatusColors(): array
    {
        return [
            self::PENDING => '#28a745', // Green for Low
            self::OPEN => '#ffc107', // Yellow for Medium
            self::RESOLVED => '#dc3545', // Red for High
            self::CLOSED => '#6c757d', //Grey for Close
        ];
    }

    public static function getTicketStatusValues(): array // New method for values
    {
        return [
            self::PENDING,
            self::OPEN,
            self::RESOLVED,
            self::CLOSED,
        ];
    }
}
