<?php

namespace Modules\Ticket\Services;

use App\Models\User;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\Helpers\AccountHelper;
use Modules\Ticket\Repositories\TicketMessageRepository;

class TicketMessageService extends ServiceAbstract
{

    public function __construct(TicketMessageRepository $repository)
    {
        parent::__construct($repository);
    }

    public function notifyAdminsOnTicket($admins, $ticket) :void
    {
        $template = NotificationsTemplate::where(['key' => 'new_ticket_replay_customer'])->first();
        if ($template) {
            foreach ($admins as $admin) {
                SendNotification::make(['fcm-web', 'email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($admin->id)
                    ->findBody(['{account}', '{ticket}'])
                    ->replaceBody([auth()->user()->name ?? '', $ticket->uuid ?? ''])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }

}


