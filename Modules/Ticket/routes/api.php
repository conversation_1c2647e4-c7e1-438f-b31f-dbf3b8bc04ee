<?php

use Illuminate\Support\Facades\Route;
use Modules\Ticket\app\Http\Controllers\Api\TicketCategoryController;
use Modules\Ticket\app\Http\Controllers\Api\TicketController;
use Modules\Ticket\app\Http\Controllers\Api\TicketMessageController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/


Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(static function () {
});




Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(static function(){
    Route::apiResource("/ticket_category", TicketCategoryController::class);

});

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(static function(){
    Route::apiResource("/ticket", TicketController::class);
});

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(static function(){
    Route::apiResource("/ticket_message", TicketMessageController::class);
});
