<div class="space-y-8"
     x-data="{
         currentSlide: 0,
         itemsPerView: 3,
         preventClose: function(e) {
             e.stopPropagation();
             e.preventDefault();
         }
     }"
     @click.stop>
    @if($getRecord()->hasMedia('ticket_messages'))
        <div class="relative">
            @php
                $mediaItems = $getRecord()->getMedia('ticket_messages');
                $totalSlides = count($mediaItems);
                $maxSlides = max(0, $totalSlides - 3);
            @endphp

            @if($totalSlides > 3)
                <button
                    type="button"
                    @click.stop.prevent="preventClose($event); currentSlide = Math.max(0, Math.min(currentSlide - 1, {{ $maxSlides }}))"
                    class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <button
                    type="button"
                    @click.stop.prevent="preventClose($event); currentSlide = Math.max(0, Math.min(currentSlide + 1, {{ $maxSlides }}))"
                    class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg">
                    <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            @endif

            <div class="overflow-hidden">
                <div class="flex transition-transform duration-300 ease-in-out"
                     x-bind:style="{ transform: `translateX(-${currentSlide * (100/3)}%)` }">
                    @foreach($mediaItems as $index => $media)
                        <div class="w-1/3 flex-shrink-0 p-2">
                            @if(str_contains($media->mime_type, 'pdf'))
                                <div class="space-y-2">
                                    <div class="w-full aspect-[3/4] rounded-lg overflow-hidden bg-white">
                                        <embed
                                            src="{{ $media->getUrl() }}#view=FitH&scrollbar=0&toolbar=0&navpanes=0"
                                            type="application/pdf"
                                            width="100%"
                                            height="100%"
                                            class="w-full h-full object-cover"
                                        >
                                    </div>
                                    <div class="flex justify-end space-x-2">
                                        <a href="{{ $media->getUrl() }}"
                                           target="_blank"
                                           @click.stop.prevent="preventClose($event); window.open('{{ $media->getUrl() }}', '_blank')"
                                           class="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700">
                                            {{ __('View PDF') }}
                                        </a>
                                        <a href="{{ $media->getUrl() }}"
                                           download
                                           @click.stop.prevent="preventClose($event); window.location.href = '{{ $media->getUrl() }}'"
                                           class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                            {{ __('Download') }}
                                        </a>
                                    </div>
                                </div>
                            @else
                                <div class="space-y-2">
                                    <img src="{{ $media->getUrl() }}"
                                         alt="{{ $media->file_name }}"
                                         class="w-full aspect-[3/4] rounded-lg object-cover cursor-pointer"
                                         @click.stop.prevent="preventClose($event); window.open('{{ $media->getUrl() }}', '_blank')" />
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>

            @if($totalSlides > 3)
                <div class="flex justify-center mt-4 space-x-2">
                    @for($i = 0; $i <= $maxSlides; $i++)
                        <button
                            type="button"
                            @click.stop.prevent="preventClose($event); currentSlide = {{ $i }}"
                            class="w-2 h-2 rounded-full transition-colors duration-200"
                            :class="currentSlide === {{ $i }} ? 'bg-primary-600' : 'bg-gray-300'">
                        </button>
                    @endfor
                </div>
            @endif
        </div>
    @else
        <div class="text-center text-gray-500 py-4">
            {{ __('No attachments available') }}
        </div>
    @endif
</div>

<style>
    embed[type="application/pdf"] {
        margin: 0;
        max-width: 100%;
        height: 100%;
    }
</style>
