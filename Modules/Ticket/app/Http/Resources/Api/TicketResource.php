<?php

namespace  Modules\Ticket\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "uuid" => $this->uuid,
            "account_type" => $this->account_type,
            "account_id" => $this->account_id,
            "user_id" => $this->user_id,
            "ticket_category" =>TicketCategoryResource::make($this->ticketCategory),
            "title" => $this->title,
            "message" => $this->message,
            "ticket_messages" => TicketMessageResource::collection($this->ticket_messages),
            "priority" => $this->priority,
            "status" => $this->status,
            "created" => $this->created_at,
        ];

        return $data;

    }
}
