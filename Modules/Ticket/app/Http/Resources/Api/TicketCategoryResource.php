<?php

namespace  Modules\Ticket\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "ticket_category_id" => $this->ticket_category_id ?? null,
            "name" => $this->name ?? null,
            "created" => $this->created_at,
        ];

        return $data;

    }
}
