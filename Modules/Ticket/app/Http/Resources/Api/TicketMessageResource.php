<?php

namespace  Modules\Ticket\app\Http\Resources\Api;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
             "id" => $this->id ?? null,
             "ticket_id" => $this->ticket_id ?? null,
             "sender_type" => $this->sender_type ?? null,
             "sender_id" => $this->sender_id ?? null,
             "message" => $this->message ?? null,
             "created" => $this->created_at,
             "attachment" => $this->getFirstMedia('ticket_messages'),
        ];

        return $data;

    }
}
