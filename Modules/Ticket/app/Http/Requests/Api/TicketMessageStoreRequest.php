<?php

namespace  Modules\Ticket\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketMessageStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'ticket_id' => 'required|exists:tickets,id',
            'message' => 'required|max:65535',
            'attachment'=>"sometimes|mimes:jpeg,jpg,png,gif,pdf|max:10000",
        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['sender_id'] = auth()->id();
        $validated['sender_type'] = auth()->user()::class;
        return $validated;
    }
}
