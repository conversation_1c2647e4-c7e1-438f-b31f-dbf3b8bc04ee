<?php

namespace  Modules\Ticket\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketCategoryStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
                        'ticket_category_id' => 'nullable',
            'name' => 'required',
            'slug' => 'nullable|max:255|string',
            'is_active' => 'required'
        ];
        return $rules;
    }

}
