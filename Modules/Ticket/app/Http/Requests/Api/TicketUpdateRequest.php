<?php

namespace  Modules\Ticket\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
                        'user_id' => 'nullable',
            'ticket_category_id' => 'nullable',
            'uuid' => 'nullable|max:36|string',
            'account_type' => 'sometimes|max:255|string',
            'account_id' => 'sometimes',
            'title' => 'nullable|max:255|string',
            'message' => 'sometimes|max:65535',
            'priority' => 'sometimes|max:255|string',
            'status' => 'sometimes|max:255|string',
            'is_resolved' => 'sometimes',
            'is_locked' => 'sometimes'
        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['sender_id'] = auth()->id();
        $validated['sender_type'] = auth()->user()::class;
        return $validated;
    }
}
