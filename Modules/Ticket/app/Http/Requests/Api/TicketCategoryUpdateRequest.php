<?php

namespace  Modules\Ticket\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketCategoryUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
                        'ticket_category_id' => 'nullable',
            'name' => 'sometimes',
            'slug' => 'nullable|max:255|string',
            'is_active' => 'sometimes'
        ];
        return $rules;
    }


}
