<?php

namespace  Modules\Ticket\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'ticket_category_id' => 'required',
            'title' => 'nullable|max:255|string',
            'message' => 'required|max:65535',

        ];
        return $rules;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated();
        $validated['account_id'] = auth()->id();
        $validated['account_type'] = auth()->user()::class;
        return $validated;
    }
}
