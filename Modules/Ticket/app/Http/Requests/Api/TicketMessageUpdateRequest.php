<?php

namespace  Modules\Ticket\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class TicketMessageUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
                        'ticket_id' => 'nullable',
            'sender_type' => 'sometimes|max:255|string',
            'sender_id' => 'sometimes',
            'message' => 'sometimes|max:65535'
        ];
        return $rules;
    }


}
