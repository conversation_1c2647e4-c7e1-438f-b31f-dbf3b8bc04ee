<?php

namespace Modules\Ticket\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Ticket\app\Http\Requests\Api\TicketCategoryStoreRequest;
use Modules\Ticket\app\Http\Requests\Api\TicketCategoryUpdateRequest;
use Modules\Ticket\app\Http\Resources\Api\TicketCategoryResource;
use Modules\Ticket\Services\TicketCategoryService;

class TicketCategoryController extends ControllerAbstract
{


    protected string $jsonResourceClass = TicketCategoryResource::class;
    protected string $storeRequestClass = TicketCategoryStoreRequest::class;
    protected string $updateRequestClass = TicketCategoryUpdateRequest::class;
    protected array $filter = ['is_active' => 1];

    public function __construct(TicketCategoryService $service)
    {
        parent::__construct($service);
    }


}
