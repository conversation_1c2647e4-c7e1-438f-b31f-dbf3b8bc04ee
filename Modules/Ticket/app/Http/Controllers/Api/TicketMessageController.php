<?php

namespace Modules\Ticket\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Account\Helpers\AccountHelper;
use Modules\Ticket\app\Http\Requests\Api\TicketMessageStoreRequest;
use Modules\Ticket\app\Http\Requests\Api\TicketMessageUpdateRequest;
use Modules\Ticket\app\Http\Resources\Api\TicketMessageResource;
use Modules\Ticket\Services\TicketMessageService;
use Khaleds\Shared\Helpers\ApiResponse;
use Illuminate\Http\Request;

class TicketMessageController extends ControllerAbstract
{


    protected string $jsonResourceClass = TicketMessageResource::class;
    protected string $storeRequestClass = TicketMessageStoreRequest::class;
    protected string $updateRequestClass = TicketMessageUpdateRequest::class;


    public function __construct(TicketMessageService $service)
    {
        parent::__construct($service);
    }

    //todo refactor this method
    public function store(Request $request)
    {
        $request = app($this->storeRequestClass);
        $data = $this->service->create($request->validated());
        //notify admins
        $this->service->notifyAdminsOnTicket(AccountHelper::AdminUsers(), $data->ticket);
        if ($request->hasFile('attachment')) {
            $data->addMedia($request->file('attachment'))
                     ->toMediaCollection('ticket_messages');
        }

        return ApiResponse::data($this->jsonResourceClass::make($this->jsonResourceClass::make($data)));
    }
}
