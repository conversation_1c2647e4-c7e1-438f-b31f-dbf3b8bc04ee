<?php

namespace Modules\Ticket\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Ticket\app\Http\Requests\Api\TicketStoreRequest;
use Modules\Ticket\app\Http\Requests\Api\TicketUpdateRequest;
use Modules\Ticket\app\Http\Resources\Api\TicketResource;
use Modules\Ticket\Services\TicketService;

class TicketController extends ControllerAbstract
{


    protected string $jsonResourceClass = TicketResource::class;
    protected string $storeRequestClass = TicketStoreRequest::class;
    protected string $updateRequestClass = TicketUpdateRequest::class;


    public function __construct(TicketService $service)
    {
        parent::__construct($service);
    }

    public function index(Request $request)
    {
        $filter = ['account_id' => auth()->id()];
        $this->filter = $filter;
        return ApiResponse::data($this->jsonResourceClass::collection($this->service->paginate($this->filter)));
    }
}
