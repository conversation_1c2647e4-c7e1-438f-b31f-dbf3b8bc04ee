<?php

namespace Modules\Ticket\app\Filament\Resources\TicketCategoryResource\Components;


use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

class FormComponent
{

    public static function getForm() :array{

        return [
      TextInput::make('name.en')
                ->label(__('Name In English')) // Translation applied
                ->required()
                ->maxLength(255)
                ->default(null)
                ->columnSpan(1)
                ->translateLabel(), // Optional: For multilanguage support

            TextInput::make('name.ar')
                ->label(__('Name In Arabic')) // Translation applied
                ->maxLength(255)
                ->default(null)
                ->columnSpan(1)
                ->translateLabel(), // Optional

            Toggle::make('is_active')
                ->label(__('Active Status')) // Translation applied
                ->required(),

            Select::make('parent_id')
                ->relationship(name: 'parent', titleAttribute: 'name')
                ->label(__('Parent Category')) // Translation applied
                ->searchable(['name'])
                ->native(false)
                ->preload(),
        ];

    }
}
