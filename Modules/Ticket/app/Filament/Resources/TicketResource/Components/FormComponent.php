<?php

namespace Modules\Ticket\app\Filament\Resources\TicketResource\Components;


use App\Models\User;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Modules\Ticket\app\Models\TicketCategory;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Modules\Ticket\Enums\TicketStatusEnum;

class FormComponent
{

    public static function getForm() :array{

        return [
            Select::make('account_type')
                ->label(__('User Type')) // Make the label translatable
                ->required()
                //todo:note you are using model here not repo

                ->options(User::select('name', 'id')->pluck('name', 'id')) // Fetch all users, show name, store ID
                ->searchable(), // Make it searchable

            Select::make('account_id')
                ->label(__('User ID')) // Make the label translatable
                ->required()
                //todo:note you are using model here not repo

                ->options(User::select('name', 'id')->pluck('name', 'id')) // Fetch all users, show name, store ID
                ->searchable() // Make it searchable
                ->placeholder(__('Select an Account')), // Make the placeholder translatable

            Select::make('user_id')
                ->label(__('User')) // Make the label translatable
                ->required()
                //todo:note you are using model here not repo

                ->options(User::select('name', 'id')->pluck('name', 'id')) // Fetch all users, show name, store ID
                ->searchable() // Make it searchable
                ->placeholder(__('Select a user')),

            Select::make('ticket_category_id')
                ->label(__('Category')) // Make the label translatable
                ->required()
                //todo:note you are using model here not repo
                ->options(TicketCategory::where('is_active' , 1)->select('name', 'id')->pluck('name', 'id'))
                ->searchable() // Make it searchable
                ->placeholder(__('Select a category')), // Make the placeholder translatable

            TextInput::make('title')
                ->label(__('Title')) // Make the label translatable
                ->required()
                ->maxLength(255),

            Textarea::make('message')
                ->label(__('Message')) // Make the label translatable
                ->required()
                ->columnSpanFull(),

            Select::make('priority')
                ->label(__('Priority')) // Make the label translatable
                ->required()
                ->options(TicketPriorityEnum::getTicketPriorityOptions()) // Retrieve enum options from the model
                ->placeholder(__('Select Priority')), // Make the placeholder translatable

            Select::make('status')
                ->label(__('Status')) // Make the label translatable
                ->required()
                ->options(TicketStatusEnum::getTicketStatusOptions()) // Retrieve enum options from the model
                ->placeholder(__('Select Status')), // Make the placeholder translatable

            Section::make(__('Attachment')) // Make the section label translatable
            ->schema([
                SpatieMediaLibraryFileUpload::make('attachment') // Use Spatie Media Library upload
                ->image()
                ->maxSize(5120)
                    ->imageEditor()
                    ->collection('tickets') // Specify the media collection
                    ->imageEditorAspectRatios([
                        '16:9',
                        '4:3',
                        '1:1',
                    ]),
            ]),
        ];

    }
}
