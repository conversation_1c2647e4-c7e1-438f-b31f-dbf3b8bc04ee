<?php

namespace Modules\Ticket\app\Filament\Resources\TicketResource\Components;

use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\SelectColumn;
use Modules\Ticket\Enums\TicketStatusEnum;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Modules\Ticket\Services\TicketService;
use App\Models\User;

class TableComponent
{
    public static function getTable($table)
    {
        $ticketService = app(TicketService::class);
        return $table
            ->columns([
                TextColumn::make(name: 'uuid')
                ->label(__('Ticket ID'))
                ->searchable(),

                TextColumn::make(name: 'account_type') // Use the relationship to access the name
                ->label(__('Account Type')) // Make the label translatable
                ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'account_id') // Use the relationship to access the name
                ->label(__('Account')) // Make the label translatable
                ->sortable()
                    ->searchable(),

                SelectColumn::make('user_id')
                    ->label(__('User')) // Make the label translatable
                    ->options($ticketService->getUserNamesAndIds()) // Fetch all users, show name, store ID
                    ->sortable()
                    ->searchable() // Make the Select searchable
                    ->afterStateUpdated(function ($state, $record) use ($ticketService) {
                        if ($state) {
                            $ticketService->notifyAssignedUser($record);
                        }
                    }),
                TextColumn::make('ticketCategory.name') // Use the relationship to access the name
                    ->label(__('Category')) // Make the label translatable
                    ->sortable()
                    ->searchable(),

                TextColumn::make('title') // Use the title directly
                    ->label(__('Title')) // Make the label translatable
                    ->searchable(),

                SelectColumn::make('priority')
                    ->label(__('Priority'))
                    ->options(
                        TicketPriorityEnum::getTicketPriorityOptions()
                    )
                    ->searchable(),

                SelectColumn::make('status')
                    ->label(__('Status'))
                    ->options(
                        TicketStatusEnum::getTicketStatusOptions()
                    )
                    ->searchable()
                    ->afterStateUpdated(function ($state, $record) use ($ticketService) {
                        if ($state == TicketStatusEnum::CLOSED) {
                            $ticketService->notifyAfterCloseTicket($record);
                        }
                    }),

                TextColumn::make('created_at')
                    ->label(__('Created')) // Make the label translatable
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated')) // Make the label translatable
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->label(__('Deleted')) // Make the label translatable
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
