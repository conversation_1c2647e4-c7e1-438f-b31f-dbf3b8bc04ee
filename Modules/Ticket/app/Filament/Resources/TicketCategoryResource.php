<?php

namespace Modules\Ticket\app\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Ticket\app\Filament\Resources\TicketCategoryResource\Components\FormComponent;
use Modules\Ticket\app\Filament\Resources\TicketCategoryResource\Components\TableComponent;
use Modules\Ticket\app\Models\TicketCategory;

class TicketCategoryResource extends Resource
{
    protected static ?string $model = TicketCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => TicketCategoryResource\Pages\ListTicketCategories::route('/'),
            'create' => TicketCategoryResource\Pages\CreateTicketCategory::route('/create'),
            'view' => TicketCategoryResource\Pages\ViewTicketCategory::route('/{record}'),
            'edit' => TicketCategoryResource\Pages\EditTicketCategory::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationLabel(): string
    {
        return __("categories");
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Support Tickets');
    }

    public static function getBreadcrumb() : string
    {
        return __('Ticket Category');
    }
    public static function getModelLabel(): string
    {
        return __('Ticket Category');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Ticket Category');
    }
}
