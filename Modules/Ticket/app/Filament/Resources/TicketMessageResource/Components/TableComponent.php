<?php

namespace Modules\Ticket\app\Filament\Resources\TicketMessageResource\Components;

use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;

class TableComponent
{
    public static function getTable($table)
    {
        return $table
            ->columns([
                TextColumn::make('sender_type')
                    ->label(__('Sender Type')) // Add label and translation
                    ->searchable(),

                TextColumn::make('sender_id')
                    ->label(__('Sender')) // Add label and translation
                    ->numeric()
                    ->sortable(),

                TextColumn::make('ticket_id')
                    ->label(__('Ticket')) // Add label and translation
                    ->numeric()
                    ->sortable(),

                TextColumn::make('attachment')
                    ->label(__('Attachment')) // Add label and translation
                    ->searchable(),

                TextColumn::make('attachment_type')
                    ->label(__('Attachment Type')) // Add label and translation
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(label: __('Created')) // Add label and translation
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(label: __('Updated')) // Add label and translation
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->label(label: __('Deleted')) // Add label and translation
                    ->since()
                    ->sortable()
                    ->tooltip(tooltip: fn($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
