<?php

namespace Modules\Ticket\app\Filament\Resources\TicketMessageResource\Components;


use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Modules\Ticket\app\Models\Ticket;

class FormComponent
{

    public static function getForm($SENDER_TYPE) :array{

        return [
            TextInput::make('sender_type')
                ->label(__('Sender Type'))
                ->default($SENDER_TYPE) // Use the constant
                ->hidden() // Disable the input field
                ->required()
                ->maxLength(255),

            TextInput::make('sender_id')
                ->label(__('Sender'))
                ->default(fn () => auth()->user()->id) // Set the default to the current authenticated user
                ->hidden() // Disable the input field so it cannot be changed
                ->required()
                ->numeric(),

            Select::make('ticket_id')
                ->label(__('Ticket'))
                ->required()
                //todo:note you are using model here not repo
                ->options(Ticket::select('id', 'title')->pluck('title', 'id')) // Fetch all tickets and display title, store ID
                ->searchable() // Make the select searchable by title
                ->placeholder(__('select_ticket')) // Placeholder translation
                ->default(null),

            Textarea::make('message')
                ->label(__('Message'))
                ->required()
                ->columnSpanFull(),

            Section::make(__('Attachment'))
                ->schema([
                  SpatieMediaLibraryFileUpload::make('attachment') // Use Spatie Media Library upload
                    ->label(__('Attachment'))
                    ->maxSize(5120)
                        ->collection('ticket_messages') // Specify the media collection
                        ->imageEditorAspectRatios([
                            '16:9',
                            '4:3',
                            '1:1',
                        ]),
                ]),
        ];

    }
}
