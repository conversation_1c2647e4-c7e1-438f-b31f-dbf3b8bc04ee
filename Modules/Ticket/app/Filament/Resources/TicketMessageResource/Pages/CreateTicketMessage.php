<?php

namespace Modules\Ticket\app\Filament\Resources\TicketMessageResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Ticket\app\Filament\Resources\TicketMessageResource;

class CreateTicketMessage extends CreateRecord
{
    protected static string $resource = TicketMessageResource::class;

    protected function afterCreate(): void
    {
        $ticketMsg = $this->record;
        $template = NotificationsTemplate::where(['key' => 'new_ticket_replay_admin'])->first();
        if ($template) {
            SendNotification::make(['fcm-api', 'email'])
                ->template($template->key)
                ->model(Account::class)
                ->id($ticketMsg->ticket->account_id)
                ->findBody(['{account}', '{ticket}'])
                ->replaceBody([$ticketMsg->ticket->account->name ?? '', $ticketMsg->ticket->uuid ?? ''])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
    }
}
