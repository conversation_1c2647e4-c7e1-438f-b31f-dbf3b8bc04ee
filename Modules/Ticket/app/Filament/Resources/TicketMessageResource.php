<?php

namespace Modules\Ticket\app\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Ticket\app\Filament\Resources\TicketMessageResource\Components\FormComponent;
use Modules\Ticket\app\Filament\Resources\TicketMessageResource\Components\TableComponent;
use Modules\Ticket\app\Models\TicketMessage;

class TicketMessageResource extends Resource
{
    protected static bool $shouldRegisterNavigation = false;
    const SENDER_TYPE = 'App/Models/User';

    protected static ?string $model = TicketMessage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(FormComponent::getForm(self::SENDER_TYPE));
    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => TicketMessageResource\Pages\ListTicketMessages::route('/'),
            'create' => TicketMessageResource\Pages\CreateTicketMessage::route('/create'),
            'view' => TicketMessageResource\Pages\ViewTicketMessage::route('/{record}'),
            'edit' => TicketMessageResource\Pages\EditTicketMessage::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
