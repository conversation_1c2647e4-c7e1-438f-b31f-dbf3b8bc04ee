<?php

namespace Modules\Ticket\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Ad\app\Filament\Resources\AdResource;
use Modules\Ticket\app\Filament\Resources\TicketCategoryResource;
use Modules\Ticket\app\Filament\Resources\TicketMessageResource;
use Modules\Ticket\app\Filament\Resources\TicketResource;

class TicketPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Ticket';
    }

    public function getId(): string
    {
        return 'Ticket';
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                TicketResource::class,
                TicketCategoryResource::class,
                TicketMessageResource::class,
            ]);
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }


}
