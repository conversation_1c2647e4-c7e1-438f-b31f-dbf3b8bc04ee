<?php

namespace Modules\Ticket\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;

// Import HasMany
// Import BelongsTo

/**
 * @property integer $id
 * @property integer $ticket_category_id
 * @property mixed $name
 * @property string $slug
 * @property boolean $is_active
 * @property string $created_at
 * @property string $updated_at
 * @property TicketCategory $ticketCategory
 * @property Ticket[] $tickets
 */
class TicketCategory extends BaseModel
{
    use SoftDeletes;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['ticket_category_id', 'name', 'slug', 'is_active', 'created_at', 'updated_at'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'ticket_category_id');
    }

    public function parent():BelongsTo
    {
        return $this->belongsTo('Modules\Ticket\app\Models\TicketCategory', 'parent_id');
    }
}
