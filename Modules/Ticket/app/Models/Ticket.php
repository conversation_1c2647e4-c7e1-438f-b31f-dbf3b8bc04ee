<?php

namespace Modules\Ticket\app\Models;

use App\Models\User;
use Khaleds\Shared\Models\BaseModel;
use Modules\Accounts\Entities\Account;

/**
 * @property integer $id
 * @property integer $user_id
 * @property integer $ticket_category_id
 * @property string $uuid
 * @property string $account_type
 * @property integer $account_id
 * @property string $title
 * @property string $message
 * @property string $priority
 * @property string $status
 * @property boolean $is_resolved
 * @property boolean $is_locked
 * @property string $created_at
 * @property string $updated_at
 * @property TicketCategory $ticketCategory
 * @property User $user
 * @property TicketMessage[] $ticketMessages
 */
class Ticket extends BaseModel
{
    protected array $allowedFilters=['status'];
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['user_id', 'ticket_category_id', 'uuid', 'account_type', 'account_id', 'title', 'message', 'priority', 'status', 'is_resolved', 'is_locked', 'created_at', 'updated_at'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ticketCategory()
    {
        return $this->belongsTo('Modules\Ticket\app\Models\TicketCategory');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function ticket_messages()
    {
        return $this->hasMany(TicketMessage::class);
    }

    public function account(){
        return $this->morphTo();
    }
}
