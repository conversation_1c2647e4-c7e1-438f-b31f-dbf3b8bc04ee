<?php

namespace Modules\Ticket\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $ticket_id
 * @property string $sender_type
 * @property integer $sender_id
 * @property string $message
 * @property string $created_at
 * @property string $updated_at
 * @property Ticket $ticket
 */
class TicketMessage extends BaseModel
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['ticket_id', 'sender_type', 'sender_id', 'message', 'created_at', 'updated_at'];

    /**
     * @return BelongsTo
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo('Modules\Ticket\app\Models\Ticket');
    }

    public function sender(): MorphTo
    {
        return $this->morphTo();
    }
}
