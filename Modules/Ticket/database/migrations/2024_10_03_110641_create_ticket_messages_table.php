<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_messages', function (Blueprint $table) {
            $table->id(); // Primary key (auto-incrementing int)
            $table->morphs('sender'); 
            $table->unsignedBigInteger('ticket_id')->nullable(); // Ticket ID (int, nullable)
            $table->text('message'); // Message text (text, not nullable)

            // Add foreign keys if needed
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade')->onUpdate('cascade');
            $table->timestamps(); // This adds created_at and updated_at fields if you are using Laravel's default timestamps
            $table->softDeletes(); // Adds the deleted_at field for soft deletes
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_messages');
    }
};
