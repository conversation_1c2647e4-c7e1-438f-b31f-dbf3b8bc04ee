<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Modules\Ticket\Enums\TicketStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->uuid('uuid')->nullable(); // UUID field
            $table->morphs('account');
            $table->unsignedBigInteger('user_id')->nullable(); // user_id (int, nullable)
            $table->unsignedBigInteger('ticket_category_id')->nullable(); // ticket_category_id (int, nullable)
            $table->string('title'); // title (varchar(255))
            $table->text('message'); // message (text)
            $table->enum('priority', TicketPriorityEnum::getTicketPriorityValues())->default(TicketPriorityEnum::LOW); // Set default using the enum
            $table->enum('status', TicketStatusEnum::getTicketStatusValues())->default(TicketStatusEnum::PENDING); // status (enum)
            $table->foreign('user_id')->references('id')->on('users')->onDelete(action: 'cascade');
            $table->foreign('ticket_category_id')->references('id')->on('ticket_categories')->onDelete('cascade')->onUpdate('cascade');

            $table->timestamps(); // This adds created_at and updated_at fields if you are using Laravel's default timestamps
            $table->softDeletes(); // Adds the deleted_at field for soft deletes
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
