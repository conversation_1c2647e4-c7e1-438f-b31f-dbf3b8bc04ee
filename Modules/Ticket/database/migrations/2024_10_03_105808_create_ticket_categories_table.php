<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_categories', function (Blueprint $table) {
            $table->id(); // Primary key (int, auto-increment)
            $table->string('name')->nullable(); // Name (varchar(255), nullable)
            $table->boolean('is_active')->default(true); // Is active (tinyint(1), default true)
            $table->unsignedBigInteger('parent_id')->nullable()->default(null); // Parent ID (int, nullable, default 0)
            $table->foreign('parent_id')->references('id')->on('ticket_categories')->onDelete('cascade')->onUpdate('cascade'); // Adjust the onDelete action as needed
            $table->timestamps(); // This adds created_at and updated_at fields if you are using <PERSON><PERSON>'s default timestamps
            $table->softDeletes(); // Adds the deleted_at field for soft deletes
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_categories');
    }
};
