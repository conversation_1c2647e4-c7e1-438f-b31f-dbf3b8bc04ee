<?php

namespace Modules\Tenancy\app\Models\scopes;

use App\Enums\RoleEnum;
use App\Models\User;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Lab404\Impersonate\Services\ImpersonateManager;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TenantCreationScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (!session()->has('userTenancy')) {
            return;
        }
        $user = session()->get('userTenancy');
        if($user->hasRole(RoleEnum::ADMIN)){
            return;
        }
        $user->company_id = $user->company_id ?? $user->company?->id ;

        $builder->where($model->qualifyColumn('company_id'), $user->company_id);
        if ($user->hasRole(RoleEnum::BROKER)) {
            $this->applyBrokerScope($builder, $model, $user);
        }
    }

    protected function applyBrokerScope(Builder $builder, Model $model, $user)
    {
        $modelClass = get_class($model);
        $brokerFilter = config("tenancy.modelsBroker.{$modelClass}");

        if ($brokerFilter === null) {
            // Check for direct broker_id column
            if(Schema::hasColumn($model->getTable(), 'broker_id')) {
                $builder->where($model->qualifyColumn('broker_id'), $user->id);
                return;
            }
            return;
        }

        if (isset($brokerFilter['paths'])) {
            $this->applyMultiPathFilter($builder, $brokerFilter['paths'], $user);
        } elseif (isset($brokerFilter['morph'])) {
            $this->applyMorphFilter($builder, $brokerFilter, $user);
        } else {
            $this->applyNestedBrokerFilter($builder, $brokerFilter, $user);
        }
    }

    protected function applyMorphFilter(Builder $builder, array $filter, $user)
    {
        $relation = $filter['relation'];
        $morphConfigs = $filter['morph'];

        $builder->where(function ($query) use ($relation, $morphConfigs, $user) {
            foreach ($morphConfigs as $morphClass => $morphConfig) {
                $query->orWhereHasMorph($relation, [$morphClass], function ($morphQuery) use ($morphConfig, $user) {
                    if (isset($morphConfig['relation'])) {
                        $this->applyNestedBrokerFilter($morphQuery, $morphConfig, $user);
                    } else {
                        $column = $morphConfig['column'] ?? config("tenancy.default.column");
                        $morphQuery->where($column, $user->id);
                    }
                });
            }
        });
    }
    protected function applyNestedBrokerFilter(Builder $builder, array $filter, $user)
    {
        $relation = $filter['relation'] ?? null;
        $column = $filter['column'] ?? config("tenancy.default.column");
        $nested = $filter['nested'] ?? null;

        if (!$relation) {
            return;
        }

        $builder->whereHas($relation, function ($query) use ($user, $column, $nested) {
            if ($nested) {
                $this->applyNestedBrokerFilter($query, $nested, $user);
            } else {
                $query->where($column, $user->id);
            }
        });
    }

    protected function applyMultiPathFilter(Builder $builder, array $paths, $user)
    {
        $builder->where(function ($query) use ($paths, $user) {
            foreach ($paths as $index => $path) {
                $method = $index === 0 ? 'whereHas' : 'orWhereHas';
                $query->{$method}($path['relation'], function ($subQuery) use ($path, $user) {
                    $subQuery->where(
                        $path['column'] ?? config("tenancy.default.column"),
                        $user->id
                    );
                });
            }
        });
    }

    public function extend(Builder $builder)
    {
//        $builder->macro('withoutTenancy', function (Builder $builder) {
//            return $builder->withoutGlobalScope($this);
//        });
    }

}
