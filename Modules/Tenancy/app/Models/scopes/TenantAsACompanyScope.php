<?php

namespace Modules\Tenancy\app\Models\scopes;

use App\Enums\RoleEnum;
use App\Models\User;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Lab404\Impersonate\Services\ImpersonateManager;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Laravel\Sanctum\Guard;

class TenantAsACompanyScope implements Scope
{

    private int|null $userID;
    private Model $user;

    private function isAuthedUser(): bool
    {

        $this->userID = Session::get('login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82');
        if (is_null($this->userID))
            return false;

        $this->user = $this->getUser();
        return true;
    }


    private function getUser()
    {
        return \Modules\Tenancy\app\Models\User::withoutGlobalScopes()->find($this->userID);
    }


    public function getCompany(): Model|null
    {
        if ($this->isAuthedUser())
            return $this->user->company;

        return null;
    }

    public function apply(Builder $builder, Model $model)
    {

        if ($this->isAuthedUser() && !request()->routeIs('api.*')) {
            if (isset($this->user) && !is_null($this->user->company)) {
                if ($model::class == Role::class) {
                    $builder->where($model->getTable() . '.company_id', $this->user->company?->id)->orWhere('companies', true);
                } else {
                    $builder->where($model->getTable() . '.company_id', $this->user->company?->id);
                }
            }
        }
    }


    public function extend(Builder $builder)
    {

        $company = $this->getCompany();
        $builder->getModel()::creating(function (Model $model) use ($company){
            if ($company) {
                $model->company_id = $company->id;
            }
        });
    }

}
