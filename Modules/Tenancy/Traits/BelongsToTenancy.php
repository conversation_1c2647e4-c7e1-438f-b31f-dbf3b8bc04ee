<?php
declare(strict_types=1);

namespace Modules\Tenancy\Traits;


use App\Enums\RoleEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Modules\Tenancy\app\Models\scopes\TenantAsACompanyScope;
use Modules\Tenancy\app\Models\scopes\TenantCreationScope;

trait BelongsToTenancy
{

    public static function bootBelongsToTenancy()
    {
        static::addGlobalScope( new TenantCreationScope());

        static::creating(function ($model) {
                if (auth()->check() && !auth()->user()->hasRole(RoleEnum::ADMIN) &&
                  (!is_null(auth()->user()->company?->id) || !is_null(auth()->user()->company_id))) {
                if(Schema::hasColumn($model->getTable(), 'company_id')) {
                    $model->company_id = auth()->user()->company ? auth()->user()->company->id : auth()->user()->company_id;
                }
            }
        });
    }
}
