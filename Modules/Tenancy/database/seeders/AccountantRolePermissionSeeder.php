<?php

namespace Modules\Tenancy\database\seeders;

use App\Enums\RoleEnum;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AccountantRolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $role = Role::firstOrCreate(['name' => RoleEnum::ACCOUNTANT,'guard_name'=>'web']);

        $role->update(['companies'=>1]);

        $permissions = [
            'view_invoice',
           'view_any_invoice',
            'create_invoice',
            'update_invoice',
            'restore_invoice',
            'restore_any_invoice',
            'replicate_invoice',
            'reorder_invoice',
            'delete_invoice',
            'delete_any_invoice',
            'force_delete_invoice',
            'force_delete_any_invoice'
            ];

        foreach ($permissions as $permission) {
            $permissionInstance = Permission::where(['name' => $permission, 'guard_name' => 'web'])->first();
            if($permissionInstance){
                $role->givePermissionTo($permissionInstance);
            }
        }
    }
}
