<?php

namespace Modules\Tenancy\database\seeders;

use App\Enums\RoleEnum;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DefaultCompanyOwnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create role if it doesn't exist
        $role = Role::firstOrCreate(['name' => RoleEnum::OWNER,'guard_name'=>'web']);
        $role->update(['companies'=>1]);
        // Define permissions with their names
        $permissions = [
            'view_ad',
            'view_any_ad',
            'create_ad',
            'update_ad',
            'restore_ad',
            'restore_any_ad',
            'replicate_ad',
            'reorder_ad',
            'delete_ad',
            'delete_any_ad',
            'force_delete_ad',
            'force_delete_any_ad',
            'view_property::type',
            'view_any_property::type',
            'create_property::type',
            'update_property::type',
            'restore_property::type',
            'restore_any_property::type',
            'replicate_property::type',
            'reorder_property::type',
            'delete_property::type',
            'delete_any_property::type',
            'force_delete_property::type',
            'force_delete_any_property::type',
            'view_role',
            'view_any_role',
            'create_role',
            'update_role',
            'delete_role',
            'delete_any_role',
            'view_usability',
            'view_any_usability',
            'create_usability',
            'update_usability',
            'restore_usability',
            'restore_any_usability',
            'replicate_usability',
            'reorder_usability',
            'delete_usability',
            'delete_any_usability',
            'force_delete_usability',
            'force_delete_any_usability',
            'view_user',
            'view_any_user',
            'create_user',
            'update_user',
            'restore_user',
            'restore_any_user',
            'replicate_user',
            'reorder_user',
            'delete_user',
            'delete_any_user',
            'force_delete_user',
            'force_delete_any_user',
            'view_amenities',
            'view_any_amenities',
            'create_amenities',
            'update_amenities',
            'restore_amenities',
            'restore_any_amenities',
            'replicate_amenities',
            'reorder_amenities',
            'delete_amenities',
            'delete_any_amenities',
            'view_ejar::company::key',
            'view_any_ejar::company::key',
            'create_ejar::company::key',
            'update_ejar::company::key',
        ];

        // Create permissions and assign them to the role
        foreach ($permissions as $permission) {
            $permissionInstance = Permission::where([
                'name' => $permission,
                'guard_name' => 'web',
            ])->first();
            if($permissionInstance){
                $role->givePermissionTo($permissionInstance);
            }
        }
    }
}
