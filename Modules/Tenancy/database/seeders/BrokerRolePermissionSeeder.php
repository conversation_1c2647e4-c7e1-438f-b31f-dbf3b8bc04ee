<?php

namespace Modules\Tenancy\Database\Seeders;

use App\Enums\RoleEnum;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class BrokerRolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create broker role if it doesn't exist
        $role = Role::firstOrCreate(['name' => RoleEnum::BROKER,'guard_name'=>'web']);

        $role->update(['companies'=>1]);

        // Define all permissions
        $permissions = [
            'view_property::type',
            'view_any_property::type',
            'create_property::type',
            'update_property::type',
            'restore_property::type',
            'restore_any_property::type',
            'replicate_property::type',
            'reorder_property::type',
            'delete_property::type',
            'delete_any_property::type',
            'force_delete_property::type',
            'force_delete_any_property::type',
            'view_usability',
            'view_any_usability',
            'create_usability',
            'update_usability',
            'restore_usability',
            'restore_any_usability',
            'replicate_usability',
            'reorder_usability',
            'delete_usability',
            'delete_any_usability',
            'force_delete_usability',
            'force_delete_any_usability',
            'view_amenities',
            'view_any_amenities',
            'create_amenities',
            'update_amenities',
            'restore_amenities',
            'restore_any_amenities',
            'replicate_amenities',
            'reorder_amenities',
            'delete_amenities',
            'delete_any_amenities',
            'force_delete_amenities',
            'force_delete_any_amenities',
            'view_amenities::category',
            'view_any_amenities::category',
            'create_amenities::category',
            'update_amenities::category',
            'restore_amenities::category',
            'restore_any_amenities::category',
            'replicate_amenities::category',
            'reorder_amenities::category',
            'delete_amenities::category',
            'delete_any_amenities::category',
            'force_delete_amenities::category',
            'force_delete_any_amenities::category',
            'view_attribute',
            'view_any_attribute',
            'create_attribute',
            'update_attribute',
            'restore_attribute',
            'restore_any_attribute',
            'replicate_attribute',
            'reorder_attribute',
            'delete_attribute',
            'delete_any_attribute',
            'force_delete_attribute',
            'force_delete_any_attribute',
            'view_document::type',
            'view_any_document::type',
            'create_document::type',
            'update_document::type',
            'restore_document::type',
            'restore_any_document::type',
            'replicate_document::type',
            'reorder_document::type',
            'delete_document::type',
            'delete_any_document::type',
            'force_delete_document::type',
            'force_delete_any_document::type',
            'view_property',
            'view_any_property',
            'create_property',
            'update_property',
            'restore_property',
            'restore_any_property',
            'replicate_property',
            'reorder_property',
            'delete_property',
            'delete_any_property',
            'force_delete_property',
            'force_delete_any_property',
            'view_accounts',
            'view_any_accounts',
            'create_accounts',
            'update_accounts',
            'restore_accounts',
            'restore_any_accounts',
            'replicate_accounts',
            'reorder_accounts',
            'delete_accounts',
            'delete_any_accounts',
            'force_delete_accounts',
            'force_delete_any_accounts',
            'view_document',
            'view_any_document',
            'create_document',
            'update_document',
            'restore_document',
            'restore_any_document',
            'replicate_document',
            'reorder_document',
            'delete_document'
        ];

        // Create permissions if they don't exist and assign them to the broker role
        foreach ($permissions as $permission) {
            $permissionInstance = Permission::where(['name' => $permission, 'guard_name' => 'web'])->first();
            if($permissionInstance){
                $role->givePermissionTo($permissionInstance);
            }
        }
    }
}
