<?php

namespace Modules\Tenancy\database\seeders;

use Illuminate\Database\Seeder;
use Modules\Tenancy\database\seeders\DefaultCompanyOwnerSeeder;

class TenancyDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         $this->call([
             DefaultCompanyOwnerSeeder::class,
             CompanyTableColumnSeeder::class,
             BrokerRolePermissionSeeder::class,
             AccountantRolePermissionSeeder::class,
         ]);
    }
}
