<?php

namespace Modules\Tenancy\database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
class CompanyTableColumnSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tables = config('tenancy.company_tables', []);

        foreach ($tables as $tableName) {
            if (!Schema::hasTable($tableName)) {
                $this->command->warn("Table '{$tableName}' does not exist!");
                continue;
            }

            if (!Schema::hasColumn($tableName, 'company_id')) {
                $this->command->info("Adding company_id column to '{$tableName}' table...");

                Schema::table($tableName, function (Blueprint $table) {
                    $table->unsignedBigInteger('company_id')->nullable();
                    $table->foreign('company_id')
                        ->references('id')
                        ->on('companies')
                        ->onDelete('cascade');
                });

                $this->command->info("Successfully added company_id column to '{$tableName}' table.");
            } else {
                $this->command->info("Table '{$tableName}' already has company_id column.");
            }
        }
    }
}
