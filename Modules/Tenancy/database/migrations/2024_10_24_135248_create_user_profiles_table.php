<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id(); // This creates bigint(20) UNSIGNED AUTO_INCREMENT
            $table->unsignedBigInteger('user_id'); // Matches the users.id type
            $table->string('first_name')->nullable();
            $table->string('second_name')->nullable();
            $table->string('third_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone_number', 50)->nullable();
            $table->string('avatar_url')->nullable();
            $table->string('national_id')->nullable();
            $table->date('birth_date')->nullable();
            $table->longText('bank_account')->nullable();
            $table->timestamps();

            // Create foreign key relationship
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');

            // Add index for better query performance
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profiles');
    }
};
