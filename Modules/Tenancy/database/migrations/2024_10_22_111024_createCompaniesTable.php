<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Check if table doesn't exist before trying to modify it
        if (!Schema::hasTable('companies')) {
            Schema::create('companies', function (Blueprint $table) {
                $table->id();
                $table->string('name')->nullable();
                $table->foreignId('user_id')->nullable();
                $table->string('phone')->nullable();
                $table->string('email')->nullable();
                $table->text('address')->nullable();
                $table->string('comp_unified_file')->nullable();
                $table->string('comp_unified_number')->nullable();
                $table->string('website_url')->nullable();
                $table->string('logo')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        } else {
            // If table exists, just add the new columns
            Schema::table('companies', function (Blueprint $table) {
                if (!Schema::hasColumn('companies', 'phone')) {
                    $table->string('phone')->nullable();
                }
                if (!Schema::hasColumn('companies', 'email')) {
                    $table->string('email')->nullable();
                }
                if (!Schema::hasColumn('companies', 'address')) {
                    $table->text('address')->nullable();
                }
                if (!Schema::hasColumn('companies', 'comp_unified_file')) {
                    $table->string('comp_unified_file')->nullable();
                }
                if (!Schema::hasColumn('companies', 'comp_unified_number')) {
                    $table->string('comp_unified_number')->nullable();
                }
                if (!Schema::hasColumn('companies', 'website_url')) {
                    $table->string('website_url')->nullable();
                }
                if (!Schema::hasColumn('companies', 'logo')) {
                    $table->string('logo')->nullable();
                }
            });
        }
    }

    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'email',
                'address',
                'comp_unified_file',
                'comp_unified_number',
                'website_url',
                'logo',
            ]);
        });
    }
};
