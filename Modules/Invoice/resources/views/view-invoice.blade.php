<?php
 $currency = '<span class="icon-saudi_riyal"></span>';
?>


<div>
    <x-filament-panels::page>
        <x-filament::section>
            <!-- Header Section -->
            <!-- Top Header with Invoice Title and Status -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700 mb-8">
                <div class="space-y-1">
                    @php
                        $logoPath = 'storage/' . $record->company?->logo;
                        $logoExists = $record->company?->logo && file_exists(public_path($logoPath));
                        $logoUrl = $logoExists ? asset($logoPath) : asset('images/Kera_light_logo.png');
                    @endphp

                    <img
                        src="{{ $logoUrl }}"
                        alt="{{ $logoExists ? $record->company?->name ?? __('KERA LOGO') : 'KERA LOGO' }}"
                        width="50"
                    />
                    <h2 class="text-2xl font-bold text-gray-950 dark:text-white">{{__('Invoice Number')}}</h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{$this->getRecord()->ejar_number}}</p>
                </div>
                <div class="flex flex-col items-end">
        <span class="inline-flex items-center justify-center rounded-xl bg-success-50 px-3 py-1 text-sm font-medium text-success-600 dark:bg-success-400/10 dark:text-success-400">
             {{ \Modules\Invoice\Enums\InvoiceStatusEnum::getLabel($this->getRecord()->status) }}
        </span>
                    @if(json_decode($this->getRecord()->extra, true)['lease_id'] ?? false)
                        <span class="text-sm text-gray-500 dark:text-gray-400 mt-1"> {{ __('Lease Number') }}: {{ json_decode($this->getRecord()->extra, true)['lease_id'] }}</span>
                    @endif
                    <span class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ __('Issue Date') }}: {{$this->getRecord()->created_at->toDateString()}}</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{__('Due Date')}}: {{$this->getRecord()->due_date}}</span>
                </div>
            </div>
            <!-- Main Header Grid -->
            <div class="grid grid-cols-1 md:grid-cols-1 gap-6 mb-8">
                <!-- From Info Card -->
                <div class="group rounded-xl bg-white dark:bg-gray-900 overflow-hidden border border-gray-100 dark:border-gray-800">
                    <div class="relative">
                        <div class="absolute top-0 left-0 right-0 h-1 bg-primary-500"></div>
                        <div class="p-5">
                            <!-- Card Header -->
                            <div class="flex items-center gap-2 mb-4">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{__('From')}}</h3>
                            </div>

                            <!-- Names Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                @if($record->invoice_type === Modules\Invoice\Enums\InvoiceTypeEnum::SCHEDULE)
                                    {{ Modules\Invoice\Services\InvoiceService::getLeaseMembers($this->getRecord()->id , Modules\Lease\Enums\LeaseMemberTypesEnum::LESSOR) }}
                                @elseif($record->invoice_type === Modules\Invoice\Enums\InvoiceTypeEnum::MAINTENANCE_REQUEST)
                                    {{ Modules\Invoice\Services\InvoiceService::getInvoiceCompany($this->getRecord()->id) }}
                                @else
                                    {{ Modules\Invoice\Services\InvoiceService::fetchInvoiceMembers($this->getRecord()->from_type, [$this->getRecord()->id]) }}
                                @endif

                            </div>
                        </div>
                    </div>
                </div>

                <!-- To Info Card -->
                <div class="group rounded-xl bg-white dark:bg-gray-900 overflow-hidden border border-gray-100 dark:border-gray-800">
                    <div class="relative">
                        <div class="absolute top-0 left-0 right-0 h-1 bg-primary-500"></div>
                        <div class="p-5">
                            <!-- Card Header -->
                            <div class="flex items-center gap-2 mb-4">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{__('To')}}</h3>
                            </div>
                            <!-- Names Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            {{ Modules\Invoice\Services\InvoiceService::getLeaseMembers($this->getRecord()->id , Modules\Lease\Enums\LeaseMemberTypesEnum::TENANT) }}
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Main Body Tables -->
            <div class="space-y-6">
                <!-- Table 1 -->
                <div class="space-y-2">
                    <div class="space-y-4">
                        <!-- Enhanced Header -->
                        <div class="px-4 space-y-4">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-lg bg-indigo-50 dark:bg-indigo-900/50 flex items-center justify-center">
                                        <svg class="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-lg font-semibold text-gray-950 dark:text-white">{{__('Products & Services')}}</h4>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{__('Detailed breakdown of items purchased and their costs')}}</p>
                                    <!-- Quick Stats -->
                                    <div class="mt-3 flex space-x-6">
                                        <div class="flex items-center text-sm">
                                            <svg class="mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                            </svg>
                                            {{ count($this->getRecord()->items) }} {{__('items')}}

                                        </div>
                                        <div class="flex items-center text-sm">
                                            <svg class="mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            {{ number_format(($this->getRecord()->total - $this->getRecord()->discount) + ($this->getRecord()->tax), 2) }} {!! $currency !!}
                                            {{__('Total')}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                            <div class="overflow-x-auto">
                                <table class="w-full table-auto divide-y divide-gray-200 dark:divide-white/5">
                                    <thead class="bg-gray-50 dark:bg-white/5">
                                    <tr>
                                        <th class="px-4 py-3.5 text-left text-sm font-medium text-gray-950 dark:text-white">{{__('Type')}}</th>
                                        <th class="px-4 py-3.5 text-left text-sm font-medium text-gray-950 dark:text-white">{{__('Description')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Quantity')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Price')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Tax')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Vat')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Discount')}}</th>
                                        <th class="px-4 py-3.5 text-right text-sm font-medium text-gray-950 dark:text-white">{{__('Total')}}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-white/5">
                                        @foreach($this->getRecord()->items as $item)
                                        <tr class="hover:bg-gray-50 dark:hover:bg-white/5 transition-colors">
                                            <td class="px-4 py-4 text-sm text-gray-900 dark:text-white">
                                            @if($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::SERVICE)
                                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceName($item->item_id) }}
                                            @elseif($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::COMMERCIAL_SERVICE)
                                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceCommercialServiceName($item->item_id) }}
                                            @else
                                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getLabel($item->type) }}
                                            @endif
                                            </td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ $item->description }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->qty, 2) }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->price, 2) }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->tax, 2) }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->vat, 2) }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->discount, 2) }}</td>
                                            <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-right">{{ number_format($item->total, 2) }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                    <tfoot class=" dark:divide-white/5">
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{ __('Sub Total') }}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->total, 2) }}<small class="text-sm font-normal"> {!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{__('Tax')}}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->tax, 2) }}<small class="text-sm font-normal">{!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{__('Vat')}}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->vat, 2) }}<small class="text-sm font-normal">{!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{__('Discount')}}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->discount, 2) }}<small class="text-sm font-normal">{!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{trans('Paid')}}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->paid, 2) }}<small class="text-sm font-normal">{!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">{{__('Remaining Amount')}}:</td>
                                        <td class="px-4 py-3.5 text-sm font-medium text-gray-900 dark:text-white text-right">
                                            {{ number_format($this->getRecord()->remaining, 2) }}<small class="text-sm font-normal">{!! $currency !!}</small>
                                        </td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    </div>

                <!-- Table 2 -->
                <div class="space-y-2">
                    <div class="space-y-4">
                        <!-- Enhanced Header -->
                        <div class="px-4 space-y-4">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-lg bg-indigo-50 dark:bg-indigo-900/50 flex items-center justify-center">
                                        <svg class="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="text-lg font-semibold text-gray-950 dark:text-white">{{__('payments')}}</h4>
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{__('Detailed breakdown of payments')}}</p>
                                    <!-- Quick Stats -->
                                    <div class="mt-3 flex space-x-6">
                                        <div class="flex items-center text-sm">
                                            <svg class="mr-1.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                            </svg>
                                            {{ count($this->getRecord()->payments) }} {{__('payments')}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                            <div class="overflow-x-auto">
                                <table class="w-full table-auto divide-y divide-gray-200 dark:divide-white/5">
                                    <thead class="bg-gray-50 dark:bg-white/5">
                                    <tr>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Reference Number')}}</th>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Payment Method')}}</th>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Payment Status')}}</th>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Amount')}}</th>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Payment Date')}}</th>
                                        <th class="px-1 py-3.5 text-center text-sm font-medium text-gray-950 dark:text-white">{{__('Commends')}}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-white/5">
                                    @foreach($this->getRecord()->payments as $payment)
                                        <tr class="hover:bg-gray-50 dark:hover:bg-white/5 transition-colors">
                                            <td class="px-1 py-4 text-center text-sm text-gray-900 dark:text-white">{{ $payment->transaction_code }}</td>
                                            <td class="px-1 py-4 text-center text-sm text-gray-900 dark:text-white">
                                                <span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700">
                                                    {{ $payment->paymentMethod?->name ?? __('None') }}
                                                </span>
                                            </td>
                                            <td class="px-1 py-4 text-sm text-center">
                                                <span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-50 text-green-700">
                                                    {{ $payment->payment_status }}
                                                </span>
                                            </td>
                                            <td class="px-1 py-4 text-sm text-gray-500 text-center dark:text-gray-400 ">
                                                {{ number_format($payment->amount, 2) }} <span class="icon-saudi_riyal"></span>
                                            </td>
                                            <td class="px-1 py-4 text-sm text-gray-900 dark:text-white text-center">{{ $payment->payment_date }}</td>
                                            <td class="px-1 py-4 text-sm text-center">
                                                <div class="flex justify-center space-x-1">
                                                    <a href="{{ route('payment.receipt.print', $payment->id) }}" target="_blank"
                                                       class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                                                        </svg>
                                                        {{ __('Print') }}
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">{{__('This is an official payment receipt. Thank you for your business.')}}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{__('For any inquiries, please contact our support team.')}}</p>
            </div>
        </x-filament::section>

        <div class="no-print">
            @php
                $relationManagers = $this->getRelationManagers();
                $hasCombinedRelationManagerTabsWithContent = $this->hasCombinedRelationManagerTabsWithContent();
            @endphp
            @if (count($relationManagers))
                <x-filament-panels::resources.relation-managers
                    :active-locale="isset($activeLocale) ? $activeLocale : null"
                    :active-manager="$this->activeRelationManager ?? ($hasCombinedRelationManagerTabsWithContent ? null : array_key_first($relationManagers))"
                    :content-tab-label="$this->getContentTabLabel()"
                    :content-tab-icon="$this->getContentTabIcon()"
                    :content-tab-position="$this->getContentTabPosition()"
                    :managers="$relationManagers"
                    :owner-record="$record"
                    :page-class="static::class"
                >
                    @if ($hasCombinedRelationManagerTabsWithContent)
                        <x-slot name="content">
                            @if ($this->hasInfolist())
                                {{ $this->infolist }}
                            @else
                                {{ $this->form }}
                            @endif
                        </x-slot>
                    @endif
                </x-filament-panels::resources.relation-managers>
            @endif
        </div>
    </x-filament-panels::page>


    <style type="text/css" media="print">
        .fi-section-content-ctn {
            padding: 0 !important;
            border: none !important;
        }
        .fi-section {
            border: none !important;
            box-shadow: none !important;
        }
        .fi-section-content {
            border: none !important;
            box-shadow: none !important;
        }
        .fi-main {
            margin: 0 !important;
            padding: 0 !important;
            background-color: white !important;
            color: black !important;
        }
        .no-print { display: none; !important; }
        .fi-header { display: none; !important; }
        .fi-topbar { display: none; !important; }
        .fi-sidebar { display: none; !important; }
        .fi-sidebar-close-overlay { display: none; !important; }
            .invoice-table table tr{
                display: flex;
                gap: 10px;
            }
            @media print {
                .filament-footer{
                    display: none;
                }
            }
    </style>

</div>
