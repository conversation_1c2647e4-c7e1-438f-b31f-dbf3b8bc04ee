<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <style>
        
    </style>
</head>
<body>
    @if(auth()->user() == null || auth()->user()->lang === 'en')
    <div dir="ltr">
        <!-- English Invoice -->
        <div class="invoice-header">
            <h1>INVOICE</h1>
            <p>Invoice Number: {{ $invoice->uuid }}</p>
        </div>

        <div class="invoice-details">
            <div style="float: left;">
                <strong>From:</strong><br>
                {{ Modules\Invoice\Services\InvoiceService::getLeaseMembers($invoice->id , Modules\Lease\Enums\LeaseMemberTypesEnum::LESSOR) }}
                <br>
            </div>
            <div style="float: right;">
                <strong>To:</strong><br>
                {{ Modules\Invoice\Services\InvoiceService::getLeaseMembers($invoice->id , Modules\Lease\Enums\LeaseMemberTypesEnum::TENANT) }}
                <br>
            </div>
            <div style="clear: both;"></div>
        </div>

        <div>
            <h3 style="text-align: center; margin-bottom: 20px;">Invoice Items</h3>
            <table>
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($items as $item)
                    <tr>
                        <td>
                            @if($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::SERVICE)
                                {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceName($item->item_id) }}
                            @elseif($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::COMMERCIAL_SERVICE)
                                {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceCommercialServiceName($item->item_id) }}
                            @else
                                {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getLabel($item->type) }}
                            @endif
                        </td>
                        <td>{{ number_format($item->price, 2) }}</td>
                        <td>{{ number_format($item->total, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        @if($payments->count() > 0)
        <div>
            <h3 style="text-align: center; margin-bottom: 20px;">Payment Details</h3>
            <table>
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Payment Status</th>
                        <th>Transaction Code</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($payments as $payment)
                    <tr>
                        <td>{{ $payment->paymentMethod->name }}</td>
                        <td>{{ $payment->payment_status }}</td>
                        <td>{{ $payment->transaction_code }}</td>
                        <td>{{ number_format($payment->amount, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif
        <div class="total-section" style="text-align: left; direction: ltr;">
            <p><strong>Subtotal:</strong> {{ number_format($invoice->total, 2) }}</p>
            <p><strong>Tax:</strong> {{ number_format($invoice->tax, 2) }}</p>
            <p><strong>discount:</strong> {{ number_format($invoice->discount, 2) }}</p>
            <p><strong>Total:</strong> {{ number_format(($invoice->total - $invoice->discount + $invoice->tax), 2) }}</p>
            <p><strong>Paid:</strong> {{ number_format($invoice->paid, 2) }}</p>
            <p><strong>Remaining:</strong> {{ number_format($invoice->remaining, 2) }}</p>
        </div>
    </div>
    @else
    <div dir="rtl">
    <!-- Arabic Invoice -->
    <div class="invoice-header" style="text-align: right;">
        <h1>فاتورة</h1>
        <p>الفاتورة: {{ $invoice->uuid }}</p>
    </div>

    <div class="invoice-details" style="text-align: right;">
        <div style="float: right; text-align: right;">
            <strong>من:</strong><br>
            {{ $from->name ?? ' '}}<br>
        </div>
        <div style="float: left; text-align: right;">
            <strong>إلى:</strong><br>
            {{ $for->name ?? ' ' }}<br>
        </div>
        <div style="clear: both;"></div>
    </div>

    <div>
        <h3 style="text-align: center; margin-bottom: 20px;">عناصر الفاتورة</h3>
        <table style="width: 100%; text-align: right; border-collapse: collapse; direction: rtl;">
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                @foreach($items as $item)
                <tr>
                    <td>
                        @if($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::SERVICE)
                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceName($item->item_id) }}
                        @elseif($item->type && $item->type === Modules\Invoice\Enums\InvoiceItemTypeEnum::COMMERCIAL_SERVICE)
                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getServiceCommercialServiceName($item->item_id) }}
                        @else
                            {{ Modules\Invoice\Enums\InvoiceItemTypeEnum::getLabel($item->type) }}
                        @endif
                    </td>
                    <td>{{ number_format($item->price, 2) }}</td>
                    <td>{{ number_format($item->total, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @if($payments->count() > 0)
    <div>
        <h3 style="text-align: center; margin-bottom: 20px;">تفاصيل الدفع</h3>
        <table style="width: 100%; text-align: right; border-collapse: collapse; direction: rtl;">
            <thead>
                <tr>
                    <th>الإجمالي</th>
                    <th>رمز المعاملة</th>
                    <th>حالة الدفع</th>
                    <th>طريقة الدفع</th>
                </tr>
            </thead>
            <tbody>
                @foreach($payments as $payment)
                <tr>
                    <td>{{ number_format($payment->amount, 2) }}</td>
                    <td>{{ $payment->transaction_code }}</td>
                    <td>{{ Modules\Payment\Enums\PaymentStatusEnum::getLabel($payment->payment_status) }}</td>
                    <td>{{ $payment->paymentMethod->name }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="total-section" style="text-align: right;">
        <p><strong>الإجمالي الفرعي:</strong> {{ number_format($invoice->total, 2) }}</p>
        <p><strong>الضريبة:</strong> {{ number_format($invoice->tax, 2) }}</p>
        <p><strong>الإجمالي:</strong> {{ number_format($invoice->total, 2) }}</p>
        <p><strong>المبلغ المدفوع:</strong> {{ number_format($invoice->paid, 2) }}</p>
        <p><strong>المتبقي:</strong> {{ number_format($invoice->remaining, 2) }}</p>
    </div>
</div>
    @endif
</body>
</html>
