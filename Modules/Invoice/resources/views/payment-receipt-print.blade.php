<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@emran-alhaddad/saudi-riyal-font/index.css">
    <style>
        :root {
            --primary-color: #246250;
            --primary-light: #307a65;
            --primary-dark: #1a4a3c;
            --secondary-color: #64748b;
            --border-color: #e2e8f0;
            --background-color: #ffffff;
            --surface-color: #f8fafc;
            --text-color: #1e293b;
            --success-color: #15803d;
            --warning-color: #b45309;
            --error-color: #b91c1c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f0f2f5;
            padding: 2.5rem;
            -webkit-font-smoothing: antialiased;
        }

        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .receipt {
            background: var(--background-color);
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            padding: 2.5rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2.5rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .company-info {
            text-align: left;
        }

        .company-info h1 {
            color: var(--primary-color);
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .company-info p {
            color: var(--secondary-color);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .receipt-title {
            text-align: right;
        }

        .receipt-title h2 {
            color: var(--primary-color);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background-color: #ecfdf5;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .details {
            display: grid;
            gap: 1.25rem;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 200px 1fr;
            padding: 1.25rem;
            border-radius: 0.75rem;
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .detail-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(36, 98, 80, 0.1);
            border-color: var(--primary-color);
        }

        .detail-label {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.9375rem;
        }

        .detail-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .amount-value {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.125rem;
        }

        .reference-value {
            font-family: 'Monaco', 'Courier New', monospace;
            color: var(--primary-dark);
            font-size: 0.9375rem;
            letter-spacing: 0.05em;
        }

        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 2px solid var(--primary-color);
            text-align: center;
        }

        .footer p {
            color: var(--secondary-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .timestamp {
            color: var(--primary-color);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .watermark {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            opacity: 0.1;
            transform: rotate(-45deg);
            font-size: 8rem;
            font-weight: 900;
            color: var(--primary-color);
            pointer-events: none;
            user-select: none;
        }

        @media print {
            body {
                background-color: var(--background-color);
                padding: 0;
            }

            .receipt {
                box-shadow: none;
            }

            .detail-row:hover {
                transform: none;
                box-shadow: none;
            }

            .watermark {
                display: none;
            }
        }

        @media (max-width: 640px) {
            body {
                padding: 1rem;
            }

            .header {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .company-info, .receipt-title {
                text-align: center;
            }

            .detail-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
                padding: 1rem;
            }

            .detail-label {
                font-size: 0.8125rem;
            }
        }

        /* Add these new styles for the print button */
        .print-button {
            position: fixed;
            bottom: 8rem;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(36, 98, 80, 0.2);
            z-index: 1000;
        }

        .print-button:hover {
            background-color: var(--primary-dark);
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 8px -1px rgba(36, 98, 80, 0.3);
        }

        .print-button:active {
            transform: translateX(-50%) translateY(0);
        }

        .print-button svg {
            width: 1.25rem;
            height: 1.25rem;
        }

        @media print {
            .print-button {
                display: none;
            }
        }

        /* Add animation for button click feedback */
        @keyframes click-wave {
            0% {
                transform: translateX(-50%) scale(0.95);
                opacity: 0.8;
            }
            100% {
                transform: translateX(-50%) scale(1);
                opacity: 1;
            }
        }

        .print-button.clicking {
            animation: click-wave 0.3s ease;
        }

        /* Add a container for the floating elements */
        .floating-elements {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            pointer-events: none;
        }

        .floating-elements > * {
            pointer-events: auto;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .print-button {
                bottom: 1rem;
                padding: 0.6rem 1.5rem;
                font-size: 0.875rem;
            }

            .print-button svg {
                width: 1rem;
                height: 1rem;
            }
        }
    </style>
</head>
<body>
<div class="receipt-container">
    <div class="receipt">
        <div class="header">

            <div class="company-info">
                <h1>Payment Receipt</h1>
{{--                <p>123 Business Street</p>--}}
{{--                <p>City, State 12345</p>--}}
{{--                <p><EMAIL></p>--}}
            </div>
            <div class="receipt-title">

                <div class="status-badge">
                    {{ $payment->payment_status }}
                </div>
            </div>
        </div>

        <div class="details">
            <div class="detail-row">
                <div class="detail-label">Reference Number</div>
                <div class="detail-value reference-value">{{ $payment->transaction_code }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Payment Method</div>
                <div class="detail-value">{{ $payment->paymentMethod?->name ?? 'None' }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Amount</div>
                <div class="detail-value amount-value">{{ number_format($payment->amount, 2) }} <span class="icon-saudi_riyal"></span></div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Payment Date</div>
                <div class="detail-value">{{ $payment->payment_date }}</div>
            </div>

            @if($payment->notes)
                <div class="detail-row">
                    <div class="detail-label">Notes</div>
                    <div class="detail-value">{{ $payment->notes }}</div>
                </div>
            @endif
        </div>

        <div class="footer">
            <p>This is an official payment receipt. Thank you for your business.</p>
            <p>For any inquiries, please contact our support team.</p>
            <p class="timestamp">Generated on: {{ date('F j, Y \a\t h:i A') }}</p>
        </div>
    </div>
    <div class="watermark">KERA</div>
</div>

<div class="floating-elements">
    <button class="print-button" id="printButton">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
        </svg>
        Print Receipt
    </button>
</div>
<script>
    document.getElementById('printButton').addEventListener('click', function() {
        // Add clicking animation class
        this.classList.add('clicking');

        // Remove the animation class after animation completes
        setTimeout(() => {
            this.classList.remove('clicking');
        }, 300);

        // Trigger print
        window.print();
    });

    // Remove automatic print on load
    // window.onload = function() {
    //     window.print();
    // }
</script>
</body>
</html>
