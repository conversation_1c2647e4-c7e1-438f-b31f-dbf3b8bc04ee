<?php

namespace Modules\Invoice\Enums;

use Modules\Lease\Services\LeaseService;
use Modules\Service\app\Models\Service;

enum InvoiceItemTypeEnum: string
{
    const RENT_AMOUNT = 'rent_amount';
    const SERVICE     = 'service';
    const COMMERCIAL_SERVICE     = 'commercialService';
    const COMMISSION_AMOUNT = 'commission_amount';
    const INSURANCE_AMOUNT = 'insurance_amount';
    const TERMINATION_REQUEST = 'termination_request';
    const MAINTENANCE_REQUEST = 'maintenance_request';
    const TERMINATION_CLOSE_REQUEST = 'termination_request_or_close_request';
    const CLOSE_REQUEST = 'close_request';
    const OTHER = 'other';
    const DAILYPENALTY = 'daily_penalty';

    public static function getInvoiceItemTypeLabel(): array
    {
        return [
            self::RENT_AMOUNT => __('Rent Amount'),
            self::SERVICE => __('Service'),
            self::COMMERCIAL_SERVICE => __('Commercial Service'),
            self::COMMISSION_AMOUNT => __('Commission Amount'),
            self::INSURANCE_AMOUNT => __('Insurance Amount'),
            self::TERMINATION_REQUEST => __('Termination Request'),
            self::TERMINATION_CLOSE_REQUEST => __('Termination-Close Request'),
            self::CLOSE_REQUEST => __('Close Request'),
            self::MAINTENANCE_REQUEST => __('Maintenance Request'),
            self::DAILYPENALTY => __('Daily Penalty'),
            self::OTHER => __('Other'),
        ];
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }
        return self::getInvoiceItemTypeLabel()[$option];
    }

    public static function getServiceName(int $id): string
    {
        return Service::find($id)->name;
    }

    public static function getServiceCommercialServiceName(int $id): string
    {
        $leaseService = app(LeaseService::class);
        return $leaseService->getServiceCommercialServiceName($id);
    }

    public static function getInvoiceItemTypeValues(): array // New method for values
    {
        return [
            self::RENT_AMOUNT,
            self::SERVICE,
            self::COMMISSION_AMOUNT,
            self::INSURANCE_AMOUNT,
            self::TERMINATION_REQUEST,
            self::CLOSE_REQUEST,
            self::MAINTENANCE_REQUEST,
            self::COMMERCIAL_SERVICE,
            self::DAILYPENALTY,
            self::OTHER,
        ];
    }
}
