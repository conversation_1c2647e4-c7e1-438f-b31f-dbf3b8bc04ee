<?php

namespace Modules\Invoice\Enums;

enum InvoiceStatusEnum: string
{
    const UNPAID = 'unpaid';
    const PARTIAL_PAID = 'partial_paid';
    const PAID = 'paid';

    const SETTLED = 'settled';

    public static function getInvoiceStatusOptions(): array
    {
        return [
            self::UNPAID => __('unpaid'),
            self::PARTIAL_PAID => __('Partial Paid'),
            self::PAID => __('Paid'),
            self::SETTLED => __('settled')
        ];
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::getInvoiceStatusOptions()[$option];
    }

    public static function getInvoiceStatusValues(): array // New method for values
    {
        return [
            self::UNPAID,
            self::PARTIAL_PAID,
            self::PAID,
            self::SETTLED
        ];
    }
}
