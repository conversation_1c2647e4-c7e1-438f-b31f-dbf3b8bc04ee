<?php

namespace Modules\Invoice\Enums;

enum InvoiceTypeEnum: string
{
    const SCHEDULE = 'schedule';
    const COMMISSION = 'commission';
    const INSURANCE = 'insurance';
    const MAINTENANCE_REQUEST = 'maintenance_request';
    const TERMINATION_CLOSE_REQUEST = 'termination_close_request';
    const TERMINATIONREQUEST = 'termination_request';
    const CLOSEREQUEST = 'close_request';

    public static function getInvoiceTypeOptions(): array
    {
        return [
            self::SCHEDULE                      =>   __('schedule'),
            self::COMMISSION                    =>   __('Commission'),
            self::INSURANCE                     =>   __('insurance'),
            self::MAINTENANCE_REQUEST           =>   __('Maintenance Request'),
            self::TERMINATION_CLOSE_REQUEST     =>   __('Termination Or Close Request'),
            self::TERMINATIONREQUEST     =>   __('Terminate Request'),
            self::CLOSEREQUEST     =>   __('Close Request'),
        ];
    }

    public static function getInvoiceTypeValues(): array // New method for values
    {
        return [
            self::SCHEDULE,
            self::COMMISSION,
            self::INSURANCE,
            self::MAINTENANCE_REQUEST,
            self::CLOSEREQUEST,
            self::TERMINATIONREQUEST,
            self::TERMINATION_CLOSE_REQUEST,
        ];
    }
}
