<?php

namespace Modules\Invoice\Traits;

use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Payment\Enums\PaymentStatusEnum;
use Modules\Payment\app\Models\KhaledsPayment;

trait InvoiceUpdate
{
    /**
     * @return $this
     */
    public function updateInvoiceStatus($payment_id)
    {   
        $payment = KhaledsPayment::find($payment_id);
        $invoice = Invoice::find($payment->order_id);
        if($payment->amount < $invoice->remaining)
        {
            $invoice->status = InvoiceStatusEnum::PARTIAL_PAID;
            $invoice->paid += $payment->amount;
            $invoice->remaining -= $payment->amount;
            $invoice->save();
        }else{
            $invoice->status = InvoiceStatusEnum::PAID;
            $invoice->paid += $payment->amount;
            $invoice->remaining = 0;
            $invoice->save();
        }
        $this->notifyAfterPayment($invoice->for_id , $invoice);
        return $invoice;
    }

    public function notifyAfterPayment($memberId , $invoice): void
    {
        $template = NotificationsTemplate::where(['key' => 'new_payment'])->first();
        $url = url('invoices/' . $invoice->uuid . '/pdf/download');
        if ($template) {
                SendNotification::make(['fcm-api' , 'email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url($url)
                    ->privacy('private')
                    ->database(true)
                    ->fire();
        }
    }
}
