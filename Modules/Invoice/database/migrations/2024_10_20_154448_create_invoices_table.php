<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Invoice\Enums\InvoiceStatusEnum;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->morphs('for');
            $table->morphs('from');
            $table->enum('status', InvoiceStatusEnum::getInvoiceStatusOptions())->default(InvoiceStatusEnum::UNPAID); // status (enum)
            $table->double('total')->default(0);
            $table->double('discount')->default(0);
            $table->double('remaining')->default(0);
            $table->double('paid')->default(0);
            $table->double('tax')->default(0);
            $table->date('release_date')->nullable();
            $table->date('due_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
