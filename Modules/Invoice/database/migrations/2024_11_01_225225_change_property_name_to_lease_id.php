<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            // Rename the 'property_name' column to 'lease_id'
            $table->renameColumn('property_name', 'lease_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            // Rename the 'lease_id' column back to 'property_name'
            $table->renameColumn('lease_id', 'property_name');
        });
    }
};