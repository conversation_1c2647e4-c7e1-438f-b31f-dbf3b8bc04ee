<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            $table->renameColumn('is_setted', 'invoice_id');
            $table->unsignedBigInteger(column: 'invoice_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            $table->renameColumn('invoice_id', 'is_setted');
            $table->unsignedBigInteger('is_setted')->nullable(false)->change();
        });
    }
};
