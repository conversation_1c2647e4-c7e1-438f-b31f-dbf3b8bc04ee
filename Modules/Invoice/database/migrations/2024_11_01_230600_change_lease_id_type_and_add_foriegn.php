<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            // Change the column type to unsigned big integer
            $table->unsignedBigInteger('lease_id')->change();

            // Add foreign key constraint to 'leases' table
            $table->foreign('lease_id')->references('id')->on('leases')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['lease_id']);

            // Change the column back to the original type (assuming string)
            $table->string('lease_id', 255)->change();
        });
    }
};
