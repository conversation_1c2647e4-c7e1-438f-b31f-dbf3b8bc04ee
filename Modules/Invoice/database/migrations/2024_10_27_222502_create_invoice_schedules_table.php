<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_schedules', function (Blueprint $table) {
            $table->id();
            $table->morphs('invoicable');
            $table->decimal('installment_amount', 10, 2);
            $table->decimal('installment_service_amount', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->integer('bill_number'); 
            $table->tinyInteger('is_setted')->default(0);
            $table->date('due_date');
            $table->date('installment_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_schedules');
    }
};
