<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            $table->decimal('paid_amount', 15, 2)->after('total_amount')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_schedules', function (Blueprint $table) {
            $table->dropColumn('paid_amount');
        });
    }
};
