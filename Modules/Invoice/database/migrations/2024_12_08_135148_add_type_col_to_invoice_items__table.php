<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Service\app\Models\Service;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the 'type' columna
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->string('type', 255)->nullable();
        });

        // Fetch all rows that need to be processed
        $items = DB::table('invoice_items')
            ->select('id', 'description', 'item_type')
            ->get();

        foreach ($items as $item) {
            // Determine the type value based on item_type
            $typeValue = $this->determineTypeValue($item->item_type, $item->description);

            // Update the 'type' column
            DB::table('invoice_items')
                ->where('id', $item->id)
                ->update(['type' => $typeValue]);
        }
    }

    private function determineTypeValue(?string $itemType, ?string $description): string
    {
        // Check if the item_type matches the service model
        if ($itemType === Service::class) {
            return InvoiceItemTypeEnum::SERVICE;
        }
    
        // If the description contains multiple words, convert to snake_case
        if (str_contains($description, ' ')) {
            return strtolower(str_replace(' ', '_', trim($description)));
        }
    
        // Otherwise, return the original description
        return strtolower(trim($description ?? ''));
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
