<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Invoice\Enums\InvoiceTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Alter the 'invoice_type' column to include new statuses
            $table->enum('invoice_type', InvoiceTypeEnum::getInvoiceTypeValues()) 
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Revert the column back to the previous enum values
            $table->enum('invoice_type', ['schedule', 'commission', 'request'])
                  ->change();
        });
    }
};
