<?php

use Illuminate\Support\Facades\Route;
use Khaleds\FilamentTranslations\Http\Middleware\LanguageMiddleware;
use Modules\Invoice\app\Http\Controllers\InvoicePdfController;
use Modules\Invoice\app\Http\Controllers\PaymentReceiptController;
use Modules\Invoice\Http\Controllers\InvoiceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('invoices')->middleware(LanguageMiddleware::class)->group(function () {
    Route::get('{uuid}/pdf/download', [InvoicePdfController::class, 'download']);
});

Route::prefix('payment-receipt')->group(function () {
    Route::get('{id}/download', [PaymentReceiptController::class, 'download'])->name('payment.receipt.pdf');
    Route::get('{id}/print', [PaymentReceiptController::class, 'print'])->name('payment.receipt.print');
});
