<?php

use Illuminate\Support\Facades\Route;
use Modules\Invoice\app\Http\Controllers\InvoiceController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::get("createInvoiceSchedule/{id}", [InvoiceController::class, 'createInvoiceSchedule']);
    Route::get("getAllBills/", [InvoiceController::class, 'getAllBills']);
    Route::get("getAllPaidBills/", [InvoiceController::class, 'getAllPaidBills']);
    Route::get("getAllInvoices/", [InvoiceController::class, 'getAllInvoices']);
});
