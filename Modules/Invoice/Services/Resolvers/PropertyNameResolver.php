<?php

namespace Modules\Invoice\Services\Resolvers;

use Modules\Invoice\app\Models\Invoice;
use Modules\Lease\app\Models\Lease;
use Modules\Request\app\Models\Request;
use Modules\Invoice\Enums\InvoiceTypeEnum;
class PropertyNameResolver
{
    private array $resolvers = [
        InvoiceTypeEnum::SCHEDULE => 'resolveScheduleProperty',
        InvoiceTypeEnum::COMMISSION => 'resolveLeaseProperty',
        InvoiceTypeEnum::INSURANCE => 'resolveLeaseProperty',
        InvoiceTypeEnum::TERMINATION_CLOSE_REQUEST => 'resolveTerminationProperty'
    ];

    public function resolve(Invoice $invoice): ?string
    {
        $resolver = $this->resolvers[$invoice->invoice_type] ?? null;
        
        return $resolver ? $this->{$resolver}($invoice) : null;
    }

    private function resolveScheduleProperty(Invoice $invoice): ?string
    {
        return $invoice->invoice_schedule?->lease?->property?->name;
    }

    private function resolveLeaseProperty(Invoice $invoice): ?string
    {
        $leaseItem = $invoice->items->firstWhere('item_type', Lease::class);
        if (!$leaseItem) return null;

        return Lease::find($leaseItem->item_id)?->property?->name;
    }

    private function resolveTerminationProperty(Invoice $invoice): ?string
    {
        $requestItem = $invoice->items->firstWhere('item_type', Request::class);
        if (!$requestItem) return null;

        $request = Request::find($requestItem->item_id);
        return $request?->requestable?->property?->name;
    }
}