<?php

namespace Modules\Invoice\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use Modules\Invoice\app\Models\Invoice as ModelsInvoice;
use Modules\Invoice\app\Models\Invoice;
use Mpdf\Mpdf;

class InvoicePdfService
{
    public function generatePdf(Invoice $invoice)
    {
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A3',
            'orientation' => 'P',
            'margin_top' => 15,
            'margin_bottom' => 15,
            'margin_header' => 0,
            'margin_footer' => 0,
            'default_font' => 'dejavusans',
            'dir' => 'rtl',
        ]);

        $stylesheet = '
        html

<style>
    :root {
        --primary-green: #2E7D32;
        --light-green: #4CAF50;
        --lighter-green: #E8F5E9;
        --dark-green: #1B5E20;
        --border-color: #E0E0E0;
    }

    body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            margin: 0;
            padding: 20px;
        }

        .invoice-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: var(--lighter-green);
            border-radius: 8px;
        }

        .invoice-header h1 {
            color: var(--primary-green);
            margin: 0;
            font-size: 28px;
            text-transform: uppercase;
        }

        .invoice-header p {
            color: var(--dark-green);
            margin: 10px 0 0;
        }

        .invoice-details {
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .invoice-details strong {
            color: var(--primary-green);
            display: block;
            margin-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid var(--border-color);
        }

        th {
            background-color: var(--primary-green);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: var(--lighter-green);
        }

        .total-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .total-section p {
            margin: 10px 0;
            padding: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .total-section strong {
            color: var(--primary-green);
        }

        /* RTL Specific Styles */
        [dir="rtl"] table {
            text-align: right;
        }

        [dir="rtl"] th, 
        [dir="rtl"] td {
            text-align: right;
        }

        /* Print Specific Styles */
        @media print {
            body {
                padding: 0;
                background: white;
            }

            .invoice-header,
            .invoice-details,
            .total-section {
                box-shadow: none;
                border: 1px solid #ddd;
            }

            table {
                box-shadow: none;
            }

            th {
                background-color: #f5f5f5 !important;
                color: #333 !important;
            }
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .invoice-header {
                padding: 15px;
            }

            .invoice-details div {
                float: none !important;
                margin-bottom: 15px;
            }

            th, td {
                padding: 8px;
            }

            .total-section {
                padding: 15px;
            }
        }

        /* Additional Enhancements */
        h3 {
            color: var(--primary-green);
            text-align: center;
            position: relative;
            padding-bottom: 10px;
        }

        h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: var(--light-green);
            border-radius: 2px;
        }

        /* Status Colors */
        .status-paid {
            color: var(--primary-green);
            font-weight: 600;
        }

        .status-pending {
            color: #FFA000;
            font-weight: 600;
        }

        .status-failed {
            color: #D32F2F;
            font-weight: 600;
        }
    </style>
    ';

        $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);

        $html = view('invoice::pdf-invoice', [
            'invoice' => $invoice,
            'items' => $invoice->items,
            'from' => $invoice->fromable,
            'for' => $invoice->forable,
            'payments' => $invoice->payments,
        ])->render();
            
        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

        return $mpdf->Output('', 'S');
    }
}