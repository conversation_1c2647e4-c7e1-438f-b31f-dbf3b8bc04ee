<?php
namespace Modules\Invoice\Services;

use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\LeasePaymentService;
use Carbon\Carbon;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Helpers\AccountHelper;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Property\app\Models\Property;

class InvoiceScheduleService extends ServiceAbstract
{

    public function __construct()
    {
    }

    public function createInvoiceSchedule($lease):bool
    {
        $service = new LeasePaymentService();
        $members = $lease->owners->map(function ($owner) {
            return $owner->member;
        })->toArray();

        $schedule = $service->generatePaymentSchedule(
            $lease->start_date, 
            $lease->end_date, 
            $lease->total_amount, 
            $lease->total_services_amount,
            $lease->paid_amount,
            $lease->payment_type, 
            $lease->payment_repeated_type, 
            $lease->days_permitted,
            $lease->lease_type,
            $members
        );

        $scheduleData = collect($schedule)->map(function ($item) use ($lease) {
            return [
                'lease_id' => $lease->id,
                'invoicable_type' => Lease::class,
                'invoicable_id' => $lease->id,
                'installment_count' => $item->count,
                'installment_amount' => $item->installment_amount,
                'installment_service_amount' => $item->installment_service_amount,
                'total_amount' => $item->total_amount,
                'vat' => $item->vat,
                'bill_number' => $item->serial,
                'invoice_id' => null,
                'paid_amount' => property_exists($item, 'paid_amount') && $item->paid_amount > 0 
                    ? $item->paid_amount 
                    : 0,
                'due_date' => $item->due_date,
                'installment_date' => $item->installment_date,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        })->toArray();
        
        // Filter out any existing records before inserting
        foreach ($scheduleData as $key => $data) {
            if (InvoiceSchedule::where('lease_id', $data['lease_id'])
                ->where('installment_date', $data['installment_date'])
                ->exists()) {
                unset($scheduleData[$key]);
            }
        }
        
        InvoiceSchedule::insert($scheduleData);
        return true;
    }

    public function getUnSettedInvoiceSchedules()
    {
        $invoiceSchedules = InvoiceSchedule::where('installment_date', '<=', Carbon::today())
        ->where(function($query) {
            $query->where('invoice_id', null);
        })
        ->get();
        return $invoiceSchedules;
    } 

    public function getOwnerSchedule($limit, $start_date = null, $end_date = null)
    {
        $current_role = AccountHelper::CurrentRole();

        if ($current_role == AccountRolesEnum::OWNER) {
            $current_role = LeaseMemberTypesEnum::LESSOR;
        } elseif ($current_role == AccountRolesEnum::OWNER_REPRESENTER) {
            $current_role = LeaseMemberTypesEnum::LESSOR_REPRESENTER;
        }
        
        $leaseIds = LeaseMember::where('member_id', auth()->id())->where('member_role' , $current_role)->pluck('lease_id');

         $query = InvoiceSchedule::with('lease') // Eager load the lease relationship
            ->where('invoicable_type', Lease::class)
            ->whereIn('lease_id', $leaseIds)
            ->whereIn('invoicable_id', $leaseIds)
            ->whereDate('installment_date', '<=', Carbon::today())
            ->where(function ($query) {
                $query->where('invoice_id', null)
                    ->orWhereHas('invoice', function ($query) {
                        $query->whereIn('status', [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID]);
                    });
        });

        // Apply date filters only if both start and end dates are provided
        if ($start_date && $end_date) {
            $query->whereBetween('installment_date', [$start_date, $end_date]);
        }

        // Paginate the results based on the provided limit
        $data = $query->paginate($limit);

        return $data;
    }
}
