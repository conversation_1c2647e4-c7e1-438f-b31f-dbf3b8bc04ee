<?php

namespace Modules\Invoice\Services;

use Modules\Account\Helpers\AccountHelper;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Services\LeaseScheduleService;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Repositories\InvoiceRepository;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Service\app\Models\Service;
use Illuminate\Support\Facades\Log;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Company\app\Models\Company;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseService;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Organization\app\Models\Organization;

class InvoiceService extends ServiceAbstract
{
    protected $invoiceScheduleService;
    protected $leaseSchedleService;

    public function __construct(InvoiceRepository $repository)
    {
        parent::__construct($repository);
        $this->invoiceScheduleService = new InvoiceScheduleService();
    }

    public static function getAvalibaleModelsForInvoiceItem(): array
    {
        return [
            MaintenanceRequest::class => __('Maintenance Request'),
            User::class => __('User'),
            Account::class => __('Account'),
        ];
    }

    public static function getAvalibaleModelsForInvoiceItemMembers(): array
    {
        return [
            Lease::class => __('Lease'),
        ];
    }

    public static function fetchModelData($model , $forType , $forId)
    {
        switch ($model) {
            case User::class:
                return $model::query()
                    ->select('name', 'id')
                    ->pluck('name', 'id')
                    ->toArray();
            case Lease::class:
                switch ($forType) {
                    case Account::class:
                        return DB::table('leases')
                        ->join('lease_members', 'leases.id', '=', 'lease_members.lease_id')
                        ->where('leases.status', LeaseEnum::PUBLISHED)  // Filter by 'published' status
                        ->where('lease_members.member_id', $forId)  // Filter by the given member_id
                        ->select('leases.id' , 'leases.id')
                        ->pluck('id' , 'id')
                        ->toArray();
                    default:
                        return [];
                }
            case MaintenanceRequest::class:
                switch($forType)
                {
                   case Account::class:
                    return DB::table('maintenance_requests')
                        ->join('leases', 'maintenance_requests.lease_id', '=', 'leases.id')
                        ->join('lease_members', 'leases.id', '=', 'lease_members.lease_id')
                        ->where('lease_members.member_id', $forId)  // Filter by the given member_id
                        ->where('leases.status', LeaseEnum::PUBLISHED)  // Filter by 'published' status on leases
                        ->select('maintenance_requests.id' , 'maintenance_requests.id')
                        ->pluck('id' , 'id')
                        ->toArray();
                    default:
                    return [];
                }
        }
    }

    public static function getItemLeaseId($model , $modelId)
    {
        switch ($model) {
            case Lease::class:
                return json_encode(['lease_id' => (int)$modelId],JSON_NUMERIC_CHECK);
            case MaintenanceRequest::class:
                $maintenance = MaintenanceRequest::find($modelId);
                return json_encode(['lease_id' => (int)$maintenance->lease_id],JSON_NUMERIC_CHECK);
            default:
                return [];
        }
    }

    public static function fetchinvoiceMembers($model, array $ids)
    {
        switch ($model) {
            case User::class:
                $record = Invoice::find($ids[0]);
                if (!$record) {
                    return null; // Handle the case where the invoice is not found
                }
                $extra = $record->extra;
                $extra = json_decode($extra, true);
                $leaseId = $extra['lease_id'] ?? null;
                if (!$leaseId) {
                    return null; // Handle the case where lease ID is not found
                }
        
                $lease = Lease::find($leaseId);
                if (!$lease) {
                    return null; // Handle the case where the lease is not found
                }
            return view('invoice::company-info', ['company' => $lease->company]);
            case Account::class:
                $accounts = [];
                $organizations = [];

                foreach ($ids as $id) {
                    if (isset($id['member_type']) && $id['member_type'] === LeaseMemberTypesEnum::ORGANIZATION_TYPE) {
                        // Handle organization case
                        $organization = Organization::find($id['member_id']);
                        $organizations[] = $organization; // Add the organization data to the array
                    } else {
                        // Handle individual case
                        $account = $model::find($id['member_id']);
                        if (!$account) return ''; // Return an empty string if account not found
                        $accounts[] = $account; // Add account to the accounts array
                    }
                }
                return view('invoice::leaseMember-info', ['organizations' => $organizations , 'accounts' => $accounts]);
            case Company::class:
                $company = $ids[0];
                return view('invoice::company-info', ['company' => $company]);
            default:
                return '';
        }
    }

    public function createInvoice($schedule, $from, $for, $leaseUnitsServices , $commercialServices = [] , $company_id)
    {
        $vat = $schedule->vat;
        $installment_amount = $schedule->installment_amount;
        $installment_service_amount = $schedule->installment_service_amount;
        $total = $installment_amount + $vat + $installment_service_amount;
        
        if ($schedule->paid_amount && $schedule->paid_amount > 0 && $schedule->paid_amount < $total) {
            $status = InvoiceStatusEnum::PARTIAL_PAID;
            $paid = $schedule->paid_amount;
            $remaining = $total - $paid;
        }elseif($schedule->paid_amount && $schedule->paid_amount > 0 && $schedule->paid_amount = $total){ 
            $status = InvoiceStatusEnum::PAID;
            $paid = $schedule->paid_amount;
            $remaining = $total - $paid;
        }else {
            $status = InvoiceStatusEnum::UNPAID;
            $paid = 0;
            $remaining = $schedule->total_amount;
        }

        // Create the invoice
        $invoice = Invoice::create([
            'uuid'          => rand(0, 9999) . $schedule->lease_id . '00' . $schedule->bill_number,
            'invoice_type'  => InvoiceTypeEnum::SCHEDULE,
            'from_type'     => User::class,
            'from_id'       => $from,
            'for_type'      => Account::class,
            'for_id'        => $for,
            'status'        => $status,
            'vat'           => $vat,
            'total'         => $schedule->total_amount,
            'remaining'     => $remaining,
            'paid'          => $paid,
            'tax'           => 0,
            'release_date'  => $schedule->installment_date,
            'due_date'      => $schedule->due_date,
            'company_id'    => $company_id,
            'extra' => json_encode(['lease_id' => $schedule->lease_id]),
        ]);

        // Add invoice items for rent
        $invoice->items()->create([
            'item_type'   => Lease::class,
            'item_id'     => $schedule->invoicable_id,
            'description' => null,
            'type'        => InvoiceItemTypeEnum::RENT_AMOUNT,
            'qty'         => 1,
            'price'       => $schedule->installment_amount,
            'vat'         => $vat,
            'discount'    => 0,
            'tax'         => 0,
            'total'       => $schedule->installment_amount + $vat,
        ]);

        $invoiceItems = [];

        foreach ($leaseUnitsServices as $service) {
            $invoiceItems[] = [
                'item_type'   => Service::class,
                'item_id'     => $service->service_id,
                'description' => null,
                'type'        => InvoiceItemTypeEnum::SERVICE,
                'qty'         => 1,
                'price'       => $service->to_be_paid_amount,
                'discount'    => 0,
                'tax'         => 0,
                'total'       => $service->to_be_paid_amount,
                'invoice_id'  => $invoice->id, // Ensure you include the foreign key
                'created_at'  => now(),
                'updated_at'  => now(),
            ];
        }

        // Add invoice items for services
        foreach ($commercialServices as $service) {
            $invoiceItems[] = [
                'item_type'   => LeaseService::class,
                'item_id'     => $service->id,
                'description' => null,
                'type'        => InvoiceItemTypeEnum::COMMERCIAL_SERVICE,
                'qty'         => 1,
                'price'       => $service->value,
                'discount'    => 0,
                'tax'         => 0,
                'total'       => $service->value,
                'invoice_id'  => $invoice->id, // Ensure you include the foreign key
                'created_at'  => now(),
                'updated_at'  => now(),
            ];
        }

        \Illuminate\Support\Facades\DB::table('invoice_items')->insert($invoiceItems);
        // Update the schedule with the invoice ID
        $schedule->invoice_id = $invoice->id;
        $schedule->save();

        return $invoice;
    }

    private function getRelevantLeaseIds()
    {
        $authUserId = auth()->id();
        $currentRole = $this->resolveCurrentRole(AccountHelper::CurrentRole());

        // Retrieve lease IDs where the authenticated user is a member with the current role
        $leaseIds = LeaseMember::where('member_id', $authUserId)
            ->where('member_role', $currentRole)
            ->pluck('lease_id');         
        return $leaseIds;
    }

    public function getBills($limit, array $status = [])
    {
        $currentRole = $this->resolveCurrentRole(AccountHelper::CurrentRole());
        $leaseIds = $this->getRelevantLeaseIds();
        $roles = $this->getRolesBasedOnRole($currentRole);
        // Retrieve member IDs associated with the leases and roles
        $memberIds = LeaseMember::whereIn('lease_id', $leaseIds)
            ->whereIn('member_role', $roles)
            ->pluck('member_id');

        // Build the query for invoices
        $query = Invoice::whereIn('for_id', $memberIds)
            ->where('for_type', Account::class)
            ->whereIn('status', $status)
            ->where(function ($q) use ($leaseIds) {
                foreach ($leaseIds as $leaseId) {
                    $q->orWhereJsonContains('extra->lease_id', $leaseId);
                }
            });

        return $query->paginate($limit);
    }

    public function getInvoices($limit, $isActive = 1, $status = null, $propertyId = null)
    {
        // Step 1: Get all relevant lease IDs
        $leaseIds = $this->getRelevantLeaseIds();

        // Step 2: If property ID is provided, filter lease IDs to match that property
        if ($propertyId !== null) {
            $leaseIds = Lease::whereIn('id', $leaseIds)
                ->where('property_id', $propertyId)
                ->pluck('id')
                ->toArray();
        }

        // Step 3: If no valid lease IDs, return an empty array early
        if (count($leaseIds) == 0) {
            return [];
        }

        // Step 4: Build invoice query
        $query = Invoice::where('invoice_type', InvoiceTypeEnum::SCHEDULE)
            ->where(function ($q) use ($leaseIds) {
                collect($leaseIds)->each(function ($leaseId, $index) use ($q) {
                    if ($index === 0) {
                        $q->whereJsonContains('extra->lease_id', $leaseId);
                    } else {
                        $q->orWhereJsonContains('extra->lease_id', $leaseId);
                    }
                });
            });

        // Step 5: Add isActive filter
        if ($isActive == 0) {
            $query->whereIn('status', [
                InvoiceStatusEnum::PAID,
                InvoiceStatusEnum::SETTLED,
            ]);
        } else {
            $query->whereNotIn('status', [
                InvoiceStatusEnum::PAID,
                InvoiceStatusEnum::SETTLED,
            ]);
        }

        // Step 6: Add specific status filter if provided
        if (!is_null($status)) {
            $query->where('status', $status);
        }

        // Step 7: Return paginated result
        return $query->paginate($limit);
    }

    /**
     * Resolves the current role to its corresponding lease member role.
     *
     * @param string $role
     * @return string
     */
    private function resolveCurrentRole(string $role): string
    {
        return match ($role) {
            LeaseMemberTypesEnum::OWNER => LeaseMemberTypesEnum::LESSOR,
            LeaseMemberTypesEnum::OWNER_REPRESENTER => LeaseMemberTypesEnum::LESSOR_REPRESENTER,
            default => $role,
        };
    }

    private function getRolesBasedOnRole($currentRole)
    {
        switch ($currentRole) {
            case LeaseMemberTypesEnum::TENANT:
            case LeaseMemberTypesEnum::TENANT_REPRESENTER:
                return [LeaseMemberTypesEnum::TENANT, LeaseMemberTypesEnum::TENANT_REPRESENTER];

            case LeaseMemberTypesEnum::LESSOR:
                return [LeaseMemberTypesEnum::LESSOR, LeaseMemberTypesEnum::LESSOR_REPRESENTER];

            case LeaseMemberTypesEnum::LESSOR_REPRESENTER:
                return [LeaseMemberTypesEnum::LESSOR, LeaseMemberTypesEnum::LESSOR_REPRESENTER];

            default:
                return [];
        }
    }

    public function getAllBills($limit)
    {
        return $this->getBills($limit, [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID]);
    }

    public function getAllPaidBills($limit)
    {
        return $this->getBills($limit, [InvoiceStatusEnum::PAID]);
    }

    public function executeFunctionForCommand()
    {
        Log::info('Executing function for command to process invoiced schedules.');
        return $this->processInvoiceSchedules($this->invoiceScheduleService->getUnSettedInvoiceSchedules());
    }

    public function executeFunctionForLeasePublish($lease)
    {
        Log::info('Executing function for command to process invoiced schedules for lease ID: ' . $lease->id);
        $filteredSchedules = $this->invoiceScheduleService
            ->getUnSettedInvoiceSchedules()
            ->where('lease_id', $lease->id);

        return $this->processInvoiceSchedules($filteredSchedules);
    }

    private function processInvoiceSchedules($schedules)
    {
        foreach ($schedules as $schedule) {
            $lease_id = $schedule->invoicable_id;
            $notifiedMembers = LeaseMember::where('lease_id', $lease_id)->pluck('member_id')->toArray();
            $leaseScheduleService = new LeaseScheduleService($lease_id);
            $from = $leaseScheduleService->leaseFromMember();
            $for = $leaseScheduleService->leaseForMember();
            $company_id = $schedule->lease->property->company_id ?? null;
            $leaseUnitsServices = $leaseScheduleService->getLeaseUnitsServices();

            $commercialServices = $leaseScheduleService->getCommercialLeaseServices();

            $invoice = $this->createInvoice($schedule, $from, $for, $leaseUnitsServices, $commercialServices ,$company_id);
            $this->notifyAfterIssueInvoice($notifiedMembers , $invoice);
        }

        return true;
    }

    public function notifyAfterIssueInvoice(array $members , $invoice): void
    {
        $template = NotificationsTemplate::where(['key' => 'new_invoice_issued'])->first();
        $url = url('invoices/' . $invoice->uuid . '/pdf/download');
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-api' , 'email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url($url)
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }

    public static function getLeaseMembers($id , $role)
    {
        $record = Invoice::find($id);

        if (!$record) {
            return null; // Handle the case where the invoice is not found
        }

        $extra = $record->extra;
        $extra = json_decode($extra, true);
        $leaseId = $extra['lease_id'] ?? null;

        if (!$leaseId) {
            return null; // Handle the case where lease ID is not found
        }

        $lease = Lease::find($leaseId);

        if (!$lease) {
            return null; // Handle the case where the lease is not found
        }

        $owners = LeaseMember::where('lease_id', $lease->id)
            ->where('member_role', $role)
            ->get()->toArray(); // Ensure it's an array
    

        if (empty($owners)) {
            return null; // Handle the case where no owners are found
        }
        // Pass the appropriate model and IDs to fetchinvoiceMembers
        return self::fetchinvoiceMembers(Account::class, $owners);
    }

    public static function getInvoiceCompany($id)
    {
        $record = Invoice::find($id);
        if (!$record) {
            return null; // Handle the case where the invoice is not found
        }

        $company []= Company::find($record->company_id);
        // Pass the appropriate model and IDs to fetchinvoiceMembers
        return self::fetchinvoiceMembers(Company::class, $company);
    }
}
