<?php

namespace Modules\Invoice\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceItem extends Model
{
    use SoftDeletes;

    protected $table = 'invoice_items';

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'release_date' => 'date',
        'due_date' => 'date',
        'total' => 'double',
        'discount' => 'double',
        'remaining' => 'double',
        'paid' => 'double',
        'tax' => 'double',
    ];

    public function itemable()
    {
        return $this->morphTo();
    }
}
