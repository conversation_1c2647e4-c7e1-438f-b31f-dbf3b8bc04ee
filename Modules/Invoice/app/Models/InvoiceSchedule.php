<?php

namespace Modules\Invoice\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Lease\app\Models\Lease;

class InvoiceSchedule extends Model
{
    protected $table = 'invoice_schedules';

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function lease()
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }
}
