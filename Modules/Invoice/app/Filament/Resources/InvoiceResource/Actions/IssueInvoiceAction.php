<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Actions;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Halt;
use Illuminate\Contracts\Foundation\MaintenanceMode;
use Illuminate\Database\Eloquent\Model;
use Modules\Account\app\Models\Account;
use Modules\Company\app\Models\Company;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;

class IssueInvoiceAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'IssueInvoice';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Issue Invoice'))
            ->modalHeading(__('Issue Invoice'))
            ->modalSubmitActionLabel(__('Submit Invoice Creation'))
            ->color('success')
            ->icon('heroicon-o-currency-dollar')
            ->visible(function ($record) {
                return $record && isset($record->invoices) && $record->invoices->isEmpty();
            })
            ->form([
                TextInput::make('total')
                    ->label(__('Invoice Amount'))
                    ->required()
                    ->numeric()
                    ->default(0)
                    ->minValue(1)
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire, $component) {
                        $state = $state === '' || $state === null ? 0 : (float)$state;
                        $livewire->validateOnly($component->getStatePath());
                    }),
                TextInput::make('discount')
                    ->label(__('Discount Amount'))
                    ->required()
                    ->numeric()
                    ->default(0)
                    ->minValue(0)
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire, $component) {
                        $state = $state === '' || $state === null ? 0 : (float)$state;
                        $livewire->validateOnly($component->getStatePath());
                    }),

                DatePicker::make('due_date')
                    ->label(__('Due Date'))
                    ->default(now())
                    ->required(),
            ])
            ->action(function (array $data): void {
                DB::beginTransaction();
                
                try {
                    $maintenanceRequest = $this->getRecord();

                    // Create the invoice
                    $invoice = Invoice::create([
                        'invoice_type' => InvoiceTypeEnum::MAINTENANCE_REQUEST,
                        'uuid'         => rand(0, 9999) . '-' . $maintenanceRequest->lease->id . '0' . $maintenanceRequest->id, // Example
                        'total'        => $data['total'],
                        'discount'     => $data['discount'],
                        'remaining'    => $data['total'] - $data['discount'],
                        'due_date'     => $data['due_date'],
                        'for_type'     => Account::class,
                        'for_id'       => $maintenanceRequest->account_id,
                        'from_type'    => Company::class,
                        'from_id'      => $maintenanceRequest->lease->property->company->id,
                        'status'       => InvoiceStatusEnum::UNPAID,
                        'paid'         => 0,
                        'release_date' => now(),
                        'company_id'   => $maintenanceRequest->lease->property->company->id,
                        'extra'        => json_encode(['lease_id' => (int)$maintenanceRequest->lease_id],JSON_NUMERIC_CHECK),
                    ]);
                    
                    $invoice->items()->create([
                        'item_type'   => MaintenanceRequest::class,
                        'item_id'     => $maintenanceRequest->id,
                        'description' => null,
                        'type'        => InvoiceItemTypeEnum::MAINTENANCE_REQUEST,
                        'qty'         => 1,
                        'price'       => $data['total'],
                        'discount'    => $data['discount'],
                        'tax'         => 0,
                        'total'       => $data['total'] - $data['discount'],
                    ]);
                    DB::commit();
            
                    Notification::make()
                        ->title(__('Invoice Creation Successful'))
                        ->success()
                        ->send();
            
                } catch (\Exception $e) {
                    DB::rollBack();
            
                    Notification::make()
                        ->title(__('Invoice Creation Failed'))
                        ->body($e->getMessage())
                        ->danger()
                        ->send();
            
                    throw new Halt();
                }
            });
    }

    public function getRecord(): ?Model
    {
        return $this->record;
    }
}