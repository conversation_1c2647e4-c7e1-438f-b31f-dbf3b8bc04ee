<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Widgets;

use Modules\Invoice\app\Models\Invoice;
use Modules\Service\app\Models\Service;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\HtmlString;

class InvoiceListOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn, $sumColumn): array
    {
        // Prepare daily totals
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData) use ($sumColumn) {
            return $dayData->sum($sumColumn);
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map totals to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        $today = now()->format('Y-m-d');
        $endDate = now()->endOfMonth()->format('Y-m-d');

        $invoices = Invoice::select('id','invoice_type','paid','total','due_date','release_date','remaining','created_at','updated_at')->with('items')->get();

        $upcomingInvoices = InvoiceSchedule::select('installment_date','created_at','total_amount')->get();

        // Total Invoice Amount
        $totalInvoiceAmountChart = $this->calculateDailyStats($invoices, 'created_at', 'total');
        $totalInvoiceAmount = $invoices->sum('total');
        $this->cards[] = Stat::make(__('Total Invoice Amount'), 
        new HtmlString(
            '<div class="">' .
            number_format($totalInvoiceAmount) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ))
            ->color('primary')
            ->chart(array_reverse($totalInvoiceAmountChart));

        // Total Earnings
        $earningsChartData = $this->calculateDailyStats($invoices, 'updated_at', 'paid');
        $totalEarnings = $invoices->sum('paid');
        $this->cards[] = Stat::make(__('Total Earnings'),
        new HtmlString(
            '<div class="">' .
            number_format($totalEarnings) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ))
            ->color('success')
            ->chart(array_reverse($earningsChartData));

        // Total Due Invoices
        $dueInvoices = $invoices->filter(function ($invoice) use ($today) {
            return $invoice->release_date <= $today && 
                   $today <= $invoice->due_date && 
                   $invoice->remaining > 0;
        });
        $totalDueChartData = $this->calculateDailyStats($dueInvoices, 'updated_at', 'remaining');
        $totalDueInvoices = $dueInvoices->sum('remaining');

        $this->cards[] = Stat::make(__('Total Due Invoices'), 
        new HtmlString(
            '<div class="">' .
            number_format($totalDueInvoices) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ));

        // Delayed Invoices
        $delayedInvoices = $invoices
        ->filter(function ($invoice) use ($today) {
            return $today > $invoice->due_date && $invoice->remaining > 0;
        });
        $totalDelayedChartData = $this->calculateDailyStats($delayedInvoices, 'updated_at', 'remaining');
        $totalDelayedInvoices = $delayedInvoices->sum('remaining');

        $this->cards[] = Stat::make(__('Delayed Invoices'), 
        new HtmlString(
            '<div class="">' .
            number_format($totalDelayedInvoices) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ))
            ->color('danger')
            ->chart(array_reverse($totalDelayedChartData));

        // Total Services Amount
        $services =  $invoices->flatMap(function ($invoice) {
            return $invoice->items->where('item_type', Service::class);
        });
        $totalServicesChartData = $this->calculateDailyStats($services, 'created_at', 'price');

        $totalServices =$services->sum('price');

        $this->cards[] = Stat::make(__('Total Services'),
        new HtmlString(
            '<div class="">' .
            number_format($totalServices) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ))
            ->color('info')
            ->chart(array_reverse($totalServicesChartData));

        // Total Commission
        $commission =  $invoices
            ->where('invoice_type', InvoiceTypeEnum::COMMISSION);

        $totalCommissionChartData = $this->calculateDailyStats($commission, 'created_at', 'total');

        $totalCommission = $commission->sum('total');

        $this->cards[] = Stat::make(__('Total Commission'), 
        new HtmlString(
            '<div class="">' .
            number_format($totalCommission) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ));

        // Upcoming Invoice Per Month
        $upcomingInvoices =  $upcomingInvoices->whereBetween('installment_date', [$today, $endDate]);

        $totalCommissionChartData = $this->calculateDailyStats($upcomingInvoices, 'created_at', 'total_amount');

        $TotalUpcomingInvoices = $upcomingInvoices->sum('total_amount');

        $this->cards[] = Stat::make(__('Upcoming Invoice Per Month'),
        new HtmlString(
            '<div class="">' .
            number_format($TotalUpcomingInvoices) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
       ))
        ->color('info')
        ->chart(array_reverse($totalCommissionChartData));

        return $this->cards;        
    }

}

