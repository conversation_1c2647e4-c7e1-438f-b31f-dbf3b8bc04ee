<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers;

use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class InvoicePaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('payments');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('payments');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('transaction_code')
                    ->label(__('Reference Number'))
                    ->searchable(),
                TextColumn::make('paymentMethod.name')
                    ->label(__('Payment Method'))
                    ->searchable()
                    ->badge()
                    ->getStateUsing(fn ($record) => $record->paymentMethod?->name ?? __('None')),
                TextColumn::make('payment_status')
                    ->label(__('Payment Status'))
                    ->searchable(),
                TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->searchable(),
                TextColumn::make('notes')
                    ->label(__('Notes'))
                    ->formatStateUsing(function ($state) {
                        return 
                        $state .' '. __('SAR');
                    })
                    ->searchable(),
                TextColumn::make('payment_date')
                    ->label(__('Payment Date'))
                    ->formatStateUsing(function ($state) {
                        return $state .' '. __('SAR');
                    })
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->sortable()
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->sortable()
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([

                Action::make('print_receipt')
                    ->label(__('Print Receipt'))
                    ->icon('heroicon-o-printer')
                    ->url(fn (Model $record) => route('payment.receipt.print', $record->id))
                    ->openUrlInNewTab(),
                Action::make('print')
                    ->label(__('Dowunload PDF'))
                    ->icon('heroicon-o-document-arrow-down')
                    ->action(function (Model $record) {
                        $pdf = Pdf::loadView('invoice::payment-receipt-print', [
                            'payment' => $record,
                        ]);

                        return response()->streamDownload(function () use ($pdf) {
                            echo $pdf->output();
                        }, 'payment-' . $record->transaction_code . '.pdf');
                    }),

            ]);
    }
}
