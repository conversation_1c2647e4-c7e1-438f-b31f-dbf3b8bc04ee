<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

class LineItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Invoice Items');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Invoice Items');
    }
    
    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('invoice_id')
                    ->label(__('Invoice Id'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('Type'))
                    ->searchable()
                    ->formatStateUsing(fn ($state) => \Modules\Invoice\Enums\InvoiceItemTypeEnum::getLabel($state)),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('Description'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('note')
                    ->label(__('Note'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('qty')
                    ->label(__('Quantity'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('Price'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('discount')
                    ->label(__('Discount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('tax')
                    ->label(__('Tax'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->label(__('Total'))
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
            ])
            ->filters([
                //
            ]);
    }
}
