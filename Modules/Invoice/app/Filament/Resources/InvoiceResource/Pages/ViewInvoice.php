<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;

use Filament\Resources\Pages\ViewRecord;
use Modules\Invoice\app\Filament\Resources\InvoiceResource;
use Filament\Actions;
use TomatoPHP\FilamentTypes\Models\Type;

class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('print')
                ->label(__('Print'))
                ->icon('heroicon-o-printer')
                ->color('info')
                ->action(function () {
                    $this->js('window.print()');
                }),
        ];
    }

    // Override the getView method to use your custom Blade template
    public function getView(): string
    {
        return 'invoice::view-invoice';
    }
}
