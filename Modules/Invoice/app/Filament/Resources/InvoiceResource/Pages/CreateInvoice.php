<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;

use Modules\Invoice\app\Filament\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\MaintenanceRequest\app\Models\MaintenanceRequest;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    /**
     * Called after a record is successfully created.
     */
    protected function afterCreate(): void
    {
        // Access the created record using $this->record
        $invoice = $this->record;
        $members [] = $invoice->for_id;
        $url = url('invoices/' . $invoice->uuid . '/pdf/download');
        $this->notifyAfterIssueInvoice($members , $url);
    }

    public function notifyAfterIssueInvoice(array $members , $url): void
    {
        $template = NotificationsTemplate::where(['key' => 'new_invoice_issued'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-api' , 'email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url($url)
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }
}
