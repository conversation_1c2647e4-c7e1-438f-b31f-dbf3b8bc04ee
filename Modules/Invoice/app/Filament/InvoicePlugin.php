<?php

namespace Modules\Invoice\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Invoice\app\Filament\Resources\InvoiceResource;

class InvoicePlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return __('Invoice');
    }

    public function getId(): string
    {
        return __('Invoice');
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                InvoiceResource::class,
            ]);
    }
}
