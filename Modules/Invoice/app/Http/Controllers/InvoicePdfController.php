<?php

namespace Modules\Invoice\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Services\InvoicePdfService;
use Illuminate\Support\Str;

class InvoicePdfController extends Controller
{
    protected $pdfService;

    public function __construct(InvoicePdfService $pdfService)
    {
        $this->pdfService = $pdfService;
    }

    public function download($uuid)
    {
        $invoice = Invoice::where('uuid', $uuid)
            ->firstOrFail();

        $pdf = $this->pdfService->generatePdf($invoice);
        

        return response()->streamDownload(
            function () use ($pdf) {
                echo $pdf;
            },
            'invoice-' . $invoice->id . '.pdf',
            [
                'Content-Type' => 'application/pdf',
            ]
        );
    }
}