<?php

namespace Modules\Invoice\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Invoice\Services\PaymentReceiptService;
use Modules\Payment\app\Models\KhaledsPayment;

class PaymentReceiptController extends Controller
{
    protected $receiptService;

    public function __construct(PaymentReceiptService $receiptService)
    {
        $this->receiptService = $receiptService;
    }

    public function download($id)
    {
        $payment = KhaledsPayment::findOrFail($id);
        $pdf = $this->receiptService->generatePdf($payment);

        return $pdf->download("payment_receipt_{$payment->transaction_code}.pdf");
    }

    public function print($id)
    {
        $payment = KhaledsPayment::findOrFail($id);
        return view('invoice::payment-receipt-print', compact('payment'));
    }
}
