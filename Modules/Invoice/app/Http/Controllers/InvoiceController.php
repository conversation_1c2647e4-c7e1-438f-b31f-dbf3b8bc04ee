<?php

namespace Modules\Invoice\app\Http\Controllers;

use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Services\InvoiceScheduleService;
use Modules\Invoice\Services\InvoiceService;
use Modules\Lease\app\Http\Resources\Api\BillsResource;
use Modules\Lease\app\Models\Lease;
use Illuminate\Http\Request;


class InvoiceController extends ControllerAbstract
{
    protected InvoiceScheduleService $leaseScheduleService;

    public function __construct(InvoiceService $service)
    {
        parent::__construct($service);
    }

    public function createInvoiceSchedule($id)
    {
        $leaseScheduleService = new InvoiceScheduleService();
        $lease = Lease::find($id);
        if($lease)
        {
            $data = $leaseScheduleService->createInvoiceSchedule($lease);
        }else{
            $data = null;
        }
        return response()->json([
            'status' => 'success',
            'data' => $data
        ], 200);
    }

    public function getAllBills()
    {
        $limit = request()->get('limit', 10); // Get 'limit' from the request, default to 10 if not provided

        $data = $this->service->getAllBills($limit);
        // Return the paginated data with the necessary pagination details
        return response()->json([
            'status' => 'success',
            'data' => BillsResource::collection($data),
        ], 200);
    }

    public function getAllPaidBills()
    {
        $limit = request()->get('limit', 10); // Get 'limit' from the request, default to 10 if not provided

        $data = $this->service->getAllPaidBills($limit);
        // Return the paginated data with the necessary pagination details
        return response()->json([
            'status' => 'success',
            'data' => BillsResource::collection($data),
        ], 200);
    }

    public function getAllInvoices(Request $request)
    {
        $limit = $request->input('limit', 10);
        $isActive = $request->input('is_active', 1);
        $status = $request->input('status');
        $propertyId = $request->input('property_id');
        
        $data = $this->service->getInvoices($limit, $isActive ,$status , $propertyId);
        return ApiResponse::data(BillsResource::collection($data) , __('Done'));
    }
}
