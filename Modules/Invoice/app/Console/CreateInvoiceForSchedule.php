<?php

namespace Modules\Invoice\app\Console;

use Illuminate\Console\Command;
use Modules\Invoice\Services\InvoiceScheduleService;
use Modules\Lease\Services\LeaseScheduleService;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Modules\Invoice\Services\InvoiceService;

class CreateInvoiceForSchedule extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'invoice:create-schedule';

    /**
     * The console command description.
     */
    protected $description = 'Create invoices for schedules';

    protected $leaseSchedleService;
    protected $invoiceScheduleService;
    protected $invoiceService;

    /**
     * Create a new command instance.
     */
    public function __construct(protected InvoiceService $InvoiceService)
    {
        parent::__construct();
        $this->invoiceService = app(InvoiceService::class);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->invoiceService->executeFunctionForCommand();
    }

    /**
     * Get the console command arguments.
     */
    protected function getArguments(): array
    {
        return [
            ['example', InputArgument::REQUIRED, 'An example argument.'],
        ];
    }

    /**
     * Get the console command options.
     */
    protected function getOptions(): array
    {
        return [
            ['example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null],
        ];
    }
}
