<?php

namespace Modules\Invoice\Repositories;

use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Invoice\app\Models\Invoice;

class InvoiceRepository extends RepositoriesAbstract
{

    public function __construct(Invoice $model)
    {
        parent::__construct($model);
    }

    public function getData($model , array $select ,  array $pluck):array
    {
        return $model::query()
                    ->select($select)
                    ->pluck($pluck)
                    ->toArray();
    }
}
