<?php

namespace Modules\General\app\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Helpers\HttpHelper;

class EjarController extends Controller
{
    use HttpHelper;

    public function createEjarContract(Request $request)
    {
        try {
            $data = [
                'data' => [
                    'contract' => [
                        'contract_start_date' => '2022-10-10',
                        'contract_end_date' => '2025-10-10',
                        'contract_type' => 'residential'
                    ]
                ]
            ];
            $url = 'https://ejar.sa/api/v1/ecrs/';
            $token = 'RUNSUy1tem1nMHYtMTRjZDpjcGwyZmcwcnU3ZzNncTU4M2o5NGxpY2Jh';
            $this->setHttpHeaders(['Authorization' => 'Basic ' . $token]);
            $response = $this->post($url . 'contracts', $data);

            return $response;
        } catch (\Exception $exception) {
            return ApiResponse::error($exception->getMessage());
        }
    }

    public function contractStatus()
    {
        try {
            $url = 'https://ejar.sa/api/v1/ecrs';
            $token = 'RUNSUy1tem1nMHYtMTRjZDpjcGwyZmcwcnU3ZzNncTU4M2o5NGxpY2Jh';
            $this->setHttpHeaders(['Authorization' => 'Basic ' . $token]);
            $response = $this->get($url, []);

            return $response;
        } catch (\Exception $exception) {
            return ApiResponse::error($exception->getMessage());
        }
    }
}
