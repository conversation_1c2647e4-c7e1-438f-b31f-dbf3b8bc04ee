<?php

namespace Modules\General\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\General\app\Http\Resources\Api\DocumentResource;
use Modules\General\Services\DocumentService;

class DocumentController extends ControllerAbstract
{
    protected string $jsonResourceClass = DocumentResource::class;

    public function __construct(DocumentService $service)
    {
        $this->with = ['documentType'];
        parent::__construct($service);
    }
}
