<?php

namespace Modules\General\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class DocumentWithoutPropertyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "document_type_id" => $this->document_type_id,
            "metadata" => $this->metadata,
            "document_type" => $this->documentType?->name,
            "image" => ($this->getMedia('upload_contract')->first() ?? $this->getMedia('ownership_document')->first())?->original_url,
        ];

        return $data;

    }
}
