<?php

namespace Modules\Ad\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;


class AdResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "title" => $this->title,
            "description" => $this->description,
            "link" => $this->link,
            "image" =>  $this->getMediaImage('ads'),
            "active" => $this->active,
            "vals" => $this->val,
            "property_id" => $this->property_id,
        ];

        return $data;

    }
}
