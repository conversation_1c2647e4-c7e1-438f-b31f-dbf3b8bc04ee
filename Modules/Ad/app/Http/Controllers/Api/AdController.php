<?php

namespace Modules\Ad\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Ad\app\Http\Resources\Api\AdResource;
use Modules\Ad\Services\AdService;

class AdController extends ControllerAbstract
{
    protected string $jsonResourceClass = AdResource::class;

    public function __construct(AdService $service)
    {
        parent::__construct($service);
    }
}
