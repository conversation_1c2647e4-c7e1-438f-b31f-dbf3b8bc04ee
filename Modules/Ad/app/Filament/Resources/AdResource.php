<?php

namespace Modules\Ad\app\Filament\Resources;

use Filament\Forms\Form;
use Filament\GlobalSearch\Actions\Action;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Ad\app\Filament\Resources\AdResource\Components\FormComponent;
use Modules\Ad\app\Models\Ad;

class AdResource extends Resource
{

    protected static ?string $model = Ad::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'App';



    public static function getGloballySearchableAttributes(): array
    {
        return [
            'title',
            'description',
        ];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return AdResource::getUrl('view', ['record' => $record]);
    }

    public static function getGlobalSearchResultActions(Model $record): array
    {
        return [
            Action::make('edit')
                ->url(static::getUrl('edit', ['record' => $record]), shouldOpenInNewTab: true)
                ->icon('heroicon-o-pencil')
                ->label('Edit'),
        ];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Title' => $record?->title,
            'Description' => $record?->description,
        ];
    }

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('link')
                    ->label(__('Link'))
                    ->url(fn ($record) => $record->link, true)
                    ->openUrlInNewTab()
                    ->formatStateUsing(fn ($state) => 'Click here'),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('image')
                    ->label(__('Image'))
                    ->collection('ads')->circular(),

                Tables\Columns\ToggleColumn::make('is_active')
                    ->label(__('Active'))
                    ->sortable()
                    ->onColor('success')
                    ->offColor('danger'),
                Tables\Columns\TextColumn::make('property.name')
                    ->label(__('Property'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('val.value')->badge()->label(__('Val license')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->sortable()
                    ->tooltip(tooltip: fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('is_active')
                ->label(__('Is Active'))
                ->options([
                    true => __('Active'),
                    false => __('Inactive'),
                ])->native(false),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Ad\app\Filament\Resources\AdResource\Pages\ListAds::route('/'),
            'create' => \Modules\Ad\app\Filament\Resources\AdResource\Pages\CreateAd::route('/create'),
            'view' => \Modules\Ad\app\Filament\Resources\AdResource\Pages\ViewAd::route('/{record}'),
            'edit' => \Modules\Ad\app\Filament\Resources\AdResource\Pages\EditAd::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
    public static function getNavigationLabel(): string
    {
        return __("ads");
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Keraa App');
    }
    public static function getBreadcrumb() : string
    {
        return __('ads');
    }
    public static function getModelLabel(): string
    {
        return __('Ad');
    }

    public static function getPluralModelLabel(): string
    {
        return __('ads');
    }
}
