<?php

namespace Modules\Ad\app\Filament\Resources\AdResource\Pages;

use App\Models\Val;
use Exception;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Modules\Ad\app\Filament\Resources\AdResource;
use Modules\Ad\app\Models\Ad;

class EditAd extends EditRecord
{
    protected static string $resource = AdResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $ad = $this->record;
        $valId = $this->data['val_id'] ?? null;

        // Attach or detach morphable Val
        if ($valId) {
            Val::where('id', $valId)->update([
                'morphable_id' => $ad->id,
                'morphable_type' => get_class($ad),
            ]);
        } else {
            try {
                Val::where('morphable_type', Ad::class)
                    ->where('morphable_id', $ad->id)
                    ->first()?->forceDelete(); // Use `delete()` if soft deletes are intended
            } catch (\Throwable $e) {
                report($e); // or log($e->getMessage())
            }
        }

        // Adjust Ad's link and property_id
        $ad->link = $this->data['is_property'] ? null : $ad->link;
        $ad->property_id = $this->data['is_property'] ? $ad->property_id : null;
        $ad->save();
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $valId = $data['val_id'] ?? null;
        unset($data['val_id']);

        $record->update($data);

        if ($valId) {
            $val = Val::find($valId);
            if ($val) {
                // Deactivate any existing active vals for this morphable type
                Val::where('morphable_type', Ad::class)
                    ->where('active', true)
                    ->where('morphable_id', $record->id)
                    ->update(['active' => false]);

                // Activate and attach the new val
                $val->update([
                    'morphable_id' => $record->id,
                    'morphable_type' => Ad::class,
                    'active' => true
                ]);
            }
        }

        return $record;
    }
}
