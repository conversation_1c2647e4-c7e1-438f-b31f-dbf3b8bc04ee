<?php

namespace Modules\Ad\app\Filament\Resources\AdResource\Pages;

use App\Models\Val;
use Filament\Resources\Pages\CreateRecord;
use Modules\Ad\app\Filament\Resources\AdResource;
use Modules\Ad\app\Models\Ad;

class CreateAd extends CreateRecord
{
    protected static string $resource = AdResource::class;

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        $valId = $data['val_id'] ?? null;
        unset($data['val_id']);

        $record = static::getModel()::create($data);

        if ($valId) {
            $val = Val::find($valId);
            if ($val) {
                // Deactivate any existing active vals for this morphable type
                Val::where('morphable_type', Ad::class)
                    ->where('active', true)
                    ->where('morphable_id', $record->id)
                    ->update(['active' => false]);

                // Activate and attach the new val
                $val->update([
                    'morphable_id' => $record->id,
                    'morphable_type' => Ad::class,
                    'active' => true
                ]);
            }
        }
        return $record;
    }
}
