<?php

namespace Modules\Ad\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Ad\app\Filament\Resources\AdResource;

class AdPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Ad';
    }

    public function getId(): string
    {
        return 'Ad';
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                AdResource::class,
            ]);
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }


}
