<?php

namespace Modules\Ad\app\Models;

use App\Models\Scopes\ActiveValScope;
use App\Models\Val;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use Modules\Property\app\Models\Property;

/**
 * @property integer $id
 * @property integer $title
 * @property integer $description
 * @property string $link
 * @property string $image
 * @property integer $is_active
 * @property string $property_id
 */
class Ad extends BaseModel
{
    use SoftDeletes;

    protected static function booted()
    {
        if (request()->is('api/*')) {
            static::addGlobalScope(new ActiveValScope);
        }
    }
    /**
     * The attributes that should be translatable.
     *
     * @var array
     */
    public $translatable = ['title', 'description'];  // Add translatable fields
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['title', 'description', 'link', 'is_active', 'property_id'];



    public function vals()
    {
        return $this->morphMany(Val::class, 'morphable');
    }

    public function val()
    {
        return $this->morphOne(Val::class, 'morphable')->where('active', 1);
    }

    public function property()
    {
        return $this->belongsTo(Property::class, 'property_id');
    }
}
