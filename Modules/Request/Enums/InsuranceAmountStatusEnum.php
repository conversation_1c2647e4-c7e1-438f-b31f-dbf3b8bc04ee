<?php

namespace Modules\Request\Enums;

enum InsuranceAmountStatusEnum: string
{
    const CLAIMED = 'claimed';
    const REFUNDED = 'refunded';

    public static function labels(): array
    {
        return [
            self::CLAIMED => __('Claimed'),
            self::REFUNDED => __('Refunded'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getInsuranceAmountStatusOptions(): array
    {
        return [
            self::CLAIMED => __('Claimed'),
            self::REFUNDED => __('Refunded'),
        ];
    }

    public static function getInsuranceAmountStatusValues(): array // New method for values
    {
        return [
            self::CLAIMED,
            self::REFUNDED,
        ];
    }
}
