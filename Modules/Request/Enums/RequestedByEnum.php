<?php

namespace Modules\Request\Enums;

enum RequestedByEnum: string
{
    const TENANT = 'tenant';
    const OWNER = 'owner';
    const BROKER = 'broker';

    public static function labels(): array
    {
        return [
            self::TENANT => __('Tenant'),
            self::OWNER => __('Owner'),
            self::BROKER => __('Broker'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getRequestRequestedByOptions(): array
    {
        return [
            self::TENANT => __('Tenant'),
            self::OWNER => __('Owner'),
            self::BROKER => __('broker'),
        ];
    }

    public static function getRequestRequestedByValues(): array // New method for values
    {
        return [
            self::TENANT,
            self::OWNER,
            self::BROKER,
        ];
    }
}
