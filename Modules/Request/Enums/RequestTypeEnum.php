<?php

namespace Modules\Request\Enums;

enum RequestTypeEnum: string
{
    const TERMINATION = 'termination';
    const CLOSE = 'close';
    const AUTO_RENEWAL = 'auto_renewal';

    public static function labels(): array
    {
        return [
            self::TERMINATION => __('Termination'),
            self::CLOSE => __('close'),
            self::AUTO_RENEWAL => ('Auto Renewal'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getRequestTypeOptions(): array
    {
        return [
            self::TERMINATION => 'termination',
            self::CLOSE => 'close',
            self::AUTO_RENEWAL => 'auto renewal',
        ];
    }


    public static function getRequestTypeValues(): array // New method
    {
        return [
            self::TERMINATION,
            self::CLOSE,
            self::AUTO_RENEWAL,
        ];
    }
}
