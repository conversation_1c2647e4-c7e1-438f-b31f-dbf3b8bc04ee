<?php

namespace Modules\Request\Enums;

enum RemainingPaymentStatusEnum: string
{
    const CLAIMED = 'claimed';
    const SETTLED = 'settled';

    public static function labels(): array
    {
        return [
            self::CLAIMED => __('Claimed'),
            self::SETTLED => __('settled'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getRemainingPaymentStatusOptions(): array
    {
        return [
            self::CLAIMED => __('Claimed'),
            self::SETTLED => __('settled'),
        ];
    }

    public static function getRemainingPaymentStatusValues(): array // New method for values
    {
        return [
            self::CLAIMED,
            self::SETTLED,
        ];
    }
}
