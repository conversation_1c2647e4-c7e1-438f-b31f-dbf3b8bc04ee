<?php

namespace Modules\Request\Enums;

enum UnitStatusEnum: string
{
    const GOOD = 'good';
    const NEED_MAINTENANCE = 'need_maintenance';

    public static function labels(): array
    {
        return [
            self::GOOD => __('Good'),
            self::NEED_MAINTENANCE => __('Need Maintenance'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getUnitStatusOptions(): array
    {
        return [
            self::GOOD => 'good',
            self::NEED_MAINTENANCE => 'need_maintenance',
        ];
    }

    public static function getUnitStatusValues(): array // New method for values
    {
        return [
            self::GOOD,
            self::NEED_MAINTENANCE,
        ];
    }
}
