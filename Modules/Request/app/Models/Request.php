<?php

namespace Modules\Request\app\Models;

use <PERSON><PERSON><PERSON>\Shared\Models\BaseModel;
use Spatie\MediaLibrary\HasMedia;
use Modules\Request\app\Models\RequestApproval;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;
use <PERSON><PERSON><PERSON>\Shared\Helpers\HandleMediaCollection;

class Request extends BaseModel implements HasMedia
{
    use InteractsWithMedia,HandleMediaCollection;
    protected $table = 'requests';

    protected $guarded = ['id'];

    public function requestable()
    {
        return $this->morphTo();
    }

    public function approvals()
    {
        return $this->hasMany(RequestApproval::class);
    }
}