<?php

namespace Modules\Request\app\Http\Resources\Api;

use App\Helpers\LeaseSettingHelper;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Request\app\Http\Resources\Api\approvalResource;
use Illuminate\Support\Carbon;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseSettingEnum;

class AutoRenewalrequestDetailsResource extends JsonResource
{
    public function toArray($request)
    {
        $noticePeriodDays = (int) LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD);
        $endDate = Carbon::parse($this->requestable->end_date);

        return [
            'id' => $this->id,
            'request_type' => $this->request_type,
            'status' => $this->status,
            'created_by' => $this->when($this->created_by_type && $this->created_by_id, function () {
                $creator = app($this->created_by_type)::find($this->created_by_id);
                return $creator ? $creator->name ?? 'N/A' : 'N/A';
            }),
            'tenant_representer' => $this->requestable->tenantRepresenter
                ? new AccountCustomerResource($this->requestable->tenantRepresenter->member)
                : null,
            'lessor_representer' => $this->requestable->lessorRepresenter
                ? new AccountCustomerResource($this->requestable->lessorRepresenter->member)
                : null,
            'approval' => approvalResource::collection($this->approvals),
            'auto_renew' => $this->requestable->auto_renewal,
            'auto_renew_date' => $this->requestable->auto_renewal == LeaseAutoRenewalEnum::ON 
                ? $endDate
                : $endDate->subDays($noticePeriodDays)->toDateString(),
        ];
    }
}