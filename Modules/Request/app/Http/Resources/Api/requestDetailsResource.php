<?php

namespace Modules\Request\app\Http\Resources\Api;

use App\Helpers\LeaseSettingHelper;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Request\app\Http\Resources\Api\approvalResource;
use Illuminate\Support\Carbon;
use Modules\Request\Enums\RequestedByEnum;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseSettingEnum;

class requestDetailsResource extends JsonResource
{

    public function toArray($request)
    {
        $days = (int) Carbon::parse($this->requestable->end_date)->diffInDays($this->created_at);
        $noticePeriodDays = (int) LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD);
        $endDate = Carbon::parse($this->requestable->end_date);
        return [
            'id' => $this->id,
            'created_by' => $this->when($this->created_by_type && $this->created_by_id, function () {
                $creator = app($this->created_by_type)::find($this->created_by_id);
                return $creator ? $creator->name ?? 'N/A' : 'N/A';
            }),
            'request_type' => $this->request_type,
            'reason' => $this->reason,
            'notes' => $this->notes,
            'status' => $this->status,
            'total_amount' => $this->total_remaining_amount,
            'request_by' => $this->request_by !== null ? RequestedByEnum::getLabel($this->request_by) : null,
            'payment_count' => $this->payments_count,
            'tenant_representer'   => $this->requestable->tenantRepresenter
                ? new AccountCustomerResource($this->requestable->tenantRepresenter->member)
                : null,
            'lessor_representer'   => $this->requestable->lessorRepresenter
                ? new AccountCustomerResource($this->requestable->lessorRepresenter->member)
                : null,
            'remaing_payment_status' => $this->remaing_payment_status,
            'remaing_payment_amount' => $this->remaing_payment_amount,
            'remaing_payment_due_date' => $this->remaing_payment_due_date,
            'insurance_amount_status' => $this->insurance_amount_status,
            'insurance_due_amount' => $this->requestable->insurance_amount,
            'insurance_total_amount' => $this->insurance_amount_amount,
            'insurance_amount_due_date' => $this->insurance_amount_due_date,
            'request_date' => $this->request_date,
            'approval' => approvalResource::collection($this->approvals),
            'unit_status' => $this->unit_status,
            'daily_penalty' => $this->requestable->daily_penalty,
            'daily_penalty_claimed' => $this->total_claimed_penalty,
            'days_permetted' => $days,
            'total_penalty_amount' => $this->total_penalty_amount,
            'auto_renew' => $this->requestable->auto_renewal,
            'auto_renew_date' => $this->requestable->auto_renewal == LeaseAutoRenewalEnum::ON
                ? $endDate
                : $endDate->subDays($noticePeriodDays)->toDateString(),
            'images' => collect($this->getMediaImages('requests'))->pluck('original_url')->toArray(),
        ];
    }
}
