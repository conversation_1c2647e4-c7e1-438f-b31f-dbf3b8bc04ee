<?php

namespace Modules\Request\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Lease\app\Http\Requests\LeaseAutoRenewalCancelRequest;
use Modules\Lease\app\Http\Requests\LeaseAutoRenewalRequest;
use Modules\Lease\app\Http\Resources\Api\LeaseResource;
use Modules\Lease\app\Http\Resources\Api\SettledPaymentResource;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\LeaseService;
use Modules\Request\app\Http\Resources\Api\requestDetailsResource;
use Modules\Request\Services\RequestService;

class RequestController extends ControllerAbstract
{
    protected $leaseService;
    public function __construct(RequestService $service , LeaseService $leaseService)
    {
        parent::__construct($service);
        $this->leaseService = $leaseService;
    }

    public function changeStatus($approval_id, $status)
    {
        try {
            if ($this->service->changeApprovalStatus($approval_id, $status)) {
                return response()->json([
                    'status' => 'success',
                    'data' => __('status changed successfully'),
                ], 200);
            } else {
                return response()->json([
                    'status' => 'error',
                    'data' => __('Please Try Again'),
                ], 422);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()), // Use getMessage() to get the exception message
            ], 422); // Use 500 for server errors
        }
    }
    
    public function requestDetails($request_id)
    {
        $data = $this->service->getrequestDetails($request_id);
       
        return response()->json([
            'status' => 'success',
            'data' => new requestDetailsResource($data),
        ], 200);
    }

    public function getSettledPayments($request_id)
    {
        $data = $this->service->getSettledPayments($request_id);
        return response()->json([
            'status' => 'success',
            'data' => SettledPaymentResource::collection($data),
        ], 200);
    }

    public function createAutoRenewalRequest(LeaseAutoRenewalRequest $request)
    {
        $lease_id = $request->input('lease_id');
        $lease = Lease::find($lease_id);
        $data = $this->service->createRenewalRequestApi($lease);
        return response()->json([
            'status' => 'success',
            'data' => new requestDetailsResource($data),
        ], 200);
    }

    public function cancelAutoRenewal(LeaseAutoRenewalCancelRequest $request)
    {
        $lease_id = $request->input('lease_id');
        $data = $this->leaseService->cancelAutoRenewal($lease_id);
        return response()->json([
            'status' => 'success',
            'data' => new LeaseResource($data),
        ], 200);
    }
}
