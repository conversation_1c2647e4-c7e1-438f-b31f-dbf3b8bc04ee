<?php
namespace Modules\Request\app\Observers;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Lease\Services\LeaseScheduleService;
use Modules\Request\app\Models\Request;
use Modules\Request\Enums\RequestStatusEnum;
use Modules\Request\Enums\RequestTypeEnum;
use Modules\Request\Services\RequestService;

class RequestObserver
{
    /**
     * Handle the Request "created" event.
     */
    public function created(Request $request): void
    {
        if (in_array($request->request_type, [RequestTypeEnum::TERMINATION, RequestTypeEnum::CLOSE])) {
            //get requested lease invoices and set as settled
            $this->settleRequestInvoices($request);
        }
    }
    /**
     * Handle the Request "updated" event.
     */
    public function updated(Request $request): void
    {
        //if request rejected => reset the invoices
        if ($request->isDirty('status') && $request->status == RequestStatusEnum::REJECTED) {
            if (in_array($request->request_type, [RequestTypeEnum::TERMINATION, RequestTypeEnum::CLOSE])) {
                $this->resetRequestInvoices($request);
            }
        }
    }

    public function settleRequestInvoices(Request $request): void
    {
        $lease = $request->requestable;
        $scheduledService = new LeaseScheduleService($lease->id);
        $invoices = $scheduledService->getUnpaidBills();
        if ($invoices->isNotEmpty()) {
            // Update all related invoices status to settled
            Invoice::whereIn('id', $invoices->pluck('id'))->update(['status' => InvoiceStatusEnum::SETTLED]);
        }
    }

    public function resetRequestInvoices(Request $request): void
    {
        $lease = $request->requestable;
        $scheduledService = new LeaseScheduleService($lease->id);
        $invoices = $scheduledService->getSettledBills();
        foreach ($invoices as $invoice) {
            $paid = $invoice->total - $invoice->remaining;
            if ($paid == 0) {
                $invoice->status = InvoiceStatusEnum::UNPAID;
            }else {
                $invoice->status = InvoiceStatusEnum::PARTIAL_PAID;
            }
            $invoice->save();
        }
    }

}
