<?php

namespace Modules\Request\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Request\app\Filament\Resources\RequestResource;

class RequestPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Request';
    }

    public function getId(): string
    {
        return 'request';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                RequestResource::class,
            ]);
    }
}
