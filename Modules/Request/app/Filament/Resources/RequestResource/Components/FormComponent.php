<?php

namespace Modules\Request\app\Filament\Resources\RequestResource\Components;

use Filament\Actions\ViewAction;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\TextColumn;
use Modules\Request\Enums\InsuranceAmountStatusEnum;
use Modules\Request\Enums\RemainingPaymentStatusEnum;
use Modules\Request\Enums\RequestedByEnum;
use Modules\Request\Enums\RequestTypeEnum;
use Modules\Request\Enums\UnitStatusEnum;

class FormComponent
{
    public static function getTerminateForm($lease): array
    {
        return [
            Select::make('request_type')
                ->label(__('Request Type'))
                ->options(
                    RequestTypeEnum::getRequestTypeOptions(),
                )->default(RequestTypeEnum::TERMINATION)
                ->hidden()
                ->required(),
            TextInput::make('insurance_amount')
                ->label(__('Insurance Amount'))
                ->default($lease->insurance_amount)  // Pass insurance amount from the lease object
                ->disabled(),

            TextInput::make('total_remaining_amount')
                ->label(__('Remaining Amount'))
                ->default($lease->remaining_amount)  // Pass remaining amount from the lease object
                ->disabled(),

            Select::make('request_by')
                ->label(__('Request By'))
                ->options(
                    RequestedByEnum::getRequestRequestedByOptions(),
                )->default(RequestedByEnum::TENANT)
                ->required(),

            DatePicker::make('request_date')
                ->label(__('Terminate Date'))
                ->minDate(now())
                ->required()
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d'),

            TextInput::make('reason')
                ->label(__('Reason'))
                ->required(),

            Textarea::make('notes')
                ->label(__('Notes'))
                ->maxLength(299),

            Select::make('remaing_payment_status')
                ->label(__('Remaining Payment Status'))
                ->options(
                    RemainingPaymentStatusEnum::getRemainingPaymentStatusOptions(),
                )
                ->live()
                ->required(),

            TextInput::make('remaing_payment_amount')
                ->label(__('Remaining Payment Amount'))
                ->numeric()
                ->visible(function ($get) {
                    return $get('remaing_payment_status') === RemainingPaymentStatusEnum::CLAIMED;
                })
                ->rule(function ($get) {
                    return 'lte:remaining_amount';  // Rule for less than or equal to remaining_amount
                })
                ->required(),

            DatePicker::make('remaing_payment_due_date')
                ->label(__('Remaining Payment Due Date'))
                ->minDate(now())
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d')
                ->visible(function ($get) {
                    return $get('remaing_payment_status') === RemainingPaymentStatusEnum::CLAIMED;
                }),

            Select::make('insurance_amount_status')
                ->label(__('Insurance Amount Status'))
                ->options(InsuranceAmountStatusEnum::getInsuranceAmountStatusOptions())
                ->live()
                ->required(),

            TextInput::make('insurance_amount_amount')
                ->label(__('Insurance Amount'))
                ->numeric()
                ->visible(function ($get) {
                    return $get('insurance_amount_status') === InsuranceAmountStatusEnum::CLAIMED;
                })
                ->rule(function ($get) {
                    return 'lte:insurance_amount';  // Rule for less than or equal to insurance_amount
                })
                ->required(),

            DatePicker::make('insurance_amount_due_date')
                ->label(__('Insurance Amount Due Date'))
                ->minDate(now())
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d')
                ->visible(function ($get) {
                    return $get('insurance_amount_status') === InsuranceAmountStatusEnum::CLAIMED;
                }),
        ];
    }

    public static function getCloseForm($lease): array
    {
        return [
            Card::make([
                Select::make('request_type')
                    ->label(__('Request Type'))
                    ->options(
                        RequestTypeEnum::getRequestTypeOptions(),
                    )->default(RequestTypeEnum::CLOSE)
                    ->disabled()
                    ->required(),
                TextInput::make('insurance_amount')
                    ->label(__('Insurance Amount'))
                    ->default($lease->insurance_amount)
                    ->disabled(),

                TextInput::make('total_remaining_amount')
                    ->label(__('Remaining Amount'))
                    ->default($lease->total_remaining_amount)
                    ->disabled(),

                TextInput::make('daily_penalty')
                    ->label(__('Daily Penalty'))
                    ->default($lease->daily_penalty)
                    ->disabled(),

                TextInput::make('number_of_days')
                    ->label(__('Number of Days'))
                    ->default($lease->number_of_days)
                    ->disabled(),

                TextInput::make('total_penalty_amount')
                    ->label(__('Total Penalty Amount'))
                    ->default($lease->total_penalty_amount)
                    ->disabled(),
            ])
                ->columnSpan(1),

            Select::make('unit_status')
                ->label(__('Unit Status'))
                ->options(
                    UnitStatusEnum::getUnitStatusOptions(),
                )
                ->live()
                ->required(),
                
            DatePicker::make('request_date')
                ->label(__('Request Date'))
                ->minDate(now())
                ->required()
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d'),

            TextInput::make('reason')
                ->label(__('Reason'))
                ->required(),

            Textarea::make('notes')
                ->label(__('Notes'))
                ->maxLength(299),
            
            TextInput::make('total_claimed_penalty')
                ->label(__('Total Penalty Claimed'))
                ->required(),
            Select::make('remaing_payment_status')
                ->label(__('Remaining Payment Status'))
                ->options(
                    RemainingPaymentStatusEnum::getRemainingPaymentStatusOptions(),
                )
                ->live()
                ->required(),

            TextInput::make('remaing_payment_amount')
                ->label(__('Remaining Payment Amount'))
                ->numeric()
                ->visible(function ($get) {
                    return $get('remaing_payment_status') === RemainingPaymentStatusEnum::CLAIMED;
                })
                ->rule(function ($get) {
                    return 'lte:remaining_amount';  // Rule for less than or equal to remaining_amount
                })
                ->required(),

            DatePicker::make('remaing_payment_due_date')
                ->label(__('Remaining Payment Due Date'))
                ->minDate(now())
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d')
                ->visible(function ($get) {
                    return $get('remaing_payment_status') === RemainingPaymentStatusEnum::CLAIMED;
                }),

            Select::make('insurance_amount_status')
                ->label(__('Insurance Amount Status'))
                ->options(InsuranceAmountStatusEnum::getInsuranceAmountStatusOptions())
                ->live()
                ->required(),

            TextInput::make('insurance_amount_amount')
                ->label(__('Insurance Amount'))
                ->numeric()
                ->visible(function ($get) {
                    return $get('insurance_amount_status') === InsuranceAmountStatusEnum::CLAIMED;
                })
                ->rule(function ($get) {
                    return 'lte:insurance_amount';  // Rule for less than or equal to insurance_amount
                })
                ->required(),

            DatePicker::make('insurance_amount_due_date')
                ->label(__('Insurance Amount Due Date'))
                ->minDate(now())
                ->native(false)
                ->format('Y-m-d')
                ->displayFormat('Y-m-d')
                ->visible(function ($get) {
                    return $get('insurance_amount_status') === InsuranceAmountStatusEnum::CLAIMED;
                }),
            SpatieMediaLibraryFileUpload::make('images')
                ->label(__('Upload Images'))
                ->collection('close')
                ->multiple()
                ->maxSize(5120)
                ->maxFiles(5),
        ];
    }

    public static function getList(): array
    {
        return [
            'columns' => [
                TextColumn::make(name: 'from_type')
                    ->label(__('From Type'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'from_id')
                    ->label(__('From'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'for_type')
                    ->label(__('For Type'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'for_id')
                    ->label(__('For'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make(name: 'total')
                    ->label(__('Total'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ],
            'filters' => [
                // Add any filters here if needed
            ],
            'actions' => [
                ViewAction::make(), // Enable the view action
            ],
            'bulkActions' => [
                // Add any bulk actions here if needed
            ],
        ];
    }
}
