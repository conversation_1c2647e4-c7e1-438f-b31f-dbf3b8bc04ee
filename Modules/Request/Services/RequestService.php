<?php

namespace Modules\Request\Services;

use App\Models\User;
use Carbon\Carbon;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\Request\app\Models\RequestApproval;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Property\app\Models\Property;
use Modules\Request\app\Models\Request;
use Modules\Request\Enums\RemainingPaymentStatusEnum;
use Illuminate\Support\Str;
use Modules\Request\Enums\RequestStatusEnum;
use Modules\Request\Enums\RequestTypeEnum;
use Illuminate\Support\Facades\DB;
use Exception;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\Helpers\AccountHelper;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;

class RequestService extends ServiceAbstract
{
    public function __construct() {}

    public static function getAvalibaleModelsForRequests(): array
    {
        return [
            Lease::class => 'Lease',
        ];
    }

    public static function fetchModelData($model)
    {
        switch ($model) {
            case Lease::class:
                return $model::query()
                    ->select('lease_number', 'id')
                    ->pluck('lease_number', 'id')
                    ->toArray();
            default:
                return [];
        }
    }

    public static function fetchRequestTypes()
    {
        return [
            'termination' => 'Termination',
        ];
    }

    public static function createRequest($data)
    {
        $request = Request::create([
            'requestable_id'            =>       $data['requestable_id'],
            'total_remaining_amount'    =>       $data['total_remaining_amount'],
            'request_type'              =>       $data['request_type'],
            'requestable_type'          =>       Lease::class,
            'request_date'              =>       $data['request_date'],
            'request_by'                =>       $data['request_by'] ?? null,
            'reason'                    =>       $data['reason'],
            'notes'                     =>       $data['notes'],
            'remaing_payment_status'    =>       $data['remaing_payment_status'] ?? null,
            'remaing_payment_amount'    =>       $data['remaing_payment_amount'] ?? null,
            'remaing_payment_due_date'  =>       $data['remaing_payment_due_date'] ?? null,
            'insurance_amount_status'   =>       $data['insurance_amount_status'] ?? null,
            'insurance_amount_amount'   =>       $data['insurance_amount_amount'] ?? null,
            'insurance_amount_due_date' =>       $data['insurance_amount_due_date'] ?? null,
            'total_penalty_amount'      =>       $data['total_penalty_amount'] ?? null,
            'total_claimed_penalty'     =>       $data['total_claimed_penalty'] ?? null,
            'unit_status'               =>       $data['unit_status'] ?? null,
            'created_by_id'             =>       auth()->id(),
            'created_by_type'           =>       auth()->user()::class,
            'created_by_role'           =>       auth()->user()->getRoleNames()->first(),
        ]);

        // Handle image uploads
        if (isset($data['images']) && is_array($data['images'])) {
            foreach ($data['images'] as $image) {
                if ($image instanceof TemporaryUploadedFile) {
                    $request->addMedia($image->getRealPath())
                        ->usingName($image->getClientOriginalName())
                        ->usingFileName($image->getClientOriginalName())
                        ->toMediaCollection('requests');
                }
            }
        }

        $members = self::getLeaseMembers($data['requestable_id'] , $data['request_type']);

        foreach ($members as $approvalData) {
            RequestApproval::create([
                'request_id'    => $request->id,
                'approval_type' => Account::class,
                'approval_id'   => $approvalData,
                'status'        => 'pending',
            ]);
        }

        $lease = Lease::find($data['requestable_id']);
        $data['request_type'] == 'termination' ?  $lease->status = LeaseEnum::TERMINATE_REQUEST : $lease->status = LeaseEnum::CLOSE_REQUEST;
        $lease->save();
    }

    public static function getLeaseMembers($leaseId , $type)
    {
        switch($type)
        {
            case RequestTypeEnum::TERMINATION:
            case RequestTypeEnum::AUTO_RENEWAL:
            $members = [];
            $tenantRepresenter = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::TENANT_REPRESENTER)
                ->pluck('member_id')
                ->first();

            $tenant = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::TENANT)
                ->pluck('member_id')
                ->first();
            $members[] = $tenantRepresenter ?? $tenant;

            $lessor_representer = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER)
                ->pluck('member_id')
                ->first();

            $lessor = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::LESSOR)
                ->pluck('member_id')
                ->first();
            $members[] = $lessor_representer ?? $lessor;
            return $members;

            case RequestTypeEnum::CLOSE:
            $members = [];
            $lessor_representer = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER)
                ->pluck('member_id')
                ->first();

            $lessor = LeaseMember::where('lease_id', $leaseId)
                ->where('member_role', LeaseMemberTypesEnum::LESSOR)
                ->pluck('member_id')
                ->first();
            $members[] = $lessor_representer ?? $lessor;
            return $members;

            default:
                return [];
        }
    }

    public static function changeApprovalStatus($request_id, $status)
    {
        DB::beginTransaction();

        try {
            // Find the approval entry for the given request ID
            $approval = RequestApproval::where('request_id', $request_id)
                ->where('approval_id', auth()->user()->id)
                ->where('approval_type', auth()->user()::class)
                ->first();

            if (!$approval) {
                DB::rollBack();
                throw new \Exception(__('Approval not found.'));
            }

            // Update the approval status
            $approval->status = $status;
            if (!$approval->save()) {
                DB::rollBack();
                throw new \Exception(__('Failed to save approval status.'));
            }

            // Fetch the associated request
            $request = Request::where('id', $approval->request_id)->with('requestable')->where('status' , RequestStatusEnum::PENDING)->first();

            if (!$request) {
                DB::rollBack();
                throw new \Exception(__('Request not found or status is invalid'));
            }

            // If current approval is rejected, immediately reject the request
            if ($status === RequestStatusEnum::REJECTED) {
                $request->status = RequestStatusEnum::REJECTED;
                $request->save();
                $lease = $request->requestable;
                $endDate = Carbon::parse($lease->end_date);
                $now = now();

                self::notifyLeaseMembers($lease, 'request_rejected', $request);
                if ($request->request_type == RequestTypeEnum::AUTO_RENEWAL) {
                    $lease->auto_renewal = LeaseAutoRenewalEnum::OFF;
                    $lease->save();
                } else {
                    $lease->status = ($request->request_type === RequestTypeEnum::TERMINATION)
                    ? (($endDate->subDays(60)->lte($now)) ? LeaseEnum::NEAR_EXPIRE : $lease->previous_status)
                    : LeaseEnum::ENDED;
                    $lease->save();
                }

                DB::commit();
                return true;
            }else{
                $lease = $request->requestable;
                self::notifyLeaseMembers($lease, 'request_member_approved', $request);
            }

            // If not rejected, check if all approvals are completed
            $allApprovals = RequestApproval::where('request_id', $request->id)->get();
            $allApproved = true;
            $requestRejected = false;
            $pendingCount = 0;
            foreach ($allApprovals as $app) {
                if ($app->status === RequestStatusEnum::PENDING) {
                    $allApproved = false;
                    $pendingCount++;
                }

                if ($app->status === RequestStatusEnum::REJECTED) {
                    $requestRejected = true;
                }
            }

            // If all approvals are completed and approved
            if ($pendingCount === 0 && $allApproved && !$requestRejected) {
                $request->status = RequestStatusEnum::APPROVED;
                $request->save();

                $lease = $request->requestable;
                self::notifyLeaseMembers($lease, 'request_approved', $request);
                if ($request->request_type == RequestTypeEnum::AUTO_RENEWAL) {
                    $lease->auto_renewal = LeaseAutoRenewalEnum::ON;
                    $lease->save();
                } else {
                    $lease->status = ($request->request_type === RequestTypeEnum::TERMINATION)
                        ? LeaseEnum::TERMINATED
                        : LeaseEnum::CLOSED;
                    $lease->save();

                    // Update unit availability
                    $units = LeaseUnit::where(['lease_id' => $lease->id])->get();
                    $propertyIds = $units->pluck('unit_id')->toArray();
                    Property::whereIn('id', $propertyIds)->update(['is_available' => 1]);

                    // Update invoice schedules and invoices
                    InvoiceSchedule::where('lease_id', $lease->id)
                        ->where('invoice_id', null)
                        ->update(['invoice_id' => 0]);

                    Invoice::whereIn('status', [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID])
                        ->where('for_id', $lease->tenant->member->id)
                        ->whereHas('items', function ($query) use ($lease) {
                            $query->where('item_id', $lease->id)->where('item_type', Lease::class);
                        })
                        ->update(['status' => InvoiceStatusEnum::SETTLED]);

                    $invoice_type = match($request->request_type) {
                        RequestTypeEnum::TERMINATION => InvoiceTypeEnum::TERMINATIONREQUEST,
                        RequestTypeEnum::CLOSE => InvoiceTypeEnum::CLOSEREQUEST,
                        default => InvoiceTypeEnum::TERMINATION_CLOSE_REQUEST,
                    };

                    // Create an invoice for remaining payments if applicable
                    if ($request->remaing_payment_status === RemainingPaymentStatusEnum::CLAIMED &&
                        $request->remaing_payment_amount > 0) {
                        $invoice = Invoice::create([
                            'invoice_type' => $invoice_type,
                            'uuid'         => rand(0, 9999) . $lease->id . '00' . $request->id,
                            'for_type'     => Account::class,
                            'for_id'       => $lease->tenant->member->id,
                            'from_type'    => User::class,
                            'from_id'      => $lease->created_by,
                            'status'       => InvoiceStatusEnum::UNPAID,
                            'total'        => $request->total_remaining_amount,
                            'discount'     => $request->total_remaining_amount - $request->remaing_payment_amount,
                            'remaining'    => $request->remaing_payment_amount,
                            'paid'         => 0,
                            'tax'          => 0,
                            'release_date' => $request->created_at->format('Y-m-d'),
                            'due_date'     => $request->remaing_payment_due_date,
                            'company_id'   => $lease->company_id,
                            'extra'        => json_encode(['lease_id' => $lease->id]),
                        ]);

                        $invoice->items()->create([
                            'item_type'   => Request::class,
                            'item_id'     => $request->id,
                            'description' => null,
                            'type'        => $request->request_type === RequestTypeEnum::TERMINATION
                            ? InvoiceItemTypeEnum::TERMINATION_REQUEST
                            : InvoiceItemTypeEnum::CLOSE_REQUEST,
                            'qty'         => 1,
                            'price'       => $request->total_remaining_amount,
                            'discount'    => $request->total_remaining_amount - $request->remaing_payment_amount,
                            'tax'         => 0,
                            'total'       => $request->remaing_payment_amount,
                        ]);

                         if($request->request_type == RequestTypeEnum::CLOSE && $request->total_penalty_amount > 0)
                         {
                            $penalty_discount = $request->total_penalty_amount - $request->total_claimed_penalty;
                            $invoice->items()->create([
                                'item_type'   => Request::class,
                                'item_id'     => $request->id,
                                'description' => null,
                                'type'        => InvoiceItemTypeEnum::DAILYPENALTY,
                                'qty'         => 1,
                                'price'       => $request->total_penalty_amount,
                                'discount'    => $penalty_discount,
                                'tax'         => 0,
                                'total'       => $request->total_claimed_penalty,
                            ]);
                            $invoice->total += $request->total_penalty_amount;
                            $invoice->discount += $penalty_discount;
                            $invoice->remaining += $request->total_claimed_penalty;
                            $invoice->save();
                         }
                         if($request->request_type == RequestTypeEnum::CLOSE && $request->insurance_amount_amount > 0)
                         {
                            $insurance_discount = $lease->insurance_amount - $request->insurance_amount_amount;
                            $invoice->items()->create([
                                'item_type'   => Request::class,
                                'item_id'     => $request->id,
                                'description' => null,
                                'type'        => InvoiceItemTypeEnum::INSURANCE_AMOUNT,
                                'qty'         => 1,
                                'price'       => $lease->insurance_amount,
                                'discount'    => $insurance_discount,
                                'tax'         => 0,
                                'total'       => $request->insurance_amount_amount,
                            ]);
                            $invoice->total += $lease->insurance_amount;
                            $invoice->discount += $insurance_discount;
                            $invoice->remaining += $request->insurance_amount_amount;
                            $invoice->save();
                         }
                    }
                }
            }elseif($requestRejected ){
                throw new \Exception(__('The Request is rejected by another user.'));
            }
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public static function getrequestDetails($leaseId)
    {
        $data = Request::find($leaseId);
        $data['payments_count'] = count(self::getSettledPayments($data['id']));
        return $data;
    }

    public static function getSettledPayments($request_id)
    {
        $request = Request::find($request_id);
        $lease = $request->requestable;

        return Invoice::whereIn('status', [InvoiceStatusEnum::SETTLED])
            ->whereHas('invoice_schedule', function ($query) use ($lease) {
                $query->where('lease_id', $lease->id)
                    ->where('invoice_id', '!=', 0);
            })->get();
    }

    public function handleAutoRenewalRequest(Lease $lease)
    {
        // Begin transaction
        DB::beginTransaction();

        try {
            // Update lease status
            $lease->update([
                'auto_renewal' => LeaseAutoRenewalEnum::PENDING,
            ]);

            // Notify relevant parties
            $this->notifyAutoRenewalStakeholders($lease);

            // Create renewal request record if needed
            $this->createRenewalRequest($lease);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function notifyAutoRenewalStakeholders(Lease $lease)
    {
        $oldStartDate = Carbon::parse($lease->start_date);
        $endDate = Carbon::parse($lease->end_date);
        $interval = $oldStartDate->diff($endDate);
        $newEndDate = Carbon::parse($lease->end_date)->add($interval);
        $recipients = [];

        // Get lease members (Account type)
        $members = $lease->allMembers()->get();
        foreach ($members as $member) {
            $recipients[] = [
                'model' => Account::class,
                'id' => $member->member_id
            ];
        }

        // Add broker (User type)
        $broker = $lease->created_by;
        if ($broker) {
            $recipients[] = [
                'model' => User::class,
                'id' => $broker
            ];
        }

        foreach ($members as $member) {
            $template = NotificationsTemplate::where(['key' => 'lease_auto_renewal_request'])->first();
                if ($template) {
                    SendNotification::make(['email' , 'fcm-api' , 'fcm-web'])
                        ->template($template->key)
                        ->model(Account::class)
                        ->id($member->member_id)
                        ->findBody(['{lease_id}', '{start_date}','{end_date}', '{newEndDate}'])
                        ->replaceBody([$lease->id , $lease->start_date, $lease->end_date, $newEndDate])
                        ->icon($template->icon)
                        ->url(url($template->url))
                        ->privacy('private')
                        ->database(true)
                        ->fire();
                }
        }
    }

    public static function notifyLeaseMembers(Lease $lease, $key, $request = null)
    {
        $recipients = [];

        // Get lease members (Account type)
        $members = $lease->allMembers()->get();
        foreach ($members as $member) {
            $recipients[] = [
                'model' => Account::class,
                'id' => $member->member_id
            ];
        }

        // Add broker (User type)
        $broker = $lease->created_by;
        if ($broker) {
            $recipients[] = [
                'model' => User::class,
                'id' => $broker
            ];
        }
        // Send notifications to all recipients
        foreach ($recipients as $recipient) {
            $template = NotificationsTemplate::where(['key' => $key])->first();
            if ($template) {
                SendNotification::make(['email', 'fcm-web', 'fcm-api'])
                    ->template($template->key)
                    ->model($recipient['model'])
                    ->id($recipient['id'])
                    ->findBody(['{request_number}'])
                    ->replaceBody([$lease->id])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }


    public function createRenewalRequest(Lease $lease)
    {
        // Create a record in renewal_requests table if you have one
        $request =  Request::create([
            'requestable_type'  => Lease::class,
            'requestable_id'    => $lease->id,
            'request_type'      => RequestTypeEnum::AUTO_RENEWAL,
            'created_by_id'     => auth()->id(),
            'created_by_type'   => auth()->user()::class,
            'created_by_role'   => auth()->user()->getRoleNames()->first(),
            'request_date'      => date('Y-m-d'),
        ]);

        $members = self::getLeaseMembers($lease->id , RequestTypeEnum::AUTO_RENEWAL);

        foreach ($members as $approvalData) {
            RequestApproval::create([
                'request_id'    => $request->id,
                'approval_type' => Account::class,
                'approval_id'   => $approvalData,
                'status'        => RequestStatusEnum::PENDING,
            ]);
        }
    }

    public function createRenewalRequestApi(Lease $lease)
    {
        // Create a record in the `requests` table
        $request = Request::create([
            'requestable_type'  => Lease::class,
            'requestable_id'    => $lease->id,
            'request_type'      => RequestTypeEnum::AUTO_RENEWAL,
            'created_by_id'     => auth()->id(),
            'created_by_type'   => auth()->user()::class,
            'created_by_role'   => AccountHelper::CurrentRole(),
            'request_date'      => date('Y-m-d'),
        ]);

        $lease->auto_renewal == LeaseAutoRenewalEnum::PENDING;
        $lease->save();

        // Get lease members eligible for approval
        $members = self::getLeaseMembers($lease->id, RequestTypeEnum::AUTO_RENEWAL);
        foreach ($members as $approvalData) {
            // Set the status to APPROVED if the current user is the approver
            $status = (auth()->id() == $approvalData) ? RequestStatusEnum::APPROVED : RequestStatusEnum::PENDING;

            // Create the `RequestApproval` record
            RequestApproval::create([
                'request_id'    => $request->id,
                'approval_type' => Account::class,
                'approval_id'   => $approvalData,
                'status'        => $status,
            ]);
        }

        return $request;
    }
}
