<?php

use Illuminate\Support\Facades\Route;
use Modules\Request\app\Http\Controllers\Api\RequestController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->group(function () {
    Route::apiResource('request', RequestController::class)->names('request');

    // Add route for Lease auto-renewal request
    Route::post('leases/auto-renewal-request', [RequestController::class, 'createAutoRenewalRequest'])
        ->name('leases.autoRenewalRequest');
    Route::post('leases/auto-renewal-cancel', [RequestController::class, 'cancelAutoRenewal'])
        ->name('leases.cancel-renewal');
});

