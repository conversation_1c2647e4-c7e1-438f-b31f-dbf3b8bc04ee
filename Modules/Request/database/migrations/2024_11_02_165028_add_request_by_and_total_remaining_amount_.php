<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Request\Enums\RequestedByEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            // Add total_remaining_amount as an integer
            $table->integer('total_remaining_amount')->after('status');

            // Add request_by as enum with 'tenant' and 'owner' values
            $table->enum('request_by', RequestedByEnum::getRequestRequestedByValues())->after('total_remaining_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            // Drop the columns if rolling back
            $table->dropColumn('total_remaining_amount');
            $table->dropColumn('request_by');
        });
    }
};
