<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use LDAP\Result;
use Modules\Request\Enums\InsuranceAmountStatusEnum;
use Modules\Request\Enums\RemainingPaymentStatusEnum;
use Modules\Request\Enums\RequestedByEnum;
use Modules\Request\Enums\RequestTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('requests', function (Blueprint $table) {
            // Make existing columns nullable
            $table->string('reason', 255)->nullable()->change();
            $table->integer('total_remaining_amount')->nullable()->change();
            $table->enum('remaing_payment_status', [RemainingPaymentStatusEnum::CLAIMED, RemainingPaymentStatusEnum::SETTLED])->nullable()->change();
            $table->enum('insurance_amount_status', [InsuranceAmountStatusEnum::CLAIMED, InsuranceAmountStatusEnum::REFUNDED])->nullable()->change();
            $table->enum('request_by', [RequestedByEnum::BROKER, RequestedByEnum::TENANT])->nullable()->change();
        });
        $requestTypeValues = implode("', '", RequestTypeEnum::getRequestTypeValues());

        $requestByNewValues = implode("', '", RequestedByEnum::getRequestRequestedByValues());
        DB::statement("ALTER TABLE requests MODIFY COLUMN request_type ENUM('$requestTypeValues')");
        DB::statement("ALTER TABLE requests MODIFY COLUMN request_by ENUM('$requestByNewValues')");        
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->string('reason', 255)->nullable(false)->change();
            $table->integer('total_remaining_amount')->nullable(false)->change();
            $table->enum('remaing_payment_status', [RemainingPaymentStatusEnum::CLAIMED, RemainingPaymentStatusEnum::SETTLED])->nullable(false)->change();
            $table->enum('insurance_amount_status', [InsuranceAmountStatusEnum::CLAIMED, InsuranceAmountStatusEnum::REFUNDED])->nullable(false)->change();
            $table->enum('request_by', [RequestedByEnum::BROKER, RequestedByEnum::TENANT])->nullable(false)->change();
        });

        $oldRequestTypeValues = implode("', '", [RequestTypeEnum::TERMINATION, RequestTypeEnum::CLOSE]);
        $oldRequestedByValues = implode("', '", [RequestedByEnum::BROKER, RequestedByEnum::TENANT]);

        DB::statement("ALTER TABLE requests MODIFY COLUMN request_type ENUM('$oldRequestTypeValues')");
        DB::statement("ALTER TABLE requests MODIFY COLUMN request_by ENUM('$oldRequestedByValues')");
    }
};
