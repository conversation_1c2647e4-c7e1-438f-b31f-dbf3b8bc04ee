<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Request\Enums\UnitStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            // Add the unit_status column with enum values from UnitStatusEnum
            $table->enum('unit_status', UnitStatusEnum::getUnitStatusValues())
                  ->nullable()
                  ->after('status');

            // Add the total_penalty column with decimal type (10, 2)
            $table->decimal('total_penalty_amount', 10, 2)->nullable()
                  ->after('unit_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            // Drop the columns when rolling back the migration
            $table->dropColumn('unit_status');
            $table->dropColumn('total_penalty');
        });
    }
};
