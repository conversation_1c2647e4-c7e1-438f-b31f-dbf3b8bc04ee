<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Request\Enums\RequestStatusEnum;
use Modules\Request\Enums\RequestTypeEnum;
use Modules\Request\Enums\InsuranceAmountStatusEnum;
use Modules\Request\Enums\RemainingPaymentStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('requests', function (Blueprint $table) {
            $table->increments('id');
			$table->morphs('requestable');//refer to model will make request for
            $table->enum('request_type', RequestTypeEnum::getRequestTypeValues());
			$table->string('created_by_type', 255);
			$table->integer('created_by_id');
			$table->string('created_by_role', 255);
			$table->string('reason', 255);
			$table->string('notes', 299)->nullable();
            $table->enum('status', RequestStatusEnum::getRequestStatusValues())->default(RequestStatusEnum::PENDING);
            $table->enum('remaing_payment_status', RemainingPaymentStatusEnum::getRemainingPaymentStatusValues());
			$table->double('remaing_payment_amount', 8.2)->nullable();
			$table->date('remaing_payment_due_date')->nullable();
            $table->enum('insurance_amount_status', InsuranceAmountStatusEnum::getInsuranceAmountStatusValues());
            $table->double('insurance_amount_amount', 8, 2)->nullable();
			$table->date('insurance_amount_due_date')->nullable();
            $table->date('request_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('requests');
    }
};
