<div class="w-full flex flex-col gap-6">
    <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 w-full p-6">
        <div class="w-full flex flex-col gap-1 border-b-[0.4px] dark:border-b-[#ffffff2d] pb-4">
         <div class="text-[#112c24] font-semibold text-lg dark:text-primary-400 flex justify-between w-full items-center">
             <div> {{__('Contract No.')}}: {{$record->id}}</div>
             <div class="d-flex">
                 <span @class([
                    'text-white rounded-3xl px-6 py-3 mx-1 text-[12px] font-bold',
                    'bg-blue-500  py-1' => \Modules\Lease\Enums\LeaseTypesEnum::getColor($record->lease_type) === 'info',
                     'bg-blue-500' => \Modules\Lease\Enums\LeaseTypesEnum::getColor($record->lease_type) === 'info',
                ])>
                    {{\Modules\Lease\Enums\LeaseTypesEnum::getLabel($record->lease_type)}}
                </span>
                 <span @class([
                    'text-white rounded-3xl px-6 py-3 mx-1 text-[12px] font-bold',
                    'bg-gray-500' => \Modules\Lease\Enums\LeaseEnum::getColor($record->status) === 'gray',
                    'bg-[#31846C]' => \Modules\Lease\Enums\LeaseEnum::getColor($record->status) === 'success',
                    'bg-red-500' => \Modules\Lease\Enums\LeaseEnum::getColor($record->status) === 'danger',
                    'bg-yellow-500' => \Modules\Lease\Enums\LeaseEnum::getColor($record->status) === 'warning',
                    'bg-blue-500' => \Modules\Lease\Enums\LeaseEnum::getColor($record->status) === 'info',
                ])>
                    {{\Modules\Lease\Enums\LeaseEnum::getLabel($record->status)}}
                </span>
             </div>

         </div>
            <div class="flex flex-wrap gap-2 sm:gap-0">
                <div class="flex items-center gap-1">
                    <x-heroicon-o-calendar class="text-primary-600 w-4 h-4 dark:text-primary-400"/>
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Created')}}:
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ \Carbon\Carbon::parse($record->created_at)->format('d M, Y') }}
                        </span>
                    </span>
                </div>
                <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90 hidden sm:block"/>
                <div class="flex items-center gap-1">
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Start Date:')}}
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ \Carbon\Carbon::parse($record->start_date)->format('d M, Y') }}
                        </span>
                    </span>
                </div>
                <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90 hidden sm:block"/>
                <div class="flex items-center gap-1">
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('End Date')}}:
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ \Carbon\Carbon::parse($record->end_date)->format('d M, Y') }}
                        </span>
                    </span>
                </div>
            </div>
        </div>

        <div class="w-full flex gap-1 pt-4 justify-between flex-wrap">
            <div class="flex flex-wrap gap-2 sm:gap-0">
                <div class="flex items-center gap-1">
                    <x-heroicon-o-calendar-date-range class="text-primary-600 w-4 h-4 dark:text-primary-400"/>
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Next Installment')}}:
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ \Carbon\Carbon::parse($leaseFutureSchedule['data'][0]['installment_date']?? '')->format('d M, Y') }}
                        </span>
                    </span>
                </div>
                <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90 hidden sm:block"/>
                <div class="flex items-center gap-1">
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Installment Amount')}}:
                        <span class="text-[#31846C] text-sm dark:dark:text-white">
                            {{ $leaseFutureSchedule['data'][0]['total_amount'] ?? 0 }} <span class="icon-saudi_riyal"></span>
                        </span>
                    </span>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                    <g clip-path="url(#clip0_8466_1345)">
                    <path d="M2.08252 10.7404V10.747V12.8792C2.22136 12.8792 15.3387 9.80483 15.3387 9.80483C15.8313 9.68913 15.9833 9.58004 15.9833 9.3321V7.104C15.98 7.38169 15.5833 7.59657 15.1668 7.69243L2.08252 10.7404Z" fill="#00AB84"/>
                    <path d="M2.08252 13.861V13.8676V15.9998C2.22136 15.9998 15.3387 12.9254 15.3387 12.9254C15.8313 12.8097 15.9833 12.7006 15.9833 12.4527V10.2246C15.98 10.5023 15.5833 10.7172 15.1668 10.813L2.08252 13.861Z" fill="#00594F"/>
                    <path d="M2.91227 6.55167L3.90401 6.77646C5.25938 6.45911 6.90897 6.07564 8.76021 5.64589L2.08252 4.13184V6.02275C2.08252 6.29713 2.30731 6.41613 2.91227 6.55167Z" fill="#C7C8C9"/>
                    <path d="M11.0941 5.10397L2.91227 3.24942C2.30731 3.11389 2.08252 2.99488 2.08252 2.7205V0.82959L15.5238 3.87752C15.385 4.03951 15.1139 4.16513 14.8296 4.23455H14.8263L11.0941 5.10397Z" fill="#C7C8C9"/>
                    <path d="M1.91074 7.63952L0 10.2478C0.191736 10.2048 15.3388 6.66762 15.3388 6.66762C15.8314 6.55192 15.9835 6.44283 15.9835 6.1949V3.9668C15.9802 4.24448 15.5835 4.45936 15.1669 4.55523L1.91074 7.63952Z" fill="#4CC2BF"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_8466_1345">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.399902)"/>
                    </clipPath>
                    </defs>
                </svg>
                <span class="text-[#112c24] text-sm dark:text-primary-300">
                    {{__('EJAR Number')}}:
                    <span class="text-[#6F6F6F] text-sm dark:text-white">
                        {{ $record->lease_number }}
                    </span>
                </span>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none" class=" absolute ltr:right-[-10px] rtl:left-[-10px] top-3">
                    <path d="M43 24.8999C35.78 24.8999 30 27.8999 30 30.8999V50.2299C30 53.7299 35.59 56.3999 43 56.3999C50.41 56.3999 56 53.7449 56 50.2299V30.8999C56 27.8999 50.22 24.8999 43 24.8999ZM54 40.6499C54 42.6199 49.295 44.8199 43 44.8199C36.705 44.8199 32 42.6199 32 40.6499V34.4499C34.265 36.1549 38.255 37.2399 43 37.2399C47.745 37.2399 51.735 36.1549 54 34.4499V40.6499ZM43 26.8999C49.295 26.8999 54 29.0999 54 31.0699C54 33.0399 49.295 35.2399 43 35.2399C36.705 35.2399 32 33.0399 32 31.0699C32 29.0999 36.705 26.8999 43 26.8999ZM43 54.3999C36.705 54.3999 32 52.1999 32 50.2299V44.0299C34.265 45.7349 38.255 46.8199 43 46.8199C47.745 46.8199 51.735 45.7349 54 44.0299V50.2299C54 52.1999 49.295 54.3999 43 54.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M29.93 17.8999C30.5137 17.9147 31.0864 18.0608 31.6058 18.3274C32.1252 18.5941 32.5778 18.9743 32.93 19.4399C33.6723 20.3764 34.1243 21.5096 34.23 22.6999C34.2438 22.8306 34.2831 22.9573 34.3458 23.0728C34.4085 23.1883 34.4934 23.2902 34.5956 23.3729C34.6977 23.4556 34.8152 23.5173 34.9412 23.5545C35.0672 23.5917 35.1994 23.6038 35.33 23.5899C35.593 23.5622 35.8343 23.4314 36.0011 23.2262C36.1679 23.0209 36.2466 22.758 36.22 22.4949C36.0779 20.917 35.4803 19.4145 34.5 18.1699C33.9699 17.4649 33.283 16.8929 32.4938 16.4989C31.7046 16.105 30.8346 15.8999 29.9525 15.8999C29.0704 15.8999 28.2005 16.105 27.4112 16.4989C26.622 16.8929 25.9352 17.4649 25.405 18.1699C24.2467 19.6663 23.6182 21.5051 23.6182 23.3974C23.6182 25.2898 24.2467 27.1285 25.405 28.6249C25.7262 29.0273 26.0772 29.4051 26.455 29.7549C26.6412 29.9203 26.881 30.0127 27.13 30.0149C27.2696 30.0146 27.4075 29.9851 27.5349 29.9282C27.6624 29.8714 27.7765 29.7885 27.87 29.6849C27.9586 29.5875 28.027 29.4736 28.0714 29.3497C28.1158 29.2258 28.1353 29.0943 28.1288 28.9629C28.1223 28.8314 28.0899 28.7025 28.0335 28.5836C27.9771 28.4647 27.8977 28.3581 27.8 28.2699C27.4944 27.9897 27.2119 27.6854 26.955 27.3599C26.0889 26.2215 25.6198 24.8304 25.6198 23.3999C25.6198 21.9694 26.0889 20.5784 26.955 19.4399C27.3049 18.9778 27.7535 18.5997 28.2683 18.3333C28.783 18.0668 29.3507 17.9188 29.93 17.8999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M27 34.3999H17.415C17.198 32.5062 16.3476 30.7414 15.0016 29.3918C13.6556 28.0422 11.8931 27.187 10 26.9649V19.9649C11.917 19.7379 13.6996 18.8651 15.0545 17.4901C16.4095 16.1152 17.2561 14.32 17.455 12.3999H42.185C42.3798 14.4188 43.2626 16.3094 44.6853 17.7549C46.1081 19.2005 47.9845 20.1131 50 20.3399V22.8999C50 23.1651 50.1054 23.4195 50.2929 23.607C50.4804 23.7945 50.7348 23.8999 51 23.8999C51.2652 23.8999 51.5196 23.7945 51.7071 23.607C51.8946 23.4195 52 23.1651 52 22.8999V11.3999C52 11.1347 51.8946 10.8803 51.7071 10.6928C51.5196 10.5053 51.2652 10.3999 51 10.3999H9C8.73478 10.3999 8.48043 10.5053 8.29289 10.6928C8.10536 10.8803 8 11.1347 8 11.3999V35.3999C8 35.6651 8.10536 35.9195 8.29289 36.107C8.48043 36.2945 8.73478 36.3999 9 36.3999H27C27.2652 36.3999 27.5196 36.2945 27.7071 36.107C27.8946 35.9195 28 35.6651 28 35.3999C28 35.1347 27.8946 34.8803 27.7071 34.6928C27.5196 34.5053 27.2652 34.3999 27 34.3999ZM50 18.3199C48.5183 18.1046 47.1482 17.4088 46.1004 16.3393C45.0525 15.2697 44.3849 13.8857 44.2 12.3999H50V18.3199ZM15.435 12.3999C15.2548 13.7908 14.6271 15.0853 13.6467 16.0882C12.6663 17.0912 11.3864 17.7481 10 17.9599V12.3999H15.435ZM10 28.9849C11.3603 29.1915 12.6184 29.829 13.5895 30.8037C14.5606 31.7784 15.1934 33.0389 15.395 34.3999H10V28.9849Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M27 40.3999H9C8.73478 40.3999 8.48043 40.5053 8.29289 40.6928C8.10536 40.8803 8 41.1347 8 41.3999C8 41.6651 8.10536 41.9195 8.29289 42.107C8.48043 42.2945 8.73478 42.3999 9 42.3999H27C27.2652 42.3999 27.5196 42.2945 27.7071 42.107C27.8946 41.9195 28 41.6651 28 41.3999C28 41.1347 27.8946 40.8803 27.7071 40.6928C27.5196 40.5053 27.2652 40.3999 27 40.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M27 46.3999H9C8.73478 46.3999 8.48043 46.5053 8.29289 46.6928C8.10536 46.8803 8 47.1347 8 47.3999C8 47.6651 8.10536 47.9195 8.29289 48.107C8.48043 48.2945 8.73478 48.3999 9 48.3999H27C27.2652 48.3999 27.5196 48.2945 27.7071 48.107C27.8946 47.9195 28 47.6651 28 47.3999C28 47.1347 27.8946 46.8803 27.7071 46.6928C27.5196 46.5053 27.2652 46.3999 27 46.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400 ">
                    {{ number_format(
                        $record->total_amount
                        + $record->total_services_amount
                        + ($record->lease_type == Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL
                            ? $record->total_amount * config('lease.vat')
                            : 0)
                    ) }} <span class="icon-saudi_riyal"></span>
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white ">
                    {{__('Total Balance')}}
                </span>
            </p>
        </div>
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none" class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3">
                    <g clip-path="url(#clip0_8459_43547)">
                    <path d="M27.8167 25.0997C26.6917 25.0122 25.8042 25.3122 25.1542 25.8247C22.4542 24.3872 12.4667 19.0497 12.4667 19.0497C11.2917 18.4497 10.3792 20.1872 11.5292 20.8122L24.0167 27.4747C23.5667 28.8247 23.8917 30.4247 25.0042 31.3997C23.4042 33.1747 19.6417 37.3247 19.6417 37.3247C18.7792 38.2997 20.2292 39.6372 21.1292 38.6747L26.9417 32.2497C31.6667 32.7622 32.5542 25.7747 27.8167 25.0997ZM27.4542 30.2872C25.3167 30.3372 25.2167 27.1372 27.3917 27.0747C29.4667 27.1372 29.5292 30.1372 27.4542 30.2872ZM26.3792 11.9122V9.62472C26.4042 8.31222 28.3542 8.31222 28.3792 9.62472V11.9122C28.3667 13.2372 26.4042 13.2372 26.3792 11.9122ZM10.5167 29.7747H8.22923C6.91673 29.7497 6.91673 27.7997 8.22923 27.7747H10.5167C11.8417 27.7997 11.8417 29.7622 10.5167 29.7747ZM54.9417 37.1497C56.4917 36.2872 56.7417 33.8122 55.0667 32.8247C55.7042 31.7247 56.5917 30.1872 56.5917 30.1872C57.9042 27.8622 56.0292 25.5372 53.9167 25.4747C52.3167 12.2622 40.8917 1.93722 27.3792 1.94972C-4.78327 2.86222 -8.93327 47.2247 21.3417 54.6747C21.1417 55.6622 21.3792 56.7497 22.0792 57.5122C20.4792 59.2747 21.2542 62.7372 24.4042 62.8372H40.6042C41.2792 62.8497 41.8917 62.6497 42.3792 62.2997C44.2417 62.6622 46.4792 62.8372 49.2167 62.8372C56.3542 62.8372 64.5417 61.7372 64.5417 53.1122C64.5417 48.5122 59.5917 42.1372 54.9417 37.1497ZM24.3917 52.1997C22.9542 52.2247 22.8042 49.9247 24.3917 49.8872H40.5917C42.1542 49.8997 42.0542 52.2247 40.5917 52.1997H24.3917ZM24.3917 47.8747C22.8292 47.8372 22.9167 45.5622 24.3917 45.5622C24.8167 45.5747 36.4292 45.5622 40.5917 45.5622C42.1167 45.5747 42.0792 47.8622 40.5917 47.8747H24.3917ZM38.1292 43.5622C33.5917 43.5622 24.4042 43.5622 24.4042 43.5622C21.2542 43.6622 20.4792 47.1247 22.0792 48.8872C22.0292 48.9372 21.9917 48.9997 21.9417 49.0622C-1.29577 42.5872 2.29173 8.31222 27.3792 7.58722C37.7917 7.56222 46.5417 15.1747 48.2167 25.2497C47.6042 25.4372 47.0667 25.7997 46.6917 26.3372C44.0542 23.9122 40.0542 27.0872 41.8292 30.1997L43.3542 32.8247C41.8542 33.7747 41.7542 36.2747 43.4917 37.1372C41.3667 39.4247 39.5667 41.5747 38.1292 43.5622ZM50.1667 27.5997C50.3792 27.8997 50.3417 28.2997 50.3542 28.6497V32.4372C48.5417 32.4372 46.8167 32.4372 45.4417 32.4372L43.5667 29.1997C42.8417 27.8997 44.7667 26.7747 45.5292 28.0622L46.2042 29.2247C46.4292 29.6122 46.8917 29.8122 47.3292 29.6872C48.0292 29.4997 48.1167 28.8497 48.0667 28.2247C48.0542 27.0997 49.5667 26.6622 50.1667 27.5997ZM44.7042 35.4622C43.9417 35.4247 44.0792 34.2997 44.8542 34.4372H53.7167C54.3792 34.4372 54.3917 35.4622 53.7167 35.4622C53.7167 35.4622 53.4042 35.4622 52.9042 35.4622C52.4917 35.4622 44.8667 35.4622 44.7042 35.4622ZM54.8542 29.1872L52.9792 32.4247C52.3542 32.4247 51.3917 32.4247 50.3417 32.4247C50.4292 32.2747 52.2667 29.0872 52.8792 28.0497C53.5542 26.8497 55.6042 27.7872 54.8542 29.1872ZM27.3792 3.96222C40.0042 3.96222 50.6792 13.6497 51.9667 26.1247C51.8792 26.1872 51.7917 26.2622 51.7167 26.3372C51.3542 25.7997 50.8167 25.4622 50.2292 25.2497C48.5542 13.9872 38.8542 5.58722 27.3792 5.58722C0.566729 6.11222 -4.47077 43.4247 21.2292 50.9622C21.2042 51.6122 21.4042 52.2247 21.7417 52.7747C-6.57077 45.4497 -1.97077 4.69972 27.3792 3.96222ZM24.3917 56.5247C22.7792 56.4872 22.9917 54.1747 24.3917 54.1997H40.5917C42.1167 54.2122 42.0792 56.5122 40.5917 56.5247H24.3917ZM24.3917 60.8372C22.8667 60.8122 22.8792 58.5497 24.3917 58.5247H40.5917C41.6167 58.4997 42.1042 59.8247 41.4042 60.5247C41.2167 60.7122 40.9542 60.8372 40.5917 60.8497C40.6042 60.8372 24.3917 60.8372 24.3917 60.8372ZM43.6542 60.4997C43.9167 59.4497 43.6792 58.3497 42.9042 57.5247C44.0167 56.3747 44.0292 54.3622 42.9042 53.1997C44.0167 52.0497 44.0292 50.0497 42.9042 48.8747C44.7917 46.9997 43.3292 43.4872 40.6167 43.5622C42.0792 41.6622 43.8667 39.6247 45.9292 37.4747H52.4792C68.3042 53.4247 65.4417 62.8747 43.6542 60.4997ZM50.4792 48.9122C54.4917 48.9497 55.3417 54.5497 51.5792 55.7997V56.2622C51.5542 57.5747 49.6042 57.5747 49.5792 56.2622V55.8497C48.0667 55.4497 46.9417 54.0747 46.9417 52.4372C46.9667 51.1247 48.9167 51.1247 48.9417 52.4372C48.9417 53.2872 49.6292 53.9747 50.4792 53.9747C52.5042 53.9247 52.5042 50.9497 50.4792 50.8997C46.2417 50.7747 45.7167 44.8872 49.5792 43.9622V43.5497C49.6042 42.2372 51.5542 42.2372 51.5792 43.5497V44.0247C52.8792 44.4247 53.9667 45.5372 54.0167 47.3622C53.9917 48.6747 52.0417 48.6747 52.0167 47.3622C52.0167 46.5122 51.3292 45.8247 50.4792 45.8247C48.4542 45.8872 48.4542 48.8622 50.4792 48.9122Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_8459_43547">
                    <rect width="64" height="64" fill="white" transform="translate(0.666748 0.399902)"/>
                    </clipPath>
                    </defs>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400 ">
                    {{isset($leaseFutureSchedule['remaningInstallmentsAmount']) ? number_format($leaseFutureSchedule['remaningInstallmentsAmount']) : 0}} <span class="icon-saudi_riyal"></span>
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white ">
                    {{__('Total Remaining')}}
                </span>
            </p>
        </div>
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none" class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3">
                    <path d="M49.9315 18.3599V17.9939C49.9315 17.4079 50.3175 16.9399 50.8375 16.6659C51.9835 16.0599 52.8315 14.3519 52.8315 12.3999C52.8315 9.9599 51.5035 7.8999 49.9315 7.8999C48.3595 7.8999 47.0315 9.9599 47.0315 12.3999H44.0315C44.0315 8.1939 46.6235 4.8999 49.9315 4.8999C53.2395 4.8999 55.8315 8.1939 55.8315 12.3999C55.8315 15.5899 54.3415 18.4639 52.1775 19.6659C51.1715 20.2259 49.9315 19.5099 49.9315 18.3599Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M32.3314 18.3599V17.9939C32.3314 17.4079 32.7174 16.9399 33.2374 16.6659C34.3834 16.0599 35.2314 14.3519 35.2314 12.3999C35.2314 9.9599 33.9034 7.8999 32.3314 7.8999C30.7594 7.8999 29.4314 9.9599 29.4314 12.3999H26.4314C26.4314 8.1939 29.0234 4.8999 32.3314 4.8999C35.6394 4.8999 38.2314 8.1939 38.2314 12.3999C38.2314 15.5899 36.7414 18.4639 34.5774 19.6659C33.5714 20.2259 32.3314 19.5099 32.3314 18.3599Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M52.8315 42.9258H43.8335C43.0051 42.9258 42.3335 43.5974 42.3335 44.4258C42.3335 45.2542 43.0051 45.9258 43.8335 45.9258H52.8315C53.6599 45.9258 54.3315 45.2542 54.3315 44.4258C54.3315 43.5974 53.6599 42.9258 52.8315 42.9258Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M24.1935 53.5123C21.2315 54.4963 18.2095 53.0083 17.0135 50.5903C16.4975 49.5483 17.2315 48.3203 18.3955 48.3183H18.7095C19.3155 48.3183 19.7955 48.7323 20.0315 49.2903C20.4115 50.1883 21.3015 50.8203 22.3355 50.8203C23.7135 50.8203 24.8355 49.6983 24.8355 48.3203C24.8355 46.9983 23.8055 45.9143 22.5075 45.8263C20.0555 45.6603 17.7555 44.3103 17.0715 41.9483C15.9935 38.2243 18.7735 34.8203 22.3335 34.8203C24.6915 34.8203 26.8015 36.3123 27.6235 38.3983C27.9875 39.3203 27.2655 40.3203 26.2735 40.3183H25.9595C25.3535 40.3183 24.8755 39.9023 24.6395 39.3463C24.2595 38.4503 23.3715 37.8203 22.3375 37.8203C20.9595 37.8203 19.8375 38.9423 19.8375 40.3183C19.8375 41.6943 20.9595 42.8203 22.3375 42.8203C25.9155 42.8203 28.7035 46.2523 27.5875 49.9963C27.1015 51.6263 25.8115 52.9763 24.1975 53.5123H24.1935Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M20.8335 36.3201V33.9781C20.8315 33.1501 21.5015 32.4781 22.3315 32.4761C23.1595 32.4761 23.8315 33.1461 23.8315 33.9741V36.3161H20.8335V36.3201Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M22.3315 56.3179C21.5035 56.3179 20.8315 55.6459 20.8315 54.8179V52.3179H23.8315V54.8179C23.8315 55.6459 23.1595 56.3179 22.3315 56.3179Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M52.3315 32.8657C53.16 32.8657 53.8315 32.1941 53.8315 31.3657C53.8315 30.5373 53.16 29.8657 52.3315 29.8657C51.5031 29.8657 50.8315 30.5373 50.8315 31.3657C50.8315 32.1941 51.5031 32.8657 52.3315 32.8657Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M40.3254 32.8657C41.1539 32.8657 41.8254 32.1941 41.8254 31.3657C41.8254 30.5373 41.1539 29.8657 40.3254 29.8657C39.497 29.8657 38.8254 30.5373 38.8254 31.3657C38.8254 32.1941 39.497 32.8657 40.3254 32.8657Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M46.3274 32.8657C47.1558 32.8657 47.8274 32.1941 47.8274 31.3657C47.8274 30.5373 47.1558 29.8657 46.3274 29.8657C45.499 29.8657 44.8274 30.5373 44.8274 31.3657C44.8274 32.1941 45.499 32.8657 46.3274 32.8657Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M52.3315 39.3721C53.16 39.3721 53.8315 38.7005 53.8315 37.8721C53.8315 37.0436 53.16 36.3721 52.3315 36.3721C51.5031 36.3721 50.8315 37.0436 50.8315 37.8721C50.8315 38.7005 51.5031 39.3721 52.3315 39.3721Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M40.3254 39.3721C41.1539 39.3721 41.8254 38.7005 41.8254 37.8721C41.8254 37.0436 41.1539 36.3721 40.3254 36.3721C39.497 36.3721 38.8254 37.0436 38.8254 37.8721C38.8254 38.7005 39.497 39.3721 40.3254 39.3721Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M46.3274 39.3721C47.1558 39.3721 47.8274 38.7005 47.8274 37.8721C47.8274 37.0436 47.1558 36.3721 46.3274 36.3721C45.499 36.3721 44.8274 37.0436 44.8274 37.8721C44.8274 38.7005 45.499 39.3721 46.3274 39.3721Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M54.3315 53.8999H38.2955C37.4675 53.8999 36.7955 53.2279 36.7955 52.3999C36.7955 51.5719 37.4675 50.8999 38.2955 50.8999H54.3315C56.8175 50.8999 58.8315 48.8859 58.8315 46.3999V18.3999C58.8315 15.9139 56.8175 13.8999 54.3315 13.8999H10.3315C7.84554 13.8999 5.83154 15.9139 5.83154 18.3999V46.3999C5.83154 47.7039 6.47554 48.8219 7.45754 49.6199C8.03354 50.0879 8.25354 50.8619 7.91154 51.5199L7.81754 51.7019C7.36754 52.5679 6.23354 52.7579 5.48954 52.1239C3.83554 50.7159 2.83154 48.6199 2.83154 46.3999V18.3999C2.83154 14.2579 6.18954 10.8999 10.3315 10.8999H54.3315C58.4735 10.8999 61.8315 14.2579 61.8315 18.3999V46.3999C61.8315 50.5419 58.4735 53.8999 54.3315 53.8999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M14.7335 18.3599V17.9939C14.7335 17.4079 15.1195 16.9399 15.6395 16.6659C16.7855 16.0599 17.6335 14.3519 17.6335 12.3999C17.6335 9.9599 16.3055 7.8999 14.7335 7.8999C13.1615 7.8999 11.8335 9.9599 11.8335 12.3999H8.8335C8.8335 8.1939 11.4255 4.8999 14.7335 4.8999C18.0415 4.8999 20.6335 8.1939 20.6335 12.3999C20.6335 15.5899 19.1435 18.4639 16.9795 19.6659C15.9735 20.2259 14.7335 19.5099 14.7335 18.3599Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M28.3315 61.8979H16.3315C13.2975 61.8979 10.8315 59.4319 10.8315 56.398V32.3979C10.8315 29.3639 13.2975 26.8979 16.3315 26.8979H28.3315C31.3655 26.8979 33.8315 29.3639 33.8315 32.3979V56.398C33.8315 59.4319 31.3655 61.8979 28.3315 61.8979ZM16.3315 29.8979C14.9535 29.8979 13.8315 31.0199 13.8315 32.3979V56.398C13.8315 57.776 14.9535 58.8979 16.3315 58.8979H28.3315C29.7095 58.8979 30.8315 57.776 30.8315 56.398V32.3979C30.8315 31.0199 29.7095 29.8979 28.3315 29.8979H16.3315Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400 ">
                    {{isset($leaseFutureSchedule['remainingInstallments']) ? $leaseFutureSchedule['remainingInstallments'] : 0}}
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white ">
                    {{__('Remaining Installments')}}
                </span>
            </p>
        </div>
    </div>
</div>
