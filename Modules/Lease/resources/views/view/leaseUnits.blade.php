<div class="mb-6">
    <span class="font-semibold text-lg">
        {{(__('Property'))}} :
        <span class="text-gray-500 dark:text-gray-400">
            {{ $property->name }}
        </span>
    </span>
</div>
<div class="grid grid-cols-1 lg:grid-cols-2 gap-4">

    @foreach ($units as $unit)
    <div class="fi-in-repeatable-item block rounded-xl bg-white p-4 shadow-sm ring-1 ring-gray-950/5 dark:bg-white/5 dark:ring-white/10">
        <div class="flex items-center space-x-4 w-full ">
            <div class="flex-1">
                <div class="font-semibold text-lg mb-2">{{ __('Unit ') }} {{ $unit->number }}</div>
                <div class="grid grid-cols-[repeat(auto-fill,_minmax(180px,_1fr))] gap-3">
                    <div class="flex flex-col w-fit">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Property Type') }}
                        </span>
                        <span class="font-semibold">
                            {{ $unit->property_type->name ?? '-' }}
                        </span>
                    </div>
                    <div class="flex flex-col w-fit">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Status') }}
                        </span>
                        <span class="font-semibold">
                            {{ \Modules\Property\Enums\PropertyStatus::getLabel($unit->status) }}
                        </span>
                    </div>
                    @if ($unit->attributes->count() > 0)
                        @foreach ($unit->attributes as $attribute)
                            <div class="flex flex-col w-fit">
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $attribute->attribute->name ?? '-' }}
                                </span>
                                <span class="font-semibold">
                                    {{ $attribute->value ?? '-' }}
                                </span>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
        @if ($unit->attributes->count() == 0)
            <div class="flex justify-center items-center mt-2 p-3 bg-gray-100 dark:bg-gray-600 rounded-lg">
                <span class="text-sm text-gray-500 dark:text-gray-200">
                    {{ __('No Attributes') }}
                </span>
            </div>
        @endif
    </div>
    @endforeach
</div>

