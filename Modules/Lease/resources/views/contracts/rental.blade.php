<!-- resources/views/lease/contracts/rental.blade.php -->
<div style="font-family: dejavusans; padding: 20px;">
    <!-- Header with Logos -->
    <table style="width: 100%; margin-bottom: 20px;">
        <tr>
            <td style="width: 30%;">
                <table style="width: 100%;">
                    <tr>
                        <td style="width: 50%;">
                            <img src="{{ asset('images/logos.png')}}" alt="Ejar Logo" style="height: 40px;">
                        </td>

                    </tr>
                </table>
            </td>
            <td style="width: 70%; text-align: right;">
                <span style="font-size: 24px; font-weight: bold; margin-left: 10px;">عقــــــد إيجــــار</span>
                <br>
                <span style="font-size: 20px; font-weight: bold; color: #DAA520;">TENANCY CONTRACT</span>
            </td>
        </tr>
    </table>

    <!-- Contract Notice -->
    <div style="text-align: center; color: #FF0000; margin: 20px 0; font-size: 14px;">
        <p style="margin: 0; font-weight: bold;">يعتبر هذا العقد عقداً موثقاً وسنداً</p>
        <p style="margin: 0; font-weight: bold;">تنفيذياً بموجب قرار مجلس الوزراء رقم (١٣١) وتاريخ ٣/٤/١٤٣٥ هـ</p>
    </div>

    <!-- Contract Data Section -->
    <div>
        {{--1--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">١.</span>
                <span>بيانات العقد</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Contract Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Contract Number:</span>
                </td>
                <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->id ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 16.5%; text-align: right; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">:رقم العقد</span>
                </td>
                <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Contract Type:</span>
                </td>
                <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->lease_type ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:نوع العقد</span>
                </td>
            </tr>

            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Contract Selling Date:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->created_at ? $lease->created_at->format('Y-m-d') : 'NA' }}
                </td>
                <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">:تاريخ إبرام العقد</span>
                </td>
            </tr>

            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Tenancy end date:</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->end_date ?? 'NA' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:تاريخ نهاية مدة الايجار</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Tenancy start date:</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->start_date ?? 'NA' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:تاريخ بداية مدة الايجار</span>
                </td>
            </tr>
        </table>

        {{--2. Lessor Data--}}

        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٢.</span>
                <span>بيانات المؤجر</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Lessor Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            @foreach($lease->leaseMembers->where('member_role', 'lessor') as $leaseMember)
                @php
                    $member = $leaseMember->member;
                @endphp

                @if($leaseMember->member_type === 'organization')
                    {{-- Organization Data --}}
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Organization Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:اسم المنشأة</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">CR Number:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->member->unified_number ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم السجل التجاري</span>
                        </td>
                    </tr>


                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Percentage:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->percentage ?? '0' }}%
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:النسبة</span>
                        </td>
                    </tr>

                @else
                    {{-- Individual Data --}}
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:الاسم</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">ID No.:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->national_id ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم الهوية</span>
                        </td>
                    </tr>

                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Percentage:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->percentage ?? '0' }}%
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:النسبة</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Email:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->email ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:البريد الالكتروني</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Mobile No.:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->phone ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:رقم الجوال</span>
                        </td>
                    </tr>
                @endif
            @endforeach
        </table>

        {{--3. Lessor Representative Data--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٣.</span>
                <span>بيانات ممثل المؤجر</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Lessor Representative Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            @php
                $lessorRepresenters = $lease->leaseMembers->where('member_role', 'lessor_representer');
            @endphp

            @if($lessorRepresenters->count() > 0)
                @foreach($lessorRepresenters as $leaseMember)
                    @php
                        $member = $leaseMember->member;
                    @endphp
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:الاسم</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">ID No.:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->national_id ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم الهوية</span>
                        </td>
                    </tr>

                    <tr>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Email:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->email ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:البريد الالكتروني</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Mobile No.:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->phone ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:رقم الجوال</span>
                        </td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; border-right: none;">
                        <span style="color: #374151; font-weight: 800;">The Lessor is represented by himself or herself</span>
                    </td>
                    <td colspan="4" style="padding: 0px; border: none; text-align: center; color: #111827; font-weight: 800;"></td>
                    <td style="padding: 8px; text-align: right; border: 1px solid #ddd; border-left: none;">
                        <span style="color: #374151; font-weight: 800;">المؤجر ممثل نفسة</span>
                    </td>
                </tr>
            @endif
        </table>

        {{--4. Tenant Data--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٤.</span>
                <span>بيانات المستأجر</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Tenant Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            @foreach($lease->leaseMembers->where('member_role', 'tenant') as $leaseMember)
                @php
                    $member = $leaseMember->member;
                @endphp

                @if($leaseMember->is_organization)
                    {{-- Organization Data --}}
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Organization Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:اسم المنشأة</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">CR Number:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->member->unified_number ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم السجل التجاري</span>
                        </td>
                    </tr>


                @else
                    {{-- Individual Data --}}
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:الاسم</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">ID No.:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->national_id ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم الهوية</span>
                        </td>
                    </tr>

                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Percentage:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $leaseMember->percentage ?? '0' }}%
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:النسبة</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Email:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->email ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:البريد الالكتروني</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Mobile No.:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->phone ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:رقم الجوال</span>
                        </td>
                    </tr>
                @endif
            @endforeach
        </table>

        {{--5. Tenant Representative Data--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٥.</span>
                <span>بيانات ممثل المستأجر</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Tenant Representative Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            @php
                $tenantRepresenters = $lease->leaseMembers->where('member_role', 'tenant_representer');
            @endphp

            @if($tenantRepresenters->count() > 0)
                @foreach($tenantRepresenters as $leaseMember)
                    @php
                        $member = $leaseMember->member;
                    @endphp
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:الاسم</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">ID No.:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->national_id ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; text-align: right; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">:رقم الهوية</span>
                        </td>
                    </tr>

                    <tr>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Email:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->email ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:البريد الالكتروني</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Mobile No.:</span>
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $member->phone ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; width: 16%; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:رقم الجوال</span>
                        </td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; border-right: none;">
                        <span style="color: #374151; font-weight: 800;">The Tenant is represented by himself or herself</span>
                    </td>
                    <td colspan="4" style="padding: 0px; border: none; text-align: center; color: #111827; font-weight: 800;"></td>
                    <td style="padding: 8px; text-align: right; border: 1px solid #ddd; border-left: none;">
                        <span style="color: #374151; font-weight: 800;">المستأجر ممثل نفسة</span>
                    </td>
                </tr>
            @endif
        </table>

        {{--6--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٦.</span>
                <span>بيانات منشأة الواسطة العقارية</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Brokerage Entity and Broker Data</div>
            <div style="clear: both;"></div>
        </div>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Brokerage Entity Name:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->name ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:اسم منشأة الوساطة العقارية</span>
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Brokerage Entity Address:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->address ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:عنوان منشأة الوساطة العقارية</span>
                </td>
            </tr>

            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Broker Name:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->user->name ?? 'NA' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:اسم الموظف</span>
                </td>
            </tr>

            <tr>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Email:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->email ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:البريد الالكتروني</span>
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Mobile No.:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->phone ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:رقم الجوال</span>
                </td>
            </tr>

            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">CR No.:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->company->comp_cr_number ?? 'NA' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:رقم السجل التجاري</span>
                </td>
            </tr>
        </table>

        {{--7--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٧.</span>
                <span>بيانات مستندات الملكّية</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Ownership Document Data</div>
            <div style="clear: both;"></div>
        </div>
        <table style="width: 100%; border-collapse: collapse;">
            @forelse($lease->documents()->where([
                'morphable_type' => 'Modules\Lease\app\Models\Lease',
                'morphable_id' => $lease->id
            ])->get() as $document)
                <tr>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Document Number:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['document_number'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:رقم المستند</span>
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Document Type:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['document_name_and_type'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:نوع المستند</span>
                    </td>
                </tr>

                <tr style="background-color: #F0F0F7;">
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Released By:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['released_by'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:جهة الأصدار</span>
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Released In:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['released_in'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:مكان الإصدار</span>
                    </td>
                </tr>

                <tr>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Release Date:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['release_date'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:تاريخ الإصدار</span>
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Expiration Date:</span>
                    </td>
                    <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $document->metadata['expiration_date'] ?? '-' }}
                    </td>
                    <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:تاريخ الانتهاء</span>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #374151; font-weight: 800;">
                        No ownership documents found
                    </td>
                </tr>
            @endforelse
        </table>

        {{--8--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٨.</span>
                <span>بيانات العقار</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Property Data</div>
            <div style="clear: both;"></div>
        </div>
            @php
                $leaseUnit = $lease->leaseUnits()->first();
                $unit = $leaseUnit ? $lease->units()->find($leaseUnit->unit_id) : null;
                $propertyDetails = $unit ? $lease->getPropertyDetails($unit->id) : null;
            @endphp

        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">National Address:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $propertyDetails->address ?? 'NA' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:العنوان الوطني</span>
                </td>
            </tr>

            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Type Property:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $propertyDetails->propertyType->name ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:نوع بناء العقار</span>
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Property Usage:</span>
                </td>
                <td style="padding: 8px; width: 35%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $propertyDetails->usability->name ?? 'NA' }}
                </td>
                <td style="padding: 8px; width: 15%; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:الغرض من استخدام العقار</span>
                </td>
            </tr>

            @php
                $propertyAttributes = $propertyDetails->attributes()
                    ->join('attributes', 'property_attributes.attribute_id', '=', 'attributes.id')
                    ->select('attributes.name', 'property_attributes.value')
                    ->get()
                    ->chunk(2);
            @endphp

            @foreach($propertyAttributes as $pair)
                <tr style="{{ $loop->index % 2 == 0 ? 'background-color: #fff' : 'background-color: #F0F0F7;' }}">
                    @foreach($pair as $attribute)
                        <td style="padding: 8px; width: 25%; border: 1px solid #ddd; text-align: center">
                            <span style="color: #374151; font-weight: 800;">{{ $attribute->name }}:</span>
                        </td>
                        <td style="padding: 8px; width: 25%; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $attribute->value ?? 'NA' }}
                        </td>
                    @endforeach
                </tr>
            @endforeach

            <tr style="background-color: #fff;">
                <td colspan="6" style="padding: 8px; border: 1px solid #ddd;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            @foreach($propertyDetails->amenities as $amenity)
                                <td style="padding: 0 3px 0 0; white-space: nowrap; border: none;">
                                    @if($amenity->icon)
                                        <i class="{{ $amenity->icon }}" style="margin-right: 3px; color: #374151;"></i>
                                    @endif
                                    <span style="color: #111827; font-weight: 800;">{{ $amenity->name ?? 'NA' }}</span>
                                </td>
                            @endforeach
                        </tr>
                    </table>
                </td>
            </tr>
        </table>


        {{--9--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">٩.</span>
                <span>بيانات الوحدات الإيجارَّية</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Rental Units Data</div>
            <div style="clear: both;"></div>
        </div>
            @php
                $leaseUnits = $lease->units->map(function($unit) {
                    return $unit->pivot->id;
                });

                $leaseUnitServices = \Modules\Lease\app\Models\LeaseUnitService::with('service')
                    ->whereIn('lease_unit_id', $leaseUnits)
                    ->get();
            @endphp

        <table style="width: 100%; border-collapse: collapse;">
            @foreach($lease->units as $unit)
                @php
                    $propertyWithType = $lease->unitWithType($unit->id);
                    $unitWithDetails = $lease->leaseProperties()
                        ->with(['usability', 'amenities'])
                        ->where('properties.id', $unit->id)
                        ->first();
                @endphp

                <tr style="background-color: #F0F0F7;">
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Unit No.:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $unit->id ?? 'NA' }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:رقم الوحدة</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Unit Type:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $lease->unitWithType($unit->id)->propertyType->name ?? 'NA' }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:نوع الوحدة</span>
                    </td>
                </tr>

                <!-- Fixed Services -->
                @foreach($leaseUnitServices->where('service.is_fixed_lease', true) as $service)
                    <tr style="background-color: #fff;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Service Name:</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $service->service->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:اسم الخدمة</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Amount:</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ number_format($service->lease_service_amount ?? 0, 2) }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:المبلغ</span>
                        </td>
                    </tr>
                @endforeach

                <!-- Non-Fixed Services -->
                @foreach($leaseUnitServices->where('service.is_fixed_lease', false) as $service)
                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Service Name:</span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $service->service->name ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:اسم الخدمة</span>
                        </td>
                    </tr>

                    <tr style="background-color: #F0F0F7;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Payment Method:</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $service->to_be_paid_by ?? 'NA' }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:طريقة الدفع</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Amount to be Paid:</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ number_format($service->to_be_paid_amount ?? 0, 2) }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:المبلغ المستحق</span>
                        </td>
                    </tr>

                    @if($service->to_be_paid_by === 'metered_fee')
                        <tr style="background-color: #F0F0F7;">
                            <td style="padding: 8px; border: 1px solid #ddd;">
                                <span style="color: #374151; font-weight: 800;">Current Meter Reading:</span>
                            </td>
                            <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                                {{ $service->meter_current_reading ?? 'NA' }}
                            </td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                                <span style="color: #374151; font-weight: 800;">:قراءة العداد الحالية</span>
                            </td>
                        </tr>
                    @endif
                @endforeach
            @endforeach
        </table>


        {{--10--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">١٠.</span>
                <span>صلاحيات المستأجر</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Tenant Authority</div>
            <div style="clear: both;"></div>
        </div>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">-</span>
                </td>
            </tr>
        </table>


{{--11--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">١١.</span>
                <span>البيانات المالَّية</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Financial Data</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            <!-- Brokerage Info -->
            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Brokerage Entity Name:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ $lease->broker->name ?? 'شركة كراء الأولى لتقنية المعلومات' }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:اسم منشأة الوساطة العقارية</span>
                </td>
            </tr>

            <!-- Fixed Services -->
            @foreach($lease->leaseUnits->first()->leaseUnitServices()->whereHas('service', function($query) {
                $query->where('is_fixed_lease', true);
            })->get() as $service)
                <!-- Basic Service Info -->
                <tr style="background-color: #fff;">
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Service Name:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $service->service->name ?? 'NA' }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:اسم الخدمة</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Service Type:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ str_replace('_', ' ', ucfirst($service->service_type ?? 'NA')) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:نوع الخدمة</span>
                    </td>
                </tr>

                <!-- Amount Information -->
                <tr style="background-color: #F0F0F7;">
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Service Amount:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ number_format($service->lease_service_amount ?? 0, 2) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:مبلغ الخدمة</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">To Be Paid Amount:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ number_format($service->to_be_paid_amount ?? 0, 2) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:المبلغ المراد دفعه</span>
                    </td>
                </tr>

                <!-- Payment Details -->
                <tr style="background-color: #fff;">
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Payment Period:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ str_replace('_', ' ', ucfirst($service->to_be_paid_per ?? 'NA')) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:فترة الدفع</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Payment Method:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ str_replace('_', ' ', ucfirst($service->to_be_paid_by ?? 'NA')) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:طريقة الدفع</span>
                    </td>
                </tr>

                <!-- Additional Service Details -->
                <tr style="background-color: #F0F0F7;">
                    <td style="padding: 8px; border: 1px solid #ddd;">
                        <span style="color: #374151; font-weight: 800;">Number of Services:</span>
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $service->number_of_service ?? 'NA' }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                        <span style="color: #374151; font-weight: 800;">:عدد الخدمات</span>
                    </td>
                    @if($service->service_type === 'meter_reading')
                        <td style="padding: 8px; border: 1px solid #ddd;">
                            <span style="color: #374151; font-weight: 800;">Current Meter Reading:</span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ number_format($service->meter_current_reading ?? 0, 2) }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                            <span style="color: #374151; font-weight: 800;">:قراءة العداد الحالية</span>
                        </td>
                    @else
                        <td colspan="3" style="padding: 8px; border: 1px solid #ddd;"></td>
                    @endif
                </tr>

                <!-- Conditional Rows Based on Service Type -->
                @if($service->service_type === 'on_lease' || $service->service_type === 'fixed_value')
                    <tr style="background-color: #fff;">
                        <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">
                        {{ __($service->service_type === 'on_lease' ? 'Lease-based Service:' : 'Fixed Value Service:') }}
                    </span>
                        </td>
                        <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                            {{ $service->service_type === 'on_lease' ? 'This service is tied to the lease duration' : 'Service with fixed payment amount' }}
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">
                        {{ __($service->service_type === 'on_lease' ? ':خدمة مرتبطة بمدة الإيجار' : ':خدمة بقيمة ثابتة') }}
                    </span>
                        </td>
                    </tr>
                @endif

                @if(!$loop->last)
                    <tr><td colspan="6" style="padding: 8px;"></td></tr>
                @endif
            @endforeach

            <!-- Total Contract Value -->
            <tr style="background-color: #F0F0F7;">
                <td style="padding: 8px; border: 1px solid #ddd;">
                    <span style="color: #374151; font-weight: 800;">Total Contract Value:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                    {{ number_format($lease->total_amount ?? 0, 2) }}
                </td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">
                    <span style="color: #374151; font-weight: 800;">:اجمالي قيمة العقد</span>
                </td>
            </tr>
        </table>

{{--12--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">١٢.</span>
                <span>جدول سداد الُدفعات</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Rent Payments Schedule</div>
            <div style="clear: both;"></div>
        </div>

        <table style="width: 100%; border-collapse: collapse;">
            <!-- Header Row -->
            <tr style="background-color: #F0F0F7;">
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 15%;">
                    <span style="color: #374151; font-weight: 800;">Amount<br>القيمة</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 15%;">
                    <span style="color: #374151; font-weight: 800;">End of payment deadline(AD)<br>نهاية مهلة السداد</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 15%;">
                    <span style="color: #374151; font-weight: 800;">Due Date(AD)<br>تاريخ الاستحقاق</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 20%;">
                    <span style="color: #374151; font-weight: 800;">Rental Period<br>الفترة الايجارية</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 15%;">
                    <span style="color: #374151; font-weight: 800;">End of payment deadline(AD)<br>نهاية مهلة السداد</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 15%;">
                    <span style="color: #374151; font-weight: 800;">Due Date(AD)<br>تاريخ الاستحقاق</span>
                </th>
                <th style="padding: 8px; border: 1px solid #ddd; text-align: center; width: 5%;">
                    <span style="color: #374151; font-weight: 800;">No<br>الرقم</span>
                </th>
            </tr>

            <!-- Payment Schedule Rows -->
            @foreach($lease->invoiceSchedules()->orderBy('installment_date')->get() as $schedule)
                <tr style="background-color: {{ $loop->even ? '#fff' : '#F0F0F7' }};">
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ number_format($schedule->total_amount ?? 0, 2) }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ \Carbon\Carbon::parse($schedule->due_date)->format('Y-m-d') }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ \Carbon\Carbon::parse($schedule->installment_date)->format('Y-m-d') }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        @php
                            $startDate = \Carbon\Carbon::parse($schedule->installment_date);
                            $endDate = \Carbon\Carbon::parse($schedule->due_date);
                            $period = $startDate->format('M d') . ' - ' . $endDate->format('M d, Y');
                        @endphp
                        {{ $period }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ \Carbon\Carbon::parse($schedule->due_date)->timezone('Asia/Riyadh')->locale('ar')->calendar() }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ \Carbon\Carbon::parse($schedule->installment_date)->timezone('Asia/Riyadh')->locale('ar')->calendar() }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #111827; font-weight: 800;">
                        {{ $schedule->bill_number ?? 'NA' }}
                    </td>
                </tr>
            @endforeach

            <!-- Summary Row -->
            <tr style="background-color: #F0F0F7;">
                <td colspan="2" style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">Total Amount:</span>
                    <span style="color: #111827; font-weight: 800;">{{ number_format($lease->invoiceSchedules()->sum('total_amount') ?? 0, 2) }}</span>
                </td>
                <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">Paid Amount:</span>
                    <span style="color: #111827; font-weight: 800;">{{ number_format($lease->invoiceSchedules()->sum('paid_amount') ?? 0, 2) }}</span>
                </td>
                <td colspan="2" style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">Remaining:</span>
                    <span style="color: #111827; font-weight: 800;">{{ number_format(($lease->invoiceSchedules()->sum('total_amount') - $lease->invoiceSchedules()->sum('paid_amount')) ?? 0, 2) }}</span>
                </td>
            </tr>

            <!-- Payment Details Row -->
            <tr style="background-color: #fff;">
                <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">Total Installments:</span>
                    <span style="color: #111827; font-weight: 800;">{{ $lease->invoiceSchedules()->count() }}</span>
                    <span style="color: #374151; font-weight: 800;">إجمالي الأقساط:</span>
                </td>
                <td colspan="4" style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                    <span style="color: #374151; font-weight: 800;">Service Amount:</span>
                    <span style="color: #111827; font-weight: 800;">{{ number_format($lease->invoiceSchedules()->sum('installment_service_amount') ?? 0, 2) }}</span>
                    <span style="color: #374151; font-weight: 800;">مبلغ الخدمات:</span>
                </td>
            </tr>
        </table>


{{--13--}}
        <div style="background-color: #205848; color: white; overflow: hidden; font-weight: bold;">
            <div style="float: right; padding: 8px; width: calc(50% - 40px); text-align: right;">
                <span style="margin-left: 5px;">١٣.</span>
                <span>التزامات الأطراف</span>
            </div>
            <div style="float: left; padding: 8px; width: 50%; text-align: left; border-right: 1px solid white;">Obligations by Parties</div>
            <div style="clear: both;"></div>
        </div>
        <table style="width: 100%; border-collapse: collapse;">
            @foreach($lease->leaseTerms as $term)
                <tr style="background-color: {{ $loop->even ? '#fff' : '#F0F0F7' }};">
                    <td style="padding: 8px; border: 1px solid #ddd; color: #111827; font-weight: 800;">
                        {{ $term->value ?? 'NA' }}
                    </td>
                    <td style="padding: 8px; border: 1px solid #ddd; width: 20%; color: #374151; font-weight: 800;">
                        {{ $term->key ?? 'NA' }}
                    </td>
                </tr>
            @endforeach
        </table>

    </div>


</div>
