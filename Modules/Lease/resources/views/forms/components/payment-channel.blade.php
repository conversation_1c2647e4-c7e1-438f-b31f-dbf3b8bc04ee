@php
    use Modules\Payment\app\Models\PaymentMethod;
    $PaymentMethod = PaymentMethod::where('is_active','1')->where('method_type',$this->data['payment_method'])->get();
@endphp

<div class="">
    <div class=" mb-3">
        <span class="flex items-center gap-1">{{__('Available payment channels based on the selected payment method')}} <x-heroicon-m-information-circle class="w-5 h-5 mx-1 text-[#3eb896]"/>
        </span>
    </div>

    <div class="">
        @foreach($PaymentMethod as $index => $method)
            <div class="flex items-center gap-3 p-2 rounded-lg">
                <div class="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-[#e5f3ef] text-[#42a396]">
                    {{ $index + 1 }}
                </div>
                <span class="">{{ $method->name}}</span>
            </div>
        @endforeach
    </div>
</div>
