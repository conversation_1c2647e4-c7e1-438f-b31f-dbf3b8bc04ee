@php

    $hasValidVAT = collect($this->data['owners'])->contains(function ($member) {
        return isset($member['vat_number']) && !is_null($member['vat_number']) && $member['vat_number'] !== 0;
    });

    $totalAmount =
        (float) $this->data['total_amount']
        + (float) $this->data['total_services_amount']
        + (($this->data['lease_type'] === Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL && $hasValidVAT)
            ? ((float) $this->data['total_amount'] * (float) config('lease.vat'))
            : 0);
@endphp
<div class="border-b border-b-gray-200 dark:border-b-gray-700 flex justify-between flex-wrap">
    <div class="sm:w-1/2 w-full min-w-[250px]">
        <h3 class="mb-2 text-xl font-semibold">{{__('Contract starting balance')}}</h3>
        <p class="text-sm mb-4">{{__('the contract balance start from start date')}}</p>
    </div>
    <div class="sm:w-1/2 w-full min-w-[210px]">
        <h3 class="text-xl font-semibold mb-2">{{__('Total contract balance')}}</h3>
        <p class="text-xl font-semibold mb-4">{{$totalAmount}} <span class="icon-saudi_riyal"></span></p>
    </div>
</div>
