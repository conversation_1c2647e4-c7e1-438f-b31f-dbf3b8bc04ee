@php
    use Modules\Lease\Services\LeasePaymentService;
    use Modules\Service\Enums\ServicePaymentEnum;


    if($this->data['total_amount'] != 0 && $this->data['start_date'] && $this->data['end_date'] && $this->data['rent_amount']){

        $units_list = [];
        foreach ($this->data['units_services'] as $unit) {
            $services = [];
            $otherServices = [];

            // Process main services
            foreach ($unit['services'] as $service) {
                if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {

                    $services[$service['service_id']] = [
                        'lease_service_amount' => (float) $service['lease_service_amount'],
                        'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                    ];
                }
            }

            // Process other services if they exist
            if (!empty($unit['other_services'])) {
                foreach ($unit['other_services'] as $service) {
                    if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {

                        $otherServices[$service['service_id']] = [
                            'lease_service_amount' => (float) $service['lease_service_amount'],
                            'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                        ];
                    }
                }
            }

            $units_list[$unit['unit_id']] = [
                'services' => $services,
                'other_services' => $otherServices
            ];
        }
        $commercialLeaseServices = $this->data["commercial_lease_services"] ?? [];
        $otherCommercialLeaseServices = $this->data["other_commercial_lease_services"] ?? [];

        // Transform other services into a compatible format
        $transformedOtherServices = array_map(function ($service) {
            return [
                "service_id" => $service["service_id"],
                "value" => $service["value"],
            ];
        }, $otherCommercialLeaseServices);

        // Merge the two service arrays
        $allServices = array_merge($commercialLeaseServices, $transformedOtherServices);

        $leasePaymentService = new LeasePaymentService();

        $TotalLeaseBalance = $leasePaymentService->getTotalLeaseBalance(
            $this->data['start_date'],
            $this->data['end_date'],
            $this->data['payment_type'],
            $this->data['payment_repeated_type'],
            $this->data['rent_amount'],
            $units_list,
            $allServices,
            $this->data['lease_type'],
            $this->data['owners']
        );
        $paid_amount = $this->data['paid_amount'];
    }


@endphp
<div>
    <div class="border-b border-b-gray-200 dark:border-b-gray-700 pb-3">
        <h3 class="text-xl font-semibold">
            {{__('Rent payment details')}}
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
            {{__('It includes the rental value, the value-added tax amount (if any), the value of public services, and the annual rent for gas, electricity, water, and sewage if they are specified as fixed amounts.')}}
        </p>
    </div>
    <div class="border-b border-b-gray-200 dark:border-b-gray-700 grid grid-cols-2 gap-4 py-3">
        <div class="">
            <h3 class="text-md font-semibold mb-1 text-gray-500 dark:text-gray-400">{{__('Total Rent amount')}}</h3>
            <p class="text-xl font-semibold ">{{isset($TotalLeaseBalance['total_lease_rent_amount']) ? $TotalLeaseBalance['total_lease_rent_amount'] : 0}} <span class="icon-saudi_riyal"></span></p>
        </div>
        <div class="">
            <h3 class="mb-1 text-md font-semibold text-gray-500 dark:text-gray-400">{{__('Total Service amount')}}</h3>
            <p class="text-xl font-semibold">{{isset($TotalLeaseBalance['total_lease_services_amount']) ? $TotalLeaseBalance['total_lease_services_amount'] : 0}} <span class="icon-saudi_riyal"></span></p>
        </div>
        @if($this->data['lease_type'] == Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL)
        <div class="">
            <h3 class="mb-1 text-md font-semibold text-gray-500 dark:text-gray-400">{{__('Vat')}}</h3>
            <p class="text-xl font-semibold">{{isset($TotalLeaseBalance['total_lease_services_amount']) ? $TotalLeaseBalance['total_lease_rent_amount'] * config('lease.vat'): 0}} <span class="icon-saudi_riyal"></span></p>
        </div>
        @endif
        <div class="">
            <h3 class="mb-1 text-md font-semibold text-gray-500 dark:text-gray-400">{{__('Total paid')}}</h3>
            <p class="text-xl font-semibold">{{isset($paid_amount) ? $paid_amount : 0}} <span class="icon-saudi_riyal"></span></p>
        </div>
    </div>

    <div class="grid grid-cols-2 gap-4 py-3">
        <div>
            <h3 class="text-md font-semibold mb-1 text-gray-500 dark:text-gray-400">
                {{ $this->data['lease_type'] === Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL 
                    ? __('Rent Scheduled including vat') 
                    : __('Rent Scheduled') }}
            </h3>
            <p class="text-xl font-semibold">
                @php
                    $totalRentAmount = $TotalLeaseBalance['total_lease_rent_amount'] ?? 0;
                    $vat = config('lease.vat');
                    $rentWithVAT = $totalRentAmount + ($totalRentAmount * $vat);
                    $paidAmount = $paid_amount ?? 0;
                    $rentBalance = $this->data['lease_type'] === Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL 
                        ? $rentWithVAT - $paidAmount 
                        : $totalRentAmount - $paidAmount;
                @endphp
                {{ $rentBalance > 0 ? $rentBalance : 0 }} <span class="icon-saudi_riyal"></span>
            </p>
        </div>
        <div>
            <h3 class="mb-1 text-md font-semibold text-gray-500 dark:text-gray-400">{{ __('Service Scheduled') }}</h3>
            <p class="text-xl font-semibold">
                {{ $TotalLeaseBalance['total_lease_services_amount'] ?? 0 }} <span class="icon-saudi_riyal"></span>
            </p>
        </div>
    </div>

</div>
