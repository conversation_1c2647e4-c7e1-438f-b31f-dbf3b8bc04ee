@php
    use Modules\Lease\Services\LeasePaymentService;
    use Modules\Service\Enums\ServicePaymentEnum;
    
    $leasePaymentService = new LeasePaymentService();
    
    if($this->data['total_amount'] != 0 && $this->data['start_date'] && $this->data['end_date'] && $this->data['rent_amount'] && $this->data['days_permitted']>=0){

        $units_list = [];
        foreach ($this->data['units_services'] as $unit) {
            $services = [];
            $otherServices = [];

            // Process main services
            foreach ($unit['services'] as $service) {
                if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                    $services[$service['service_id']] = [
                        'lease_service_amount' => (float) $service['lease_service_amount'],
                        'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                    ];
                }
            }

            // Process other services if they exist
            if (!empty($unit['other_services'])) {
                foreach ($unit['other_services'] as $service) {
                    if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                        $otherServices[$service['service_id']] = [
                            'lease_service_amount' => (float) $service['lease_service_amount'],
                            'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                        ];
                    }
                }
            }

            $units_list[$unit['unit_id']] = [
                'services' => $services,
                'other_services' => $otherServices
            ];
        }
        
        $commercialLeaseServices = $this->data["commercial_lease_services"] ?? [];
        $otherCommercialLeaseServices = $this->data["other_commercial_lease_services"] ?? [];

        // Transform other services into a compatible format
        $transformedOtherServices = array_map(function ($service) {
            return [
                "service_id" => $service["service_id"],
                "value" => $service["value"],
            ];
        }, $otherCommercialLeaseServices);

        // Merge the two service arrays
        $allServices = array_merge($commercialLeaseServices, $transformedOtherServices);
        $TotalLeaseBalance = $leasePaymentService->getTotalLeaseBalance(
            $this->data['start_date'],
            $this->data['end_date'],
            $this->data['payment_type'],
            $this->data['payment_repeated_type'],
            $this->data['rent_amount'],
            $units_list,
            $allServices,
            $this->data['lease_type'],
            $this->data['owners']
        );
        $total_services_amount = $TotalLeaseBalance['total_lease_services_amount'];
        $total_amount = $TotalLeaseBalance['total_lease_rent_amount'];
        $paymentSchedule = $leasePaymentService->generatePaymentSchedule($this->data['start_date'],$this->data['end_date'],$this->data['total_amount'] ,$total_services_amount,$this->data['paid_amount'],$this->data['payment_type'],$this->data['payment_repeated_type'],$this->data['days_permitted'] ?? 0 , $this->data['lease_type'] , $this->data['owners']);
    }
    $hasValidVAT = $leasePaymentService->checkValidVatForLeaseMembers($this->data['owners']);
@endphp

<div class="border-b border-b-gray-200 dark:border-b-gray-700 pb-3">
    <h3 class="text-xl font-semibold">
        {{__('Payment invoice table')}}
    </h3>
    <p class="text-sm text-gray-500 dark:text-gray-400">
        {{__('The tenant must make rent payments according to the following schedule. Payment is considered due 15 or 30 days after the date the payment was issued.')}}
    </p>
</div>
<div class="pt-3 overflow-x-auto">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('id')}}
                </th>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Release Date')}}
                </th>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Due Date')}}
                </th>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Rent')}}
                </th>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Services')}}
                </th>
                @if($this->data['lease_type'] == Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL && $hasValidVAT)
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Vat  (15% of the total)')}}
                </th>
                @endif
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('Total')}}
                </th>
            </tr>
        </thead>
        <tbody>
            @if(isset($paymentSchedule))
                @foreach($paymentSchedule as $payment)
                    <tr class="border-b {{ isset($payment->previous) && $payment->previous ? 'bg-[#bc5b09] text-white' : 'bg-white dark:bg-gray-800' }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ $loop->iteration }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ \Carbon\Carbon::parse($payment->installment_date)->format('d-m-Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ \Carbon\Carbon::parse($payment->due_date)->format('d-m-Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ number_format($payment->installment_amount, 2) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ number_format($payment->installment_service_amount, 2) }}
                        </td>
                        @if($this->data['lease_type'] == Modules\Lease\Enums\LeaseTypesEnum::COMMERCIAL &&  $hasValidVAT)
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ number_format($payment->vat, 2) }}
                        </td>
                        @endif
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{ number_format($payment->total_amount, 2) }}
                        </td>
                    </tr>
                @endforeach
            @endif
        </tbody>
    </table>
</div>
