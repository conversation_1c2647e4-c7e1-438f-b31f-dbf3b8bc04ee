<div class="bg-white rounded-lg shadow p-4 border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
    <div class="flex flex-col space-y-2">
        @if(!$getState()['is_organization'])
        <div class="flex items-center space-x-2">
            {{-- <span class="text-gray-600 font-medium">{{ __('Name') }} : </span> --}}
            <span class="text-gray-800 font-bold dark:text-gray-200">{{ $getState()['name'] ?? '-' }}</span>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-400 dark:text-gray-400 font-medium">{{ __('National Number') }}</span>
            <span class="text-[#1a5848] dark:text-[#3eb896] font-semibold"> #{{ $getState()['national_id'] ?? '-' }}</span>
        </div>
        @else
        <div class="flex items-center space-x-2">
            {{-- <span class="text-gray-600 font-medium">{{ __('Name') }} : </span> --}}
            <span class="text-gray-800 font-bold dark:text-gray-200">{{ $getState()['name'] ?? '-' }}</span>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-400 dark:text-gray-400 font-medium">{{ __('Unified Number') }}</span>
            <span class="text-[#1a5848] dark:text-[#3eb896] font-semibold"> #{{ $getState()['unified_number'] ?? '-' }}</span>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-400 dark:text-gray-400 font-medium">{{ __('Ownership Document Number') }}</span>
            <span class="text-[#1a5848] dark:text-[#3eb896] font-semibold"> #{{ $getState()['ownership_document_number'] ?? '-' }}</span>
        </div>
        @endif
    </div>
</div>
