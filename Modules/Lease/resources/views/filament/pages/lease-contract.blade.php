<x-filament-panels::page>
    <div class="flex flex-col    bg-white rounded-xl shadow">
        <!-- Contract Header -->
        <div class="contract-header">
            <div class="logo-container">
                <div class="logo-left">
                    <img src="{{ config('app.url') }}/images/logos_contracts.svg" alt="Ejar Logo" class="ejar-logo">
                </div>
                <div class="title-right">
                    <div class="title-ar">عقــــــــد إيجــــــار</div>
                    <div class="title-en">TENANCY CONTRACT</div>
                </div>
            </div>
            <div class="contract-notice">
                <p class="notice-ar">يعتبر هذا العقد عقداً موثقاً وسنداً</p>
                <p class="notice-ar">تنفيذياً بموجب قرار مجلس الوزراء رقم (١٣١) وتاريخ ١٤٣٥/٤/٣ هـ</p>
            </div>
        </div>

        <style>
            .contract-header {
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                background-color: white;
            }

            .logo-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .logo-left, .logo-center {
                /*width: 120px;*/
            }

            .logo-left img, .logo-center img {
                width: 100%;
                height: auto;
            }

            .title-right {
                text-align: right;
                font-weight: bold;
            }

            .title-ar {
                font-size: 1.5rem;
                color: #1f2937;
                margin-bottom: 0.5rem;
            }

            .title-en {
                font-size: 1.25rem;
                color: darkgoldenrod;
            }

            .contract-notice {
                text-align: center;
                color: #ef4444;
                font-size: 0.875rem;
                margin-top: 1rem;
            }

            .notice-ar {
                margin: 0.25rem 0;
                direction: rtl;
                font-size: 20px;
                font-weight: 800;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .logo-container {
                    flex-direction: column;
                    align-items: center;
                    gap: 1rem;
                }

                .title-right {
                    text-align: center;
                    margin-top: 1rem;
                }

                .logo-left, .logo-center {
                    width: 100px;
                }
            }
        </style>
        @php
            $tenantRepresenters = $lease->leaseMembers->where('member_role', 'tenant_representer');
            // Debug information
//             dd([
//                 'all_members' => $lease->leaseMembers->toArray(),
//                 'tenant_representers' => $tenantRepresenters->toArray()
//             ]);
        @endphp
        <!-- 1. Contract Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">1</div>
                <div class="header-cell border-right">
                    <span class="header-en">Contract Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات العقد</span>
                </div>
            </div>

            <div class="data-grid">
                <div class="data-row">
                    <div class="data-cell">
                        <span class="label-en">Contract Number:</span>
                        <span class="value">{{ $lease->id ?? '-' }}</span>
                        <span class="label-ar">رقم العقد:</span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">Contract Type:</span>
                        <span class="value">{{ $lease->lease_type ?? '-' }}</span>
                        <span class="label-ar">نوع العقد:</span>
                    </div>
                </div>

                <div class="data-row odd">
                    <div class="data-cell data-cell-100 border-right">
                        <span class="label-en">Contract Selling Date:</span>
                        <span class="value">{{ $lease->created_at->format('Y-m-d') ?? '-' }}</span>
                        <span class="label-ar">تاريخ ابرام العقد:</span>
                    </div>
                </div>

                <div class="data-row">
                    <div class="data-cell border-right">
                        <span class="label-en">Tenancy end date:</span>
                        <span class="value">{{ $lease->end_date ?? '-' }}</span>
                        <span class="label-ar">تاريخ نهاية مدة الايجار:</span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">Tenancy start date:</span>
                        <span class="value">{{ $lease->start_date ?? '-'}}</span>
                        <span class="label-ar">تاريخ بداية مدة الايجار:</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 2. Lessor Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">2</div>
                <div class="header-cell border-right">
                    <span class="header-en">Lessor Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات المؤجر</span>
                </div>
            </div>

            @php
                $lessors = $lease->leaseMembers->where('member_role', 'lessor');
            @endphp

            @foreach($lessors as $leaseMember)
                @php
                    $member = $leaseMember->member;
                @endphp
                <div class="data-grid">
                    @if($leaseMember->member_type === 'organization')
                        {{-- Organization Data --}}
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Organization Name:</span>
                                <span class="value">{{ $leaseMember->member->name ?? '-' }}</span>
                                <span class="label-ar">اسم المنشأة:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">CR Number:</span>
                                <span class="value">{{ $leaseMember->member->unified_number ?? '-' }}</span>
                                <span class="label-ar">رقم السجل التجاري:</span>
                            </div>
                        </div>

{{--                        <div class="data-row">--}}
{{--                            <div class="data-cell data-cell-100 border-right">--}}
{{--                                <span class="label-en">Ownership Document Number:</span>--}}
{{--                                <span class="value">{{ $leaseMember->ownership_document_number ?? '-' }}</span>--}}
{{--                                <span class="label-ar">رقم صك الملكية:</span>--}}
{{--                            </div>--}}
{{--                        </div>--}}

                        <div class="data-row ">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Percentage:</span>
                                <span class="value">{{ $leaseMember->percentage ?? '0' }}%</span>
                                <span class="label-ar">النسبة:</span>
                            </div>
                        </div>
                    @else
                        {{-- Individual Data --}}
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Name:</span>
                                <span class="value">{{ $member->name ?? '-' }}</span>
                                <span class="label-ar">الاسم:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">ID No.:</span>
                                <span class="value">{{ $member->national_id ?? '-' }}</span>
                                <span class="label-ar">رقم الهوية:</span>
                            </div>
                        </div>

                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Percentage:</span>
                                <span class="value">{{ $leaseMember->percentage ?? '0' }}%</span>
                                <span class="label-ar">النسبة:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell border-right">
                                <span class="label-en">Email:</span>
                                <span class="value">{{ $member->email ?? '-' }}</span>
                                <span class="label-ar">البريد الالكتروني:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Mobile No.</span>
                                <span class="value">{{ $member->phone ?? '-' }}</span>
                                <span class="label-ar">رقم الجوال:</span>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- 3. Lessor Representative Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">3</div>
                <div class="header-cell border-right">
                    <span class="header-en">Lessor Representative Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات ممثل المؤجر</span>
                </div>
            </div>

            @php
                $lessorRepresenters = $lease->leaseMembers->where('member_role', 'lessor_representer');
            @endphp

            @if($lessorRepresenters->count() > 0)
                @foreach($lessorRepresenters as $leaseMember)
                    @php
                        $member = $leaseMember->member;
                    @endphp
                    <div class="data-grid">
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Name:</span>
                                <span class="value">{{ $member->name ?? '-' }}</span>
                                <span class="label-ar">الاسم:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">ID No.</span>
                                <span class="value">{{ $member->national_id ?? '-' }}</span>
                                <span class="label-ar">رقم الهوية:</span>
                            </div>
                        </div>

                        <div class="data-row">
                            <div class="data-cell border-right">
                                <span class="label-en">Email:</span>
                                <span class="value">{{ $member->email ?? '-' }}</span>
                                <span class="label-ar">البريد الالكتروني:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Mobile No.</span>
                                <span class="value">{{ $member->phone ?? '-' }}</span>
                                <span class="label-ar">رقم الجوال:</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="data-grid">
                    <div class="data-row">
                        <div class="data-cell border-right">
                            <span class="label-en">The Lessor is represented by himself or herself</span>
                        </div>
                        <div class="data-cell">
                            <span class="label-ar">المؤجر ممثل نفسة</span>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- 4. Tenant Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">4</div>
                <div class="header-cell border-right">
                    <span class="header-en">Tenant Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات المستأجر</span>
                </div>
            </div>

            @php
                $tenants = $lease->leaseMembers->where('member_role', 'tenant');
            @endphp

            @foreach($tenants as $leaseMember)
                @php
                    $member = $leaseMember->member;
                @endphp
                <div class="data-grid">
                    @if($leaseMember->is_organization)
                        {{-- Organization Data --}}
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Organization Name:</span>
                                <span class="value">{{ $leaseMember->member->name ?? '-' }}</span>
                                <span class="label-ar">اسم المنشأة:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">CR Number:</span>
                                <span class="value text-center">{{ $leaseMember->member->unified_number ?? '-' }}</span>
                                <span class="label-ar">رقم السجل التجاري:</span>
                            </div>
                        </div>


                        @if($leaseMember->registration_date)
                            <div class="data-row odd">
                                <div class="data-cell data-cell-100 border-right">
                                    <span class="label-en">Registration Date:</span>
                                    <span class="value">{{ $leaseMember->registration_date ?? '-' }}</span>
                                    <span class="label-ar">تاريخ التسجيل:</span>
                                </div>
                            </div>
                        @endif

                        @if($leaseMember->account_identity)
                            <div class="data-row {{ $leaseMember->registration_date ? '' : 'odd' }}">
                                <div class="data-cell data-cell-100 border-right">
                                    <span class="label-en">Account Identity:</span>
                                    <span class="value">{{ $leaseMember->account_identity ?? '-' }}</span>
                                    <span class="label-ar">هوية الحساب:</span>
                                </div>
                            </div>
                        @endif

                    @else
                        {{-- Individual Data --}}
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Name:</span>
                                <span class="value">{{ $member->name ?? '-' }}</span>
                                <span class="label-ar">الاسم:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">ID No.:</span>
                                <span class="value">{{ $member->national_id ?? '-' }}</span>
                                <span class="label-ar">رقم الهوية:</span>
                            </div>
                        </div>



                        {{-- Contact Information (only for individuals) --}}
                        <div class="data-row ">
                            <div class="data-cell border-right">
                                <span class="label-en">Email:</span>
                                <span class="value">{{ $member->email ?? '-' }}</span>
                                <span class="label-ar">البريد الالكتروني:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Mobile No.:</span>
                                <span class="value">{{ $member->phone ?? '-' }}</span>
                                <span class="label-ar">رقم الجوال:</span>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- 5. Tenant Representative Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">5</div>
                <div class="header-cell border-right">
                    <span class="header-en">Tenant Representative Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات ممثل المستأجر</span>
                </div>
            </div>

            @php
                $tenantRepresenters = $lease->leaseMembers->where('member_role', 'tenant_representer');
            @endphp

            @if($tenantRepresenters->count() > 0)
                @foreach($tenantRepresenters as $leaseMember)
                    @php
                        $member = $leaseMember->member;
                    @endphp
                    <div class="data-grid">
                        <div class="data-row">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">Name:</span>
                                <span class="value">{{ $member->name ?? '-' }}</span>
                                <span class="label-ar">الاسم:</span>
                            </div>
                        </div>

                        <div class="data-row odd">
                            <div class="data-cell data-cell-100 border-right">
                                <span class="label-en">ID No.</span>
                                <span class="value">{{ $member->national_id ?? '-' }}</span>
                                <span class="label-ar">رقم الهوية:</span>
                            </div>
                        </div>

                        <div class="data-row">
                            <div class="data-cell border-right">
                                <span class="label-en">Email:</span>
                                <span class="value">{{ $member->email ?? '-' }}</span>
                                <span class="label-ar">البريد الالكتروني:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Mobile No.</span>
                                <span class="value">{{ $member->phone ?? '-' }}</span>
                                <span class="label-ar">رقم الجوال:</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="data-grid">
                    <div class="data-row">
                        <div class="data-cell border-right">
                            <span class="label-en">The Tenant is represented by himself or herself</span>
                        </div>
                        <div class="data-cell">
                            <span class="label-ar">المستأجر ممثل نفسة</span>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- 6. Brokerage Entity and broker Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">6</div>
                <div class="header-cell border-right">
                    <span class="header-en">Brokerage Entity and Broker Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات منشأة الواسطة العقارية</span>
                </div>
            </div>
            <div class="data-grid">
                <div class="data-row">
                    <div class="data-cell border-right">
                        <span class="label-en">Brokerage Entity Name:</span>
                        <span class="value">{{ $lease->company->name ?? '' }}</span>
                        <span class="label-ar">اسم منشأة الوساطة العقارية:</span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">Brokerage Entity Name Address:</span>
                        <span class="value">{{ $lease->company->address ?? '-' }}</span>
                        <span class="label-ar">عنوان منشأة الوساطة العقارية:</span>
                    </div>
                </div>
                <div class="data-row odd">
                    <div class="data-cell data-cell-100">
                        <span class="label-en">Broker Name:</span>
                        <span class="value">{{ $lease->company->user->name ?? '-' }}</span>
                        <span class="label-ar">اسم الموظف:</span>
                    </div>
                </div>
                <div class="data-row">
                    <div class="data-cell border-right">
                        <span class="label-en">Email:</span>
                        <span class="value">{{ $lease->company->email ?? '-' }}</span>
                        <span class="label-ar">البريد الالكتروني:</span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">Mobile No.</span>
                        <span class="value">{{ $lease->company->phone ?? '-' }}</span>
                        <span class="label-ar">رقم الجوال:</span>
                    </div>
                </div>
                <div class="data-row odd">
                    <div class="data-cell data-cell-100 border-right">
                        <span class="label-en">CR No.</span>
                        <span class="value">{{ $lease->company->comp_cr_number ?? '-' }}</span>
                        <span class="label-ar">رقم السجل التجاري:</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 7. Ownership Document Data -->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">7</div>
                <div class="header-cell border-right">
                    <span class="header-en">Ownership Document Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات مستندات الملكّية</span>
                </div>
            </div>

            @php
                $documents = $lease->documents()->where([
                    'morphable_type' => 'Modules\Lease\app\Models\Lease',
                    'morphable_id' => $lease->id
                ])->get();
            @endphp

            @if($documents->count() > 0)
                @foreach($documents as $document)
                    <div class="data-grid">
                        <div class="data-row">
                            <div class="data-cell border-right">
                                <span class="label-en">Document Number:</span>
                                <span class="value">{{ $document->metadata['document_number'] ?? '-' }}</span>
                                <span class="label-ar">رقم المستند:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Document Name & Type:</span>
                                <span class="value">{{ $document->metadata['document_name_and_type'] ?? '-' }}</span>
                                <span class="label-ar">اسم ونوع المستند:</span>
                            </div>
                        </div>
                        <div class="data-row odd">
                            <div class="data-cell border-right">
                                <span class="label-en">Released By:</span>
                                <span class="value">{{ $document->metadata['released_by'] ?? '-' }}</span>
                                <span class="label-ar">جهة الأصدار:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Released In:</span>
                                <span class="value">{{ $document->metadata['released_in'] ?? '-' }}</span>
                                <span class="label-ar">مكان الإصدار:</span>
                            </div>
                        </div>
                        <div class="data-row">
                            <div class="data-cell border-right">
                                <span class="label-en">Release Date:</span>
                                <span class="value">{{ $document->metadata['release_date'] ?? '-' }}</span>
                                <span class="label-ar">تاريخ الإصدار:</span>
                            </div>
                            <div class="data-cell">
                                <span class="label-en">Expiration Date:</span>
                                <span class="value">{{ $document->metadata['expiration_date'] ?? '-' }}</span>
                                <span class="label-ar">تاريخ الانتهاء:</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="data-grid">
                    <div class="data-row">
                        <div class="data-cell text-center" colspan="2">
                            No ownership documents found
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- 8. Property Data -->
        @php
            // Get the first unit from the lease
            $leaseUnit = $lease->leaseUnits()->first();
            $unit = $leaseUnit ? $lease->units()->find($leaseUnit->unit_id) : null;
            $propertyDetails = $unit ? $lease->getPropertyDetails($unit->id) : null;
//            dd($propertyDetails);
        @endphp
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">8</div>
                <div class="header-cell border-right">
                    <span class="header-en">Property Data</span>
                </div>

                <div class="header-cell">
                    <span class="header-ar"> بيانات العقار</span>
                </div>

            </div>
            <div class="data-grid">
                <div class="data-row ">
                    <div class="data-cell data-cell-100 border-right">
                        <span class="label-en">National Address.</span>
                        <span class="value">{{ $propertyDetails->address ?? '' }}</span>
                        <span class="label-ar">:العنوان الوطني</span>
                    </div>
                </div>
                <div class="data-row odd">
                    <div class="data-cell border-right">
                        <span class="label-en">Type Property:</span>
                        <span class="value">{{ $propertyDetails->propertyType->name ?? '-'}}</span>
                        <span class="label-ar">:نوع بناء العقار </span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">Property Usage:</span>
                        <span class="value"> {{ $propertyDetails->usability->name  ?? '-'}} </span>
                        <span class="label-ar">:الغرض من استخدام العقار</span>
                    </div>
                </div>
                <!-- Dynamic Attributes -->
                @php
                    $propertyAttributes = $propertyDetails->attributes()
                        ->join('attributes', 'property_attributes.attribute_id', '=', 'attributes.id')
                        ->select('attributes.name', 'property_attributes.value')
                        ->get()
                        ->chunk(2);
                @endphp

                @foreach($propertyAttributes as $pair)
                    <div class="data-row {{ $loop->index % 2 == 0 ? '' : 'odd' }}">
                        @foreach($pair as $attribute)
                            <div class="data-cell">
                                <span class="label-en">{{$attribute->name ?? '-'}}:</span>
                                <span class="value">{{ $attribute->value ?? '-'}}</span>
                                <span class="label-ar"></span>
                            </div>
                        @endforeach
                    </div>
                @endforeach

                <div class="data-row ">
                    <div class="data-cell data-cell-100 amenity  border-right">
                        <span class="label-en"></span>
                        <span class="value amenity">
    @foreach($propertyDetails->amenities as $amenity)
                                <div class="amenity-item">
                                     @if($amenity->icon)<i class="{{ $amenity->icon ?? '-' }}"></i>@endif <span>{{ $amenity->name ?? '' }} / </span>
                                </div>
                            @endforeach
                            </span>
                        <span class="label-ar">

                        </span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 9. Rental Units Data-->
        @php
            // Get all services for the lease units
            $leaseUnits = $lease->units->map(function($unit) {
                return $unit->pivot->id;
            });

            $leaseUnitServices = \Modules\Lease\app\Models\LeaseUnitService::with('service')
                ->whereIn('lease_unit_id', $leaseUnits)
                ->get();


        @endphp
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">9</div>
                <div class="header-cell border-right">
                    <span class="header-en">Rental Units Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">بيانات الوحدات الإيجارَّية</span>
                </div>
            </div>
            @foreach($lease->units as $unit)
                @php
                    $propertyWithType = $lease->unitWithType($unit->id);
                    $unitWithDetails = $lease->leaseProperties()
                        ->with(['usability', 'amenities'])
                        ->where('properties.id', $unit->id)
                        ->first();
                @endphp
                <div class="data-grid">
                    <div class="data-row">
                        <div class="data-cell border-right">
                            <span class="label-en">Unit No.</span>
                            <span class="value">{{ $unit->id ?? '-' }}</span>
                            <span class="label-ar">رقم الوحدة:</span>
                        </div>
                        <div class="data-cell">
                            <span class="label-en">Unit Type:</span>
                            <span class="value">{{ $lease->unitWithType($unit->id)->propertyType->name ?? '-' }}</span>
                            <span class="label-ar">نوع الوحدة:</span>
                        </div>
                    </div>


                    <!-- Fixed Services -->
                    <div class="subsection">
                        @foreach($leaseUnitServices->where('service.is_fixed_lease', true) as $service)
                            <div class="data-grid">
                                <div class="data-row">
                                    <div class="data-cell">
                                        <span class="label-en">Service Name:</span>
                                        <span class="value">{{ $service->service->name ?? '-' }}</span>
                                        <span class="label-ar">اسم الخدمة:</span>
                                    </div>
                                    <div class="data-cell">
                                        <span class="label-en">Amount:</span>
                                        <span class="value">{{ number_format($service->lease_service_amount, 2) ?? '-' }}</span>
                                        <span class="label-ar">المبلغ:</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Non-Fixed Services -->
                    <div class="subsection">
                        @foreach($leaseUnitServices->where('service.is_fixed_lease', false) as $service)
                            <div class="data-grid">
                                <div class="data-row odd">
                                    <div class="data-cell w-100">
                                        <span class="label-en">Service Name:</span>
                                        <span class="value">{{ $service->service->name ?? '-' }}</span>
                                        <span class="label-ar">اسم الخدمة:</span>
                                    </div>
                                </div>
                                <div class="data-row odd">
                                    <div class="data-cell">
                                        <span class="label-en">Payment Method:</span>
                                        <span class="value">{{ $service->to_be_paid_by ?? '-' }}</span>
                                        <span class="label-ar">طريقة الدفع:</span>
                                    </div>
                                    <div class="data-cell">
                                        <span class="label-en">Amount to be Paid:</span>
                                        <span class="value">{{ number_format($service->to_be_paid_amount, 2) ?? '-'}}</span>
                                        <span class="label-ar">المبلغ المستحق:</span>
                                    </div>
                                </div>
                                @if($service->to_be_paid_by === 'metered_fee')
                                    <div class="data-row">
                                        <div class="data-cell">
                                            <span class="label-en">Current Meter Reading:</span>
                                            <span class="value">{{ $service->meter_current_reading ?? '-'}}</span>
                                            <span class="label-ar">قراءة العداد الحالية:</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>

        <!-- 10. Tenant Authority-->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">10</div>
                <div class="header-cell border-right">
                    <span class="header-en">Tenant Authority</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">صلاحيات المستأجر</span>
                </div>
            </div>
            <div class="data-grid">
                <div class="data-row">
                    <div class="data-cell border-right">
                        <span class="label-en">-</span>
                        <span class="value">-</span>
                        <span class="label-ar">-</span>
                    </div>
                    <div class="data-cell">
                        <span class="label-en">-</span>
                        <span class="value">-</span>
                        <span class="label-ar">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 11. Financial Data-->
        <div class="contract-section">
            <div class="section-header">
                <div class="section-number">11</div>
                <div class="header-cell border-right">
                    <span class="header-en">Financial Data</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">البيانات المالَّية</span>
                </div>
            </div>

            <div class="data-grid">
                <!-- Brokerage Info -->
                <div class="data-row">
                    <div class="data-cell data-cell-100 border-right">
                        <span class="label-en">Brokerage Entity Name</span>
                        <span class="value">{{ $lease->broker->name ?? 'شركة كراء الأولى لتقنية المعلومات' }}</span>
                        <span class="label-ar">اسم منشأة الوساطة العقارية:</span>
                    </div>
                </div>

                <!-- Fixed Services -->
                <div class="subsection">
                    @foreach($lease->leaseUnits->first()->leaseUnitServices()->whereHas('service', function($query) {
                        $query->where('is_fixed_lease', true);
                    })->get() as $service)
                        <div class="data-grid">
                            <!-- Basic Service Info -->
                            <div class="data-row">
                                <div class="data-cell border-right">
                                    <span class="label-en">Service Name:</span>
                                    <span class="value">{{ $service->service->name ?? '-' }}</span>
                                    <span class="label-ar">اسم الخدمة:</span>
                                </div>
                                <div class="data-cell">
                                    <span class="label-en">Service Type:</span>
                                    <span class="value">{{ str_replace('_', ' ', ucfirst($service->service_type ?? '-')) }}</span>
                                    <span class="label-ar">نوع الخدمة:</span>
                                </div>
                            </div>

                            <!-- Amount Information -->
                            <div class="data-row odd">
                                <div class="data-cell border-right">
                                    <span class="label-en">Service Amount:</span>
                                    <span class="value">{{ number_format($service->lease_service_amount, 2) ?? '-' }}</span>
                                    <span class="label-ar">مبلغ الخدمة:</span>
                                </div>
                                <div class="data-cell">
                                    <span class="label-en">To Be Paid Amount:</span>
                                    <span class="value">{{ number_format($service->to_be_paid_amount ?? 0, 2) ?? '-' }}</span>
                                    <span class="label-ar">المبلغ المراد دفعه:</span>
                                </div>
                            </div>

                            <!-- Payment Details -->
                            <div class="data-row">
                                <div class="data-cell border-right">
                                    <span class="label-en">Payment Period:</span>
                                    <span class="value">{{ str_replace('_', ' ', ucfirst($service->to_be_paid_per ?? 'N/A')) }}</span>
                                    <span class="label-ar">فترة الدفع:</span>
                                </div>
                                <div class="data-cell">
                                    <span class="label-en">Payment Method:</span>
                                    <span class="value">{{ str_replace('_', ' ', ucfirst($service->to_be_paid_by ?? 'N/A')) }}</span>
                                    <span class="label-ar">طريقة الدفع:</span>
                                </div>
                            </div>

                            <!-- Additional Service Details -->
                            <div class="data-row odd">
                                <div class="data-cell">
                                    <span class="label-en">Number of Services:</span>
                                    <span class="value">{{ $service->number_of_service ?? '-' }}</span>
                                    <span class="label-ar">عدد الخدمات:</span>
                                </div>
                                @if($service->service_type === 'meter_reading')
                                    <div class="data-cell">
                                        <span class="label-en">Current Meter Reading:</span>
                                        <span class="value">{{ number_format($service->meter_current_reading ?? 0, 2) ?? '-' }}</span>
                                        <span class="label-ar">قراءة العداد الحالية:</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Conditional Rows Based on Service Type -->
                            @if($service->service_type === 'on_lease')
                                <div class="data-row">
                                    <div class="data-cell w-100">
                                        <span class="label-en">Lease-based Service</span>
                                        <span class="value">This service is tied to the lease duration</span>
                                        <span class="label-ar">خدمة مرتبطة بمدة الإيجار</span>
                                    </div>
                                </div>
                            @elseif($service->service_type === 'fixed_value')
                                <div class="data-row">
                                    <div class="data-cell data-cell-100">
                                        <span class="label-en">Fixed Value Service</span>
                                        <span class="value">Service with fixed payment amount</span>
                                        <span class="label-ar">خدمة بقيمة ثابتة</span>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Separator between services -->
                        @if(!$loop->last)
                            <hr class="service-separator">
                        @endif
                    @endforeach
                </div>

                <!-- Total Contract Value -->
                <div class="data-row">
                    <div class="data-cell data-cell-100">
                        <span class="label-en">Total Contract Value:</span>
                        <span class="value">{{ number_format($lease->total_amount ?? 0, 2 ) ?? '-' }}</span>
                        <span class="label-ar">اجمالي قيمة العقد:</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 12. Rent Payments Schedule-->
        <div class="contract-section">
            <div class="section-header">
                <div class="header-cell border-right">
                    <span class="header-en">Rent Payments Schedule</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">جدول سداد الُدفعات</span>
                </div>
                <div class="section-number">12</div>
            </div>

            <table class="payment-schedule">
                <thead>
                <tr>
                    <th class="amount-header">القيمة<br>Amount</th>
                    <th class="date-header">نهاية مهلة السداد<br>End of payment deadline(AD)</th>
                    <th class="date-header">تاريخ الاستحقاق<br>Due Date(AD)</th>
                    <th class="period-header">الفترة الايجارية<br>Rental Period</th>
                    <th class="date-header">نهاية مهلة السداد<br>End of payment deadline(AD)</th>
                    <th class="date-header">تاريخ الاستحقاق<br>Due Date(AD)</th>
                    <th class="number-header">الرقم<br>No</th>
                </tr>
                </thead>
                <tbody>
                @foreach($lease->invoiceSchedules()->orderBy('installment_date')->get() as $schedule)
                    <tr>
                        <td>{{ number_format($schedule->total_amount, 2) ?? '-' }}</td>
                        <td>{{ \Carbon\Carbon::parse($schedule->due_date)->format('Y-m-d') ?? '-' }}</td>
                        <td>{{ \Carbon\Carbon::parse($schedule->installment_date)->format('Y-m-d') ?? '-' }}</td>
                        <td>
                            @php
                                $startDate = \Carbon\Carbon::parse($schedule->installment_date);
                                $endDate = \Carbon\Carbon::parse($schedule->due_date);
                                $period = $startDate->format('M d') . ' - ' . $endDate->format('M d, Y');
                            @endphp
                            {{ $period }}
                        </td>
                        <td>{{ \Carbon\Carbon::parse($schedule->due_date)->timezone('Asia/Riyadh')->locale('ar')->calendar() ?? '-' }}</td>
                        <td>{{ \Carbon\Carbon::parse($schedule->installment_date)->timezone('Asia/Riyadh')->locale('ar')->calendar() ?? '-' }}</td>
                        <td>{{ $schedule->bill_number }}</td>
                    </tr>
                @endforeach
                </tbody>
                <tfoot>
                <tr class="summary-row">
                    <td colspan="2">
                        <strong>Total Amount: {{ number_format($lease->invoiceSchedules()->sum('total_amount'), 2) ?? '-' }}</strong>
                    </td>
                    <td colspan="3">
                        <strong>Paid Amount: {{ number_format($lease->invoiceSchedules()->sum('paid_amount'), 2) ?? '-'}}</strong>
                    </td>
                    <td colspan="2">
                        <strong>Remaining: {{ number_format($lease->invoiceSchedules()->sum('total_amount') - $lease->invoiceSchedules()->sum('paid_amount'), 2) ?? '-' }}</strong>
                    </td>
                </tr>
                </tfoot>
            </table>

            <div class="payment-details">
                <div class="details-row">
                    <div class="detail-item">
                        <span class="label-en">Total Installments:</span>
                        <span class="value">{{ $lease->invoiceSchedules()->count() ?? '-' }}</span>
                        <span class="label-ar">إجمالي الأقساط:</span>
                    </div>
                    <div class="detail-item">
                        <span class="label-en">Service Amount:</span>
                        <span class="value">{{ number_format($lease->invoiceSchedules()->sum('installment_service_amount'), 2) ?? '-' }}</span>
                        <span class="label-ar">مبلغ الخدمات:</span>
                    </div>
                </div>
            </div>
        </div>


        <!-- 13. Obligations by Parties-->

        <div class="contract-section">
            <div class="section-header">
                <div class="header-cell border-right">
                    <span class="header-en">Obligations by Parties</span>
                </div>
                <div class="header-cell">
                    <span class="header-ar">التزامات الأطراف</span>
                </div>
                <div class="section-number">13</div>
            </div>

            <div class="articles-container">
                @foreach($lease->leaseTerms as $term)
                    <div class="article">
                        <div class="article-title">{{$term->key ?? '-'}}</div>
                        <div class="article-content">
                            {{$term->value ?? '-'}}
                        </div>
                    </div>
                @endforeach



            </div>
        </div>


        <style>
            .contract-section {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                background: white;
            }

            .section-header {
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid #e5e7eb;
                background-color: #205848;
                position: relative;
            }
            .section-number {
                color: #fff;
                position: absolute;
                right: 10px;
                top: 17px;
                font-size: 1.1rem;
                font-weight: 600;

            }

            .header-cell {
                width: 50%;
                padding: 1rem;
                text-align: center;
            }

            .header-en {
                font-weight: 600;
                font-size: 1.1rem;
                color: #fff;
            }

            .header-ar {
                font-weight: 600;
                font-size: 1.1rem;
                color: #fff;
                display: flex;
                justify-content: center;
            }

            .data-grid {
                padding: 0;
            }

            .data-row {
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid #e5e7eb;
            }
            .data-row.odd {
                background-color: #d4daec; /* Light red background for odd rows */
            }

            .data-row:last-child {
                border-bottom: none;
            }

            .data-cell {
                display: flex;
                gap: 0.5rem;
                align-items: center;
                width: 50%;
                padding: 1rem;
                justify-content: space-between;
            }
            .data-cell-100{
                width: 100%;
            }

            .border-right {
                border-right: 1px solid #e5e7eb;
            }

            .label-en {
                color: #374151;
                font-weight: 800;
            }

            .value {
                color: #111827;
                font-weight: 800;
            }

            .label-ar {
                color: #374151;
                font-weight: 800;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .section-header {
                    flex-direction: column;
                }

                .header-cell {
                    width: 100%;
                    padding: 0.75rem 1rem;
                }

                .header-cell.border-right {
                    border-right: none;
                    border-bottom: 1px solid #e5e7eb;
                }

                .data-row {
                    flex-direction: column;
                }

                .data-cell {
                    width: 100%;
                }

                .border-right {
                    border-right: none;
                    border-bottom: 1px solid #e5e7eb;
                }
            }
            .payment-schedule {
                width: 100%;
                border-collapse: collapse;
                font-size: 0.9rem;
            }

            .payment-schedule th,
            .payment-schedule td {
                border: 1px solid #374151;
                padding: 0.5rem;
                text-align: center;
                color: #374151;
            }

            .payment-schedule thead {
                background-color: #f3f4f6;
            }

            .payment-schedule th {
                font-weight: normal;
                white-space: nowrap;
            }

            .amount-header {
                width: 100px;
            }

            .date-header {
                width: 150px;
            }

            .period-header {
                width: 120px;
            }

            .number-header {
                width: 60px;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .contract-section {
                    overflow-x: auto;
                }

                .payment-schedule {
                    min-width: 800px;
                }
            }


            .articles-container {
                padding: 1rem;
            }

            .article {
                margin-bottom: 1.5rem;
            }

            .article-title {
                font-weight: bold;
                margin-bottom: 0.5rem;
                direction: rtl;
                color: #374151;
                background: #f3f4f6;
                padding: 1rem;
            }

            .article-content {
                direction: rtl;
                line-height: 1.6;
                color: #4b5563;

                border-radius: 0.25rem;
            }

            .sub-article {
                font-weight: bold;
                margin: 0.5rem 0;
                color: #374151;
            }

            .sub-sub-article {
                margin-right: 1.5rem;
                font-weight: bold;
                margin-top: 0.5rem;
                color: #374151;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .section-header {
                    flex-direction: column;
                    text-align: center;
                }

                .article-content {
                    padding: 0.75rem;
                }
            }

            .appendix-container {
                padding: 1rem;
            }

            .appendix-intro {
                text-align: right;
                margin-bottom: 1rem;
                color: #374151;
            }

            .appendix-table {
                width: 100%;
                border-collapse: collapse;
                direction: rtl;
            }

            .appendix-table th,
            .appendix-table td {
                border: 1px solid #e5e7eb;
                padding: 0.75rem;
                text-align: right;
            }

            .appendix-table th {
                background-color: #f3f4f6;
                font-weight: 600;
            }

            .appendix-table td {
                vertical-align: top;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .appendix-table {
                    display: block;
                    overflow-x: auto;
                }


            }
            .amenity{
                display: flex;
                justify-content: flex-start;
                width: 100%;
            }
        </style>
    </div>
</x-filament-panels::page>
