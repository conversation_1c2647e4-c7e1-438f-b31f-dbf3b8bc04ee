<?php
namespace Modules\Lease\Services;

use Filament\Notifications\Notification;
use GPBMetadata\Google\Api\Auth;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\LeaseRetrieving\RetrieveLeases;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Jobs\ProcessRetrievalLeases;

class RetrieveCompanyLeasesService
{
    protected Company $company;
    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function retrieve(): void
    {
        //check all Leases are synced
        $nonSyncedLeaseCount = $this->notSyncedLeasesCount($this->company);
        if ($nonSyncedLeaseCount > 0) {
            $this->notifyMustCompleteSyncing($nonSyncedLeaseCount);
            return;
        }
        $this->processRetrievalLeases();
    }

    /**
     * @throws \Throwable
     */
    public function processRetrievalLeases(): void
    {
        $this->notifyProcessing();
        $user = auth()->user();
        ProcessRetrievalLeases::dispatch($this->company, $user);
    }

    protected function getCompanyLeases(Company $company): Builder
    {
        return Lease::where(['company_id' => $company->id]);
    }

    protected function notSyncedLeasesCount(Company $company)
    {
        $data = Lease::where('ejar_uuid', null)->where(['company_id' => $company->id])->count();
        return $data;
    }

    protected function notifyMustCompleteSyncing($count): void
    {
        Notification::make()
            ->success()
            ->title(__("Must complete Syncing {$count} Leases first"))
            ->body(__('There are a non synced Leases found, you should to complete syncing first to.'))
            ->icon('heroicon-o-check-circle')
            ->iconColor('warning')
            ->duration(5000)
            ->send();
    }
    protected function notifyProcessing(): void
    {
        Notification::make()
            ->title(__('Leases Retrieve Started'))
            ->body(__('Processing Leases in the background.'))
            ->info()
            ->duration(5000)
            ->send();
    }
}
