<?php

namespace Modules\Lease\Services;

use App\Events\LeasePublished;
use App\Helpers\LeaseSettingHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseService as ModelsLeaseService;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseSettingEnum;
use Modules\Lease\Repositories\LeaseMemberRepository;
use Modules\Lease\Repositories\LeaseRepository;
use Modules\Lease\Repositories\LeaseSettingRepository;
use Modules\Request\Services\RequestService;
use Modules\Service\Enums\ServicePaymentEnum;

class LeaseService extends ServiceAbstract
{
    protected $leaseMemberRepository;
    protected $leaseSettingRepository;

    public function __construct(LeaseRepository $repository , LeaseMemberRepository $leaseMemberRepository , LeaseSettingRepository $leaseSettingRepository)
    {
        parent::__construct($repository);
        $this->leaseMemberRepository = $leaseMemberRepository;
        $this->leaseSettingRepository = $leaseSettingRepository;
    }

    public function paginate(array $filter = [], array $with = [], array $select = ['*'])
    {
        return $this->repository->getAllByMember($filter, $with, $select);
    }

    public function getUnitServices(array $filter = [], array $select = ['*'], array $with = [])
    {
        return $this->repository->getUnitServices($filter, $select, $with);
    }

    public function changeStatusJobLogic()
    {
        $statusUpdateMethods = [
            'ended' => 'updateEndedLeases',
            'reserved' => 'updateReservedLeases',
            'near_to_expire' => 'updateNearExpireLeases'
        ];
    
        $updatedLeases = array_map(fn($method) => $this->repository->$method(), $statusUpdateMethods);
    
        // Remove empty updates
        $updatedLeases = array_filter($updatedLeases);
    
        if (empty($updatedLeases)) {
            Log::channel('lease_status_updates')->error('No Status Updated For Today', [
                'date' => now()->format('Y-m-d H:i:s'),
            ]);
        }else{
            Log::channel('lease_status_updates')->info('Status Updated For Today', [
                'date' => now()->format('Y-m-d H:i:s'),
                'updated_leases' => $updatedLeases,
            ]);
    
            // Dispatch event only if 'ended' leases are updated
            if (!empty($updatedLeases['ended'])) {
                \Modules\Lease\app\Events\LeasesStatusEndedEvent::dispatch($updatedLeases['ended']);
            }
        }
    }

    public function updateAutoRenewalLeases()
    {
        $leases = $this->repository->getAutoRenwalLeases();

        foreach ($leases as $lease) {
            $oldStartDate = Carbon::parse($lease->start_date);
            $endDate = Carbon::parse($lease->end_date);

            // Calculate the interval between start_date and end_date
            $interval = $oldStartDate->diff($endDate);
            // Add one day to the end date for the new start date
            $newStartDate = $endDate->addDay();
            // Add the interval to the original end date for the new end date
            $newEndDate = Carbon::parse($lease->end_date)->add($interval);
            // Update the lease record
            $lease->start_date = $newStartDate;
            $lease->end_date = $newEndDate;
            LeasePublished::dispatch($lease);
            $lease->leaseRenewals()->create(
                [
                    'start_date'   => $newStartDate,
                    'end_date'   => $newEndDate,
                ]);
            // Update only the end_date after external service execution
            $lease->update(['end_date' => $newEndDate , 'start_date' => $oldStartDate , 'status' => LeaseEnum::Renewed]);
        }
    }

    public static function getAutoRenewalRequestModal($lease)
    {
            $oldStartDate = Carbon::parse($lease->start_date);
            $endDate = Carbon::parse($lease->end_date);

            $interval = $oldStartDate->diff($endDate);
            $newEndDate = Carbon::parse($lease->end_date)->add($interval);
            $noticePeriod = LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD);
            $lastDayToCancel = $endDate->subDays($noticePeriod)->format('Y-m-d');
            return (__('Your current lease duration start at') .' '. $oldStartDate->format('Y-m-d') .' '. __('and ends at') . ' '. $endDate->format('Y-m-d') .' ' . __('and the new end date after renewal will be') . ' ' . $newEndDate->format('Y-m-d') . ' ' . __('and the last date to cancel the auto renwal will be after that you cannot cancel it') . ' ' . $lastDayToCancel);
    }

    public static function getCancelAutoRenewalModal($lease)
    {
            $endDate = Carbon::parse($lease->end_date);

            $noticePeriod = LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD);
            $lastDayToCancel = $endDate->subDays($noticePeriod)->format('Y-m-d');
            return (__('your request will be cancelled and the last day to request enabling auto renewal again will be ') . ' ' . $lastDayToCancel . ' ' . __(', please be careful'));
    }

    public function RemindAutoRenewalLeasesMembers()
    {
        $leases = $this->repository->getAutoRenwalLeasesReminders();
        $daysToRemind = $this->leaseSettingRepository->getFirstBy(['key' => LeaseSettingEnum::DAYS_TO_REMIND_BEFORE_RENEWAL]);

        foreach ($leases as $lease) {
            $oldStartDate = Carbon::parse($lease->start_date);
            $endDate = Carbon::parse($lease->end_date);

            // Calculate the interval between start_date and end_date
            $interval = $oldStartDate->diff($endDate);
            // Add the interval to the original end date for the new end date
            $newEndDate = Carbon::parse($lease->end_date)->add($interval);

            $leaseMembers = $this->leaseMemberRepository->getAllBy(['lease_id' => $lease->id]);

            $template = NotificationsTemplate::where(['key' => 'lease_renewal_reminder'])->first();
            if ($template) {
                foreach ($leaseMembers as $member) {
                    SendNotification::make(['fcm-web', 'email' , 'fcm-api'])
                        ->template($template->key)
                        ->model(Account::class)
                        ->id($member->id)
                        ->findBody(['{lease_id}', '{days}' , '{start_date}' , '{end_date}' , '{new_end_date}'])
                        ->replaceBody([$lease->id, $daysToRemind , $lease->start_date , $lease->end_date , $newEndDate])
                        ->icon($template->icon)
                        ->url(url($template->url))
                        ->privacy('private')
                        ->database(true)
                        ->fire();
                }
            }
        }
    }

    public function getLeaseSchedule($id)
    {
        $lease = Lease::find($id);
        $service =new LeasePaymentService();
        return $service->generatePaymentSchedule($lease->start_date, $lease->end_date, $lease->start_scheduled_date, $lease->total_amount , $lease->paid_amount , 0 , $lease->payment_type, $lease->payment_repeated_type , 0 , $lease->owners);
    }

    public function getServiceCommercialServiceName(int $id): string
    {
        $leaseService = ModelsLeaseService::find($id);
        if($leaseService->name == null)
        {
            return $leaseService->service->name;
        }elseif($leaseService->service_id == null){
            return $leaseService->name;
        }else{
            return '-';
        }
    }

    public static function cancelAutoRenewal($lease_id)
    {
        $repository = app(LeaseRepository::class); // Resolve the repository instance
        $lease = $repository->getFirstBy(['id' => $lease_id]);

        $lease->auto_renewal = LeaseAutoRenewalEnum::OFF;
        $lease->save();

        $requestService = new RequestService();
        $requestService->notifyLeaseMembers($lease, 'lease_auto_renewal_cancel', null);
        return $lease;
    }

    public function getSchedule($total_amount , $start_date , $end_date , $rent_amount , $days_permitted , $units_services , $payment_type , $payment_repeated_type , $lease_type , $paid_amount , $commercial_lease_services ,$other_commercial_lease_services , $owners)
    {
        if($total_amount != 0 && $start_date && $end_date  && $rent_amount && $days_permitted>=0){

            $units_list = [];
            foreach ($units_services as $unit) {
                $services = [];
                $otherServices = [];

                // Process main services
                foreach ($unit['services'] as $service) {
                    if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                        $services[$service['service_id']] = [
                            'lease_service_amount' => (float) $service['lease_service_amount'],
                            'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                        ];
                    }
                }

                // Process other services if they exist
                if (!empty($unit['other_services'])) {
                    foreach ($unit['other_services'] as $service) {
                        if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                            $otherServices[$service['service_id']] = [
                                'lease_service_amount' => (float) $service['lease_service_amount'],
                                'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                            ];
                        }
                    }
                }

                $units_list[$unit['unit_id']] = [
                    'services' => $services,
                    'other_services' => $otherServices
                ];
            }

            $commercialLeaseServices = $commercial_lease_services ?? [];
            $otherCommercialLeaseServices = $other_commercial_lease_services ?? [];

            // Transform other services into a compatible format
            $transformedOtherServices = array_map(function ($service) {
                return [
                    "service_id" => $service["service_id"],
                    "value" => $service["value"],
                ];
            }, $otherCommercialLeaseServices);

            // Merge the two service arrays
            $allServices = array_merge($commercialLeaseServices, $transformedOtherServices);

            $leasePaymentService = new LeasePaymentService();

            $TotalLeaseBalance = $leasePaymentService->getTotalLeaseBalance(
                $start_date,
                $end_date,
                $payment_type,
                $payment_repeated_type,
                $rent_amount ,
                $units_list,
                $allServices,
                $lease_type,
                $owners
            );
            $total_services_amount = $TotalLeaseBalance['total_lease_services_amount'];
            $total_amount = $TotalLeaseBalance['total_lease_rent_amount'];
            $paymentSchedule = $leasePaymentService->generatePaymentSchedule($start_date,$end_date,$total_amount ,$total_services_amount,$paid_amount,$payment_type,$payment_repeated_type,$days_permitted ?? 0 , $lease_type , $owners);
            return$paymentSchedule;
        }
    }

    public function deleteLease(Lease $lease)
    {
         return $this->repository->deleteLease($lease);
    }
}
