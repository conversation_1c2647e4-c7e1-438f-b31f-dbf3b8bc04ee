<?php

namespace Modules\Lease\Services;

use Modules\Lease\Enums\LeaseTypesEnum;
use Illuminate\Support\Str;


class LeaseTermsFormatter
{
    public function formatTerms(string $leaseType, array $data): array
    {
        return $leaseType === LeaseTypesEnum::RESIDENTIAL
            ? $this->handleResidentialTerms($data['terms'])
            : $this->handleCommercialTerms($data['commercialTerms']);
    }

    private function handleResidentialTerms(array $terms): array
    {
        $termsData = [];
        foreach ($terms as $key => $value) {
            if ($key === 'additional_terms' && is_array($value)) {
                $termsData = array_merge($termsData, $this->formatAdditionalTerms($value));
            } else {
                $termsData[] = [
                    'key' => $key,
                    'value' => $value ?? ''
                ];
            }
        }
        return $termsData;
    }

    private function handleCommercialTerms(array $terms): array
    {
        $termsData = [];
        if ($terms['contract_type'] !== "UnifiedContract") {
            foreach ($terms['generalItems'] as $key => $value) {
                $termsData = array_merge(
                    $termsData,
                    $this->processCommercialTermField($key, $value, 'generalItems')
                );
            }
            $termsData[] = [
                'key' => 'contract_type',
                'value' => $terms['contract_type'] ?? ''
            ];
            return $termsData;
        }

        foreach ($terms as $arrayKey => $arrayData) {
            if (!is_array($arrayData)) {
                $termsData[] = [
                    'key' => $arrayKey,
                    'value' => $arrayData ?? ''
                ];
                continue;
            }

            foreach ($arrayData as $key => $value) {
                $termsData = array_merge(
                    $termsData,
                    $this->processCommercialTermField($key, $value, $arrayKey)
                );
            }
        }
        return $termsData;
    }

    private function processCommercialTermField(string $key, $value, string $arrayKey): array
    {
        if ($key === 'additional_terms' && is_array($value)) {
            return $this->formatAdditionalTerms($value, $arrayKey);
        }

        if ($key === 'conditions_checkbox' && is_array($value)) {
            return $this->formatConditionsCheckbox($value, $arrayKey);
        }

        return [[
            'extra_data' => json_encode(['array_key' => $arrayKey]),
            'key' => $key,
            'value' => $value ?? ''
        ]];
    }

    private function formatAdditionalTerms(array $terms, ?string $arrayKey = null): array
    {
        $formattedTerms = [];
        foreach ($terms as $term) {
            if (isset($term['additional_terms'])) {
                $data = [
                    'key' => 'additional_terms',
                    'value' => $term['additional_terms']
                ];

                if ($arrayKey) {
                    $data['extra_data'] = json_encode(['array_key' => $arrayKey]);
                }

                $formattedTerms[] = $data;
            }
        }
        return $formattedTerms;
    }

    private function formatConditionsCheckbox(array $conditions, string $arrayKey): array
    {
        return array_map(function ($condition) use ($arrayKey) {
            return [
                'extra_data' => json_encode([
                    'array_key' => $arrayKey,
                    'label' => $condition['label'],
                    'description' => $condition['description']
                ]),
                'key' => 'conditions_checkbox',
                'value' => $condition['value']
            ];
        }, $conditions);
    }

    public function refillTerms(string $leaseType, $terms): array
    {
        return $leaseType === LeaseTypesEnum::RESIDENTIAL
            ? $this->refillResidentialTerms($terms)
            : $this->refillCommercialTerms($terms);
    }

    private function refillResidentialTerms($terms): array
    {
        $formattedTerms = $terms->whereNotIn('key', ['additional_terms'])
            ->pluck('value', 'key')
            ->toArray();

        $additionalTerms = $terms->where('key', 'additional_terms')
            ->map(function ($term) {
                return [
                    'additional_terms' => $term->value
                ];
            })
            ->values()
            ->toArray();

        if (!empty($additionalTerms)) {
            $formattedTerms['additional_terms'] = $additionalTerms;
        }

        return ['terms' => $formattedTerms];
    }

    private function refillCommercialTerms($terms): array
    {
        $commercialTerms = [];

        foreach ($terms as $term) {
            $extraData = json_decode($term->extra_data, true);
            $arrayKey = $extraData['array_key'] ?? null;

            if ($term->key === 'conditions_checkbox') {
                if (!isset($commercialTerms[$arrayKey]['conditions_checkbox'])) {
                    $commercialTerms[$arrayKey]['conditions_checkbox'] = [];
                }
                $key = Str::snake($extraData['label']['en']);
                $commercialTerms[$arrayKey]['conditions_checkbox'][$key] = [
                    'label' => $extraData['label'],
                    'description' => $extraData['description'],
                    'value' => $term->value
                ];
            } elseif ($term->key === 'additional_terms') {
                if (!isset($commercialTerms[$arrayKey]['additional_terms'])) {
                    $commercialTerms[$arrayKey]['additional_terms'] = [];
                }
                $commercialTerms[$arrayKey]['additional_terms'][] = [
                    'additional_terms' => $term->value
                ];
            } else {
                if ($arrayKey) {
                    $commercialTerms[$arrayKey][$term->key] = $term->value;
                } else {
                    $commercialTerms[$term->key] = $term->value;
                }
            }
        }

        return ['commercialTerms' => $commercialTerms];
    }
}
