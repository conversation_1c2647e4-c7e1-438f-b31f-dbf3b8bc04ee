<?php

namespace Modules\Lease\Services;

use Modules\Lease\app\Models\Lease;
use Mpdf\Mpdf;

class LeaseContractPdfService
{
    public function generatePdf(Lease $lease)
    {
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A3',
            'orientation' => 'P',
            'margin_top' => 15,
            'margin_bottom' => 15,
            'margin_header' => 0,
            'margin_footer' => 0,
            'default_font' => 'dejavusans',
            'dir' => 'rtl',
        ]);

        $stylesheet = '
        <style>
            @page {
                margin: 10px;
            }
            body {
                font-family: dejavusans;
            }
        </style>';

        $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);

        $html = view('lease::contracts.rental', [
            'lease' => $lease
        ])->render();

        $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

        return $mpdf->Output('', 'S');
    }
}
