<?php

namespace Modules\Lease\Services;

use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\app\Models\LeaseUnitService;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Invoice\app\Models\Invoice;
use Modules\Lease\app\Models\Lease;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Modules\Account\Helpers\AccountHelper;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Lease\app\Models\LeaseService;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;

class LeaseScheduleService extends ServiceAbstract
{
    protected $lease;

    public function __construct($leaseId)
    {
        $this->lease = Lease::find($leaseId);
    }

    public function leaseFromMember()
    {
        return $this->lease->created_by;
    }

    public function leaseForMember()
    {
        return LeaseMember::where('lease_id', $this->lease->id)
            ->where(function ($query) {
                $query->where('member_role', 'tenant_representer')
                    ->orWhere('member_role', 'tenant');
            })
            ->pluck('member_id')
            ->first();
    }

    public function getLeaseUnitsServices()
    {
        $leaseUnitsIds = LeaseUnit::where('lease_id', $this->lease->id)->pluck('id');
        return LeaseUnitService::whereIn('lease_unit_id', $leaseUnitsIds)->where('to_be_paid_by', 'fixed_fee')->get();
    }

    public function getCommercialLeaseServices()
    {
        return LeaseService::where('lease_id', $this->lease->id)->get();
    }

    public function getLeaseFuturePayments($limit)
    {
        $data = InvoiceSchedule::where('invoicable_type', Lease::class)
            ->where('invoicable_id', $this->lease->id)
            ->where('invoice_id', null)
            ->paginate($limit);
        return $data;
    }

    public function getLeaseFutureSchedule()
    {
        $data = InvoiceSchedule::where('invoicable_type', Lease::class)
            ->with('Invoice')
            ->where('invoicable_id', $this->lease->id)
            ->where(function ($query) {
                $query->whereNull('invoice_id')
                    ->orWhereHas('invoice', function ($query) {
                        $query->whereIn('status', [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID]);
                    });
            })
            ->get();

        $remaningInstallmentsAmount = $data->sum('total_amount');

        $remaningInstallmentsAmount -= $data->map(function ($schedule) {
            $invoice = $schedule->invoice;
            return $invoice && $invoice->status == InvoiceStatusEnum::PARTIAL_PAID ? $invoice->paid : 0;
        })->sum();

        return [
            'remainingInstallments' => $data->count(),
            'remaningInstallmentsAmount' => $remaningInstallmentsAmount,
            'data' => $data->toArray(),
        ];
    }

    private function getMemberRoles(string $currentRole): array
    {
        $roleMapping = [
            LeaseMemberTypesEnum::TENANT => [
                LeaseMemberTypesEnum::TENANT,
                LeaseMemberTypesEnum::TENANT_REPRESENTER
            ],
            LeaseMemberTypesEnum::TENANT_REPRESENTER => [
                LeaseMemberTypesEnum::TENANT,
                LeaseMemberTypesEnum::TENANT_REPRESENTER
            ],
            LeaseMemberTypesEnum::OWNER => [
                LeaseMemberTypesEnum::LESSOR,
                LeaseMemberTypesEnum::LESSOR_REPRESENTER
            ],
            LeaseMemberTypesEnum::OWNER_REPRESENTER => [
                LeaseMemberTypesEnum::LESSOR,
                LeaseMemberTypesEnum::LESSOR_REPRESENTER
            ]
        ];

        return $roleMapping[$currentRole] ?? [];
    }
    public function getLeaseBills($limit): LengthAwarePaginator
    {
        $currentRole = AccountHelper::CurrentRole(); // current auth account role
        $roles = $this->getMemberRoles($currentRole);
        $leaseId = $this->lease->id;
        $memberIds = LeaseMember::where('lease_id', $leaseId)->whereIn('member_role', $roles)->pluck('member_id');

        $data = Invoice::whereIn('status', [InvoiceStatusEnum::UNPAID , InvoiceStatusEnum::PARTIAL_PAID])
        ->whereJsonContains('extra->lease_id', $leaseId)
        ->whereIn('for_id', $memberIds)
        ->paginate($limit);

        return $data;
    }

    public function getLeaseBillsForRequest()
    {
        // Group the status conditions and sum the remaining amounts
        return $this->getUnpaidBills()->sum('remaining');  // Sum the 'remaining' column
    }


    public function getSettledBills()
    {
        $leaseId = $this->lease->id;
        $tenatMembers = LeaseMember::where('lease_id', $leaseId)
            ->whereIn('member_role', [LeaseMemberTypesEnum::TENANT, LeaseMemberTypesEnum::TENANT_REPRESENTER])->pluck('member_id');

        // Group the status conditions and sum the remaining amounts
        return Invoice::whereIn('for_id', $tenatMembers)  // whereIn for 'for_id'
            ->where(function ($query) {
                $query->where('status', InvoiceStatusEnum::SETTLED);
            })
            ->whereHas('items', function ($query) use ($leaseId) {
                $query->where('item_id', $leaseId)
                    ->where('item_type', Lease::class);
            })
            ->get();
    }

    public function getUnpaidBills()
    {
        return Invoice::whereIn('status', [InvoiceStatusEnum::UNPAID, InvoiceStatusEnum::PARTIAL_PAID])
            ->whereHas('invoice_schedule', function ($query) {
                $query->where('lease_id', $this->lease->id)
                    ->where('invoice_id', '!=', 0);
            })->get();
    }

    public function getLeasePastSchedule($limit, $start_date = null, $end_date = null)
    {
        $currentRole = AccountHelper::CurrentRole();
        $roles = $this->getMemberRoles($currentRole);
        $leaseId = $this->lease->id;
        $memberIds = LeaseMember::where('lease_id', $leaseId)
            ->whereIn('member_role', $roles)
            ->pluck('member_id');

        $query = Invoice::whereIn('status', [InvoiceStatusEnum::PAID])
            ->whereJsonContains('extra->lease_id', $leaseId)
            ->whereIn('for_id', $memberIds);

        if ($start_date && $end_date) {
            $query->whereBetween('release_date', [$start_date, $end_date]);
        }

        return $query->paginate($limit);
    }

    public function getLeaseSchedule($limit, $start_date = null, $end_date = null)
    {
        $query = InvoiceSchedule::where('invoicable_type', Lease::class)->where('invoicable_id', $this->lease->id);

        // Apply date filters only if both start and end dates are provided
        if ($start_date && $end_date) {
            $query->whereBetween('installment_date', [$start_date, $end_date]);
        }

        // Paginate the results based on the provided limit
        $data = $query->paginate($limit);

        return $data;
    }
}
