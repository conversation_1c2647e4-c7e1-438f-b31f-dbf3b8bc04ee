<?php

namespace Modules\Lease\Services;

use Filament\Notifications\Notification;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\EjarIntegration\Services\EjarSyncLeaseService;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseSyncStatus;
use Modules\Lease\Enums\LeaseSyncStep;
use Modules\Lease\app\Models\SyncEjarLeaseStep;
use Modules\Property\Enums\EjarSyncStatus;

class LeaseSyncService
{
    protected $leaseTenants;
    protected readonly EjarSyncLeaseService $ejatSyncLeaseService;
    public function __construct(private Lease $lease) {
        $this->ejatSyncLeaseService = resolve(EjarSyncLeaseService::class);
        $this->leaseTenants= $this->lease->allMembers->whereIn('member_role' , [AccountRolesEnum::TENANT , AccountRolesEnum::TENANT_REPRESENTER]);
    }

    public function sync()
    {
        if (!$this->checkMembers()['all_verified']) {
            return [
                'status' => false,
                'message' => __('All Lease Members are not authenticated in Ejar, please verify'),
            ];
        }
    
        $this->prepareSteps();
    
        foreach (LeaseSyncStep::cases() as $step) {
            $this->executeStep($step); // this will throw on failure
        }
    
        $this->successSync();
    
        return [
            'status' => true,
            'message' => __('Lease synced successfully with Ejar'),
        ];
    }

    protected function executeStep(LeaseSyncStep $step): void
    {
        $syncStep = $this->initializeStep($step);

        // If syncStep is null, it means the step was already completed, so we skip it
        if (!$syncStep) {
            return;
        }
    
        $response = match($step) {
            LeaseSyncStep::INITIALIZE_LEASE => $this->ejatSyncLeaseService->initializeLease($this->lease),
            LeaseSyncStep::PROPERTY_UNITS => $this->ejatSyncLeaseService->attachUnitsToLease($this->lease, $this->lease->units->toArray()),
            LeaseSyncStep::MEMBERS => $this->ejatSyncLeaseService->attachTenantToLease($this->lease , $this->leaseTenants),
            LeaseSyncStep::FINANCIAL => $this->ejatSyncLeaseService->attachFinancialDataToLease($this->lease),
            LeaseSyncStep::UNIT_SERVICE => $this->ejatSyncLeaseService->attachLeaseUnitServices($this->lease),
            LeaseSyncStep::RENTAL_FEES => $this->ejatSyncLeaseService->insertRentalFees($this->lease),
            LeaseSyncStep::TERMS => $this->ejatSyncLeaseService->syncTerms($this->lease),
            LeaseSyncStep::CUSTOM_TERMS => $this->ejatSyncLeaseService->syncCustomTerms($this->lease),
            LeaseSyncStep::SUBMIT_LEASE => $this->ejatSyncLeaseService->submitContract($this->lease),
        };

        if ($response['code'] == 201 || $response['code'] == 200) {
            $this->completeStep($syncStep, $response);
            Notification::make()
                ->success()
                ->icon('heroicon-o-check-circle')
                ->iconColor('success')
                ->duration(5000)
                ->send();
        } else {
            $this->failStep($syncStep, $response);
            $this->failedSync();
            throw new \Exception($response['message']);
        }
    }

    protected function initializeStep(LeaseSyncStep $step): ?SyncEjarLeaseStep
    {
        $initializedStep = SyncEjarLeaseStep::where([
            'lease_id' => $this->lease->id,
            'step' => $step->value
        ])->first();

        // If step is already completed, return null to indicate skipping
        if ($initializedStep && $initializedStep->status->value === LeaseSyncStatus::COMPLETED->value) {
            return null;
        }

        // Otherwise, update the status to processing and return the step
        $initializedStep->update(['status' => LeaseSyncStatus::PROCESSING->value, 'started_at' => now()]);
        return $initializedStep;
    }

    protected function completeStep(SyncEjarLeaseStep $step, array $response): void
    {
        $step->update([
            'status' => LeaseSyncStatus::COMPLETED->value,
            'response_data' => json_encode($response['data']),
            'error_message' => null,
            'completed_at' => now(),
        ]);
    }

    protected function failStep(SyncEjarLeaseStep $step, array $response): void
    {
        $err_msg = $response['data']['Body']['errors'][0]['detail'] ?? $response['message'];
        $step->update([
            'status' => LeaseSyncStatus::FAILED->value,
            'error_message' => $err_msg,
            'response_data' => json_encode($response['data']),
            'completed_at' => now(),
        ]);
    }

    public function failedSync(): void
    {
        $this->lease->update([
            'ejar_sync_status' => EjarSyncStatus::FAILED->value,
            'last_synced_at' => now(),
        ]);
    }

    protected function successSync(): void
    {
        $this->lease->update([
            'ejar_sync_status' => EjarSyncStatus::SYNCED->value,
            'last_synced_at' => now(),
        ]);
    }

    protected function prepareSteps(): void
    {
        $leaseSteps = SyncEjarLeaseStep::where(['lease_id' => $this->lease->id])->get();
        if ($leaseSteps->isEmpty()) {
            foreach (LeaseSyncStep::cases() as $step) {
                SyncEjarLeaseStep::create([
                    'lease_id' => $this->lease->id,
                    'step' => $step->value,
                    'status' => LeaseSyncStatus::PENDING->value,
                    'started_at' => now(),
                ]);
            }
        }
    }

    protected function checkMembers()
    {
        return $this->ejatSyncLeaseService->checkEjarUsers($this->leaseTenants);
    }

}
