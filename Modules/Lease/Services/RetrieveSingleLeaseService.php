<?php
namespace Modules\Lease\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Account\app\Models\Account;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\LeaseRetrieving\RetrieveLeases;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Lease\app\Jobs\ProcessSingleLeaseInRetrieve;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseCommission;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseCommissionTypesEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Services\RetrieveSinglePropertyService;
use Illuminate\Support\Str;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\Invoice\app\Models\InvoiceItem;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;

class RetrieveSingleLeaseService
{
    protected Company $company;
    protected RetrieveLeases $leaseRetrieving;
    protected RetrieveSinglePropertyService $retrieveSinglePropertyService;


    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->leaseRetrieving = new RetrieveLeases($this->company);
        $this->retrieveSinglePropertyService = new RetrieveSinglePropertyService($this->company);
    }

    /**
     * Retrieve a single lease by reference number.
     *
     * @param array $data
     * @return Lease
     * @throws \Exception
     */
    public function retrieve(array $data): Lease
    {
        // Check if lease already exists
        if ($this->checkIfLeaseExistsByReferenceNumber($data['reference_number'])) {
            throw new \Exception(__('Lease with this reference number already exists'));
        }

        // Retrieve lease from Ejar
        $leaseData = $this->retrieveLeaseFromEjar($data['reference_number']);

        if (!$leaseData) {
            throw new \Exception(__('Lease not found with the provided reference number'));
        }

        // Process the lease data
        $user = auth()->user();
        $lease = ProcessSingleLeaseInRetrieve::dispatchSync($leaseData, $this->company, $user);

        return $lease;
    }

    /**
     * Check if a lease with the given reference number already exists.
     *
     * @param string $referenceNumber
     * @return bool
     */
    protected function checkIfLeaseExistsByReferenceNumber(string $referenceNumber): bool
    {
        return Lease::where('lease_number', $referenceNumber)
            ->where('company_id', $this->company->id)
            ->exists();
    }

    /**
     * Retrieve lease data from Ejar by reference number.
     *
     * @param string $referenceNumber
     * @return object|null
     * @throws \Exception
     */
    protected function retrieveLeaseFromEjar(string $referenceNumber)
    {
        try {
            Log::info("Retrieving lease from Ejar", [
                'reference_number' => $referenceNumber,
                'company_id' => $this->company->id
            ]);

            // Call the Ejar API to retrieve the lease
            $lease = $this->leaseRetrieving->retrieveSingleLease($referenceNumber);

            if (!$lease) {
                Log::warning("No lease found with reference number", [
                    'reference_number' => $referenceNumber
                ]);
                return null;
            }

            Log::info("Successfully retrieved lease from Ejar", [
                'reference_number' => $referenceNumber,
                'lease_id' => $lease->Body->data->id ?? 'unknown'
            ]);

            return $lease->Body;
        } catch (\Exception $e) {
            Log::error("Failed to retrieve lease from Ejar: " . $e->getMessage(), [
                'reference_number' => $referenceNumber,
                'trace' => $e->getTraceAsString()
            ]);

            throw new \Exception(__('Failed to retrieve lease from Ejar: :message', [
                'message' => $e->getMessage()
            ]));
        }
    }

    public function saveLeaseDetails($leaseData): Lease
    {
        $lease_uuid = $leaseData->data->id;
        $attributes = $leaseData->data->attributes;
        $include = collect($leaseData->included);
        $status = $attributes->state;
        // Step 1: Index individual entities by ID for quick lookup
        $financialInformation = $include->where('type', 'financial_information')->keyBy('id');
        $payments = $include->where('type', 'payments')->keyBy('id');
        $rental_fees = $include->where('type', 'rental_fees')->keyBy('id');
        $unit_data = $include->where('type', 'portfolio_units')->keyBy('id');
        $property_data = $include->where('type', 'portfolio_properties')->keyBy('id');

        // Extract data with null safety
        $start_schedule = optional($payments->first())->attributes->schedule_date ?? null;
        $days_permitted = optional($payments->first())->attributes->payment_duration_in_days ?? 0;
        $retainerFee = optional($financialInformation->first())->attributes->retainer_fee->amount ?? 0;
        $retainerFeeRequired = optional($financialInformation->first())->attributes->retainer_fee_required ?? false;
        $insuranceAmount = optional($financialInformation->first())->attributes->security_deposit->amount ?? 0;
        $selectedOption = optional($financialInformation->first())->attributes->selected_payment_option ?? '';
        $paymentType = str_contains($selectedOption, 'online') ? 'online' : 'offline';
        $daily_penalty = optional($financialInformation->first())->attributes->late_fees_charged->amount ?? 0;
        $vat_amount = optional($rental_fees->first())->attributes->total_payments_vat->amount ?? 0;
        $rent_amount = (optional($rental_fees->first())->attributes->total_units_rent->amount ?? 0) + $vat_amount;
        $service_amount = optional($rental_fees->first())->attributes->total_payments_services->amount ?? 0;
        $total_amount = optional($rental_fees->first())->attributes->total_payments->amount ?? 0;
        $due_amount = optional($rental_fees->first())->attributes->to_be_billed->amount ?? 0;
        $billingType = optional($rental_fees->first())->attributes->billing_type ?? 'once';
        $unit_id = optional($unit_data->first())->attributes->root_uuid ?? null;

        $property_number = optional($property_data->first())->attributes->property_number ?? null;
        $property_id = $attributes->property_id ?? null;

        // Determine payment type and rental type
        if ($billingType === 'once') {
            $payment_type = 'once';
            $rental_type = null;
        } else {
            $paymentTypeMapping = [
                'monthly' => 'monthly',
                'quarterly' => 'quarterly',
                'bi-annual' => 'half_annually',
                'annual' => 'annually',
            ];

            $payment_type = in_array($billingType, array_keys($paymentTypeMapping)) ? 'repeated' : 'once';
            $rental_type = $paymentTypeMapping[$billingType] ?? null;
        }

        // Try to find property and unit
        $property = null;
        $unit = null;

        if ($property_id) {
            $property = Property::where('ejar_uuid', $property_id)->first();
        }

        if ($unit_id) {
            $unit = Property::where('ejar_uuid', $unit_id)->first();
        }

        // Log unit ID as an array, not a string
        Log::error('Unit information', ['unit_id' => $unit_id]);
        if(!$property || !$unit){
            try {
               $property =  $this->retrieveSinglePropertyService->retrieve(['ejar_uuid' => $property_id]);

                // Check again if property exists after retrieval attempt
                $property = Property::where('ejar_uuid', $property_id)->first();
                $unit = Property::where('ejar_uuid', $unit_id)->first();

                if (!$property || !$unit) {
                    throw new \Exception(__('Property Date are not avaliable , we cannot retrive this lease' . $lease_uuid));
                } else {
                    Log::info("Property successfully retrieved", [
                        'property_id' => $property->id,
                        'ejar_uuid' => $property->ejar_uuid
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Failed to retrieve property: " . $e->getMessage(), [
                    'property_id' => $property_id,
                    'property_number' => $property_number,
                    'lease_id' => $lease_uuid,
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        // Create lease with default property_id if property not found
        $lease = Lease::create([
            'ejar_uuid' => $leaseData->data->id ?? null,
            'start_date' => $attributes->contract_start_date ?? now(),
            'end_date' => $attributes->contract_end_date ?? now()->addYear(),
            'lease_number' => $attributes->contract_number ?? null,
            'status' => match ($status) {
                'active' => 'published',
                'expired' => 'ended',
                'reserved' => 'registered',
                'terminated' => 'terminated',
                default => 'draft',
            },
            'auto_renewal' => isset($attributes->auto_renew) && $attributes->auto_renew == true
                ? LeaseAutoRenewalEnum::ON
                : LeaseAutoRenewalEnum::OFF,
            'lease_type' => $attributes->contract_type ?? LeaseTypesEnum::RESIDENTIAL,
            'ejar_synced' => true,
            'last_synced_at' => now(),
            'company_id' => $leaseData->company_id,
            'broker_id' => $leaseData->broker_id,
            'property_id' => $property->id,
            'created_by' => $leaseData->broker_id,
            'retainer_amount' => $retainerFee,
            'has_retainer' => $retainerFeeRequired,
            'insurance_amount' => $insuranceAmount,
            'payment_method' => $paymentType,
            'start_scheduled_date' => $start_schedule,
            'days_permitted' => $days_permitted,
            'daily_penalty' => $daily_penalty,
            'payment_type' => $payment_type,
            'payment_repeated_type' => $rental_type,
            'rent_amount' => $rent_amount,
            'total_services_amount' => $service_amount,
            'total_amount' => $total_amount,
            'due_amount' => $due_amount,
            'paid_amount' => ($total_amount - $due_amount),
            'ejar_sync_status' => EjarSyncStatus::SYNCED->value,
        ]);

        // Only create lease unit if we have a valid unit
        if ($unit) {
            try {
                LeaseUnit::create([
                    'lease_id' => $lease->id,
                    'unit_id' => $unit->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                Log::info("Created lease unit", [
                    'lease_id' => $lease->id,
                    'unit_id' => $unit->id,
                    'ejar_id' => $unit_id
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to create lease unit: " . $e->getMessage(), [
                    'lease_id' => $lease->id,
                    'unit_id' => $unit ? $unit->id : 'null',
                    'ejar_id' => $unit_id,
                    'trace' => $e->getTraceAsString()
                ]);
            }
        } else {
            Log::warning("Could not create lease unit - unit not found", [
                'lease_id' => $lease_uuid,
                'ejar_unit_id' => $unit_id
            ]);
        }

        // Create commercial meta data if needed
        if (isset($attributes->contract_type) && $attributes->contract_type == LeaseTypesEnum::COMMERCIAL) {
            $lease->commercial_meta_data()->create([
                'lease_id' => $lease->id,
                'engineering_supervision' => 0,
                'unit_finishing' => 0,
                'waste_removal' => 0,
            ]);
        }

        return $lease;
    }

        /**
     * Save lease members from the API response.
     *
     * @param int $lease_id The ID of the lease to associate members with
     * @param object $leaseData The lease data from the API
     * @return void
     * @throws \Exception
     */
    public function saveLeaseMemebers($lease_id, $leaseData): void
    {
        $include = collect($leaseData->included);
        $financialInformation = $include->where('type', 'financial_information')->keyBy('id');
        $brokerage_fee_paid_by = optional($financialInformation->first())->attributes->brokerage_fee_paid_by ?? null;
        $brokerage_fee_due_date = optional($financialInformation->first())->attributes->brokerage_fee_due_date ?? null;
        $brokerage_fee = optional($financialInformation->first())->attributes->brokerage_fee->amount ?? null;

        try {
            Log::info("Starting to save lease members for lease ID: {$lease_id}");

            if (!isset($leaseData->included) || !is_array($leaseData->included)) {
                Log::warning("No included data found for lease members", ['lease_id' => $lease_id]);
                return;
            }

            $include = collect($leaseData->included);

            // Step 1: Index individual entities by ID for quick lookup
            $individualEntities = $include->where('type', 'individual_entities')
                                        ->keyBy('id');

            $organizationEntities = $include->where('type', 'organization_entities')
                                        ->keyBy('id');

            // Step 2: Get contract parties
            $contractParties = $include
                ->filter(function($item) {
                    return isset($item->type) && $item->type === 'contract_parties';
                })
                ->map(function ($item) use ($individualEntities, $organizationEntities) {
                    $attributes = $item->attributes ?? new \stdClass();
                    $relationships = $item->relationships ?? new \stdClass();

                    $entityData = null;
                    $entityType = null;

                    if (isset($relationships->entity) && isset($relationships->entity->data)) {
                        $entityId = $relationships->entity->data->id ?? null;
                        $entityType = $relationships->entity->data->type ?? null;

                        if ($entityType === 'individual_entities' && isset($individualEntities[$entityId])) {
                            $entityData = $individualEntities[$entityId];
                        } elseif ($entityType === 'organization_entities' && isset($organizationEntities[$entityId])) {
                            $entityData = $organizationEntities[$entityId];
                        }
                    }

                    return [
                        'party_id' => $item->id ?? null,
                        'role' => $attributes->role ?? null,
                        'party_type' => $attributes->party_type ?? null,
                        'is_representative' => $attributes->is_representative ?? false,
                        'entity_data' => $entityData,
                        'entity_type' => $entityType
                    ];
                })
                ->values()
                ->all();

            Log::info("Found " . count($contractParties) . " contract parties for lease ID: {$lease_id}");

            // Step 3: Process each member
            foreach ($contractParties as $party) {
                try {
                    if (!$party['entity_data']) {
                        Log::warning("Missing entity data for party", ['party_id' => $party['party_id']]);
                        continue;
                    }

                    $entityAttributes = $party['entity_data']->attributes ?? null;
                    if (!$entityAttributes) {
                        Log::warning("Missing entity attributes for party", ['party_id' => $party['party_id']]);
                        continue;
                    }

                    $memberRole = match ($party['role']) {
                        'lessor_representative' => 'lessor_representer',
                        'tenant_representative' => 'tenant_representer',
                        default => $party['role'],
                    };


                    // Determine member type and get the member entity
                    $isOrganization = $party['entity_type'] === 'organization_entities';
                    $memberableType = $isOrganization
                        ? 'Modules\\Organization\\app\\Models\\Organization'
                        : 'Modules\\Account\\app\\Models\\Account';

                    $memberId = null;

                    if ($isOrganization) {
                        // Handle organization
                        $organization = $this->getOrganizationFromEntityData($entityAttributes);
                        $memberId = $organization->id;
                    } else {
                        // Handle individual
                        $account = $this->getAccountFromEntityData($entityAttributes, $memberRole);
                        $memberId = $account->id;

                            if ($memberRole === 'tenant') {
                                $tenantAccount = $account->id;
                            } elseif ($memberRole === 'lessor') {
                                $ownerAccount = $account->id;
                            }
                    }


                    $lessors = collect($contractParties)->filter(function ($party) {
                        return $party['role'] === 'lessor';
                    });

                    $lessorCount = $lessors->count();
                    $percentage = $lessorCount > 0 ? 100 / $lessorCount : 0;

                    // Create the lease member record
                    $leaseMember = [
                        'lease_id' => $lease_id,
                        'memberable_type' => $memberableType,
                        'member_id' => $memberId,
                        'member_role' => $memberRole,
                        'member_type' => $isOrganization ? 'organization' : 'individual',
                        'percentage' => $percentage,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];

                    // Insert the lease member
                    DB::table('lease_members')->insert($leaseMember);

                    Log::info("Created lease member", [
                        'lease_id' => $lease_id,
                        'member_id' => $memberId,
                        'member_role' => $memberRole
                    ]);

                } catch (\Exception $e) {
                    Log::error("Error processing lease member: " . $e->getMessage(), [
                        'party_id' => $party['party_id'] ?? 'unknown',
                        'trace' => $e->getTraceAsString()
                    ]);
                    // Continue with next member
                }
            }

            if ($brokerage_fee_paid_by == LeaseMemberTypesEnum::TENANT) {
                $baid_by_id = $tenantAccount ?? null;
            } elseif ($brokerage_fee_paid_by == LeaseMemberTypesEnum::OWNER) {
                $baid_by_id = $ownerAccount ?? null;
            } else {
                $baid_by_id = null;
            }

            $lease_commision = LeaseCommission::create([
                'lease_id' => $lease_id,
                'commission_amount' => $brokerage_fee,
                'commission_type' => LeaseCommissionTypesEnum::FIXED,
                'commission_paid_by' => $baid_by_id,
                'commission_due_date' => $brokerage_fee_due_date,
            ]);

            Log::info("Finished saving lease members for lease ID: {$lease_id}");

        } catch (\Exception $e) {
            Log::error("Failed to save lease members: " . $e->getMessage(), [
                'lease_id' => $lease_id,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to be caught by the calling method
        }
    }

    /**
     * Get or create an organization from entity data
     */
    private function getOrganizationFromEntityData($entityAttributes)
    {
        $entityUuid = $entityAttributes->entity_uuid ?? null;

        if (!$entityUuid) {
            throw new \Exception("Missing entity UUID for organization");
        }

        // Try to find existing organization
        $organization = \Modules\Organization\app\Models\Organization::where('ejar_uuid', $entityUuid)->first();

        if (!$organization) {
            // Create new organization
            $organization = \Modules\Organization\app\Models\Organization::create([
                'name' => $entityAttributes->name ?? 'Unknown Organization',
                'unified_number' => $entityAttributes->unified_number ?? rand(1000000000 , **********),
                'ownership_document_number' => $entityAttributes->registration_number ?? null,
                'ejar_uuid' => $entityUuid,
                'organization_type' => $entityAttributes->organization_type ?? null,
                'registration_number' => $entityAttributes->registration_number ?? null,
                'registration_date' => $entityAttributes->registration_date ?? null
            ]);
        }

        return $organization;
    }

    /**
     * Get or create an account from entity data
     */
    private function getAccountFromEntityData($entityAttributes, $role)
    {
        $entityUuid = $entityAttributes->entity_uuid ?? null;
        $fullName = $entityAttributes->full_name ?? '';
        $nameParts = explode(' ', trim($fullName));
        $phone = $entityAttributes->phone_number ?? null;
        $email = $entityAttributes->email ?? null;
        // Fill in name parts with null if not enough parts
        $firstName = $nameParts[0] ?? null;
        $secondName = $nameParts[1] ?? null;
        $thirdName = $nameParts[2] ?? null;
        $lastName = $nameParts[3] ?? null;
        if (!$entityUuid) {
            throw new \Exception("Missing entity UUID for account");
        }
        // Try to find existing account
        $account = \Modules\Account\app\Models\Account::where('ejar_uuid', $entityUuid)->first();
        if (!$account) {
            // Create new account
            $account = \Modules\Account\app\Models\Account::create([
                'name' => $entityAttributes->full_name ?? null,
                'first_name' => $firstName ?? null,
                'second_name' => $secondName ?? null,
                'third_name' => $thirdName ?? null,
                'last_name' => $lastName ?? null,
                'phone' => $phone,
                'email' => $email == null ? 'dummy' . rand(10000, 99999) . '@example.com' : $email, // Temporary email
                'national_id' => $entityAttributes->id_number ?? null,
                'birth_date' => $entityAttributes->date_of_birth ?? null,
                'ejar_uuid' => $entityUuid,
                'lang' => 'ar',
                'ejar_verified' => true,
                'is_active' => true,
                'otp_activated_at' => now(),
                'password' => bcrypt(rand(1000, 9999)) // Temporary password
            ]);

            // Add account role
            if (method_exists($account, 'accountRoles')) {
                $account->accountRoles()->create([
                    'role' => $this->mapMemberRoleToAccountRole($role),
                    'is_default' => true,
                ]);
            }
        }
        return $account;
    }

        /**
     * Map member role to account role
     */
    private function mapMemberRoleToAccountRole(string $memberRole): string
    {
        $roleMap = [
            'lessor' => 'owner',
            'lessor_representer' => 'owner_representer',
            'tenant' => 'tenant',
            'tenant_representer' => 'tenant_representer'
        ];

        return $roleMap[$memberRole] ?? 'tenant';
    }

    public function getLeaseSchedule($invoicesData, $leaseData, $lease)
    {
        try {
            $invoices = $invoicesData->data ?? [];

            $installments = property_exists($invoicesData, 'included') && $invoicesData->included !== null? $invoicesData->included: []; 

            foreach ($invoices as $invoice) {
                $data = $invoice->attributes;
                $invoiceNum = $data->invoice_number;

                $extra = json_encode([
                    'lease_id' => $lease->id,
                ]);

                $firstInstallment = collect($installments)->first(function ($installment) use ($invoiceNum) {
                    return $installment->attributes->invoice_number == $invoiceNum;
                });

                $release_date = $firstInstallment->attributes->due_date ?? null;
                $due_date = $firstInstallment->attributes->late_date ?? null;

                if ($data->total_paid > 0 && $data->total_paid < $data->total_amount) {
                    $status = InvoiceStatusEnum::PARTIAL_PAID;
                } elseif ($data->total_paid > 0 && $data->total_paid == $data->total_amount) {
                    $status = InvoiceStatusEnum::PAID;
                } else {
                    $status = InvoiceStatusEnum::UNPAID;
                }

                $fromAccount = Account::where('national_id', $data->beneficiary_from_id)->first();
                $toAccount = Account::where('national_id', $data->beneficiary_to_id)->first();

                if (!$fromAccount || !$toAccount) {
                    RetrievalLogger::failed($this->company->id, $lease->lease_number, __('invoice partenrs are not one of the contract parties , we cannot create the invoice'));
                    $lease->delete();
                    return false;
                }                
                
                $invoice = Invoice::create([
                    'invoice_type' => InvoiceTypeEnum::SCHEDULE,
                    'uuid'         => Str::uuid(),
                    'for_type'     => Account::class,
                    'for_id'       => $fromAccount?->id,
                    'from_type'    => Account::class,
                    'from_id'      => $toAccount?->id,
                    'status'       => $status,
                    'total'        => $data->total_amount,
                    'remaining'    => $data->total_amount - $data->total_paid,
                    'paid'         => $data->total_paid,
                    'release_date' => $release_date,
                    'due_date'     => $due_date,
                    'company_id'   => $this->company->id,
                    'extra'        => $extra,
                    'ejar_number'  => $data->invoice_number 
                ]);

                $invocieItems = InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type'  => Lease::class,
                    'item_id'    => $lease->id,
                    'price'      => $data->total_amount,
                    'total'      => $data->total_amount,
                    'type'  => InvoiceItemTypeEnum::RENT_AMOUNT,
                ]);
            }
        } catch (\Exception $e) {
            // You can replace this with proper logging if needed
            \Log::error('Error generating lease schedule: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e; // Rethrow if you want the exception to bubble up
        }
    }
}
