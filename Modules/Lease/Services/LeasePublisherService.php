<?php

namespace Modules\Lease\Services;

use App\Events\LeasePublished;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Filament\Support\Exceptions\Halt;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Invoice\Services\InvoiceService;

class LeasePublisherService
{
    /**
     * Publishes the lease and handles related invoices.
     */
    public function publishLease(Lease $record): void
    {
        DB::beginTransaction();

        try {
            $endDate = Carbon::parse($record->end_date);
            $now = now();

            // Determine lease status based on end_date
            if ($record->end_date < $now) {
                $record->status = LeaseEnum::ENDED;
            } elseif ($endDate->subDays(60) <= $now) {
                $record->status = LeaseEnum::NEAR_EXPIRE;
            } elseif ($record->start_date > $now && $record->end_date > $now) {
                $record->status = LeaseEnum::RESERVED;
            } else {
                $record->status = LeaseEnum::PUBLISHED;
            }

            $record->save();

            // Dispatch lease published event
            LeasePublished::dispatch($record);

            // Handle commission invoice
            if ($record?->commission?->commission_amount > 0) {
                $this->createCommissionInvoice($record);
            }

            // Handle insurance invoice
            if ($record->insurance_amount > 0) {
                $this->createInsuranceInvoice($record);
            }

            DB::commit();

            Notification::make()
                ->title(__('Lease published successfully'))
                ->success()
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->title(__('Lease Publishing Failed'))
                ->body($e->getMessage())
                ->danger()
                ->send();

            throw new Halt();
        }
    }

    /**
     * Creates a commission invoice for the lease.
     */
    private function createCommissionInvoice(Lease $record): void
    {
        $invoice = Invoice::create([
            'invoice_type' => InvoiceTypeEnum::COMMISSION,
            'uuid'         => rand(0, 9999) . $record->id . '00' . $record->commission->id,
            'for_type'     => Account::class,
            'for_id'       => $record?->commission->commission_paid_by,
            'from_type'    => User::class,
            'from_id'      => $record->created_by,
            'status'       => InvoiceStatusEnum::UNPAID,
            'total'        => $record->commission->commission_amount,
            'discount'     => 0,
            'remaining'    => $record->commission->commission_amount,
            'paid'         => 0,
            'tax'          => 0,
            'release_date' => $record->created_at->format('Y-m-d'),
            'due_date'     => $record->created_at->format('Y-m-d'),
            'company_id'   => $record->broker->company->id ?? null,
            'extra'        => json_encode(['lease_id' => $record->id]),
        ]);

        $invoice->items()->create([
            'item_type'   => Lease::class,
            'item_id'     => $record->id,
            'description' => null,
            'type'        => InvoiceItemTypeEnum::COMMISSION_AMOUNT,
            'qty'         => 1,
            'price'       => $record->commission->commission_amount,
            'discount'    => 0,
            'tax'         => 0,
            'total'       => $record->commission->commission_amount,
        ]);

        $invoiceService = app(InvoiceService::class);
        $invoiceService->notifyAfterIssueInvoice([$record?->commission->commission_paid_by], $invoice);
    }

    /**
     * Creates an insurance invoice for the lease.
     */
    private function createInsuranceInvoice(Lease $record): void
    {
        $invoice = Invoice::create([
            'invoice_type' => InvoiceTypeEnum::INSURANCE,
            'uuid'         => 'INS' . rand(0, 9999) . $record->id . '00',
            'for_type'     => Account::class,
            'for_id'       => $record->tenant->member->id,
            'from_type'    => User::class,
            'from_id'      => $record->created_by,
            'status'       => InvoiceStatusEnum::PAID,
            'total'        => $record->insurance_amount,
            'discount'     => 0,
            'remaining'    => 0,
            'paid'         => 0,
            'tax'          => 0,
            'release_date' => $record->created_at->format('Y-m-d'),
            'due_date'     => $record->created_at->format('Y-m-d'),
            'company_id'   => $record->broker->company->id ?? null,
            'extra'        => json_encode(['lease_id' => $record->id]),
        ]);

        $invoice->items()->create([
            'item_type'   => Lease::class,
            'item_id'     => $record->id,
            'description' => null,
            'type'        => InvoiceItemTypeEnum::INSURANCE_AMOUNT,
            'qty'         => 1,
            'price'       => $record->insurance_amount,
            'discount'    => 0,
            'tax'         => 0,
            'total'       => $record->insurance_amount,
        ]);
    }
}
