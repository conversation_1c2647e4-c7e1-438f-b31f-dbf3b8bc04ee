<?php

namespace Modules\Lease\Services;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Account\app\Models\Account;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\EjarHttpHelper;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\EjarIntegration\LeaseRetrieving\RetrieveLeases;
use Modules\Lease\app\Jobs\ProcessSingleLeaseInRetrieve;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseCommission;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseCommissionTypesEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Services\RetrieveSinglePropertyService;

class LeaseRetrievalService
{
    use EjarHttpHelper;
    protected Company $company;
    protected User $user;
    protected RetrieveLeases $leaseRetrieving;
    protected RetrieveSinglePropertyService $retrieveSinglePropertyService;

    /**
     * Create a new service instance.
     *
     * @param Company $company
     */
    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->leaseRetrieving = new RetrieveLeases($this->company);
        $this->user = auth()->user();
        $this->retrieveSinglePropertyService = new RetrieveSinglePropertyService($this->company);
    }

    protected function getCompanyLeases(Company $company)
    {
        return Lease::where(['company_id' => $company->id]);
    }

    protected function checkExistingLease(string $lease_number): bool
    {
        return Lease::where('lease_number', $lease_number)->exists();
    }

    public function getLeaseByNumber(string $lease_number)
    {
        if ($this->checkExistingLease($lease_number)) {
            return Notification::make()
                ->title(__('Lease already exists'))
                ->body(__("The lease is already exists."))
                ->danger()
                ->send();
        }

        
        $count = $this->leaseRetrieving->getLeasesCount();
        ProcessSingleLeaseInRetrieve::dispatch($this->company , $this->user , $count , $lease_number);
        return Notification::make()
                ->title(__('Retrieve Single Lease'))
                ->body(__("Retrieve single Lease is proccessing in the background."))
                ->success()
                ->send();
    }


    /**
     * Process a lease from Ejar data
     *
     * @param object $leaseData The lease data from Ejar
     * @param int $userId The ID of the user processing the lease
     * @return Lease The created lease
     * @throws \Exception
     */
    public function processLease(object $leaseData, int $userId): Lease
    {
        try {
            DB::beginTransaction();
            
            // Add company and broker IDs to the lease data
            $leaseData->company_id = $this->company->id;
            $leaseData->broker_id = $userId;
            
            // Save lease details
            $lease = $this->saveLeaseDetails($leaseData);
            
            // Save lease members
            $this->saveLeaseMemebers($lease->id, $leaseData);
            
            DB::commit();
            
            // Log successful retrieval
            RetrievalLogger::success(
                $this->company->id, 
                $lease->lease_number, 
                __('Lease retrieved successfully')
            );
            
            return $lease;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Lease processing failed: " . $e->getMessage(), [
                'lease_id' => $leaseData->data->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
}