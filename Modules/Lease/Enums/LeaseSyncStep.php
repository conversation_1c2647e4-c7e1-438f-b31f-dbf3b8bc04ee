<?php

namespace Modules\Lease\Enums;

enum LeaseSyncStep: string
{
    case INITIALIZE_LEASE = 'initialize_lease';
    case PROPERTY_UNITS = 'attach_property_and_units';
    case MEMBERS = 'attach_members';
    case FINANCIAL = 'financial_data';
    case UNIT_SERVICE = 'attach_unit_services';
    case RENTAL_FEES = 'rental_fees';
    case TERMS = 'terms';
    case CUSTOM_TERMS = 'custom_terms';
    case SUBMIT_LEASE = 'create_lease';


    public function getLabel(): string
    {
        return match($this) {
            static::INITIALIZE_LEASE => __('Initialize Lease'),
            static::PROPERTY_UNITS => __('Property Units'),
            static::MEMBERS => __('Members'),
            static::FINANCIAL => __('Financial Information'),
            static::UNIT_SERVICE => __('Unit Services'),
            static::RENTAL_FEES => __('Rental Fees'),
            static::TERMS => __('Lease Terms'),
            static::CUSTOM_TERMS => __('Custom Terms'),
            static::SUBMIT_LEASE => __('Submit Lease'),
        };
    }

    public static function getOptions(): array
    {
        return [
            self::INITIALIZE_LEASE->value,
            self::PROPERTY_UNITS->value,
            self::MEMBERS->value,
            self::FINANCIAL->value,
            self::UNIT_SERVICE->value,
            self::RENTAL_FEES->value,
            self::TERMS->value,
            self::CUSTOM_TERMS->value,
            self::SUBMIT_LEASE->value,
        ];
    }

    public function getClass(): string
    {
        return match($this) {
            static::INITIALIZE_LEASE => "SyncInitializeLease",
            static::PROPERTY_UNITS => "SyncPropertyUnits",
            static::MEMBERS => "SyncMembers",
            static::FINANCIAL => "SyncFinancial",
            static::UNIT_SERVICE => "SyncUnitService",
            static::RENTAL_FEES => "SyncRentalFees",
            static::TERMS => "SyncTerms",
            static::CUSTOM_TERMS => "SyncCustomTerms",
            static::SUBMIT_LEASE => "SyncSubmitLease",
        };
    }
}
