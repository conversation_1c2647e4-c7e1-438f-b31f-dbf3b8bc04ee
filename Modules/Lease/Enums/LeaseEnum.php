<?php

namespace Modules\Lease\Enums;

enum LeaseEnum: string
{
    const DRAFT = 'draft';
    const PUBLISHED = 'published';
    const RESERVED = 'reserved';
    const NEAR_EXPIRE = 'near_to_expire';
    const TERMINATED = 'terminated';
    const TERMINATE_REQUEST = 'terminate_request';
    const CLOSED = 'closed';
    const CLOSE_REQUEST = 'close_request';
    const ENDED = 'ended';
    const Renewed = 'renewed';

    public static function getLeaseStatus(): array
    {
        return [
            self::DRAFT,
            self::PUBLISHED,
            self::RESERVED,
            self::NEAR_EXPIRE,
            self::TERMINATED,
            self::TERMINATE_REQUEST,
            self::CLOSED,
            self::CLOSE_REQUEST,
            self::ENDED,
            self::Renewed,
        ];
    }
    public static function labels(): array
    {
        return [
            self::DRAFT => __('Draft'),
            self::PUBLISHED => __('Active'),
            self::RESERVED => __('Registerd'),
            self::NEAR_EXPIRE => __('Near To Expire'),
            self::TERMINATED => __('Terminated'),
            self::TERMINATE_REQUEST => __('Terminate Request'),
            self::CLOSED => __('Closed'),
            self::CLOSE_REQUEST => __('Close Request'),
            self::ENDED => __('Expired'),
            self::Renewed => __('Renewed'),
        ];
    }
    
    public static function trans(string $status): string
    {
        return match($status) {
            self::DRAFT => __('Draft'),
            self::PUBLISHED => __('Active'),
            self::RESERVED => __('Registerd'),
            self::NEAR_EXPIRE => __('Near To Expire'),
            self::TERMINATED => __('Terminated'),
            self::TERMINATE_REQUEST => __('Terminate Request'),
            self::CLOSED => __('Closed'),
            self::CLOSE_REQUEST => __('Close Request'),
            self::ENDED => __('Expired'),
            self::Renewed => __('Renewed'),
            default => $status,
        };
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
    public static function getColor(string $status): string
    {
        return match($status) {
            self::DRAFT => 'gray',
            self::PUBLISHED => 'success',
            self::RESERVED => 'info',
            self::NEAR_EXPIRE => 'danger',
            self::TERMINATED => 'danger',
            self::TERMINATE_REQUEST => 'warning',
            self::CLOSED => 'info',
            self::CLOSE_REQUEST => 'warning',
            self::ENDED => 'info',
            self::Renewed => 'info'
        };
    }

    public static function getActiveStatus()
    {
        return [
            self::PUBLISHED,
            self::TERMINATE_REQUEST,
            self::RESERVED,
            self::NEAR_EXPIRE,
            self::Renewed
        ];
    }
    public static function getOptions()
    {
        return collect(self::getLeaseStatus())->mapWithKeys(fn ($status) => [$status => __($status)]);
    }

    public static function getFilterOptions()
    {
        return collect(self::labels())->mapWithKeys(fn ($label, $status) => [$status => $label]);
    }
}
