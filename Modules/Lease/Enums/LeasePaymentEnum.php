<?php

namespace Modules\Lease\Enums;

enum LeasePaymentEnum: string
{
    const MONTHLY = 'monthly';
    const QUARTERLY = 'quarterly';
    const HALF_ANNUALLY = 'half_annually';
    const ANNUALLY = 'annually';
    const ONCE = 'once';
    const FLEXIBLE = 'flexible';
    const REPEATED = 'repeated';
    const ONLINE = 'online';
    const OFFLINE = 'offline';


    public static function paymentMethodLabels(): array
    {
        return [
            self::ONLINE => __('Online'),
            self::OFFLINE => __('Offline'),
        ];
    }
    public static function paymentTypeLabels(): array
    {
        return [
            self::ONCE => __('Once'),
            self::FLEXIBLE => __('Flexible'),
            self::REPEATED => __('Repeated'),
        ];
    }
    public static function repeatedLabels(): array
    {
        return [
            self::MONTHLY => __('Monthly'),
            self::QUARTERLY => __('Quarterly'),
            self::HALF_ANNUALLY => __('Half Annually'),
            self::ANNUALLY => __('Annually'),
        ];
    }

    public static function getRepeatedLabel(string $role): string
    {
        if (!$role) {
            return '-';
        }

        return self::repeatedLabels()[$role];
    }
    public static function getMethodLabel(string $type): string
    {
        if (!$type) {
            return '-';
        }

        return self::paymentMethodLabels()[$type];
    }
    public static function getPaymentTypeLabel(string $type): string
    {
        if (!$type) {
            return '-';
        }

        return self::paymentTypeLabels()[$type];
    }

    public static function getLeasePaymentRepeatedTypes(): array // New method for values
    {
        return [
            self::MONTHLY,
            self::QUARTERLY,
            self::HALF_ANNUALLY,
            self::ANNUALLY,
        ];
    }
    public static function getLeasePaymentTypes(): array // New method for values
    {
        return [
            self::ONCE,
            self::FLEXIBLE,
            self::REPEATED,
        ];
    }
    public static function getLeasePaymentMethodTypes(): array // New method for values
    {
        return [
            self::ONLINE,
            self::OFFLINE,
        ];
    }
}
