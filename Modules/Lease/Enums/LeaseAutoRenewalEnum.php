<?php

namespace Modules\Lease\Enums;

enum LeaseAutoRenewalEnum: string
{
    const ON = 'on';
    const OFF = 'off';
    const PENDING = 'pending';

    public static function getLeaseTypes(): array // New method for values
    {
        return [
            self::ON,
            self::OFF,
        ];
    }

    public static function labels(): array
    {
        return [
            self::ON => __('Turned On'),
            self::OFF => __('Turned Off'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getValues(): array
    {
        return [
            self::ON,
            self::OFF,
            self::PENDING,
        ];
    }
}
