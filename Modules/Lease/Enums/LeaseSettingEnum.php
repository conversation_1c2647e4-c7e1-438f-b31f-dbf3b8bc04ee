<?php

namespace Modules\Lease\Enums;

enum LeaseSettingEnum: string
{
    const AUTO_RENEWAL_NOTICE_PERIOD = 'renewal_notice_period';
    const DAYS_TO_REMIND_BEFORE_RENEWAL = 'days_to_remind_before_renewal';

    public static function getLeaseTypes(): array // New method for values
    {
        return [
            self::AUTO_RENEWAL_NOTICE_PERIOD,
            self::DAYS_TO_REMIND_BEFORE_RENEWAL,
        ];
    }
}
