<?php

namespace Modules\Lease\Enums;

enum LeaseRequestsEnum: string
{
    const TERMINATION = 'termination';
    const CLOSE = 'close';


    public static function labels(): array
    {
        return [
            self::TERMINATION => __('Termination'),
            self::CLOSE => __('close'),
        ];
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
}
