<?php

namespace Modules\Lease\Enums;

enum EjarEnum: string
{
    const INITIAL_STEP = 'initial';
    const PROPERTY_UNITS_STEP = 'property_units';
    const PARTIES_STEP = 'parties';
    const TERMS_STEP = 'terms';
    const SERVICES_STEP = 'services';
    const FINANCIAL_STEP = 'financial';
    const SIGNED_DOCUMENTS_STEP = 'signed_documents';
    const SUBMIT_STEP = 'submit';

    public static function getEjarContractSteps(): array
    {
        return [
            self::INITIAL_STEP,
            self::PROPERTY_UNITS_STEP,
            self::SERVICES_STEP,
            self::TERMS_STEP,
            self::FINANCIAL_STEP,
            self::PARTIES_STEP,
            self::SIGNED_DOCUMENTS_STEP,
            self::SUBMIT_STEP,
        ];
    }
}
