<?php

namespace Modules\Lease\Enums;

enum LeaseCommissionTypesEnum: string
{
    const FIXED = 'fixed';
    const PERCENTAGE = 'percentage';

    public static function getLeaseCommissionTypes(): array
    {
        return [
            self::FIXED,
            self::PERCENTAGE,
        ];
    }

    public static function labels(): array
    {
        return [
            self::FIXED => __('Fixed'),
            self::PERCENTAGE => __('Percentage'),
        ];
    }

    /**
     * Static method to get label from types
     */
    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
}
