<?php

namespace Modules\Lease\Enums;

enum LeaseMemberTypesEnum: string
{
    const TENANT = 'tenant';
    const LESSOR = 'lessor';
    const TENANT_REPRESENTER = 'tenant_representer';
    const LESSOR_REPRESENTER = 'lessor_representer';
    const INDIVIDUAL_TYPE = 'individual';
    const ORGANIZATION_TYPE = 'organization';
    const OWNER = 'owner';
    const OWNER_REPRESENTER = 'owner_representer';


    public static function roleLabels(): array
    {
        return [
            self::TENANT => __('Tenant'),
            self::LESSOR => __('Lessor'),
            self::TENANT_REPRESENTER => __('Tenant Representer'),
            self::LESSOR_REPRESENTER => __('Lessor Representer'),
        ];
    }
    public static function typeLabels(): array
    {
        return [
            self::INDIVIDUAL_TYPE => __('Individual'),
            self::ORGANIZATION_TYPE => __('Organization'),
        ];
    }

    public static function getRoleLabel(string $role): string
    {
        if (!$role) {
            return '-';
        }

        return self::roleLabels()[$role];
    }

    public static function getTypeLabel(string $type): string
    {
        if (!$type) {
            return '-';
        }

        return self::typeLabels()[$type];
    }

    public static function getLeaseMemberRoles(): array // New method for values
    {
        return [
            self::TENANT,
            self::LESSOR,
            self::TENANT_REPRESENTER,
            self::LESSOR_REPRESENTER,
        ];
    }
    public static function getRepresenterRoleCategory(): array
    {
        return [
            self::TENANT_REPRESENTER,
            self::LESSOR_REPRESENTER,
        ];
    }
    public static function getLeaseMemberTypes(): array // New method for values
    {
        return [
            self::INDIVIDUAL_TYPE,
            self::ORGANIZATION_TYPE,
        ];
    }
}
