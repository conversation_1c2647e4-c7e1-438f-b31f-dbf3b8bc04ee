<?php

namespace Modules\Lease\Enums;

enum LeaseTypesEnum: string
{
    const RESIDENTIAL = 'residential';
    const COMMERCIAL = 'commercial';

    public static function getLeaseTypes(): array // New method for values
    {
        return [
            self::RESIDENTIAL,
            self::COMMERCIAL,
        ];
    }

    public static function labels(): array
    {
        return [
            self::RESIDENTIAL => __('Residential'),
            self::COMMERCIAL => __('Commercial'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }

    public static function getColor(string $status): string
    {
        return match($status) {
            self::COMMERCIAL => 'info',
            self::RESIDENTIAL => 'info',
        };
    }

    public static function trans(string $status): string
    {
        return match($status) {
            self::RESIDENTIAL => __('residential'),
            self::COMMERCIAL => __('commercial'),
        };
    }
}
