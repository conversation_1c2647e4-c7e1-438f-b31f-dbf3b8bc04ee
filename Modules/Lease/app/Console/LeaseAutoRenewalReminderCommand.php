<?php

namespace Modules\Lease\app\Console;

use Illuminate\Console\Command;
use Modules\Lease\Services\LeaseService;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

class LeaseAutoRenewalReminderCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'lease:renewal-reminder';

    /**
     * The console command description.
     */
    protected $description = 'remind that <PERSON><PERSON> will be renewed after # of days entered in the setting';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $leaseService = app(LeaseService::class);
        $leaseService->RemindAutoRenewalLeasesMembers();
    }

    /**
     * Get the console command arguments.
     */
    protected function getArguments(): array
    {
        return [
            ['example', InputArgument::REQUIRED, 'An example argument.'],
        ];
    }

    /**
     * Get the console command options.
     */
    protected function getOptions(): array
    {
        return [
            ['example', null, InputOption::VALUE_OPTIONAL, 'An example option.', null],
        ];
    }
}
