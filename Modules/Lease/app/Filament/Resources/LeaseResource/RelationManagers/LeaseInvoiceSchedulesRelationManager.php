<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Modules\Lease\Enums\LeaseEnum;
use Illuminate\Database\Eloquent\Model;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\HtmlString;

class LeaseInvoiceSchedulesRelationManager extends RelationManager
{
    protected static string $relationship = 'invoiceSchedules';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Invoice Schedules');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Invoice Schedules');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('bill_number')
                    ->label(__('Bill Number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('installment_amount')
                    ->label(__('Installment Amount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('installment_service_amount')
                    ->label(__('Installment Service Amount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('vat')
                    ->label(__('Vat'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label(__('Total Amount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('installment_count')
                    ->label(__('Installment Count'))
                    ->formatStateUsing(function ($state) {
                        return $state;
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('paid_amount')
                    ->label(__('Paid Amount'))
                    ->formatStateUsing(function ($record) {
                        return 
                        new HtmlString(
                            '<div class="">' .
                            number_format($record->paid_amount) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->state(function ($record) {
                        if (!$record->invoice_id) {
                            return __('Draft');
                        }

                        $status = $record->invoice?->status;
                        return match ($status) {
                            InvoiceStatusEnum::UNPAID => __('unpaid'),
                            InvoiceStatusEnum::PARTIAL_PAID => __('Partially Paid'),
                            InvoiceStatusEnum::PAID => __('Paid'),
                            InvoiceStatusEnum::SETTLED => __('settled'),
                            default => __('Draft')
                        };
                    })
                    ->colors([
                        'warning' => fn ($state) => in_array($state, ['Draft', 'Unpaid']),
                        'primary' => fn ($state) => $state === 'Partially Paid',
                        'success' => fn ($state) => in_array($state, ['Paid', 'Settled']),
                    ]),
                Tables\Columns\TextColumn::make('due_date')
                    ->label(__('Due Date'))
                    ->date()
                    ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d-m-Y') : null)  // Format date as dd-mm-yyyy
                    ->sortable(),

                Tables\Columns\TextColumn::make('installment_date')
                    ->label(__('Installment Date'))
                    ->date()
                    ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d-m-Y') : null)  // Format date as dd-mm-yyyy
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Creation Date'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updating Date'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Add any actions you want to perform on individual records
            ])
            ->bulkActions([
                // Add any bulk actions
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord?->status == LeaseEnum::DRAFT ? false : true;
    }
}
