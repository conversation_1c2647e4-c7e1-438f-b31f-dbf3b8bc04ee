<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use function Termwind\style;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Modules\Service\app\Models\Service;
use Filament\Forms\Components\TextInput;
use Filament\Support\Enums\VerticalAlignment;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Service\Enums\ServiceTypeEnum;
use Illuminate\Support\HtmlString;

class LeaseServicesRelationManager extends RelationManager
{
    protected static string $relationship = 'leaseUnits';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Services');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Services');
    }

    public function table(Table $table): Table
    {
        return $table
//            ->defaultGroup('unit.number')
            ->recordTitleAttribute('unit.number')
            ->columns([
                TextColumn::make('unit.number')
                    ->alignment(Alignment::Center)
                    ->label(__('Unit Number')),

                TextColumn::make('service.name')
                    ->alignment(Alignment::Center)
                    ->label(__('Service'))
                    ->state(function ($record) {
                        return $record->AllleaseUnitServices->map(function ($service) {
                            return $service->service->name;
                        })->join('<br><div class="py-1"></div>');
                    })
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state)),

                TextColumn::make('lease_service_amount')
                    ->alignment(Alignment::Center)
                    ->label(__('Service Amount'))
                    ->state(function ($record) {
                        return $record->AllleaseUnitServices->map(function ($service) {
                            if($service->service_type == ServiceTypeEnum::ON_DEMAND)
                            {
                                return __('will be decided on demand');
                            }elseif($service->service_type == ServiceTypeEnum::METERE_FEE)
                            {
                                return ('-');
                            }
                            return
                            new HtmlString(
                                '<div class="">' .
                                number_format($service->lease_service_amount, 2) .
                                ' <span class="icon-saudi_riyal"></span>' .
                                '</div>'
                            );
                        })->join('<br><div class="py-1"></div>');
                    })
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state)),

                TextColumn::make('meter_current_reading')
                    ->alignment(Alignment::Center)
                    ->label(__('Current Meter Reading'))
                    ->state(function ($record) {
                        return $record->AllleaseUnitServices->map(function ($service) {
                            if($service->service_type !== ServiceTypeEnum::METERE_FEE)
                            {
                                return ('-');
                            }
                            return number_format($service->meter_current_reading);
                        })->join('<br><div class="py-1"></div>');
                    })
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state)),

                TextColumn::make('to_be_paid_amount')
                    ->alignment(Alignment::Center)
                    ->label(__('To Be Paid Amount'))
                    ->state(function ($record) {
                        return $record->AllleaseUnitServices->map(function ($service) {
                            if($service->service_type == ServiceTypeEnum::ON_DEMAND)
                            {
                                return __('will be decided on demand');
                            }elseif($service->service_type == ServiceTypeEnum::METERE_FEE)
                            {
                                return ('-');
                            }
                            return
                            new HtmlString(
                                '<div class="">' .
                                number_format($service->to_be_paid_amount, 2) .
                                ' <span class="icon-saudi_riyal"></span>' .
                                '</div>'
                            );
                        })->join('<br><div class="py-1"></div>');
                    })
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state)),

                TextColumn::make('to_be_paid_by')
                    ->alignment(Alignment::Center)
                    ->label(__('Type'))
                    ->state(function ($record) {
                        return $record->AllleaseUnitServices->map(function ($service) {
                            return '<span class="bg-[#0f2c24] text-white rounded-full px-3 py-1 my-2 text-sm font-medium">' . ServiceTypeEnum::getLabel($service->service_type) . '</span>';
                        })->join('<br><div class="py-1"></div>');
                    })
                    ->html(),
            ])
            ->filters([
                //
            ]);
    }



}
