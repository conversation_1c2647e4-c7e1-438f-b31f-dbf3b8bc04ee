<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Tables\Table;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Lease\Services\LeaseService;

class CommercialLeaseServicesRelationManager extends RelationManager
{
    protected static string $relationship = 'leaseServices';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Commercial Lease Services');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Commercial Lease Services');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->alignment(Alignment::Center)
                    ->label(__('Service'))
                    ->html()
                    ->getStateUsing(function ($record) {
                        $leaseService = app(LeaseService::class);
                        return nl2br($leaseService->getServiceCommercialServiceName($record->id));
                    }),
                TextColumn::make('value')
                    ->alignment(Alignment::Center)
                    ->label(__('Value'))
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state)),
            ])
            ->filters([
                //
            ]);
    }
    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord?->lease_type == LeaseTypesEnum::RESIDENTIAL ? false : true;
    }
}
