<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Database\Eloquent\Model;
use App\Enums\DocumentTypeEnum;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Resources\Pages\EditRecord;
use App\Models\DocumentType;

class LeaseDocumentsRelationManager extends RelationManager
{
    protected static string $relationship = 'documents';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Documents');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Documents');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Document Type & Name')
                    ->label(__('Document Type & Name'))
                    ->schema([
                        Forms\Components\Select::make('document_type_id')
                            ->required()
                            ->label(__('Document Type'))
                            ->options(DocumentType::select('id','name')->get()->pluck('name', 'id'))
                            ->hidden(fn($livewire) => $livewire instanceof EditRecord)
                            ->searchable(['name'])
                            ->native(false)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('metadata.document_name_and_type')
                            ->required()
                            ->label(__('Document Name and Type'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('metadata.document_number')
                                ->required()
                                ->label(__('Document Number'))
                                ->maxLength(255),
                    ])->columns(2),
                Forms\Components\Section::make('Document Release Information')
                    ->label(__('Document Release Information'))
                    ->schema([
                        Forms\Components\DatePicker::make('metadata.release_date')
                            ->required()
                            ->native(false)
                            ->label(__('Release Date')),
                        Forms\Components\DatePicker::make('metadata.expiration_date')
                            ->required()
                            ->native(false)
                            ->label(__('Expiration Date')),
                        Forms\Components\TextInput::make('metadata.released_by')
                            ->required()
                            ->label(__('Released By'))
                            ->maxLength(255),
                        Forms\Components\TextInput::make('metadata.released_in')
                            ->required()
                            ->label(__('Released In'))
                            ->maxLength(255)
                    ])->columns(2),
                Forms\Components\Section::make('Upload Contract')
                    ->label(__('Upload Contract'))
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('upload_contract')
                            ->label(__('Upload Contract Authorization'))
                            ->image()
                            ->imageEditor()
                            ->downloadable()
                            ->panelLayout('grid')
                            ->reorderable()
                            ->multiple()
                            ->maxSize(5120)
                            ->collection(function (Forms\Get $get) {
                                $documentTypeId = $get('document_type_id');
                                $documentType = DocumentType::find($documentTypeId)->key;
                                return $documentType == DocumentTypeEnum::Ownership->value ? 'ownership_document' :'upload_contract';
                            })
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->columnSpanFull()
                    ])->columns(1),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('document_type_id')
                    ->label(__('Document Type'))
                    ->badge(),

                TextColumn::make('metadata.document_name_and_type')
                    ->label(__('Document Name'))
                    ->searchable(),
//                    ->badge(),

                TextColumn::make('metadata.document_number')
                    ->label(__('Document Number'))
                    ->searchable()
                    ->badge(),

                TextColumn::make('metadata.release_date')
                    ->label(__('Release Date'))
                    ->dateTime('d-m-Y')  // Format date as dd-mm-yyyy
                    ->badge()
                    ->color('success'),

                TextColumn::make('metadata.expiration_date')
                    ->label(__('Expiration Date'))
                    ->dateTime('d-m-Y')  // Format date as dd-mm-yyyy
                    ->badge()
                    ->color('warning')
                    ->description(function (Model $record) {
                        $expirationDate = \Carbon\Carbon::parse($record->metadata['expiration_date']);
                        $now = now();
                        $daysLeft = $now->diffInDays($expirationDate, false);

                        if ($daysLeft < 0) {
                            return "Expired " . abs($daysLeft) . " days ago";  // Handle negative days correctly
                        }
                        return "{$daysLeft} days left";
                    }),

                TextColumn::make('metadata.released_by')
                    ->label(__('Released By'))
                    ->badge()
                    ->searchable(),

                TextColumn::make('metadata.released_in')
                    ->label(__('Released In'))
                    ->badge(),

                TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->date('d-m-Y') // Format the date in dd-mm-yyyy
                    ->badge()
                    ->color('gray'),
            ])
            ->filters([
                Tables\Filters\Filter::make('expiring_soon')
                    ->label(('Expiring Soon (30 days)'))
                    ->query(function ($query) {
                        $thirtyDaysFromNow = now()->addDays(30)->format('Y-m-d');
                        return $query->whereJsonContains('metadata->expiration_date', $thirtyDaysFromNow);
                    }),
                Tables\Filters\Filter::make('expired')
                    ->query(function ($query) {
                        $today = now()->format('Y-m-d');
                        return $query->whereJsonContains('metadata->expiration_date', ['$lt' => $today]);
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth('7xl'),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
