<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\HtmlString;

class LeaseCommissionRelationManager extends RelationManager
{
    protected static string $relationship = 'commission';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Commission');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Commission');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('commission_amount')
                    ->label(__('Amount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->badge(),

                TextColumn::make('commission_percentage')
                    ->label(__('Percentage'))
                    ->formatStateUsing(fn ($state) => $state . '%')
                    ->badge(),
                TextColumn::make('commission_type')
                    ->label(__('Type'))
                    ->badge(),

                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime('d-m-Y H:i')  // Format date as dd-mm-yyyy hh:mm
                    ->badge(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
//                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
//                ]),
            ]);
    }
}
