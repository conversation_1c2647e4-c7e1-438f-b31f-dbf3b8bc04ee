<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Tables\Table;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;

class LeaseRenewalRelationManager extends RelationManager
{
    protected static string $relationship = 'leaseRenewals';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Lease Renewals');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Lease Renewals');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->alignment(Alignment::Center)
                    ->label(__('ID'))
                    ->sortable(),
    
                TextColumn::make('start_date')
                    ->alignment(Alignment::Center)
                    ->label(__('Start Date'))
                    ->sortable()
                    ->dateTime('Y-m-d'), // Format as a readable date
    
                TextColumn::make('end_date')
                    ->alignment(Alignment::Center)
                    ->label(__('End Date'))
                    ->sortable()
                    ->dateTime('Y-m-d'),
    
                TextColumn::make('created_at')
                    ->alignment(Alignment::Center)
                    ->label(__('Created At'))
                    ->sortable()
                    ->dateTime('Y-m-d H:i:s'), // Show date & time
            ]);
    }
}
