<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Organization\app\Models\Organization;

class LeaseMembersRelationManager extends RelationManager
{
    protected static string $relationship = 'leaseMembers';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Members');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Members');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('member_type')
                    ->label(__('Member Type'))
                    ->searchable(),
                TextColumn::make('member.name')
                    ->searchable()
                    ->label(__('Name'))
                    ->sortable()
                    ->url(function ($record) {
                        if ($record->member_type === LeaseMemberTypesEnum::ORGANIZATION_TYPE) {
                            return url("/admin/organizations/{$record->member_id}");
                        } else {
                            return url("/admin/accounts/{$record->member_id}");
                        }
                    }),
                TextColumn::make('member.email')
                    ->searchable()
                    ->label(__('Email')),
                TextColumn::make('member.phone')
                    ->searchable()
                    ->label(_('Phone')),
                TextColumn::make('member.national_id')
                    ->searchable()
                    ->badge()
                    ->label(__('National ID')),
                TextColumn::make('member.unified_number')
                    ->label(__('Unified Number'))
                    ->badge()
                    ->searchable(),
                TextColumn::make('member_role')
                    ->badge()
                    ->searchable()
                    ->label(__('Role'))
                    ->formatStateUsing(fn ($state) => LeaseMemberTypesEnum::getRoleLabel($state)),

                TextColumn::make('bankAccount.bank_name')
                    ->searchable()
                    ->label(__('Bank'))
                    ->badge(),

                TextColumn::make('bankAccount.account_number')
                    ->searchable()
                    ->label(__('Account Number'))
                    ->badge(),

                TextColumn::make('bankAccount.iban')
                    ->searchable()
                    ->label(__('IBAN'))
                    ->badge(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
//                Tables\Actions\EditAction::make(),
//                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
//                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
//                ]),
            ]);
    }
}
