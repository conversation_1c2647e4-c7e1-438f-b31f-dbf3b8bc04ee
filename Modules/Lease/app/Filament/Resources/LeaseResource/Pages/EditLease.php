<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Filament\Actions;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\app\Models\Lease;
use Filament\Resources\Pages\EditRecord;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Enums\LeaseCommissionTypesEnum;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Modules\Lease\Services\LeaseTermsFormatter;
use Modules\Lease\Enums\LeaseTypesEnum;
use Filament\Notifications\Notification;


class EditLease extends EditRecord
{
    protected static string $resource = LeaseResource::class;

    protected static ?LeaseTermsFormatter $termsFormatter = null;
    
    protected static function getLeaseFormatterService(): LeaseTermsFormatter
    {
        if (static::$termsFormatter === null) {
            static::$termsFormatter = new LeaseTermsFormatter();
        }
        return static::$termsFormatter;
    }

    public function mount($record): void
    {
        parent::mount($record);

        if ($this->record->status !== LeaseEnum::DRAFT) {
            redirect()->route('filament.admin.resources.leases.index')->with('error', __('Cannot edit a published lease.'));
        }
    }

    protected function beforeSave(): void
    {
        // Runs before the form fields are saved to the database.
        $addRepresenter = $this->data['add_representer'];
        $representer = $this->record->tenantRepresenter?->exists();
        if ($this->record->lease_type === LeaseTypesEnum::COMMERCIAL) {
            if ($this->record->commercial_meta_data) {
                $this->record->commercial_meta_data->delete();
            }
        }
        if(!$addRepresenter && $representer){
            $this->record->tenantRepresenter()->delete();
            if ($this->record->document()->whereNull('deleted_at')->exists()) {
                $this->record->document()->whereNull('deleted_at')->first()->delete();
            }
        }

        $dailyPenalty = $this->data['daily_penalty_toggle'];
        if(!$dailyPenalty){
            $this->record->update(['daily_penalty' => 0]);
        }
        $insuranceAmount = $this->data['insurance_amount_toggle'];
        if(!$insuranceAmount){
            $this->record->update(['insurance_amount' => 0]);
        }

        $commission_percentage = $this->data['commission']['commission_amount'];
        $commissionType = $this->data['commission']['commission_type'];
        $commissionToggle = $this->data['commission_toggle'];
        $commissionExists = $this->record?->commission()->exists();
        if($commissionExists && !$commissionToggle){
            $this->record->commission()->delete();
        }
        if($commissionToggle && $commissionExists){
            if($commissionType === LeaseCommissionTypesEnum::FIXED && $commission_percentage){
                $this->record->commission()->update(['commission_percentage' => 0]);
            }
        }

        $owners = $this->data['owners'];
        if($owners){
            $owners = collect($this->data['owners'])->map(function ($owner) {
                if ($owner['is_representer'] && $owner['percentage'] > 0) {
                    // Create a copy of the representer with percentage = 0
                    $representerCopy = $owner;
                    $representerCopy['percentage'] = 0;
                    $representerCopy['member_role'] = LeaseMemberTypesEnum::LESSOR_REPRESENTER;

                     // Remove bank_account_id from the original owner
                    $owner['bank_account_id'] = null;

                    return [$owner, $representerCopy];
                }
                return [$owner];
            })->flatten(1)->toArray();

            $this->record->members()->delete();
            $this->record->members()->createMany($owners);
        }

        $enable_external_lease = $this->data['enable_external_lease'];
        if(!$enable_external_lease){
            $this->record->update(['external_lease_id' => null]);
        }

        $TermsFormatter = static::getLeaseFormatterService();
        $termsData = $TermsFormatter->formatTerms($this->data['lease_type'], $this->data);

        $this->record->terms()->delete();
        $this->record->terms()->createMany($termsData);

    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $TermsFormatter = static::getLeaseFormatterService();
        return array_merge(
            $data,
            $TermsFormatter->refillTerms($this->record->lease_type, $this->record->terms)
        );
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn (Lease $record): bool => $record->status !== LeaseEnum::PUBLISHED),
            Actions\ViewAction::make(),
        ];
    }

    protected function getFormActions(): array
    {
        return [
                Actions\Action::make('customAction')
                ->action(function () {
                    try{

                        LeaseResource::updateDueAmount($this);
                        LeaseResource::getStartScheduleDate($this);
                        $this->save();
                        Notification::make()
                            ->success()
                            ->title(__('Success'))
                            ->body(__('Lease updated successfully.'))
                            ->send();
                    }catch (\Illuminate\Validation\ValidationException $e) {
                        $validator = $e->validator;
                        $messages = $validator->getMessageBag()->getMessages();

                        $formattedMessages = collect($messages)->map(function ($message) {
                            return  $message;
                        })->flatten()->implode(' <hr class="my-2"/>');
                        Notification::make()
                            ->danger()
                            ->title(__('Validation Error'))
                            ->body($formattedMessages)
                            ->send();
                    }
                })
                ->label(__('Update Lease')),
        ];
    }
}
