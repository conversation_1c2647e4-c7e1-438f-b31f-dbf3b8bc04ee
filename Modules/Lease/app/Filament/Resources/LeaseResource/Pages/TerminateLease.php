<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseRequestsEnum;
use Modules\Lease\Services\LeaseScheduleService;
use Modules\Request\app\Filament\Resources\RequestResource\Components\FormComponent;
use Modules\Request\app\Models\Request;
use Modules\Request\Enums\RequestTypeEnum;
use Modules\Request\Services\RequestService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Request\Enums\RequestStatusEnum;

class TerminateLease extends Page
{
    protected static string $resource = LeaseResource::class;
    protected static string $view = 'Request::terminate-lease';
    public ?array $data = [];
    public Lease $record;

    public static function getNavigationLabel(): string
    {
        return __('Terminate Lease');
    }

    public function getTitle(): string 
    {
        return __('Terminate Lease Request');
    }

    public function getBreadcrumb(): string
    {
        return __('Terminate');
    }

    public function mount(Lease $record): void
    {
        $this->record = $record;
        if ($this->hasPendingRequest() || !in_array($this->record->status, [
            LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE, LeaseEnum::Renewed])){
            Notification::make()
                ->title(__('Cannot create new request'))
                ->warning()
                ->body(__('This lease already has a pending request.'))
                ->send();

            redirect()->to(LeaseResource::getUrl('index'));
            return;
        }

        $remiaingPayment = new LeaseScheduleService($record->id);
        $remiaining = $remiaingPayment->getLeaseBillsForRequest();

        $this->form->fill([
            'request_date' => now(),
            'insurance_amount' => $record->insurance_amount,
            'total_remaining_amount' => $remiaining ?? 0,
            'request_type' => RequestTypeEnum::TERMINATION,
        ]);
    }

    protected function hasPendingRequest(): bool
    {
        return Request::where('requestable_id', $this->record->id)
            ->where('requestable_type', Lease::class)
            ->whereIn('request_type', [RequestTypeEnum::TERMINATION , RequestTypeEnum::CLOSE , RequestTypeEnum::AUTO_RENEWAL])
            ->whereIn('status', [RequestStatusEnum::PENDING])
            ->exists();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(FormComponent::getTerminateForm($this->record))
            ->statePath('data');
    }

    protected function getFormValidationRules(): array
    {
        return [
            'request_type' => ['required'],
            'request_by' => ['required'],
            'request_date' => ['required', 'date'],
            'reason' => ['required', 'string'],
            'remaing_payment_status' => ['required'],
            'remaing_payment_amount' => [
                'exclude_unless:remaing_payment_status,claimed',
                'numeric',
                'lte:total_remaining_amount'
            ],
            'remaing_payment_due_date' => [
                'exclude_unless:remaing_payment_status,claimed',
                'date'
            ],
            'insurance_amount_status' => ['required'],
            'insurance_amount_amount' => [
                'exclude_unless:insurance_amount_status,claimed',
                'required_if:insurance_amount_status,claimed',    // Added required_if
                'numeric',
                'lte:insurance_amount'
            ],
            'insurance_amount_due_date' => [
                'exclude_unless:insurance_amount_status,claimed',
                'required_if:insurance_amount_status,claimed',    // Added required_if
                'date'
            ],
        ];
    }

    protected function getValidationMessages(): array
    {
        return [
            'request_type.required' => __('Request type is required'),
            'request_by.required' => __('Request by is required'),
            'request_date.required' => __('Request date is required'),
            'request_date.date' => __('Request date must be a valid date'),
            'reason.required' => __('Reason is required'),
            'reason.string' => __('Reason must be text'),
            'remaing_payment_status.required' => __('Remaining payment status is required'),
            'remaing_payment_amount.required_if' => __('Remaining payment amount is required when status is claimed'),
            'remaing_payment_amount.numeric' => __('Remaining payment amount must be a number'),
            'remaing_payment_amount.lte' => __('Remaining payment amount cannot exceed total remaining amount'),
            'remaing_payment_due_date.required_if' => __('Remaining payment due date is required when status is claimed'),
            'remaing_payment_due_date.date' => __('Remaining payment due date must be a valid date'),
            'insurance_amount_status.required' => __('Insurance amount status is required'),
            'insurance_amount_amount.required_if' => __('Insurance amount is required when status is claimed'),
            'insurance_amount_amount.numeric' => __('Insurance amount must be a number'),
            'insurance_amount_amount.lte' => __('Insurance amount cannot exceed total insurance amount'),
            'insurance_amount_due_date.required_if' => __('Insurance amount due date is required when status is claimed'),
            'insurance_amount_due_date.date' => __('Insurance amount due date must be a valid date'),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('confirm')
                ->label(__('Submit Termination Request'))
                ->color('danger')
                ->action(function () {
                    try {
                        // Validate the form data
                        $validator = Validator::make(
                            $this->data,
                            $this->getFormValidationRules(),
                            array_merge(
                                $this->getValidationMessages(),
                                [
                                    'remaing_payment_amount.lte' => __('The remaining payment amount cannot be greater than the remaining amount.'),
                                    'insurance_amount_amount.lte' => __('The insurance amount cannot be greater than the specified insurance amount.'),
                                    'required' => __('The :attribute field is required.'),
                                    'required_if' => __('The :attribute field is required when :other is :value.'),
                                ]
                            )
                        );

                        if ($validator->fails()) {
                            // Collect all errors and display them in a single notification
                            $errorMessages = collect($validator->errors()->all())
                                ->map(fn($error) => "• {$error}")
                                ->join('<br>');

                            Notification::make()
                                ->title(__('Validation Errors'))
                                ->danger()
                                ->body($errorMessages)
                                ->persistent()
                                ->send();
                            return;
                        }

                        // Process the request
                        $data = $this->data;
                        $data['requestable_id'] = $this->record->id;
                        $requestService = new RequestService();
                        $requestService->createRequest($data);

                        Notification::make()
                            ->title(__('Termination request submitted successfully'))
                            ->success()
                            ->send();

                        // Notify
                        $this->notifyAfterTerminate($requestService->getLeaseMembers($this->record->id, LeaseRequestsEnum::TERMINATION));

                        return redirect()->to(LeaseResource::getUrl('index'));
                    } catch (ValidationException $e) {
                        Notification::make()
                            ->title(__('Validation Error'))
                            ->danger()
                            ->body($e->getMessage())
                            ->persistent()
                            ->send();
                        return;
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title(__('Error'))
                            ->danger()
                            ->body($e->getMessage())
                            ->persistent()
                            ->send();
                        return;
                    }
                })
                ->requiresConfirmation()
                ->modalHeading(__('Confirm Termination Request'))
                ->modalDescription(__('Are you sure you want to submit a termination request for this lease?'))
                ->modalSubmitActionLabel(__('Yes, submit request')),
        ];
    }

    protected function notifyAfterTerminate(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'termination_request_add'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-api'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }
}
