<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Modules\Lease\Services\LeaseTermsFormatter;

class CreateLease extends CreateRecord
{
    protected static string $resource = LeaseResource::class;

    protected function afterCreate(): void
    {
        $lease = $this->record;
        if ($this->data['owners']) {
            $owners = collect($this->data['owners'])->map(function ($owner) {
                if ($owner['is_representer'] && $owner['percentage'] > 0) {
                    // Create a copy of the representer with percentage = 0
                    $representerCopy = $owner;
                    $representerCopy['percentage'] = 0;
                    $representerCopy['member_role'] = LeaseMemberTypesEnum::LESSOR_REPRESENTER;

                     // Remove bank_account_id from the original owner
                    $owner['bank_account_id'] = null;

                    return [$owner, $representerCopy];
                }
                return [$owner];
            })->flatten(1)->toArray();

            $lease->members()->createMany($owners);
        }

        $TermsFormatter = new LeaseTermsFormatter();
        $termsData = $TermsFormatter->formatTerms($this->data['lease_type'], $this->data);
        $lease->terms()->createMany($termsData);

            //notify
        $template = NotificationsTemplate::where(['key' => 'new_lease_created'])->first();
        foreach ($lease->leaseMembers() as $leaseMember) {
            SendNotification::make(['fcm-api'])
                ->template($template->key)
                ->model(Account::class)
                ->id($leaseMember->id)
                ->icon($template->icon)
                ->findBody(['{property_name}'])
                ->replaceBody([$lease->property?->name])
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
        //todo send to super admin

    }

    protected function getFormActions(): array
    {
        return [];
    }
}
