<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Modules\Lease\app\Filament\Resources\LeaseResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Lease\app\Filament\Actions\RetrieveLeasesAction;
use Modules\Lease\app\Filament\Resources\LeaseResource\Widgets\LeaseListOverview;

class ListLeases extends ListRecords
{
    protected static string $resource = LeaseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Leases')),
            RetrieveLeasesAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            LeaseListOverview::class
        ];
    }
}
