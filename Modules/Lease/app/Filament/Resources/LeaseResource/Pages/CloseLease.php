<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Exception;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseRequestsEnum;
use Modules\Request\app\Filament\Resources\RequestResource\Components\FormComponent;
use Modules\Request\app\Models\Request;
use Modules\Lease\Services\LeaseScheduleService;
use Modules\Request\Enums\RequestTypeEnum;
use Modules\Request\Services\RequestService;
use Illuminate\Support\Facades\Validator;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Request\Enums\RequestStatusEnum;

class CloseLease extends Page
{
    protected static string $resource = LeaseResource::class;

    protected static string $view = 'Request::close-lease';

    public ?array $data = [];

    public Lease $record;

    public static function getNavigationLabel(): string
    {
        return __('Close Lease');
    }

    public function getTitle(): string
    {
        return __('Close Lease Request');
    }

    public function getBreadcrumb(): string
    {
        return __('close');
    }

    public function mount(Lease $record): void
    {
        $this->record = $record;

        // Check if there's a pending termination request
        if ($this->hasPendingRequest() || $this->record->status !== LeaseEnum::ENDED) {
            Notification::make()
                ->title('Cannot create new request')
                ->warning()
                ->body(__('This lease already has a pending close request.'))
                ->send();

            redirect()->to(LeaseResource::getUrl('index'));
            return;
        }
        $remiaingPayment = new LeaseScheduleService($record->id);
        $remiaining = $remiaingPayment->getLeaseBillsForRequest();

        $days = (int) Carbon::parse($record->end_date)->diffInDays(Carbon::today());
        $totalPenalty = $record->daily_penalty * $days;
        $this->form->fill([
            'request_date'              =>       now(),
            'insurance_amount'          =>       $record->insurance_amount,
            'total_remaining_amount'    =>       $remiaining,
            'daily_penalty'             =>       $record->daily_penalty,
            'number_of_days'            =>       $days,
            'total_penalty_amount'      =>       $totalPenalty,
            'request_type'              =>       RequestTypeEnum::CLOSE,
        ]);
    }

    protected function getFormValidationRules(): array
    {
        return [
            'request_type' => ['required'],
            'unit_status' => ['required'],
            'request_date' => ['required', 'date'],
            'reason' => ['required', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:299'],
            'remaing_payment_status' => ['required'],
            'remaing_payment_amount' => [
                'exclude_unless:remaing_payment_status,claimed',  // Changed from required_if
                'numeric',
                'lte:total_remaining_amount'
            ],
            'remaing_payment_due_date' => [
                'exclude_unless:remaing_payment_status,claimed',  // Changed from required_if
                'date'
            ],
            'insurance_amount_status' => ['required'],
            'insurance_amount_amount' => [
                'exclude_unless:insurance_amount_status,claimed', // Changed from required_if
                'numeric',
                'lte:insurance_amount'
            ],
            'insurance_amount_due_date' => [
                'exclude_unless:insurance_amount_status,claimed', // Changed from required_if
                'date'
            ],
            'images.*' => ['nullable', 'image', 'max:5120'],
            'total_claimed_penalty' => [
            'required',
            'numeric',
            'lte:total_penalty_amount', // Ensure claimed penalty is not greater than total penalty
            ],
        ];
    }

    protected function getValidationMessages(): array
    {
        return [
            'request_type.required' => __('Request type is required'),
            'unit_status.required' => __('Unit status is required'),
            'request_date.required' => __('Request date is required'),
            'request_date.date' => __('Request date must be a valid date'),
            'reason.required' => __('Reason is required'),
            'reason.string' => __('Reason must be text'),
            'reason.max' => __('Reason cannot exceed 255 characters'),
            'notes.max' => __('Notes cannot exceed 299 characters'),
            'remaing_payment_status.required' => __('Remaining payment status is required'),
            'remaing_payment_amount.required_if' => __('Remaining payment amount is required when status is claimed'),
            'remaing_payment_amount.numeric' => __('Remaining payment amount must be a number'),
            'remaing_payment_amount.lte' => __('Remaining payment amount cannot exceed total remaining amount'),
            'remaing_payment_due_date.required_if' => __('Remaining payment due date is required when status is claimed'),
            'remaing_payment_due_date.date' => __('Remaining payment due date must be a valid date'),
            'insurance_amount_status.required' => __('Insurance amount status is required'),
            'insurance_amount_amount.required_if' => __('Insurance amount is required when status is claimed'),
            'insurance_amount_amount.numeric' => __('Insurance amount must be a number'),
            'insurance_amount_amount.lte' => __('Insurance amount cannot exceed total insurance amount'),
            'insurance_amount_due_date.required_if' => __('Insurance amount due date is required when status is claimed'),
            'insurance_amount_due_date.date' => __('Insurance amount due date must be a valid date'),
            'images.*.image' => __('File must be an image'),
            'images.*.max' => __('Image size cannot exceed 5MB'),
            'total_claimed_penalty.required' => __('Claimed Penalty is required'),
            'total_claimed_penalty.numeric' => __('Claimed Penalty must be a number'),
            'total_claimed_penalty.lte' => __('Claimed Penalty cannot exceed the Total Penalty Amount'),
        ];
    }

    protected function hasPendingRequest(): bool
    {
        return Request::where('requestable_id', $this->record->id)
            ->where('requestable_type', Lease::class)
            ->whereIn('request_type', [RequestTypeEnum::TERMINATION , RequestTypeEnum::CLOSE , RequestTypeEnum::AUTO_RENEWAL])
            ->whereIn('status', [RequestStatusEnum::PENDING])
            ->exists();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(FormComponent::getCloseForm($this->record))
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('confirm')
                ->label(__('Submit Close Request'))
                ->color('danger')
                ->action(function () {
                    try {
                        // Validate the form data
                        $validator = Validator::make(
                            $this->data,
                            $this->getFormValidationRules(),
                            $this->getValidationMessages()
                        );

                        if ($validator->fails()) {
                            // Collect all errors and display them in a single notification
                            $errorMessages = collect($validator->errors()->all())
                                ->map(fn($error) => "• {$error}")
                                ->join('<br>');

                            Notification::make()
                                ->title(__('Validation Errors'))
                                ->danger()
                                ->body($errorMessages)
                                ->persistent()
                                ->send();
                            return;
                        }

                        // Process the request
                        $data = $this->data;
                        $data['requestable_id'] = $this->record->id;
                        $requestService = new RequestService();
                        $requestService->createRequest($data);

                        Notification::make()
                            ->title(__('Close request submitted successfully'))
                            ->success()
                            ->send();

                        // Notify members
                        $this->notifyAfterClose($requestService->getLeaseMembers($this->record->id, LeaseRequestsEnum::CLOSE));

                        return redirect()->to(LeaseResource::getUrl('index'));
                    } catch (Exception $e) {
                        Notification::make()
                            ->title(__('Validation Error'))
                            ->danger()
                            ->body($e->getMessage())
                            ->persistent()
                            ->send();
                        return;
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title(__('Error'))
                            ->danger()
                            ->body($e->getMessage())
                            ->persistent()
                            ->send();
                        return;
                    }
                })
                ->requiresConfirmation()
                ->modalHeading(__('Confirm Close Request'))
                ->modalDescription(__('Are you sure you want to submit a close request for this lease?'))
                ->modalSubmitActionLabel(__('Yes, submit request')),
        ];
    }

    protected function notifyAfterClose(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'close_request_add'])->first();
        if ($template) {
            //notify
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-api'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }
}
