<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Filament\Actions\Action;
use Filament\Resources\Pages\Page;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\LeaseContractPdfService;

class LeaseContract extends Page
{
    protected static string $resource = LeaseResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'lease::filament.pages.lease-contract';
    protected static ?string $title = 'Lease Contract';
    protected static ?string $navigationLabel = 'Lease Contract';

    public $record;

    public function mount(Lease $record): void
    {
        $this->record = $record;
    }

    protected function getHeaderActions(): array
    {
        return [

            Action::make('downloadPdf')
                ->label(__('Download PDF'))
                ->action(function () {
                    $pdfService = app(LeaseContractPdfService::class);
                    $pdf = $pdfService->generatePdf($this->record);

                    return response()->streamDownload(
                        function () use ($pdf) {
                            echo $pdf;
                        },
                        'lease-contract-' . $this->record->id . '.pdf',
                        [
                            'Content-Type' => 'application/pdf',
                        ]
                    );
                })
        ];
    }

    protected function getViewData(): array
    {
        return [
            'lease' => $this->record,
        ];
    }
}
