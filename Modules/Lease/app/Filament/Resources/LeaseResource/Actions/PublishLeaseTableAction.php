<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Actions;

use Filament\Tables\Actions\Action;
use Modules\Lease\Services\LeasePublisherService;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;

class PublishLeaseTableAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'publish';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Publish'))
            ->modalHeading(__('Publish Lease'))
            ->modalDescription(__('Are you sure you want to publish this lease?'))
            ->modalSubmitActionLabel(__('Yes, publish it'))
            ->color('success')
            ->icon('heroicon-o-paper-airplane')
            ->requiresConfirmation()
            ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT)
            ->action(function (Lease $record): void {
                $leasePublisher = app(LeasePublisherService::class);
                $leasePublisher->publishLease($record);
            });
    }
}
