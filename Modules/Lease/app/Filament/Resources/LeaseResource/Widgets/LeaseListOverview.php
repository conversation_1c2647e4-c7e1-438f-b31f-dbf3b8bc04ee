<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Widgets;

use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\HtmlString;

class LeaseListOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        // Get all leases with their status and type in one query
        $leases = Lease::select('status', 'lease_type','created_at','updated_at')
            ->get();

         // Total Leases
         $totalLeasesChart = $this->calculateDailyStats($leases, 'created_at');
         $totalLeases = $leases->count();
         $this->cards[] = Stat::make(__('Total Leases'), $totalLeases)
             ->color('primary')
             ->chart(array_reverse($totalLeasesChart));
 
         // Active Leases (Published status)
         $activeLeasesData = $leases->where('status', LeaseEnum::PUBLISHED);
         $activeLeasesChart = $this->calculateDailyStats($activeLeasesData, 'updated_at');
         $activeLeases = $activeLeasesData->count();
         $this->cards[] = Stat::make(__('Active Leases'), $activeLeases)
             ->color('success')
             ->chart(array_reverse($activeLeasesChart));
 
         // Non-Active Leases
         $nonActiveLeasesData = $leases->whereNotIn('status', [LeaseEnum::PUBLISHED]);
         $nonActiveLeasesChart = $this->calculateDailyStats($nonActiveLeasesData, 'updated_at');
         $nonActiveLeases = $nonActiveLeasesData->count();
         $this->cards[] = Stat::make(__('Non-Active Leases'), $nonActiveLeases)
             ->color('danger')
             ->chart(array_reverse($nonActiveLeasesChart));
 
         // Residential Leases
         $residentialLeasesData = $leases->where('lease_type', LeaseTypesEnum::RESIDENTIAL);
         $residentialLeasesChart = $this->calculateDailyStats($residentialLeasesData, 'created_at');
         $residentialLeases = $residentialLeasesData->count();
         $this->cards[] = Stat::make(__('Residential Leases'), $residentialLeases)
             ->color('info')
             ->chart(array_reverse($residentialLeasesChart));
 
         // Commercial Leases
         $commercialLeasesData = $leases->where('lease_type', LeaseTypesEnum::COMMERCIAL);
         $commercialLeasesChart = $this->calculateDailyStats($commercialLeasesData, 'created_at');
         $commercialLeases = $commercialLeasesData->count();
         $this->cards[] = Stat::make(__('Commercial Leases'), $commercialLeases)
             ->color('warning')
             ->chart(array_reverse($commercialLeasesChart));


        $totalEarnings = $this->getAllPaidForLeases();
        $this->cards[] = Stat::make(__('Total Earnings'),
        new HtmlString(
            '<div class="">' .
            number_format($totalEarnings) .
            ' <span class="icon-saudi_riyal"></span>' .
            '</div>'
        ))
            ->color('success');

        return $this->cards;
        
    }

    public function getAllPaidForLeases (){
        $data = InvoiceSchedule::where('invoicable_type', Lease::class)
            ->with('Invoice')
            ->where(function ($query) {
                $query->WhereHas('invoice', function ($query) {
                        $query->whereIn('status', [InvoiceStatusEnum::PAID, InvoiceStatusEnum::PARTIAL_PAID]);
                    });
            })
            ->get();

        return $data->sum(function ($schedule) {
            return $schedule->invoice->paid ?? 0;
        });
    }
}

