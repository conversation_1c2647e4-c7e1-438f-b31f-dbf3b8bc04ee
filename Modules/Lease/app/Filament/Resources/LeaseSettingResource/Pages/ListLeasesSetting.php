<?php

namespace Modules\Lease\app\Filament\Resources\LeaseSettingResource\Pages;

use Modules\Lease\app\Filament\Resources\LeaseSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLeasesSetting extends ListRecords
{
    protected static string $resource = LeaseSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
