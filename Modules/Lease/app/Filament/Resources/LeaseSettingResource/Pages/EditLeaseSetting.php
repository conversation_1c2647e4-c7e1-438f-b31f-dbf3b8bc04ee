<?php

namespace Modules\Lease\app\Filament\Resources\LeaseSettingResource\Pages;

use Filament\Actions;
use Modules\Lease\app\Filament\Resources\LeaseSettingResource;
use Filament\Resources\Pages\EditRecord;

class EditLeaseSetting extends EditRecord
{
    protected static string $resource = LeaseSettingResource::class;

    // Override the getActions method to add your custom actions (optional)
    protected function getActions(): array
    {
        return [
            // Optional: Add custom actions here if needed
            // For example, you could add a custom action for saving in a different way or triggering specific behavior
        ];
    }

    // You don't need to override getFormActions as the Save and Cancel buttons are included by default
}