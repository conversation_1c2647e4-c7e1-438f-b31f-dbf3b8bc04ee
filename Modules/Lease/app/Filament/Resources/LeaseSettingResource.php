<?php

namespace Modules\Lease\app\Filament\Resources;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\GlobalSearch\Actions\Action;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\Lease\app\Models\LeaseSetting;

class LeaseSettingResource extends Resource
{

    protected static ?string $model = LeaseSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Leases Management';



    public static function getGloballySearchableAttributes(): array
    {
        return [

        ];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return LeaseSettingResource::getUrl('view', ['record' => $record]);
    }

    public static function getGlobalSearchResultActions(Model $record): array
    {
        return [
            Action::make('edit')
                ->url(static::getUrl('edit', ['record' => $record]), shouldOpenInNewTab: true)
                ->icon('heroicon-o-pencil')
                ->label('Edit'),
        ];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Description' => $record?->description,
        ];
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Group::make([
                Section::make(__('Name & Value'))
                    ->schema([
                        Grid::make(4) // 4-column grid for better flexibility
                            ->schema([
                                // Tabs for Arabic and English Name
                                Tabs::make('Tabs')
                                    ->tabs([
                                        Tabs\Tab::make(__('Arabic'))
                                            ->schema([
                                                TextInput::make('name.ar')
                                                    ->label(__('Name (Arabic)'))
                                                    ->required()
                                                    ->placeholder(__('Enter name in Arabic')),
                                            ])
                                            ->columnSpan(2), // Takes 2 columns
                                        Tabs\Tab::make(__('English'))
                                            ->schema([
                                                TextInput::make('name.en')
                                                    ->label(__('Name (English)'))
                                                    ->required()
                                                    ->placeholder(__('Enter name in English')),
                                            ])
                                            ->columnSpan(2), // Takes 2 columns
                                    ])
                                    ->columnSpan(3), // Takes 3 columns for the tabs

                                // Value Field
                                TextInput::make('value')
                                    ->label(__('Value'))
                                    ->required()
                                    ->placeholder(__('Enter value here'))
                                    ->columnSpan(1), // Takes 1 column
                            ])
                            ->columnSpan(1), // Whole group takes 1 column
                    ])
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('ID'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('value')
                    ->label(__('Value'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Lease\app\Filament\Resources\LeaseSettingResource\Pages\ListLeasesSetting::route('/'),
            'view' => \Modules\Lease\app\Filament\Resources\LeaseSettingResource\Pages\ViewLeaseSetting::route('/{record}'),
            'edit' => \Modules\Lease\app\Filament\Resources\LeaseSettingResource\Pages\EditLeaseSetting::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
            ]);
    }
    public static function getNavigationLabel(): string
    {
        return __("lease settings");
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Leases Management');
    }
    public static function getBreadcrumb() : string
    {
        return __('lease settings');
    }
    public static function getModelLabel(): string
    {
        return __('leaseSettings');
    }

    public static function getPluralModelLabel(): string
    {
        return __('leaseSettings');
    }
}
