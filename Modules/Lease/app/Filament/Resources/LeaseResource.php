<?php

namespace Modules\Lease\app\Filament\Resources;

use Closure;
use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use App\Models\DocumentType;
use App\Events\LeasePublished;
use Illuminate\Support\Carbon;
use App\Enums\DocumentTypeEnum;
use App\Enums\RoleEnum;
use Filament\Resources\Resource;
use App\Forms\ChangePasswordForm;
use Filament\Support\Colors\Color;
use Illuminate\Support\HtmlString;
use Modules\Lease\Enums\LeaseEnum;
use App\Helpers\LeaseSettingHelper;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\View;
use Filament\Tables\Filters\Filter;
use Modules\Lease\app\Models\Lease;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Illuminate\Support\Facades\Blade;
use App\Shared\Helpers\FormComponents;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Model;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use Modules\Property\app\Models\PropertyMeter;
use Modules\Service\app\Models\Service;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Modules\Lease\Enums\LeasePaymentEnum;
use Modules\Property\app\Models\Property;
use Filament\Forms\Components\Placeholder;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Property\Enums\PropertyStatus;
use Modules\Service\Enums\ServiceTypeEnum;
use Filament\Forms\Components\CheckboxList;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Filament\Forms\Components\Actions\Action;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Service\Enums\ServicePaymentEnum;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use Modules\Lease\Enums\LeaseCommissionTypesEnum;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Modules\Lease\app\Filament\Resources\LeaseResource\Pages\EditLease;
use Modules\Lease\app\Filament\Resources\LeaseResource\Pages\ViewLease;
use Modules\Lease\app\Filament\Resources\LeaseResource\Pages\ListLeases;
use Modules\Lease\app\Filament\Resources\LeaseResource\Pages\CreateLease;
use Modules\Lease\app\Filament\Resources\LeaseResource\Pages\LeaseContract;
use Modules\Lease\app\Filament\Resources\LeaseResource\Widgets\LeaseListOverview;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseInvoiceRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseMembersRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseServicesRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseDocumentsRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseCommissionRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseInvoiceSchedulesRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\CommercialLeaseServicesRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\LeaseRenewalRelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\Actions\PublishLeaseTableAction;
use Modules\Organization\app\Models\Organization;
use App\Forms\Components\HijriDatePicker;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Invoice\Services\InvoiceService;
use Filament\Forms\Components\Radio;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseSettingEnum;
use Modules\Lease\Services\LeaseService;
use Modules\Request\Services\RequestService;
use Modules\BankAccount\Forms\BankAccountQuickForm;
use Modules\BankAccount\app\Models\BankAccount;
use Modules\Lease\Enums\PaymentMethodEnum;
use Filament\Infolists\Infolist;
use App\Shared\Components\CustomInfoTab as Tab;
use App\Shared\Components\CustomInfoTabs;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid as InfolistGrid;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\View as InfolistView;
use Modules\Lease\Services\LeaseScheduleService;
use Filament\Resources\Pages\ViewRecord;
use Modules\Lease\app\Filament\forms\LeaseWizardStep;
use Modules\Request\Enums\RequestStatusEnum;
use Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers\SyncLeaseStepsRelationManager;
use Modules\Lease\Services\LeasePaymentService;

class LeaseResource extends Resource implements HasShieldPermissions
{
    protected static ?string $model = Lease::class;

    protected static ?LeasePaymentService $leasePaymentService = null;
    protected static ?LeaseScheduleService $leaseScheduleService = null;

    //Add this method to get the service instance
    protected static function getLeaseScheduleService($lease_id): LeaseScheduleService
    {
        if (static::$leaseScheduleService === null) {
            static::$leaseScheduleService = new LeaseScheduleService($lease_id);
        }
        return static::$leaseScheduleService;
    }

    // Add this method to get the service instance
    protected static function getLeasePaymentService(): LeasePaymentService
    {
        if (static::$leasePaymentService === null) {
            static::$leasePaymentService = new LeasePaymentService();
        }
        return static::$leasePaymentService;
    }

    protected const RADIO_DECK_COLOR = '#0f2c24';

    protected static ?string $navigationIcon = 'heroicon-o-document-check';



    public static function getNavigationLabel(): string
    {
        return __("Leases");
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Leases Management');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make(__('Lease Duration'))
                    ->description(__('Manage lease duration'))
                    ->icon('heroicon-o-calendar')
                    ->completedIcon('heroicon-o-check-badge')
                    ->schema([
                        // add lease type
                        RadioDeck::make('lease_type')
                            ->label(__('lease type'))
                            ->required()
                            ->options([
                                LeaseTypesEnum::RESIDENTIAL => __('Residential'),
                                LeaseTypesEnum::COMMERCIAL => __('Commercial'),
                            ])
                            ->live()
                            ->direction('column') // Column | Row (Allows to place the Icon on top)
                            ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                'class' => 'rounded-xl radio-deck-cards-color'
                            ])
                            ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                'class' => 'text-sm leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                            ])
                            ->columns(2)
                            ->color(Color::hex(self::RADIO_DECK_COLOR))
                            ->afterStateUpdated(function ($state, callable $set) {
                                // Check if the selected lease_type is 'RESIDENTIAL'
                                if ($state === LeaseTypesEnum::RESIDENTIAL) {
                                    // Set the other fields to 0 if lease_type is 'RESIDENTIAL'
                                    $set('engineering_supervision', 0);
                                    $set('unit_finishing', 0);
                                    $set('waste_removal', 0);
                                    $set('commercial_lease_services', []);
                                }else{
                                    static::getFixedCommircialService($set);
                                }
                            })
                            ->validationMessages([
                                'required' => __('lease type is required'),
                            ]),
                        //finished lease type
                        Grid::make([
                            'default' => 1,
                            'sm' => 2,
                        ])
                            ->schema([
                                // Toggle::make('EJAR_registration_status')
                                //     ->label(__('EJAR Registration Status'))
                                //     ->helperText(__('if you have ejar code please open toggle and add it'))
                                //     ->live()
                                //     ->default(false),
                                TextInput::make('ejar_uuid')
                                    ->numeric()
                                    ->label(__('EJAR UUID Number'))
                                    ->placeholder(__('Enter Your EJAR lease number'))
                                    ->hidden(fn (Get $get): bool => ! $get('EJAR_registration_status')),
                                Toggle::make('enable_external_lease')
                                    ->label(__('Enable External Lease Input'))
                                    ->helperText(__('If you have an external lease number, please toggle this option and add the number below.'))
                                    ->live()
                                    ->default(!is_null($record->external_lease_id ?? null))
                                    ->afterStateHydrated(function (callable $set,?Model $record) {
                                        if ($record?->external_lease_id) {
                                            $set('enable_external_lease', true);
                                        }
                                    }),
                                TextInput::make('external_lease_id')
                                    ->numeric()
                                    ->label(__('External Lease ID'))
                                    ->placeholder(__('Enter the external lease number'))
                                    ->hidden(fn (Get $get): bool => !$get('enable_external_lease')),
                            ]),
                        Grid::make([
                            'default' => 1,
                            'sm' => 2,
                        ])
                            ->schema([
                                Hidden::make('created_by')
                                    ->default(auth()->id()),
                                HijriDatePicker::make('start_date')
                                    ->label(__('Start Date'))
                                    ->required()
                                    ->placeholder(__('Select start date'))
                                    ->native(false)
                                    ->live()
                                    ->syncWith('lease_dates')
                                    ->showConvertedDate()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire,$component) {
                                        $set('unit_ids', []);
                                        self::updateEndDate($state, $set, $get);
                                    })->validationMessages([
                                        'required' => __('Start Date is required'),
                                    ]),
                                HijriDatePicker::make('end_date')
                                    ->label(__('End Date'))
                                    ->minDate(fn (Get $get) => $get('start_date'))
                                    ->required()
                                    ->placeholder(__('Select end date'))
                                    ->native(false)
                                    ->live()
                                    ->hideSwitcher()
                                    ->syncWith('lease_dates')
                                    ->showConvertedDate()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get, $livewire) {
                                        // If the end date is in the past, force auto_renewal to OFF
                                        if ($state && Carbon::parse($state)->isPast()) {
                                            $set('auto_renewal', LeaseAutoRenewalEnum::OFF);
                                        }
                                        self::updateDurationInputs($state, $set, $get);
                                    })
                                    ->afterStateHydrated(function ($state, callable $set, callable $get) {
                                        // Ensure auto_renewal is OFF if the end date is already in the past
                                        if ($state && Carbon::parse($state)->isPast()) {
                                            $set('auto_renewal', LeaseAutoRenewalEnum::OFF);
                                        }
                                        self::updateDurationInputs($state, $set, $get);
                                    })->validationMessages([
                                        'required' => __('End Date is required'),
                                        'after_or_equal' => __('End Date must be after or equal to Start Date.'),
                                    ]),
                                Grid::make([
                                    'default' => 1,
                                    'sm' => 3,
                                ])
                                    ->schema([
                                        TextInput::make('days')
                                            ->label(__('Days'))
                                            ->required()
                                            ->placeholder(__('Enter days'))
                                            ->default(0)
                                            ->minValue(0)
                                            ->maxValue(31)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                                'max' => 31,
                                            ])
                                            ->numeric()
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('days', $state);
                                                self::updateEndDate($state, $set, $get);
                                            })->validationMessages([
                                                'required' => __(' Days is required'),
                                                'min' => __('The entered value is invalid. It must be at least 0'),
                                                'max' => __('The entered value is invalid. It must be a maximum of 31.')
                                            ]),
                                        TextInput::make('months')
                                            ->label(__('Months'))
                                            ->required()
                                            ->placeholder(__('Enter months'))
                                            ->default(0)
                                            ->minValue(0)
                                            ->maxValue(12)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                                'max' => 12,
                                            ])
                                            ->numeric()
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('months', $state);
                                                self::updateEndDate($state, $set, $get);
                                            })->validationMessages([
                                                'required' => __(' Months is required'),
                                                'min' => __('The entered value is invalid. It must be at least 0'),
                                                'max' => __('The entered value is invalid. It must be a maximum of 12.')
                                            ]),
                                        TextInput::make('years')
                                            ->label(__('Years'))
                                            ->required()
                                            ->placeholder(__('Enter years'))
                                            ->default(0)
                                            ->minValue(0)
                                             ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->numeric()
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('years', $state);
                                                self::updateEndDate($state, $set, $get);
                                            })->validationMessages([
                                                'required' => __(' Years is required'),
                                                'min' => __('The entered value is invalid. It must be at least 0'),
                                            ]),
                                            Radio::make('auto_renewal')
                                            ->label(__('Lease Auto Renewal'))
                                            ->helperText(
                                                __('You cannot cancel it if remaining days equal or are less than ') .
                                                LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD) . __(' days')
                                            )
                                            ->options(LeaseAutoRenewalEnum::labels())
                                            ->default(LeaseAutoRenewalEnum::OFF)
                                            ->inline()
                                            ->disabled(function (Get $get) {
                                                $endDate = $get('end_date');
                                                return $endDate && Carbon::parse($endDate)->isPast();
                                            })
                                            ->hidden(fn ($get) => $get('auto_renewal') === RequestStatusEnum::PENDING),
                                    ])->columns(6)
                            ]),
                    ]),
                    Wizard\Step::make(__('Property and Units'))
                    ->description(__('Manage property and units'))
                    ->icon('heroicon-o-building-office')
                    ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Select::make('property_id')
                                ->label(__('Property'))
                                ->options(Property::select('id', 'name')->where('status',PropertyStatus::ACTIVE)->whereNull('parent_id')->get()->pluck('name', 'id'))
                                ->required()
                                ->live()
                                ->placeholder(__('Select property'))
                                ->searchable()
                                ->afterStateUpdated(function ($state, callable $set, callable $get , $livewire){
                                    $set('company_id',Property::firstWhere('id', $state)?->company_id);
                                    $livewire->data['document']['company_id'] = $state?->company_id ?? null;
                                    self::propertySelect($state, $set, $get);
                                })->validationMessages([
                                    'required' => __(' Property is required'),
                                ]),
                            Hidden::make("has_vat_number")
                                ->dehydrated(false),
                            Hidden::make('company_id'),
                            CheckboxList::make('unit_ids')
                                ->label(__('Units'))
                                // ->relationship('units')
                                ->live()
                                ->options(function (callable $get,$livewire,$record) {
                                    $propertyId = $get('property_id');
                                    if (!$propertyId) return [] ;

                                    $units = Property::where('parent_id', $propertyId)
                                        ->with(['property_type','attributes.attribute'])
                                        ->where(function($query) use($record){
                                            $query->where('status',PropertyStatus::ACTIVE);
                                            if ($record) {
                                                $query->orWhereIn('id', $record->units()->pluck('unit_id'));
                                            }
                                        })
                                        ->get();

                                        if ($units->isEmpty()) {
                                            return [];
                                        }

                                        return $units->mapWithKeys(function ($unit) {
                                            return [$unit->id => new HtmlString(view('lease::forms.components.unit-checkbox-card', [
                                                'unit' => $unit,
                                            ])->render())];
                                        })
                                        ->toArray();
                                })
                                ->columns(['default' => 1, 'sm' => 1, 'lg' => 2, 'xl' => 3])
                                ->bulkToggleable()
                                ->required()
                                ->gridDirection('row')
                                ->visible(fn (callable $get) => filled($get('property_id')))
                                ->afterStateUpdated(function (Get $get, Set $set, $livewire) {
                                    self::getFixedService($get, $set, $livewire);
                                    self::getUnitsMeters($get, $set, $livewire);
                                })
                                ->afterStateHydrated(function (CheckboxList $component, $state, ?Model $record, Set $set) {
                                    if ($record) {
                                        $set('unit_ids', $record?->leaseUnits->pluck('unit_id'));
                                    }
                                })
                                ->extraFieldWrapperAttributes([
                                    'class' => 'checkbox-list-wrapper-units',
                                ])->validationMessages([
                                    'required' => __(' you must select at least one unit'),
                                ]),
                            // Add a section to display meter information after units are selected
                                View::make('lease::forms.components.no-units-available')
                                    ->visible(function (callable $get, $livewire) {
                                        $propertyId = $get('property_id');
                                        if (!$propertyId) return false;

                                        return !Property::where('parent_id', $propertyId)
                                            ->where('status', PropertyStatus::ACTIVE)
                                            ->exists();
                                    })
                        ]),
                    Wizard\Step::make(__('Stackholder'))
                        ->description(__('Manage stakeholder'))
                        ->icon('heroicon-o-user-group')
                        ->completedIcon('heroicon-o-check-badge')
                        ->schema([
                            Section::make(__('Owners'))
                                ->label(__('Owners'))
                                ->schema([
                                    Repeater::make('owners')
                                        ->hiddenLabel()
                                        ->grid(2)
                                        ->extraAttributes(['class'=>'owner_lease_list'])
                                        ->deletable(false)
                                        ->reorderable(false)
                                        ->addable(false)
                                        ->schema([
                                            Grid::make(1)
                                                ->schema([
                                                    Hidden::make('name'),
                                                    Hidden::make('national_id'),
                                                    View::make('lease::forms.components.owner-card'),
                                                    Hidden::make('member_role'),
                                                    Hidden::make('member_id'),
                                                    Hidden::make('percentage'),
                                                    Hidden::make('is_organization'),
                                                    Hidden::make('organization_name'),
                                                    Hidden::make('unified_number'),
                                                    Hidden::make('ownership_document_number'),
                                                    Hidden::make('member_type'),
                                                    Hidden::make('memberable_type'),
                                                    Hidden::make('vat_number'),
                                                ]),
                                            Hidden::make('member_id')->reactive()->live(),

                                            Select::make('owner_bank_account_id')
                                                ->label(__('Bank Account'))
                                                ->placeholder(__('Select bank account'))
                                                ->prefixIcon('heroicon-o-credit-card')
                                                ->allowHtml()
                                                ->options(function (Get $get , $set) {
                                                    $memberId = $get('member_id');
                                                    if (!$memberId) return [];
                                                    $bankAccounts = Account::find($memberId)?->bankAccounts()
                                                        ->where('is_active', true)
                                                        ->get();

                                                    $bankAccountsView = $bankAccounts->mapWithKeys(function ($bankAccount) {
                                                        return [
                                                            $bankAccount->id =>  static::getOptionView($bankAccount)
                                                        ];
                                                    });
                                                    $set('owner_bank_account_id', $bankAccounts->first()?->id);
                                                    return $bankAccountsView;
                                                })
                                                ->createOptionForm( BankAccountQuickForm::make())
                                                ->createOptionAction(
                                                    fn(Action $action) => $action
                                                        ->modalWidth('4xl')
                                                )
                                                ->createOptionUsing(function (array $data,$get) {
                                                    $bankAccount = BankAccount::create(
                                                        [
                                                            'bankable_id' => $get('member_id'),
                                                            'bankable_type' => Account::class,
                                                            'bank_name' => $data['bank_name'],
                                                            'branch_name' => $data['branch_name'],
                                                            'account_name' => $data['account_name'],
                                                            'account_number' => $data['account_number'],
                                                            'swift_code' => $data['swift_code'],
                                                            'iban' => $data['iban'],
                                                            'is_primary' => $data['is_primary'],
                                                            'is_active' => $data['is_active'],
                                                        ]
                                                    );
                                                    return $bankAccount->id;
                                                })
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    $memberId = $get('member_id');
                                                    if (!$memberId) return [];

                                                    Account::find($memberId)?->bankAccounts()
                                                        ->where('is_active', true)
                                                        ->get()
                                                        ->mapWithKeys(function ($bankAccount) {
                                                            return [$bankAccount->id =>static::getOptionView($bankAccount)];
                                                        });
                                                })
                                                ->searchable()
                                                ->debounce(500),
                                            Toggle::make('is_representer')
                                                ->label(__('Representer'))
                                                ->fixIndistinctState()
                                                ->visible(fn (Get $get)=>$get('is_representer'))
                                                ->disabled()
                                                ->live()
                                                ->onIcon('heroicon-m-bolt')
                                                ->offIcon('heroicon-m-user'),
                                        ])
                                        ->afterStateHydrated(fn ($state, callable $set, callable $get,?Model $record) => self::propertySelectHydrated($state, $set, $get,$record))
                                ]),
                                Section::make(__('Tenant'))
                                    ->label(__('Tenant'))
                                    ->relationship('tenant')
                                    ->schema([
                                        Grid::make(1)
                                            ->schema([
                                                RadioDeck::make('is_organization')
                                                ->label(__('Tenant Type'))
                                                ->required()
                                                ->options([
                                                    0 => __('Tenant'),
                                                    1 => __('Organization'),
                                                ])
                                                ->default(0)
                                                ->live()
                                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                ])
                                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                    'class' => 'text-sm leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                ])
                                                ->columns(2)
                                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                ->afterStateUpdated(function ($state, callable $set, callable $get,$livewire) {
                                                    $set('member_id',null);
                                                    $livewire->data['tenantRepresenter']['member_id'] = null;
                                                    if (!$state) {
                                                        $set('member_type',LeaseMemberTypesEnum::INDIVIDUAL_TYPE);
                                                        $set('memberable_type',Account::class);
                                                    }else{
                                                        $set('member_type',LeaseMemberTypesEnum::ORGANIZATION_TYPE);
                                                        $set('memberable_type',Organization::class);
                                                        $set('../add_representer',true);
                                                    }
                                                }),
                                                Select::make('member_id')
                                                    ->label(function (Get $get) {
                                                        return $get('is_organization')
                                                            ? __('Organization')
                                                            : __('Individual');
                                                    })
                                                    ->placeholder(__('Select tenant'))
                                                    ->searchable()
                                                    ->native(false)
                                                    ->required()
                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                        $representerId = $get('../tenantRepresenter.member_id');
                                                        $ownerIds = collect($get('../owners'))->pluck('member_id')->toArray();
                                                        $isOrganization = $get('is_organization');

                                                        if ($isOrganization) {
                                                            // Search for organizations
                                                            return Organization::where(function ($query) use ($search) {
                                                                $query->where('name', '=', "{$search}")
                                                                    ->orWhere('unified_number', '=', "{$search}");
                                                            })
                                                            ->whereNotIn('id', $ownerIds)
                                                            ->get()
                                                            ->mapWithKeys(function ($organization) {
                                                                return [$organization->id => "{$organization->name} - {$organization->unified_number}"];
                                                            });
                                                        } else {
                                                            // Search for individuals (accounts)
                                                            return Account::where(function ($query) use ($search) {
                                                                $query->where('name', '=', "{$search}")
                                                                    ->orWhere('national_id', '=', "{$search}");
                                                            })
                                                            ->where('is_active', true)
                                                            ->when($representerId, function ($query) use ($representerId) {
                                                                return $query->where('id', '!=', $representerId);
                                                            })
                                                            ->whereNotIn('id', $ownerIds)  // Exclude owner IDs
                                                            ->get()
                                                            ->mapWithKeys(function ($account) {
                                                                return [$account->id => "{$account->name} - {$account->national_id}"];
                                                            });
                                                        }
                                                    })
                                                    ->getOptionLabelUsing(function ($value, Get $get) {
                                                        $isOrganization = $get('is_organization');

                                                        if ($isOrganization) {
                                                            // Get organization label
                                                            $organization = Organization::find($value);
                                                            if ($organization) {
                                                                return "{$organization->name} - {$organization->unified_number}";
                                                            }
                                                        } else {
                                                            // Get account label
                                                            $account = Account::find($value);
                                                            if ($account) {
                                                                return "{$account->name} - {$account->national_id}";
                                                            }
                                                        }
                                                        return null;
                                                    })  ->validationMessages([
                                                        'required' => __('You must select a Tenant'),
                                                    ])
                                                    ->createOptionForm(function ($form, Get $get) {
                                                        $isOrganization = $get('is_organization');
                                                        if ($isOrganization) {
                                                            return [
                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('name')
                                                                            ->label(__('Organization Name'))
                                                                            ->placeholder(__('Enter organization name'))
                                                                            ->required()
                                                                            ->validationMessages([
                                                                                'required' => __('Organization Name is required'),
                                                                            ]),
                                                                        TextInput::make('ownership_document_number')
                                                                            ->label(__('Ownership Document Number'))
                                                                            ->placeholder(__('Enter ownership document number'))
                                                                            ->numeric()
                                                                            ->required()
                                                                            ->validationMessages([
                                                                                'required' => __('Ownership Document Number is required'),
                                                                            ]),
                                                                        TextInput::make('unified_number')
                                                                            ->label(__('Unified Number'))
                                                                            ->numeric()
                                                                            ->required()
                                                                            ->unique('organizations', 'unified_number')
                                                                            ->placeholder('Just Start with 70xxxxxxxx')
                                                                            ->extraInputAttributes([
                                                                                'maxlength' => '10',
                                                                                'minlength' => '10',
                                                                                'pattern' => '70[0-9]{8}',
                                                                                'oninput' => "
                                                                                    this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                                                                    if (this.value.length > 1 && !this.value.startsWith('70')) {
                                                                                        this.value = '70';
                                                                                    }
                                                                                    this.dispatchEvent(new Event('input'));
                                                                                "
                                                                            ])
                                                                            ->rules([
                                                                                'required',
                                                                                'numeric',
                                                                                'digits:10',
                                                                                'regex:/^70\d{8}$/',
                                                                            ])
                                                                            ->validationMessages([
                                                                                'required' => __('Unified Number is required'),
                                                                                'digits' => __('Unified Number must be exactly 10 digits'),
                                                                                'numeric' => __('Unified Number must contain only numbers'),
                                                                                'regex' => __('Unified Number must start with 70'),
                                                                                'unique' => __('Unified Number is already taken'),
                                                                            ]),
                                                                        TextInput::make('registration_number')
                                                                            ->label(__('Registration Number'))
                                                                            ->placeholder(__('Enter Registration Number'))
                                                                            ->required()
                                                                            ->validationMessages([
                                                                                'required' => __('Registration Number is required'),
                                                                            ]),
                                                                        DatePicker::make('registration_date')
                                                                            ->label(__('Registration Date'))
                                                                            ->placeholder(__('Enter Registration Date'))
                                                                            ->required()
                                                                            ->before(now())
                                                                            ->native(false)
                                                                            ->validationMessages([
                                                                                'required' => __('Registration Date is required'),
                                                                            ]),
                                                                    ])
                                                            ];
                                                        }else{

                                                            $updateFullName = function ($set, $get) {
                                                                $set('name', trim(
                                                                    $get('first_name') . ' ' .
                                                                    $get('second_name') . ' ' .
                                                                    $get('third_name') . ' ' .
                                                                    $get('last_name')
                                                                ));
                                                            };

                                                            return [
                                                                Hidden::make('name'),
                                                                Hidden::make('lang')->default('ar'),
                                                                Hidden::make('is_active')->default(true),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('first_name')
                                                                            ->required()
                                                                            ->label(__('First Name'))
                                                                            ->placeholder(__('Enter first name'))
                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('First Name is required'),
                                                                            ]),

                                                                        TextInput::make('second_name')
                                                                            ->required()
                                                                            ->label(__('Second Name'))
                                                                            ->placeholder(__('Enter second name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('Second Name is required'),
                                                                            ]),
                                                                    ]),
                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('third_name')
                                                                            ->required()
                                                                            ->label(__('Third Name'))
                                                                            ->placeholder(__('Enter third name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('Third Name is required'),
                                                                            ]),
                                                                        TextInput::make('last_name')
                                                                            ->required()
                                                                            ->label(__('Last Name'))
                                                                            ->placeholder(__('Enter last name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('Last Name is required'),
                                                                            ]),
                                                                    ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('email')
                                                                            ->email()
                                                                            ->required()
                                                                            ->unique('accounts', 'email')
                                                                            ->label(__('Email'))
                                                                            ->validationMessages([
                                                                                'required' => __('Email  is required'),
                                                                            ]),
                                                                        TextInput::make('phone')
                                                                            ->label(__('Phone Number'))
                                                                            ->tel()
                                                                            ->required()
                                                                            ->prefix('+966')
                                                                            ->placeholder('5xxxxxxxx')
                                                                            ->rules([
                                                                                'required',
                                                                                'string',
                                                                                'regex:/^5[0-9]{8}$/',
                                                                                'size:9',
                                                                                fn () => function($attribute, $value, $fail) {
                                                                                    $fullPhone = '+966' . $value;
                                                                                    if (Account::where('phone', $fullPhone)->exists()) {
                                                                                        $fail(__('This phone number is already registered.'));
                                                                                    }
                                                                                }
                                                                            ])
                                                                            ->extraInputAttributes([
                                                                                'maxlength' => '9',
                                                                                'pattern' => '5[0-9]*',
                                                                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                                                            ])
                                                                            ->dehydrateStateUsing(fn ($state) => '+966' . $state),
                                                                    ]),

                                                            Grid::make(2)
                                                                ->schema([
                                                                    FormComponents::idFieldSet(),
                                                                    FormComponents::birthDateInput('birth_date')
                                                                        ->label(__('Birth Date'))
                                                                        ->helperText(__('Must be at least 18 years old')),
                                                                ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        ...ChangePasswordForm::make($form->getOperation(), true),
                                                                    ]),

                                                                Hidden::make('accountRoles')
                                                                    ->default([
                                                                        [
                                                                            'role' => 'owner',
                                                                            'is_default' => true
                                                                        ]
                                                                    ]),
                                                            ];
                                                        }
                                                    })
                                                    ->createOptionUsing(function (array $data, Get $get) {
                                                        $isOrganization = $get('is_organization');
                                                        if ($isOrganization) {
                                                            $organization = Organization::create([
                                                                'name' => $data['name'],
                                                                'unified_number' => $data['unified_number'],
                                                                'ownership_document_number' => $data['ownership_document_number'],
                                                            ]);
                                                            return $organization->id;
                                                        }else{
                                                            $account = Account::create([
                                                                'name' => $data['name'],
                                                                'first_name' => $data['first_name'],
                                                                'second_name' => $data['second_name'],
                                                                'third_name' => $data['third_name'],
                                                                'last_name' => $data['last_name'],
                                                                'email' => $data['email'],
                                                                'phone' => $data['phone'],
                                                                'national_id' => $data['national_id'],
                                                                'id_type' => $data['id_type'],
                                                                'country_of_issue' => $data['country_of_issue'] ?? 'SA',
                                                                'birth_date' => $data['birth_date'],
                                                                'lang' => $data['lang'],
                                                                'is_active' => $data['is_active'],
                                                                'password' => $data['password'],
                                                            ]);

                                                            if (isset($data['accountRoles']) && is_array($data['accountRoles'])) {
                                                                foreach ($data['accountRoles'] as $roleData) {
                                                                    $account->accountRoles()->create([
                                                                        'role' => $roleData['role'],
                                                                        'is_default' => $roleData['is_default'] ?? true,
                                                                    ]);
                                                                }
                                                            }

                                                            return $account->id;
                                                        }
                                                    })
                                                    ->createOptionAction(fn(Action $action) => $action->visible(function (Get $get) {
                                                        $isOrganization = $get('is_organization');
                                                        if ($isOrganization) {
                                                            return auth()->user()->can('create', Organization::class);
                                                        } else {
                                                            return auth()->user()->can('create', Account::class);
                                                        }
                                                     })
                                                    )
                                                    ->debounce(500)
                                                    ->visible(function($livewire) {
                                                        if($livewire instanceof ViewRecord) {
                                                            return false;
                                                        }
                                                        return true;
                                                    }),
                                                Hidden::make('member_role')
                                                    ->default(LeaseMemberTypesEnum::TENANT),
                                                Hidden::make('memberable_type')
                                                    ->default(Account::class),
                                                Hidden::make('member_type')
                                                    ->default(LeaseMemberTypesEnum::INDIVIDUAL_TYPE)

                                            ]),
                                    ]),
                                    Toggle::make('add_representer')
                                        ->label(__('add representer'))
                                        ->onIcon('heroicon-m-bolt')
                                        ->offIcon('heroicon-m-user')
                                        ->live()
                                        ->afterStateUpdated(function ($get , $set,$livewire) {
                                            $set('tenantRepresenter.member_id',null);
                                            foreach ($get('document.metadata') as $key => $value) {
                                                $set('document.metadata.'.$key,null);
                                            }
                                            $set('document.metadata.document_name_and_type',null);
                                        })
                                        ->afterStateHydrated(function (callable $set,?Model $record) {
                                            $representer = $record?->tenantRepresenter;
                                            if ($representer) {
                                                $set('add_representer', true);
                                            }
                                        })
                                        ->hidden(fn (Get $get)=>$get('tenant.is_organization')),

                                    Section::make(__('Representer'))
                                        ->label(__('Representer'))
                                        ->relationship('tenantRepresenter')
                                        ->schema([
                                            Grid::make(1)
                                                ->schema([
                                                    Select::make('member_id')
                                                        ->label(__('Representer'))
                                                        ->placeholder(__('Select representer'))
                                                        ->searchable(['national_id', 'name'])
                                                        ->native(false)
                                                        ->live()
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('Please select a member representer'),
                                                        ])
                                                        ->getSearchResultsUsing(function (string $search, Get $get) {
                                                            $tenantId = $get('../tenant.member_id');
                                                            $ownerIds = collect($get('../owners'))->pluck('member_id')->toArray();
                                                            return Account::where(function ($query) use ($search) {
                                                                $query->where('name', '=', "{$search}")
                                                                    ->orWhere('national_id', '=', "{$search}");
                                                            })
                                                            ->where('is_active', true)
                                                            ->when($tenantId, function ($query) use ($tenantId) {
                                                                return $query->where('id', '!=', $tenantId);
                                                            })
                                                            ->whereNotIn('id', $ownerIds)  // Exclude owner IDs
                                                            ->get()
                                                            ->mapWithKeys(function ($account) {
                                                                return [$account->id => "{$account->name} - {$account->national_id}"];
                                                            });
                                                        })
                                                        ->getOptionLabelUsing(function ($value) {
                                                            // Only query the database for the selected value
                                                            $account = Account::find($value);
                                                            if ($account) {
                                                                return "{$account->name} - {$account->national_id}";
                                                            }
                                                            return null;
                                                        })
                                                        ->debounce(500)
                                                        ->createOptionForm(function ($form) {
                                                            $updateFullName = function ($set, $get) {
                                                                $set('name', trim(
                                                                    $get('first_name') . ' ' .
                                                                    $get('second_name') . ' ' .
                                                                    $get('third_name') . ' ' .
                                                                    $get('last_name')
                                                                ));
                                                            };

                                                            return [
                                                                Hidden::make('name'),
                                                                Hidden::make('lang')->default('ar'),
                                                                Hidden::make('is_active')->default(true),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('first_name')
                                                                            ->required()
                                                                            ->label(__('First Name'))
                                                                            ->placeholder(__('Enter first name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('The First Name is required.'),
                                                                            ]),
                                                                        TextInput::make('second_name')
                                                                            ->required()
                                                                            ->label(__('Second Name'))
                                                                            ->placeholder(__('Enter second name'))
                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('The First Name is required.'),
                                                                            ]),
                                                                    ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('third_name')
                                                                            ->required()
                                                                            ->label(__('Third Name'))
                                                                            ->placeholder(__('Enter third name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('The Third Name is required.'),
                                                                            ]),
                                                                        TextInput::make('last_name')
                                                                            ->required()
                                                                            ->label(__('Last Name'))
                                                                            ->placeholder(__('Enter last name'))

                                                                            ->afterStateUpdated($updateFullName)
                                                                            ->validationMessages([
                                                                                'required' => __('The Last Name is required.'),
                                                                            ]),
                                                                    ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        TextInput::make('email')
                                                                            ->email()
                                                                            ->required()
                                                                            ->unique('accounts', 'email')
                                                                            ->label(__('Email'))
                                                                            ->validationMessages([
                                                                                'required' => __('The Email is required.'),
                                                                                'unique' => __('The Email has already been taken.'),
                                                                            ]),
                                                                        TextInput::make('phone')
                                                                            ->label(__('Phone Number'))
                                                                            ->tel()
                                                                            ->required()
                                                                            ->prefix('+966')
                                                                            ->placeholder('5xxxxxxxx')
                                                                            ->rules([
                                                                                'required',
                                                                                'string',
                                                                                'regex:/^5[0-9]{8}$/',
                                                                                'size:9',
                                                                                fn () => function($attribute, $value, $fail) {
                                                                                    $fullPhone = '+966' . $value;
                                                                                    if (Account::where('phone', $fullPhone)->exists()) {
                                                                                        $fail(__('This phone number is already registered.'));
                                                                                    }
                                                                                }
                                                                            ])
                                                                            ->extraInputAttributes([
                                                                                'maxlength' => '9',
                                                                                'pattern' => '5[0-9]*',
                                                                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                                                                            ])
                                                                            ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                                                                            ->validationMessages([
                                                                                'required' => __('The Phone Number is required.'),
                                                                                'regex' => __('The Phone Number must start with 5 and be 9 digits.'),
                                                                                'size' => __('The Phone Number must be exactly 9 digits.'),
                                                                            ])

                                                                    ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        FormComponents::idFieldSet(),
                                                                        FormComponents::birthDateInput('birth_date')
                                                                            ->label(__('Birth Date'))
                                                                            ->helperText(__('Must be at least 18 years old')),
                                                                    ]),

                                                                Grid::make(2)
                                                                    ->schema([
                                                                        ...ChangePasswordForm::make($form->getOperation(), true),
                                                                    ]),

                                                                Hidden::make('accountRoles')
                                                                    ->default([
                                                                        [
                                                                            'role' => 'owner',
                                                                            'is_default' => true
                                                                        ]
                                                                    ]),
                                                            ];
                                                        })
                                                        ->createOptionUsing(function (array $data) {
                                                            $account = Account::create([
                                                                'name' => $data['name'],
                                                                'first_name' => $data['first_name'],
                                                                'second_name' => $data['second_name'],
                                                                'third_name' => $data['third_name'],
                                                                'last_name' => $data['last_name'],
                                                                'email' => $data['email'],
                                                                'phone' => $data['phone'],
                                                                'national_id' => $data['national_id'],
                                                                'id_type' => $data['id_type'],
                                                                'country_of_issue' => $data['country_of_issue'] ?? 'SA',
                                                                'birth_date' => $data['birth_date'],
                                                                'lang' => $data['lang'],
                                                                'is_active' => $data['is_active'],
                                                                'password' => $data['password'],
                                                            ]);

                                                            if (isset($data['accountRoles']) && is_array($data['accountRoles'])) {
                                                                foreach ($data['accountRoles'] as $roleData) {
                                                                    $account->accountRoles()->create([
                                                                        'role' => $roleData['role'],
                                                                        'is_default' => $roleData['is_default'] ?? true,
                                                                    ]);
                                                                }
                                                            }

                                                            return $account->id;
                                                        }),
                                                    Hidden::make('member_role')
                                                        ->default(LeaseMemberTypesEnum::TENANT_REPRESENTER),
                                                    Hidden::make('member_type')
                                                        ->default(LeaseMemberTypesEnum::INDIVIDUAL_TYPE),
                                                    Hidden::make('memberable_type')
                                                        ->default(Account::class),

                                                ]),
                                        ])
                                        ->visible(fn(Get $get)=>$get('add_representer')),

                                    Section::make(__('Document'))
                                        ->relationship('document')
                                        ->schema([
                                            Grid::make(2)
                                                ->schema([
                                                    Hidden::make('document_type_id')
                                                        ->default(function () {
                                                            return DocumentType::where('key', DocumentTypeEnum::Representative->value)->first()->id;
                                                        }),
                                                    Hidden::make('company_id')
                                                    ->default(function($livewire) {
                                                        if(!auth()->user()->hasRole(RoleEnum::ADMIN)){
                                                            return auth()->user()->company_id;
                                                        }
                                                        return $livewire->data['company_id'] ?? null;
                                                    }),
                                                    TextInput::make('metadata.document_name_and_type')
                                                        ->label(__('Document Name and Type'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('The Document Name and Type is required.'),
                                                        ]),
                                                    TextInput::make('metadata.document_number')
                                                        ->label(__('Document Number'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('The Document Number is required.'),
                                                        ]),
                                                    HijriDatePicker::make('metadata.release_date')
                                                        ->label(__('Release Date'))
                                                        ->syncWith('document_date')
                                                        ->showConvertedDate()
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('The Release Date is required.'),
                                                        ]),

                                                    HijriDatePicker::make('metadata.expiration_date')
                                                        ->label(__('Expiration Date'))
                                                        ->syncWith('document_date')
                                                        ->showConvertedDate()
                                                        ->hideSwitcher()
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('The Expiration Date is required.'),
                                                        ]),
                                                    TextInput::make('metadata.released_by')
                                                        ->label(__('Released By'))
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('The Released By is required.'),
                                                        ]),
                                                    TextInput::make('metadata.released_in')
                                                        ->label(__('Released In'))
                                                        ->required()
                                                         ->validationMessages([
                                                        'required' => __('The Released In is required.'),
                                                      ]),
                                                    SpatieMediaLibraryFileUpload::make('upload_contract')
                                                        ->label(__('Upload Contract Authorization'))
                                                        ->downloadable()
                                                        ->openable()
                                                        ->panelLayout('grid')
                                                        ->reorderable()
                                                        ->multiple()
                                                        ->maxSize(5120)
                                                        ->collection('upload_contract')
                                                        ->imageEditorAspectRatios([
                                                            '16:9',
                                                            '4:3',
                                                            '1:1',
                                                        ])
                                                        ->columnSpanFull()
                                                        ->required()
                                                        ->validationMessages([
                                                            'required' => __('Upload Contract Authorization is required.'),
                                                        ]),
                                                ])
                                        ])
                                        ->visible(fn(Get $get)=>$get('add_representer')),
                            ]),
                    Wizard\Step::make(__('Financial data'))
                    ->description(__('Manage financial data'))
                    ->icon('heroicon-o-banknotes')
                    ->completedIcon('heroicon-o-check-badge')
                    ->schema([
                        Section::make(__('Rent Amount'))
                            ->label(__('Rent Amount'))
                            ->schema([
                                TextInput::make('rent_amount')
                                    ->label(__('Rent Amount'))
                                    ->required()
                                    ->placeholder(__('Enter total amount'))
                                    ->numeric()
                                    ->minValue(0)
                                    ->extraInputAttributes([
                                        'min' => 0,
                                    ])
                                    ->validationMessages([
                                        'required' => __('Rent Amount is required   .'),
                                        'min' => __('The Total Amount Must Be Greater Than 0.'),
                                    ]),
                                Hidden::make('total_amount'),
                                Hidden::make('total_services_amount')
                                ->validationMessages([
                                    'required' => __('The Total Amount is required.'),
                                    'min' => __('The Total Amount Must Be Greater Than 0.'),
                                ]),
                            ]),
                        Section::make(__('Retainer'))
                            ->label(__('Retainer'))
                            ->description(__('Tenant is required to pay a retainer deposit to reserve selected units'))
                            ->schema([
                                Toggle::make('has_retainer')
                                    ->label(__('Enable Retainer'))
                                    ->default(false)
                                    ->reactive(),

                                TextInput::make('retainer_amount')
                                    ->label(__('Retainer Amount'))
                                    ->prefix('SAR')
                                    ->numeric()
                                    ->rules(['required_if:has_retainer,true', 'numeric', 'min:0'])
                                    ->hidden(fn (Get $get) => ! $get('has_retainer'))
                                    ->validationMessages([
                                        'required' => __('The Retainer Amount is required.'),
                                        'numeric' => __('The Retainer Amount must be a number only.'),
                                        'min' => __('The Retainer Amount must be greater than 0.'),
                                    ])
                            ]),
                        Section::make(__('Repeat Payment'))
                            ->label(__('Repeat Payment'))
                            ->schema([
                                Grid::make(1)
                                    ->schema([
                                        RadioDeck::make('payment_type')
                                            ->hiddenLabel()
                                            ->required()
                                            ->options([
                                                LeasePaymentEnum::ONCE => __('Once payment'),
                                                LeasePaymentEnum::REPEATED => __('Repeated payment'),
                                            ])
                                            ->disableOptionWhen(function (string $value, Get $get,Set $set) {
                                                if ($get('years') == 0 && $get('months') == 0 && $value === LeasePaymentEnum::REPEATED) {
                                                    $set('payment_type',LeasePaymentEnum::ONCE);
                                                    return true;
                                                }
                                            })
                                            ->default(LeasePaymentEnum::ONCE)
                                            ->live()
                                            ->direction('column') // Column | Row (Allows to place the Icon on top)
                                            ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                'class' => 'rounded-xl radio-deck-cards-color'
                                            ])
                                            ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                'class' => 'text-sm leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                            ])
                                            ->columns(2)
                                            ->color(Color::hex(self::RADIO_DECK_COLOR))
                                            ->afterStateUpdated(function ($state, callable $set, callable $get,$livewire) {
                                                if ($state === LeasePaymentEnum::ONCE) {
                                                    $set('payment_repeated_type', null);
                                                }else{
                                                    $set('payment_repeated_type', LeasePaymentEnum::MONTHLY);
                                                }
                                            }),
                                    ]),
                                RadioDeck::make('payment_repeated_type')
                                    ->required()
                                    // ->hiddenLabel()
                                    ->label(__('Payment Repeated'))
                                    ->options([
                                        LeasePaymentEnum::MONTHLY => __('Monthly'),
                                        LeasePaymentEnum::QUARTERLY => __('Quarterly'),
                                        LeasePaymentEnum::HALF_ANNUALLY => __('Half Annually'),
                                        LeasePaymentEnum::ANNUALLY => __('Annually'),
                                    ])
                                    ->disableOptionWhen(function (string $value, Get $get) {
                                        $disabledOptions = [];

                                        // Example condition: Disable ANNUALLY option if lease duration is less than 1 year
                                        if ($get('years') < 1 && $value === LeasePaymentEnum::ANNUALLY) {
                                            return true;
                                        }

                                        // Example condition: Disable HALF_ANNUALLY option if lease duration is less than 6 months
                                        if (($get('years') == 0 && $get('months') < 6) && $value === LeasePaymentEnum::HALF_ANNUALLY) {
                                            return true;
                                        }

                                        if (($get('years') == 0 && $get('months') < 4) && $value === LeasePaymentEnum::QUARTERLY) {
                                            return true;
                                        }

                                        return false;
                                    })
                                    ->visible(fn (Get $get): bool => $get('payment_type') === LeasePaymentEnum::REPEATED)
                                    ->direction('column') // Column | Row (Allows to place the Icon on top)
                                    ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                        'class' => 'rounded-xl radio-deck-cards-color'
                                    ])
                                    ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                        'class' => 'text-md leading-none w-full flex flex-col items-center justify-center p-2 radio-deck-options-color'
                                    ])
                                    ->color(Color::hex(self::RADIO_DECK_COLOR))
                                    ->columns(['default' => 1, 'sm' => 2, 'lg' => 4])
                                    ->afterStateHydrated(function (RadioDeck $component, $state, ?Model $record) {
                                        if ($record && $record->payment_type === LeasePaymentEnum::REPEATED) {
                                            $component->state($record->payment_repeated_type);
                                        }
                                    }),
                                ]),
                        Section::make(__('Services'))
                            ->collapsible()
                            ->collapsed()
                            ->label(__('Services'))
                            ->schema([
                                // Unit Meters heading and description
//                                Placeholder::make('unit_meters_heading')
//                                    ->label(__('Unit Meters Number'))
//                                    ->content(__('Current meter number for selected units'))
//                                    ->extraAttributes(['class' => 'text-lg font-medium'])
//                                    ->visible(fn (Get $get): bool => !empty($get('unit_meters'))),

                                // Unit Meters repeater
                                Repeater::make('unit_meters')
                                    ->label(__('Unit Meters Number'))
                                    ->schema([
                                        Hidden::make('unit_id'),

                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('electronic_meter')
                                                    ->label(__('Electronic Meter'))
                                                    ->placeholder(__('No meter data'))
                                                    ->prefixIcon('heroicon-o-bolt')
                                                    ->disabled(),

                                                TextInput::make('water_meter')
                                                    ->label(__('Water Meter'))
                                                    ->placeholder(__('No meter data'))
                                                    ->prefixIcon('heroicon-o-beaker')
                                                    ->disabled(),

                                                TextInput::make('gas_meter')
                                                    ->label(__('Gas Meter'))
                                                    ->prefixIcon('heroicon-o-fire')
                                                    ->placeholder(__('No meter data'))
                                                    ->disabled(),
                                            ]),
                                    ])
                                    ->itemLabel(function (array $state) {
                                        $unitId = $state['unit_id'] ?? null;
                                        if (!$unitId) return __('Unit');

                                        $unit = Property::find($unitId);
                                        return __('Unit') . ' ' . ($unit?->number ?? $unitId);
                                    })
                                    ->columns(1)
                                    ->collapsible(false)  // Don't make individual items collapsible
                                    ->reorderable(false)
                                    ->addable(false)
                                    ->deletable(false)
                                    ->extraAttributes(['class' => 'space-y-4'])
                                    ->visible(fn (Get $get): bool => !empty($get('unit_meters'))),

                                // Optional visual separator
                                Placeholder::make('unit_meters_separator')
                                    ->label('')
                                    ->content('')
                                    ->extraAttributes(['class' => 'border-t border-gray-200 my-4'])
                                    ->visible(fn (Get $get): bool => !empty($get('unit_meters'))),
                                Repeater::make('units_services')
                                ->relationship('leaseUnits')
                                ->hiddenLabel()
                                ->collapsed()
                                ->schema(fn (Get $get) => [
                                    Hidden::make('unit_id')
                                        ->default(fn (Get $get) => $get('lease_unit_id')),
                                    Repeater::make('services')
                                        ->relationship('leaseUnitServices')
                                        ->hiddenLabel()
                                        ->collapsed()
                                        ->schema([
                                            Hidden::make('service_id')
                                                ->default(fn (Get $get) => $get('service_id')),
                                                RadioDeck::make('to_be_paid_by')
                                                    ->required()
                                                    ->hiddenLabel()
                                                    ->live()
                                                    ->options([
                                                        ServicePaymentEnum::FIXED_FEE_TYPE => __('Fixed Value'),
                                                        ServicePaymentEnum::METERED_FEE_TYPE => __('Metered Value'),
                                                    ])
                                                    ->afterStateUpdated(function ($state, callable $set) {
                                                        if ($state === ServicePaymentEnum::FIXED_FEE_TYPE) {
                                                            $set('meter_current_reading', 0);
                                                            $set('service_type', ServiceTypeEnum::FIXED_FEE);
                                                        } else if ($state === ServicePaymentEnum::METERED_FEE_TYPE) {
                                                            $set('lease_service_amount', 0);
                                                            $set('service_type', ServiceTypeEnum::METERE_FEE);
                                                        }
                                                    })
                                                    ->direction('column')
                                                    ->extraCardsAttributes([
                                                        'class' => 'rounded-xl radio-deck-cards-color'
                                                    ])
                                                    ->extraOptionsAttributes([
                                                        'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                    ])
                                                    ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                    ->columns(['default' => 1, 'sm' => 1, 'lg' => 1, 'xl' => 2]),

                                                TextInput::make('lease_service_amount')
                                                    ->label(__('Fixed Amount'))
                                                    ->numeric()
                                                    ->required()
                                                    ->minValue(0)
                                                    ->default(0)
                                                    ->extraInputAttributes([
                                                        'min' => 0,
                                                    ])
                                                    ->readonly(fn (callable $get) => $get('to_be_paid_by') === ServicePaymentEnum::METERED_FEE_TYPE)
                                                    ->live(true)
                                                    // Conditional styling based on the value of 'to_be_paid_by'
                                                    ->extraAttributes(fn (callable $get) => [
                                                        'style' => $get('to_be_paid_by') === ServicePaymentEnum::FIXED_FEE_TYPE ? 'opacity: 1;' : 'opacity: 0.3;',
                                                    ])
                                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                        $state = $state === '' || $state === null ? 0 : $state;
                                                        $set('lease_service_amount', $state);
                                                    })->validationMessages([
                                                        'required' => __('Please enter a fixed amount.'),
                                                        'min' => __('The entered value is invalid. It must be at least 0'),
                                                    ]),

                                                TextInput::make('meter_current_reading')
                                                    ->label(__('Meter Reading'))
                                                    ->numeric()
                                                    ->required()
                                                    ->minValue(0)
                                                    ->default(0)
                                                    ->extraInputAttributes([
                                                        'min' => 0,
                                                    ])
                                                    ->readonly(fn (callable $get) => $get('to_be_paid_by') === ServicePaymentEnum::FIXED_FEE_TYPE)
                                                    ->live(true)
                                                    // Conditional styling based on the value of 'to_be_paid_by'
                                                    ->extraAttributes(fn (callable $get) => [
                                                        'style' => $get('to_be_paid_by') === ServicePaymentEnum::FIXED_FEE_TYPE ? 'opacity: 0.3;' : 'opacity: 1;',
                                                    ])
                                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                        $state = $state === '' || $state === null ? 0 : $state;
                                                        $set('meter_current_reading', $state);
                                                    })
                                                    ->validationMessages([
                                                        'required' => __('Please enter a fixed amount.'),
                                                        'numeric' => __('The value must be a number only.'),
                                                        'min' => __('The entered value is invalid. It must be at least 0'),
                                                    ]),
                                            Hidden::make('to_be_paid_amount'),
                                            Hidden::make('service_type'),
                                        ])
                                        ->grid(3)
                                        ->deletable(false)
                                        ->collapsible()
                                        ->maxItems(fn (Get $get) => count($get('services') ?? []))
                                        ->reorderable(false)
                                        ->itemLabel(fn (array $state): ?string =>
                                            Service::find($state['service_id'])?->name ?? ''
                                        ),

                                        Section::make(__('Other Services'))
                                        ->label(__('Other Services'))
                                        ->collapsible()
                                        ->collapsed()
                                        ->schema([
                                            Repeater::make('other_services')
                                            ->relationship('otherLeaseUnitServices')
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->default([])
                                            ->schema([
                                                Grid::make(2)
                                                ->schema([
                                                    Select::make('service_id')
                                                        ->label(__('Services Type'))
                                                        ->options(Service::where('is_fixed_lease',false)->where('is_fixed_commercial_lease' , false)->where('active',true)->get()->pluck('name','id'))
                                                        ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                                        ->searchable()
                                                        ->required()
                                                        ->native(false),
                                                    TextInput::make('lease_service_amount')
                                                        ->label(__('Amount'))
                                                        ->numeric()
                                                        ->required(fn (Get $get) => $get('to_be_paid_by') === ServicePaymentEnum::FIXED_FEE_TYPE)
                                                        ->minValue(0)
                                                        ->default(0)
                                                        ->extraInputAttributes([
                                                            'min' => 0,
                                                        ])
                                                        ->readOnly(function (Get $get, Set $set) {
                                                            if($get('to_be_paid_by') === ServicePaymentEnum::METERED_FEE_TYPE){
                                                                return true;
                                                            }
                                                            return false;
                                                        })
                                                        ->validationMessages([
                                                            'required' => __('Please enter a Amount.'),
                                                        ])
                                                        ->live(true)
                                                        ->afterStateUpdated(function ($state, callable $set, callable $get , $livewire) {
                                                            $state = $state === '' || $state === null ? 0 : $state;
                                                            $set('lease_service_amount', $state);
                                                        }),
                                                        Hidden::make('to_be_paid_amount')
                                                    ]),
                                                Grid::make(2)
                                                ->schema([
                                                    RadioDeck::make('to_be_paid_by')
                                                        ->required()
                                                        ->hiddenLabel()
                                                        ->options([
                                                            ServicePaymentEnum::FIXED_FEE_TYPE => __('On Lease'),
                                                            ServicePaymentEnum::METERED_FEE_TYPE => __('On Demand'),
                                                        ])
                                                        ->default(ServicePaymentEnum::FIXED_FEE_TYPE)
                                                        ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                        ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                            'class' => 'rounded-xl radio-deck-cards-color'
                                                        ])
                                                        ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                            'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                        ])
                                                        ->live()
                                                        ->afterStateUpdated(function ($livewire,$get,$set) {
                                                            $set('lease_service_amount', 0);
                                                            $state = $get('to_be_paid_by'); // Assuming you have a field for the payment type
                                                            if ($state === ServicePaymentEnum::FIXED_FEE_TYPE) {
                                                                $set('service_type', ServiceTypeEnum::ON_LEASE);
                                                            } else if ($state === ServicePaymentEnum::METERED_FEE_TYPE) {
                                                                $set('service_type', ServiceTypeEnum::ON_DEMAND);
                                                            }
                                                        })
                                                        ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                        ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                                                        Hidden::make('service_type')->default(ServiceTypeEnum::ON_LEASE),
                                                ])
                                            ])
                                            ->addActionLabel(__('Add Other Service'))
                                            ->grid(2)
                                        ])

                                ])
                                ->reorderable(false)
                                ->columns(1)
                                ->itemLabel(fn (array $state): ?string =>
                                    __('Unit ') . (Property::find($state['unit_id'])?->number ?? '')
                                )
                                ->collapsible()
                                ->deletable(false)
                                ->addable(false)

                            ]),
                        Section::make(__('Annual unit services'))
                            ->collapsible()
                            ->collapsed()
                            ->label(__('Annual unit services'))
                            ->schema([
                            Repeater::make('commercial_lease_services')
                            ->relationship('leaseServices', function ($query) {
                                $query->whereNull('name')
                                      ->whereNotNull('service_id');
                                })
                                ->label('Fixed services')
                                ->schema([
                                    Hidden::make("service_id"),
                                    TextInput::make("value")
                                        ->numeric()
                                        ->required()
                                        ->placeholder(__('Enter service value'))
                                    ->validationMessages([
                                        'required' => __('Please enter a value.'),
                                        'numeric' => __('Please enter a Number Only.'),
                                    ]),
                                ])
                                ->addable(false)
                                ->grid(2)
                                ->deletable(false)
                                ->itemLabel(fn (array $state): ?string =>
                                    Service::find($state['service_id'])?->name ?? ''
                                )
                                ->collapsible()
                                ->defaultItems(1),
                            Repeater::make('other_commercial_lease_services')
                                ->relationship('leaseServices', function ($query) {
                                    $query->whereNull('service_id')
                                          ->whereNotNull('name');
                                })
                                ->label('Other Services')
                                ->schema([
                                    TextInput::make('name.en')
                                        ->required()
                                        ->placeholder(__('Enter service name in English'))
                                        ->validationMessages([
                                        'required' => __('Please enter a name.'),
                                    ]),
                                    TextInput::make('name.ar')
                                        ->required()
                                        ->placeholder(__('Enter service name in Arabic'))
                                        ->validationMessages([
                                            'required' => __('Please enter a service name.'),
                                        ]),
                                    Hidden::make('service_id')
                                        ->default(null),
                                    TextInput::make('value')
                                        ->numeric()
                                        ->default(fn () => 0) // Default value to 0
                                        ->required()
                                        ->placeholder(__('Enter service value'))
                                        ->validationMessages([
                                            'required' => __('Please enter a  Service value.'),
                                            'numeric' => __('Please enter a Number Only.'),
                                        ]),
                                ])
                                ->addable(true)
                                ->grid(2)
                                ->deletable(true)
                                ->collapsible()
                                ->defaultItems(0) // Ensure at least one item by default
                                ->visible(fn (Get $get) => $get('lease_type') === LeaseTypesEnum::COMMERCIAL)
                                ->columns(1)
                            ])
                            ->visible(fn (Get $get) => $get('lease_type') === LeaseTypesEnum::COMMERCIAL)
                            ->columns(1),
                        ]),

                    Wizard\Step::make(__('Contract start balance'))
                    ->description(__('Manage contract start balance'))
                    ->icon('heroicon-o-currency-dollar')
                    ->completedIcon('heroicon-o-check-badge')
                    ->schema([
                        View::make('lease::forms.components.contract-start-balance')
                            ->columnSpanFull(),
                        Section::make(__('Amounts paid'))
                            ->collapsible()
                            ->label(__('Amounts paid'))
                            ->schema([
                                TextInput::make('paid_amount')
                                    ->label(__('Amounts paid'))
                                    ->numeric()
                                    ->required()
                                    ->minValue(0)
                                    ->default(0)
                                    ->live(false,700)
                                    ->hint(function (Get $get,$livewire) {
                                        $result = self::isPaidAmountValid($livewire);
                                        if($result){
                                            return __('max paid amount is ') . $result['max_paid_amount'];
                                        }
                                    })
                                    ->extraInputAttributes([
                                        'min' => 0,
                                    ])
                                    ->validationMessages([
                                        'required' => __('The Total Amount Paid is required.'),
                                        'min' => __('The Total Amount Paid Must Be Greater Than 0.'),
                                    ])
                                    ->afterStateUpdated(function ($state, callable $set, callable $get , $livewire,$component) {
                                        $state = $state === '' || $state === null ? 0 : $state;
                                        $isValidVat = self::checkValidVatAndValidType($livewire);
                                        $set('paid_amount', $state);
                                        $set('due_amount',$get('total_amount') + $get('total_services_amount') + ($isValidVat ? $get('total_amount') * config('lease.vat') : 0) - $state);
                                        $livewire->validateOnly($component->getStatePath());
                                    })
                                    ->rule(function ($livewire,$set,$get) {
                                        return function (string $attribute, $value, Closure $fail) use ($livewire,$set,$get) {
                                            $result = self::isPaidAmountValid($livewire);
                                            $isValidVat = self::checkValidVatAndValidType($livewire);
                                            if ($result && !$result['status']) {
                                                $set('due_amount',$get('total_amount') + $get('total_services_amount') + ($isValidVat ? $get('total_amount') * config('lease.vat') : 0));
                                                $fail(__('max paid amount is ').$result['max_paid_amount']);
                                            }
                                        };
                                    })
                            ]),
                        Section::make(__('Invoices'))
                            ->description(__('Invoices that will be scheduled for the coming period'))
                            ->collapsible()
                            ->label(__('Invoices'))
                            ->schema([
                                Grid::make(3)
                                    ->schema([
                                        TextInput::make('due_amount')
                                            ->label(__('Total remaining contract value to be scheduled'))
                                            ->numeric()
                                            ->required()
                                            ->readOnly()
                                            ->minValue(0)
                                            ->live(true)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->afterStateUpdated(function($get,$set,$state,$livewire){
                                                $state = ($livewire->data['total_amount'] + ($livewire->data['total_amount'] * config('lease.vat'))) - $livewire->data['paid_amount'];
                                                $set('due_amount', $state);
                                            }),
                                        DatePicker::make('start_scheduled_date')
                                            ->label(__('Start Schedule Date'))
                                            ->required()
                                            ->readOnly()
                                            ->live(true)
                                            ->afterStateUpdated(fn ($livewire) => self::getStartScheduleDate($livewire)),
                                        TextInput::make('days_permitted')
                                            ->label(__('Days Permitted'))
                                            ->numeric()
                                            ->required()
                                            ->extraInputAttributes([
                                                'min' => 0,
                                                'max' => 30,
                                            ])
                                            ->minValue(0)
                                            ->maxValue(30)
                                            ->default(0)
                                            ->live(false,700)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state > 30 ? 30 : $state;
                                                $set('days_permitted', $state);
                                            })
                                            ->validationMessages([
                                                'required' => __('The Days Permitted is required.'),
                                                'min' => __('The Days Permitted Must Be Greater Than 0'),
                                            ]),

                                    ])
                            ])
                    ]),


                    Wizard\Step::make(__('Installment payments'))
                    ->description(__('show installment payments'))
                    ->icon('heroicon-o-document-text')
                    ->completedIcon('heroicon-o-check-badge')
                    ->schema([
                        Section::make(__('Rent payment channel'))
                            ->description(__('The tenant is required to pay the rent electronically through the electronic payment channels available in the Kera network'))
                            ->schema([
                                RadioDeck::make('payment_method')
                                    ->label(__('choose payment method'))
                                    ->required()
                                    ->validationAttribute(__('Commission Paid By'))
                                    ->options([
                                        PaymentMethodEnum::ONLINE => __('Electronic Payment Only'),
                                        PaymentMethodEnum::OFFLINE => __('Electronic Payment or Receipt Issuance through Landlord/Real Estate Agent'),
                                    ])
                                    ->live()
                                    ->default(PaymentMethodEnum::ONLINE)
                                    ->direction('column')
                                    ->extraCardsAttributes([
                                        'class' => 'rounded-xl radio-deck-cards-color'
                                    ])
                                    ->extraOptionsAttributes([
                                        'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                    ])
                                    ->color(Color::hex(self::RADIO_DECK_COLOR))
                                    ->columnSpanFull()
                                    ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                                view::make('lease::forms.components.payment-channel'),
                                  Select::make('bank_account_id')
                                      ->label(__('Bank Account'))
                                      ->placeholder(__('Select bank account'))
                                      ->prefixIcon('heroicon-o-credit-card')
                                      ->allowHtml()
                                      ->options(function (Get $get, $livewire) {
                                          $owners = $livewire->data['owners'] ?? [];
                                          $memberIds = array_column($owners, 'member_id');

                                          // Use the correct polymorphic relationship columns
                                          return BankAccount::query()
                                              ->where('is_active', true)
                                              ->whereIn('bankable_id', $memberIds)
                                              ->where('bankable_type', Account::class)
                                              ->get()
                                              ->mapWithKeys(function ($bankAccount) {
                                                  return [
                                                      $bankAccount->id => static::getOptionView($bankAccount),
                                                  ];
                                              });
                                      })
                                      ->searchable()
                                      ->required(),
                            ]),
                        Section::make(__('Installment payments'))
                            ->label(__('Installment payments'))
                            ->collapsible()
                            ->schema([
                                View::make('lease::forms.components.installment-payments')

                            ]),
                        Section::make(__('Installment payments table'))
                            ->label(__('Installment payments table'))
                            ->collapsible()
                            ->schema([
                                View::make('lease::forms.components.installment-payments-table')
                            ])
                    ]),
                    Wizard\Step::make(__('Additional Amounts'))
                    ->description(__('Manage additional amounts'))
                    ->icon('heroicon-o-credit-card')
                    ->completedIcon('heroicon-o-check-badge')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Section::make(__('Engineering supervision'))
                                    ->label(__('Engineering supervision'))
                                    ->collapsible()
                                    ->relationship('commercial_meta_data')
                                    ->collapsed(function (?Model $record) {
                                        return !$record?->commercial_meta_data?->engineering_supervision > 0;
                                    })
                                    ->schema([
                                        Toggle::make('engineering_supervision_toggle')
                                            ->label(__('Engineering supervision'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if (!$state) {
                                                    $set('engineering_supervision', 0);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set, ?Model $record) {
                                                if ($record?->commercial_meta_data && $record?->commercial_meta_data->engineering_supervision > 0) {
                                                    $set('engineering_supervision_toggle', true);
                                                }
                                            }),
                                        TextInput::make('engineering_supervision')
                                            ->label(__('Engineering supervision'))
                                            ->numeric()
                                            ->required(fn (Get $get) => $get('engineering_supervision_toggle'))
                                            ->minValue(0)
                                            ->default(0)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('engineering_supervision', $state);
                                            }),
                                    ])
                                    ->columnSpan(1)
                                    ->visible(fn (Get $get) => $get('lease_type') === LeaseTypesEnum::COMMERCIAL),


                                Section::make(__('Unit finishing'))
                                    ->label(__('Unit finishing'))
                                    ->collapsible()
                                    ->relationship('commercial_meta_data')
                                    ->collapsed(function (?Model $record) {
                                        return !$record?->unit_finishing > 0;
                                    })
                                    ->schema([
                                        Toggle::make('unit_finishing_toggle')
                                            ->label(__('Unit finishing'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if(!$state){
                                                    $set('unit_finishing', 0);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set, ?Model $record) {
                                                if ($record?->unit_finishing > 0) {
                                                    $set('unit_finishing_toggle', true);
                                                }
                                            }),
                                        TextInput::make('unit_finishing')
                                            ->label(__('Unit finishing'))
                                            ->numeric()
                                            ->required(fn (Get $get) => $get('unit_finishing_toggle'))
                                            ->minValue(0)
                                            ->default(0)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('unit_finishing', $state);
                                            })
                                    ])->columnSpan(1)
                                    ->visible(fn (Get $get) => $get('lease_type') === LeaseTypesEnum::COMMERCIAL),

                                Section::make(__('Waste removal'))
                                    ->label(__('Waste removal'))
                                    ->collapsible()
                                    ->relationship('commercial_meta_data')
                                    ->collapsed(function (?Model $record) {
                                        return !$record?->waste_removal > 0;
                                    })
                                    ->schema([
                                        Toggle::make('waste_removal_toggle')
                                            ->label(__('Waste removal'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if(!$state){
                                                    $set('waste_removal', 0);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set, ?Model $record) {
                                                if ($record?->waste_removal > 0) {
                                                    $set('waste_removal_toggle', true);
                                                }
                                            }),
                                        TextInput::make('waste_removal')
                                            ->label(__('Waste removal'))
                                            ->numeric()
                                            ->required(fn (Get $get) => $get('waste_removal_toggle'))
                                            ->minValue(0)
                                            ->default(0)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('waste_removal', $state);
                                            })
                                    ])->columnSpan(1)
                                    ->visible(fn (Get $get) => $get('lease_type') === LeaseTypesEnum::COMMERCIAL),
                                Section::make(__('Insurance Amount'))
                                    ->label(__('Insurance Amount'))
                                    ->collapsible()
                                    ->collapsed(function (?Model $record) {
                                        return !$record?->insurance_amount > 0;
                                    })

                                    ->schema([
                                        Toggle::make('insurance_amount_toggle')
                                            ->label(__('Insurance Amount'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if(!$state){
                                                    $set('insurance_amount', 0);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set,?Model $record) {
                                                if ($record?->insurance_amount > 0) {
                                                    $set('insurance_amount_toggle', true);
                                                }
                                            }),
                                        TextInput::make('insurance_amount')
                                            ->label(__('Insurance Amount'))
                                            ->numeric()
                                            ->required(fn (Get $get) => $get('insurance_amount_toggle'))
                                            ->minValue(0)
                                            ->default(0)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('insurance_amount', $state);
                                            })
                                            ->validationMessages([
                                                'min' => __('The Insurance Amount Must Be Greater Than 0.'),
                                            ])
                                            ->visible(fn (Get $get) => $get('insurance_amount_toggle'))
                                    ])->columnSpan(1),
                                Section::make(__('Daily Penalty'))
                                    ->label(__('Daily Penalty'))
                                    ->collapsible()
                                    ->collapsed(function (?Model $record) {
                                        return !$record?->daily_penalty > 0;
                                    })
                                    ->schema([
                                        Toggle::make('daily_penalty_toggle')
                                            ->label(__('Daily Penalty'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if(!$state){
                                                    $set('daily_penalty', 0);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set,?Model $record) {
                                                if ($record?->daily_penalty > 0) {
                                                    $set('daily_penalty_toggle', true);
                                                }
                                            }),

                                        TextInput::make('daily_penalty')
                                            ->label(__('Daily Penalty'))
                                            ->numeric()
                                            ->integer()
                                            ->required(fn (Get $get) => $get('daily_penalty_toggle'))
                                            ->minValue(0)
                                            ->default(0)
                                            ->extraInputAttributes([
                                                'min' => 0,
                                            ])
                                            ->live(true)
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $state = $state === '' || $state === null ? 0 : $state;
                                                $set('daily_penalty', $state);
                                            })
                                            ->validationMessages([
                                                'min' => __('The Daily Penalty Must Be Greater Than 0.'),
                                            ])
                                            ->visible(fn (Get $get) => $get('daily_penalty_toggle'))
                                    ])->columnSpan(1),
                                Section::make(__('Commission'))
                                    ->label(__('Commission'))
                                    ->collapsible()
                                    ->collapsed()
                                    ->schema([
                                        Toggle::make('commission_toggle')
                                            ->label(__('Commission'))
                                            ->live()
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                if(!$state){
                                                    $set('commission.commission_percentage', 0);
                                                    $set('commission.commission_amount', 0);
                                                    $set('commission.commission_due_date',null);
                                                }
                                            })
                                            ->afterStateHydrated(function (callable $set,?Model $record) {
                                                if ($record?->commission) {
                                                    $set('commission_toggle', true);
                                                }
                                            }),
                                        Fieldset::make('commission_data')
                                            ->relationship('commission')
                                            ->label(__('Commission Data'))
                                            ->schema([
                                                Grid::make(2)
                                                    ->schema([
                                                        RadioDeck::make('commission_paid_by')
                                                            ->label(__('Commission Paid By'))
                                                            ->required()
                                                            ->rules(['required'])
                                                            ->validationAttribute(__('Commission Paid By'))
                                                            ->options(function (Get $get, $livewire) {
                                                                $owners = $livewire->data['owners'] ?? [];
                                                                $lessorId = null;
                                                                if (count($owners) === 1) {
                                                                    $lessorId = $owners[0]['member_id'];
                                                                } else {
                                                                    foreach ($owners as $owner) {
                                                                        if ($owner['is_representer']) {
                                                                            $lessorId = $owner['member_id'];
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                                $tenantId = $livewire->data['tenant']['member_id'];

                                                                return [
                                                                    $tenantId => __('Tenant'),
                                                                    $lessorId => __('Lessor'),
                                                                ];
                                                            })
                                                            ->direction('column')
                                                            ->extraCardsAttributes([
                                                                'class' => 'rounded-xl radio-deck-cards-color'
                                                            ])
                                                            ->extraOptionsAttributes([
                                                                'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                            ])
                                                            ->afterStateUpdated(function ($livewire,$get,$set) {
                                                                $set('lease_service_amount', 0);
                                                            })
                                                            ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                            ->columnSpanFull()
                                                            ->live()
                                                            ->afterStateUpdated(function ($livewire,$get,$set) {
                                                                $set('commission_amount', 0);
                                                                $set('commission_percentage', 0);
                                                            })
                                                            ->validationMessages([
                                                                'required' => __('You must select a commission paid')
                                                            ])
                                                            ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),

                                                        RadioDeck::make('commission_type')
                                                            ->label(__('Commission Type'))
                                                            ->required()
                                                            ->rules(['required'])
                                                            ->validationAttribute(__('Commission Type'))
                                                            ->options([
                                                                LeaseCommissionTypesEnum::FIXED => __('Fixed'),
                                                                LeaseCommissionTypesEnum::PERCENTAGE => __('Percentage'),
                                                            ])
                                                            ->default(LeaseCommissionTypesEnum::FIXED)
                                                            ->direction('column')
                                                            ->extraCardsAttributes([
                                                                'class' => 'rounded-xl radio-deck-cards-color'
                                                            ])
                                                            ->extraOptionsAttributes([
                                                                'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                            ])
                                                            ->afterStateUpdated(function ($livewire,$get,$set) {
                                                                $set('lease_service_amount', 0);
                                                            })
                                                            ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                            ->columnSpanFull()
                                                            ->live()
                                                            ->afterStateUpdated(function ($livewire,$get,$set) {
                                                                $set('commission_amount', 0);
                                                                $set('commission_percentage', 0);
                                                            })
                                                            ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),

                                                        TextInput::make('commission_percentage')
                                                            ->label(__('Commission Percentage'))
                                                            ->numeric()
                                                            ->required()
                                                            ->rules([
                                                                'required_if:commission_type,'.LeaseCommissionTypesEnum::PERCENTAGE,
                                                                'numeric',
                                                                'min:0',
                                                                'max:2.5',
                                                                function () {
                                                                    return function (string $attribute, $value, Closure $fail) {
                                                                        if ($value > 2.5) {
                                                                            $fail(__('The commission percentage cannot exceed 2.5%'));
                                                                        }
                                                                    };
                                                                }
                                                            ])
                                                            ->validationAttribute(__('Commission Percentage'))
                                                            ->minValue(0)
                                                            ->maxValue(2.5)
                                                            ->default(0)
                                                            ->extraInputAttributes([
                                                                'min' => 0,
                                                                'max' => 2.5,
                                                                'step' => '0.01'
                                                            ])
                                                            ->live(false,1000)
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get,$livewire) {
                                                                $state = $state > 2.5 ? 2.5 : $state;
                                                                $set('commission_percentage', $state);
                                                                $set('commission_amount', ($state/100)*$livewire->data['rent_amount']);
                                                            })
                                                            ->visible(fn (Get $get) => $get('commission_type') === LeaseCommissionTypesEnum::PERCENTAGE),

                                                        TextInput::make('commission_amount')
                                                            ->label(__('Commission'))
                                                            ->numeric()
                                                            ->required()
                                                            ->rules([
                                                                'required_if:commission_type,'.LeaseCommissionTypesEnum::FIXED,
                                                                'numeric',
                                                                'min:0',
                                                                function (Get $get,$livewire) {
                                                                    return function (string $attribute, $value, Closure $fail) use ($get,$livewire) {
                                                                        if ($get('commission_type') === LeaseCommissionTypesEnum::FIXED) {
                                                                            $rentAmount = $livewire->data['rent_amount'];
                                                                            $maxCommission = $rentAmount * 0.025; // 2.5%
                                                                            if ($value > $maxCommission) {
                                                                                $fail(__('The commission amount cannot exceed :max SAR (2.5% of rent amount)', ['max' => number_format($maxCommission, 2)]));
                                                                            }
                                                                        }
                                                                    };
                                                                }
                                                            ])
                                                            ->validationAttribute(__('Commission Amount'))
                                                            ->minValue(0)
                                                            ->default(0)
                                                            ->readOnly(fn (Get $get) => $get('commission_type') === LeaseCommissionTypesEnum::PERCENTAGE)
                                                            ->extraInputAttributes([
                                                                'min' => 0,
                                                            ])
                                                            ->live(true)
                                                            ->afterStateUpdated(function ($state, callable $set, callable $get,$livewire) {
                                                                $state = $state === '' || $state === null ? 0 : $state;
                                                                $set('commission_amount', $state);
                                                            }),

                                                        HijriDatePicker::make('commission_due_date')
                                                            ->label(__('Commission Due Date'))
                                                            ->placeholder(__('Select commission due date'))
                                                            ->required()
                                                            ->rules([
                                                                'required',
                                                                'date',
                                                                'after_or_equal:today'
                                                            ])
                                                            ->showConvertedDate()
                                                            ->validationAttribute(__('Commission Due Date'))
                                                            ->live(true)
                                                            ->validationMessages([
                                                                'required' => __('The Commission Due Date is required'),
                                                                'after_or_equal' => __('End commission due date must be after or equal to Start Date.')
                                                            ])
                                                    ])
                                            ])->visible(fn (Get $get) => $get('commission_toggle'))
                                    ])
                                    ->columnSpan(2),
                            ]),


                    ]),
                    LeaseWizardStep::TermsConditionsStep(),
                ])
                ->submitAction(
                    new HtmlString(
                        Blade::render(
                            <<<BLADE
                                <x-filament::button
                                    type="submit"
                                    size="sm"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="opacity-70 cursor-wait"
                                    x-data

                                    wire:loading.remove
                                    wire:loading.hidden
                                >
                                <span wire:loading.remove>{{__('Submit')}}</span>
                                <span wire:loading>{{__('Submitting...')}}</span>
                                </x-filament::button>
                            BLADE
                        )
                    )
                )
                ->nextAction(
                function (Forms\Components\Actions\Action $action, Forms\Get $get, Forms\Set $set, Wizard $wizard,$livewire) {
                        if ($wizard->getCurrentStepIndex() == 4) {
                            self::updateDueAmount($livewire);
                            self::getStartScheduleDate($livewire);
                        }
                    }
                )

                ->columnSpanFull()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('lease_number')
                    ->label(__('Ejar Number'))
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(function ($record) {
                        return $record->lease_number ?? __('-');
                    }),
                Tables\Columns\TextColumn::make('external_lease_id')
                    ->label(__('External Number'))
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(function ($record) {
                        return $record->external_lease_id ?? __('-');
                    }),
                Tables\Columns\TextColumn::make('property.name')
                    ->label(__('Property'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tenant.member.first_name')
                    ->label(__('Tenant'))
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('leaseMembers', function (Builder $q) use ($search) {
                            $q->where('member_role', 'tenant')
                                ->whereHasMorph('member', [
                                    \Modules\Account\app\Models\Account::class
                                ], function (Builder $query) use ($search) {
                                    $query->where('first_name', 'like', "%{$search}%");
                                });
                        });
                    })
                    ->sortable(),

                // For leaseUnits.unit.number column
                Tables\Columns\TextColumn::make('leaseUnits.unit.number')
                    ->label(__('Units'))
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('leaseUnits.unit', function (Builder $query) use ($search) {
                            $query->where('number', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),
                    Tables\Columns\TextColumn::make('start_date')
                    ->label(__('Start Date'))
                    ->dateTime('d-m-Y')  // Use 'd-m-Y' to display the date in dd-mm-yyyy format
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label(__('End Date'))
                    ->dateTime('d-m-Y')  // Use 'd-m-Y' to display the date in dd-mm-yyyy format
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (string $state): string => LeaseEnum::getColor($state))
                    ->formatStateUsing(fn (string $state): string => LeaseEnum::trans($state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('lease_type')
                    ->label(__('lease type'))
                    ->sortable()
                    ->formatStateUsing(fn (string $state): string => LeaseTypesEnum::trans($state))
                    ->searchable(),
                Tables\Columns\TextColumn::make('rent_amount')
                    ->label(__('Rent Amount'))
                    ->formatStateUsing(function ($state) {
                        return
                        new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->sortable(),
                IconColumn::make('EJAR_registration_status')
                    ->label(__('EJAR Registration Status'))
                    ->boolean()
                    ->sortable(),
                IconColumn::make('auto_renewal')
                    ->label(__('Auto Renewal Status'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle') // Right mark
                    ->falseIcon('heroicon-o-x-circle')    // Wrong mark
                    ->getStateUsing(fn ($record) => $record->auto_renewal === 'on'), // Map "on" to true
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
                //status filter
                SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(LeaseEnum::getFilterOptions())
                    ->native(false)
                    ->searchable(),
                //ejar registration status filter
                SelectFilter::make('EJAR_registration_status')
                    ->label(__('EJAR Registration Status'))
                    ->options([
                        true => __('Approved'),
                        false => __('Rejected'),
                    ])
                    ->native(false)
                    ->searchable(),
                SelectFilter::make('auto_renewal')
                    ->label(__('Auto Renewal'))
                    ->options(LeaseAutoRenewalEnum::labels())
                    ->native(false)
                    ->searchable(),
                //start date filter
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label(__('Start Date'))
                            ->native(false)
                            ->closeOnDateSelection(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['start_date'],
                            fn (Builder $query, $date): Builder => $query->whereDate('start_date', '=', $date)
                        );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['start_date']) {
                            return null;
                        }

                        return __('Start Date') . ': ' . Carbon::parse($data['start_date'])->format('Y-m-d');
                    }),
                // End date filter
                Filter::make('end_date')
                    ->form([
                        DatePicker::make('end_date')
                            ->label(__('End Date'))
                            ->native(false)
                            ->closeOnDateSelection(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['end_date'],
                            fn (Builder $query, $date): Builder => $query->whereDate('end_date', '=', $date)
                        );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['end_date']) {
                            return null;
                        }

                        return __('End Date') . ': ' . Carbon::parse($data['end_date'])->format('Y-m-d');
                    }),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT),
                    PublishLeaseTableAction::make(),
                    Tables\Actions\Action::make(__('terminate'))->color('danger')->icon('heroicon-o-x-circle')
                        ->visible(fn (Lease $record): bool =>
                            in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE, LeaseEnum::Renewed])
                            && $record->auto_renewal !== LeaseAutoRenewalEnum::PENDING)
                        ->url(fn (Lease $record): string => static::getUrl('terminate', ['record' => $record])),
                    Tables\Actions\Action::make(__('close'))
                        ->color('danger')
                        ->icon('heroicon-o-x-circle')
                        ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::ENDED)
                        ->url(fn (Lease $record): string => static::getUrl('close', ['record' => $record])),
                    Tables\Actions\Action::make(__('Auto Renew Request'))
                        ->color('danger')
                        ->icon('heroicon-o-x-circle')
                        ->visible(fn (Lease $record): bool => $record->auto_renewal === LeaseAutoRenewalEnum::OFF
                            && in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE, LeaseEnum::Renewed]))                    ->requiresConfirmation()
                        ->modalHeading(__('Auto Renew Request'))
                        ->modalDescription(fn (Lease $record): string => LeaseService::getAutoRenewalRequestModal($record))
                        ->modalSubmitActionLabel(__('Yes, proceed'))
                        ->modalCancelActionLabel(__('Cancel'))
                        ->action(function (Lease $record) {
                            try {
                                $service = new RequestService();
                                $service->handleAutoRenewalRequest($record);

                                Notification::make()
                                    ->title(__('Auto renewal request sent successfully'))
                                    ->success()
                                    ->send();

                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title(__('Error'))
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),
                    Tables\Actions\Action::make(__('Cancel Auto Renew'))
                        ->color('danger')
                        ->icon('heroicon-o-x-circle')
                        ->visible(fn (Lease $record): bool =>
                            $record->auto_renewal === LeaseAutoRenewalEnum::ON
                            && in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE])
                            && Carbon::parse($record->end_date)
                                ->subDays(LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD))
                                ->format('Y-m-d') > now()->format('Y-m-d') // Ensure correct date format comparison
                        )
                        ->requiresConfirmation()
                        ->modalHeading(__('Cancel Auto Renew'))
                        ->modalDescription(fn (Lease $record): string => LeaseService::getCancelAutoRenewalModal($record))
                        ->modalSubmitActionLabel(__('Yes, proceed'))
                        ->modalCancelActionLabel(__('Cancel'))
                        ->action(function (Lease $record) {
                            try {
                                LeaseService::cancelAutoRenewal($record->id);

                                Notification::make()
                                    ->title(__('Auto renewal cancellation sent successfully'))
                                    ->success()
                                    ->send();

                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title(__('Error'))
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),
                    Tables\Actions\Action::make('contract')
                        ->label(__('Contract'))
                        ->icon('heroicon-o-document-text')
                        ->url(fn (Lease $record): string => LeaseResource::getUrl('contract', ['record' => $record]))
                        ->openUrlInNewTab(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

//    public static function getRelations(): array
//    {
//        return [
//            //
//        ];
//    }

    public static function getPages(): array
    {
        return [
            'index' => ListLeases::route('/'),
            'create' => CreateLease::route('/create'),
            'edit' => EditLease::route('/{record}/edit'),
            'view' => ViewLease::route('/{record}'),
            'terminate' => \Modules\Lease\app\Filament\Resources\LeaseResource\Pages\TerminateLease::route('/{record}/terminate'),
            'close' => \Modules\Lease\app\Filament\Resources\LeaseResource\Pages\CloseLease::route('/{record}/close'),
            'contract' => LeaseContract::route('/{record}/contract'),

        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfolistView::make('lease::view.leaseDetails')
                    ->viewData(['record' => $infolist->record,'leaseFutureSchedule' => static::getLeaseScheduleService($infolist->record->id)->getLeaseFutureSchedule()])
                    ->columnSpanFull(),
                CustomInfoTabs::make('Lease Information')
                    ->tabs([
                        Tab::make(__('Property and Units'))
                            ->icon('heroicon-o-building-office')
                            ->schema([
                                InfolistView::make('lease::view.leaseUnits')
                                    ->viewData(['units' => $infolist->record->viewUnits,'property'=> $infolist->record->property]),
                            ]),
                        Tab::make(__('Stackholder'))
                            ->icon('heroicon-o-user-group')
                            ->schema([
                                InfolistSection::make(__('Owners'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistView::make('lease::view.leaseMembers')
                                            ->viewData(['members' => static::processLeaseMembers($infolist->record->ViewMembers),'record' => $infolist->record]),
                                    ]),
                                InfolistSection::make(__('Tenant'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('tenant.member.name')
                                                    ->label(__('Tenant'))
                                                    ->visible(fn ($record) => !$record->tenant?->is_organization),
                                                TextEntry::make('tenant.member.national_id')
                                                    ->label(__('National ID'))
                                                    ->visible(fn ($record) => !$record->tenant?->is_organization),
                                                TextEntry::make('tenant.member.name')
                                                    ->label(__('Organization Name'))
                                                    ->visible(fn ($record) => $record->tenant?->is_organization),
                                                TextEntry::make('tenant.member.unified_number')
                                                    ->label(__('Unified Number'))
                                                    ->visible(fn ($record) => $record->tenant?->is_organization),
                                                TextEntry::make('tenant.member.ownership_document_number')
                                                    ->label(__('Ownership Document Number'))
                                                    ->visible(fn ($record) => $record->tenant?->is_organization),
                                            ]),
                                    ]),
                                InfolistSection::make(__('Representative'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('tenantRepresenter.member.name')
                                                    ->label(__('Representative')),
                                                TextEntry::make('tenantRepresenter.member.national_id')
                                                    ->label(__('National ID')),
                                            ]),
                                    ])
                                    ->visible(fn ($record) => $record->tenantRepresenter !== null),
                            ]),
                        Tab::make(__('Financial data'))
                            ->icon('heroicon-o-banknotes')
                            ->schema([
                                InfolistSection::make(__('Financial data'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('rent_amount')
                                                    ->label(__('Rent Amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                                TextEntry::make('retainer_amount')
                                                    ->label(__('Retainer Amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return $state .' '. __('SAR');
                                                    }),
                                                TextEntry::make('total_amount')
                                                    ->label(__('Total Amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                                TextEntry::make('total_services_amount')
                                                    ->label(__('Total Service amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                                TextEntry::make('payment_type')
                                                    ->label(__('Repeat Payment'))
                                                    ->badge()
                                                    ->formatStateUsing(fn (string $state): string => LeasePaymentEnum::getPaymentTypeLabel($state))
                                                    ->color(fn (string $state): string => match ($state) {
                                                        LeasePaymentEnum::ONCE => 'success',
                                                        LeasePaymentEnum::REPEATED => 'warning',
                                                        default => 'gray',
                                                    }),
                                                TextEntry::make('payment_repeated_type')
                                                    ->label(__('Payment Repeated'))
                                                    ->formatStateUsing(fn (string $state): string => LeasePaymentEnum::getRepeatedLabel($state))
                                                    ->badge()
                                                    ->visible(fn ($record) => $record->payment_type === LeasePaymentEnum::REPEATED),
                                                TextEntry::make('days_permitted')
                                                    ->label(__('Days Permitted'))
                                                    ->badge(),
                                            ]),
                                    ]),

                                    InfolistSection::make(__('Insurance data'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('insurance_amount')
                                                    ->label(__('Insurance Amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                                TextEntry::make('daily_penalty')
                                                    ->label(__('Daily Penalty'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                            ]),
                                    ]),


                                    InfolistSection::make(__('Commission Data'))
                                    ->collapsed()
                                    ->schema([
                                        InfolistGrid::make(['default' => 1, 'sm' => 2])
                                            ->schema([
                                                TextEntry::make('commission.account.name')
                                                    ->label(__('Commission Paid By')),
                                                TextEntry::make('commission.commission_type')
                                                    ->label(__('Commission Type'))
                                                    ->badge()
                                                    ->formatStateUsing(fn (string $state): string => LeaseCommissionTypesEnum::getLabel($state)),
                                                TextEntry::make('commission.commission_amount')
                                                    ->label(__('Commission Amount'))
                                                    ->formatStateUsing(function ($state) {
                                                        return new HtmlString(
                                                            '<div class="">' .
                                                            number_format($state) .
                                                            ' <span class="icon-saudi_riyal"></span>' .
                                                            '</div>'
                                                       );
                                                    }),
                                                TextEntry::make('commission.commission_percentage')
                                                    ->label(__('Commission Percentage'))
                                                    ->formatStateUsing(fn ($state) => $state . '%'),
                                            ]),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    protected static function processLeaseMembers($members)
    {
        $hasOrganization = collect($members)->contains('is_organization', 1);

        if ($hasOrganization) {
            return collect($members)->values()->all();
        }
        return collect($members)
            ->groupBy('member_id')
            ->map(function ($group) {
                if ($group->count() > 1) {
                    $representer = $group->firstWhere('member_role', 'lessor_representer');
                    $lessor = $group->firstWhere('member_role', 'lessor');
                    if ($representer && $lessor) {
                        $representer->percentage = $lessor->percentage;
                        return $representer;
                    }
                }
                return $group->first();
            })
            ->values()
            ->all();
    }


    protected static function updateEndDate($state, callable $set, callable $get)
    {
        $startDate = $get('start_date');
        if ($get('payment_type') === LeasePaymentEnum::REPEATED) {
            $set('payment_repeated_type', LeasePaymentEnum::MONTHLY);
        }

        if (!$startDate) return;

        $days = (int) $get('days');
        $months = (int) $get('months');
        $years = (int) $get('years');

        if ($days > 0 || $months > 0 || $years > 0) {
            $endDate = new Carbon($startDate);
            $endDate->modify("+{$years} years +{$months} months +{$days} days -1 day");

            $set('end_date', $endDate->format('Y-m-d'));

            // Check if the end date is in the past
            if ($endDate->isPast()) {
                $set('auto_renewal', false);
            }
        }
    }

    protected static function updateDurationInputs($state, callable $set, callable $get)
    {
        $startDate = $get('start_date');
        $endDate = $get('end_date');
        if($get('payment_type') === LeasePaymentEnum::REPEATED){
            $set('payment_repeated_type',LeasePaymentEnum::MONTHLY);
        }
        if (!$startDate || !$endDate) return;

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->startOfDay();

        // Add one day to end date for calculation since we always subtract one day
        $end->modify('+1 day');

        $interval = $start->diff($end);

        $set('years', $interval->y);
        $set('months', $interval->m);
        $set('days', $interval->d);
    }

    protected static function propertySelect($state, callable $set, callable $get)
    {
        $set('unit_ids', []);

        if (!$state) {
            $set('owners', []);
            return;
        }

        $owners = Property::find($state)->owners()->with('ownerable')->get();
        $hasVatNumber = $owners->contains(function ($owner) {
            $ownerable = $owner->ownerable;
            $vatNumber = $ownerable->vat_number ?? 0;
            return ($vatNumber != 0 && !empty($vatNumber));
        });

        $set('has_vat_number', $hasVatNumber);

        $ownersData = $owners->sortBy('is_representer')->map(function ($owner) {
            $isOrganization = $owner->ownerable_type === Organization::class;
            return [
                'name' => $owner->ownerable->name,
                'member_id' => $owner->ownerable->id,
                'percentage' => $owner->percentage,
                'national_id' => $isOrganization ? null : $owner->ownerable->national_id,
                'member_role' => $owner->percentage > 0 ? LeaseMemberTypesEnum::LESSOR : LeaseMemberTypesEnum::LESSOR_REPRESENTER,
                'is_representer' => $owner->is_representer,
                'is_organization' => $isOrganization,
                'unified_number' => $isOrganization ? $owner->ownerable->unified_number : null,
                'ownership_document_number' => $isOrganization ? $owner->ownerable->ownership_document_number : null,
                'member_type' => $isOrganization ? LeaseMemberTypesEnum::ORGANIZATION_TYPE : LeaseMemberTypesEnum::INDIVIDUAL_TYPE,
                'memberable_type' => $isOrganization? Organization::class : Account::class,
                'vat_number' =>$owner->ownerable->vat_number
            ];
        })->toArray();

        $set('owners', $ownersData);
    }

    protected static function propertySelectHydrated($state, callable $set, callable $get,?Model $record)
    {

        if (!$get('property_id')) {
            $set('owners', []);
            return;
        }

        $owners = Property::find($get('property_id'))->owners()->with('ownerable')->get();
        $hasVatNumber = $owners->contains(function ($owner) {
            $ownerable = $owner->ownerable;
            $vatNumber = $ownerable->vat_number ?? 0;
            return ($vatNumber != 0 && !empty($vatNumber));
        });
        $set('has_vat_number', $hasVatNumber);

        $ownersData = $owners->sortBy('is_representer')->map(function ($owner) use ($record) {
            $memberRecord = $record?->members
                ->where('member_id', $owner->ownerable->id)
                ->when($owner->is_representer, function ($query) {
                    // If is_representer, get the record with percentage = 0
                    return $query->where('percentage', 0);
                })
                ->first();

                $isOrganization = $owner->ownerable_type === Organization::class;
            return [
                'name' => $owner->ownerable->name,
                'member_id' => $owner->ownerable->id,
                'percentage' => $owner->percentage,
                'national_id' => $isOrganization ? null : $owner->ownerable->national_id,
                'member_role' => $owner->percentage > 0 ? LeaseMemberTypesEnum::LESSOR : LeaseMemberTypesEnum::LESSOR_REPRESENTER,
                'is_representer' => $owner->is_representer,
                'bank_account_id' => $memberRecord?->bank_account_id,
                'is_organization' => $isOrganization,
                'unified_number' => $isOrganization ? $owner->ownerable->unified_number : null,
                'ownership_document_number' => $isOrganization ? $owner->ownerable->ownership_document_number : null,
                'member_type' => $isOrganization ? LeaseMemberTypesEnum::ORGANIZATION_TYPE : LeaseMemberTypesEnum::INDIVIDUAL_TYPE,
                'memberable_type' => $isOrganization? Organization::class : Account::class,
            ];
        })->toArray();

        $set('owners', $ownersData);
    }

    protected static function getFixedService(Get $get, Set $set, $livewire)
    {
        $unitIds = $get('unit_ids') ?? [];
        $existingUnitServices = $get('units_services') ?? [];
        $fixedServices = Service::where('is_fixed_lease', true)->where('active', true)->get();
        $existingUnitServicesMap = collect($existingUnitServices)->keyBy('unit_id')->toArray();
        $unitServices = [];
        foreach ($unitIds as $unitId) {
            // If unit already exists, keep its existing services
            if (isset($existingUnitServicesMap[$unitId])) {
                $unitServices[] = $existingUnitServicesMap[$unitId];
            } else {
                $unitServices[] = [
                    'unit_id' => $unitId,
                    'services' => $fixedServices->map(function ($service) {
                        return [
                            'service_id' => $service->id,
                            'lease_service_amount' => 0,
                            'to_be_paid_by' => ServicePaymentEnum::FIXED_FEE_TYPE,
                            'service_type' => ServiceTypeEnum::FIXED_FEE,
                            'meter_current_reading' => 0,
                        ];
                    })->toArray(),
                ];
            }
        }
        $set('units_services', $unitServices);
    }

    public static function getPropertyMeterData($unitId)
    {
        return PropertyMeter::where('property_id', $unitId)->first();
    }

    protected static function getUnitsMeters(Get $get, Set $set, $livewire)
    {
        $set('unit_meters', []);

        // Get selected unit IDs
        $selectedUnitIds = $get('unit_ids');

        if (!empty($selectedUnitIds)) {
            $meterData = [];

            // For each selected unit, retrieve its meter data
            foreach ($selectedUnitIds as $unitId) {
                $meter = self::getPropertyMeterData($unitId);
                if ($meter) {
                    $meterData[$unitId] = [
                        'unit_id' => $unitId,
                        'electronic_meter' => $meter->electronic_meter,
                        'water_meter' => $meter->water_meter,
                        'gas_meter' => $meter->gas_meter
                    ];
                } else {
                    $meterData[$unitId] = [
                        'unit_id' => $unitId,
                        'electronic_meter' => null,
                        'water_meter' => null,
                        'gas_meter' => null
                    ];
                }
            }

            $set('unit_meters', $meterData);
        }
    }

    protected static function getFixedCommircialService(Set $set)
    {
        $fixedLeaseServices = Service::where('is_fixed_commercial_lease', true)
            ->where('active', true)
            ->get();

        $commercialFixedLeaseServices = $fixedLeaseServices->map(function ($service) {
            return [
                'service_id' => $service->id,
                'value' => 0,
            ];
        })->toArray();

        $set('commercial_lease_services', $commercialFixedLeaseServices);
    }

    static function updateDueAmount($livewire)
    {
        if(empty($livewire->data['unit_ids']) || !$livewire->data['rent_amount']) return;
        $units_list = [];
        foreach ($livewire->data['units_services'] as $unit) {
            $services = [];
            $otherServices = [];

            // Process main services
            foreach ($unit['services'] as $service) {
                if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                    $services[$service['service_id']] = [
                        'lease_service_amount' => (float) $service['lease_service_amount'],
                        'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                    ];
                }
            }

            // Process other services if they exist
            if (!empty($unit['other_services'])) {
                foreach ($unit['other_services'] as $service) {
                    if ($service['to_be_paid_by'] === ServicePaymentEnum::FIXED_FEE_TYPE) {
                        $otherServices[$service['service_id']] = [
                            'lease_service_amount' => (float) $service['lease_service_amount'],
                            'to_be_paid_amount' => $service['to_be_paid_amount'] ?? null
                        ];
                    }
                }
            }

            $units_list[$unit['unit_id']] = [
                'services' => $services,
                'other_services' => $otherServices
            ];
        }


        $leasePaymentService = static::getLeasePaymentService();

        $commercialLeaseServices = $livewire->data["commercial_lease_services"] ?? [];
        $otherCommercialLeaseServices = $livewire->data["other_commercial_lease_services"] ?? [];

        // Transform other services into a compatible format
        $transformedOtherServices = array_map(function ($service) {
            return [
                "service_id" => $service["service_id"],
                "value" => $service["value"],
            ];
        }, $otherCommercialLeaseServices);

        // Merge the two service arrays
        $allCommercialServices = array_merge($commercialLeaseServices, $transformedOtherServices);

        // Output the merged services
        $TotalLeaseBalance = $leasePaymentService->getTotalLeaseBalance(
            $livewire->data['start_date'],
            $livewire->data['end_date'],
            $livewire->data['payment_type'],
            $livewire->data['payment_repeated_type'],
            $livewire->data['rent_amount'],
            $units_list,
            $allCommercialServices,
            $livewire->data['lease_type'],
            $livewire->data['owners'],
        );

        foreach ($livewire->data['units_services'] as &$unit) {
            $unitId = $unit['unit_id'];

            // Update main services
            foreach ($unit['services'] as &$service) {
                $serviceId = $service['service_id'];
                if (isset($TotalLeaseBalance['units_services'][$unitId]['services'][$serviceId])) {
                    $service['to_be_paid_amount'] = $TotalLeaseBalance['units_services'][$unitId]['services'][$serviceId]['to_be_paid_amount'];
                }
            }

            // Update other services if they exist
            if (!empty($unit['other_services'])) {
                foreach ($unit['other_services'] as &$service) {
                    $serviceId = $service['service_id'];
                    if (isset($TotalLeaseBalance['units_services'][$unitId]['other_services'][$serviceId])) {
                        $service['to_be_paid_amount'] = $TotalLeaseBalance['units_services'][$unitId]['other_services'][$serviceId]['to_be_paid_amount'];
                    }
                }
            }
        }
        unset($unit, $service);

        $livewire->data['total_amount'] = $TotalLeaseBalance['total_lease_rent_amount'];
        $livewire->data['total_services_amount'] = $TotalLeaseBalance['total_lease_services_amount'];
        $isValidVat = self::checkValidVatAndValidType($livewire);
        $livewire->data['due_amount'] =
        ($livewire->data['total_amount']
        + ($isValidVat ? ($livewire->data['total_amount'] * (float) config('lease.vat')) : 0)
        + (float) $livewire->data['total_services_amount'])
        - (float) $livewire->data['paid_amount'];
    }

    protected static function isPaidAmountValid($livewire)
    {
        $leasePaymentService = app(LeaseService::class);
        $data = $leasePaymentService->getSchedule($livewire->data['total_amount'] , $livewire->data['start_date'] , $livewire->data['end_date'] , $livewire->data['rent_amount'] , $livewire->data['days_permitted'] , $livewire->data['units_services'] , $livewire->data['payment_type'] , $livewire->data['payment_repeated_type'] , $livewire->data['lease_type'] ,$livewire->data['paid_amount'] , $livewire->data['commercial_lease_services'] , $livewire->data['other_commercial_lease_services'] , $livewire->data['owners']);
        if($data !== null)
        {
            $first_installment_amount = $data[0]->installment_amount + $data[0]->vat + $data[0]->installment_service_amount ?? 0;
            $paid_amount = $livewire->data['paid_amount'];
            return ['status'=>$paid_amount <= $first_installment_amount, 'max_paid_amount' => $first_installment_amount];
        }
    }


    static function getStartScheduleDate($livewire)
    {
        $leasePaymentService = static::getLeasePaymentService();
        $start_scheduled_date = $leasePaymentService->getStartScheduleDate($livewire->data['start_date'],$livewire->data['payment_type'],$livewire->data['payment_repeated_type']);
        $livewire->data['start_scheduled_date'] = $start_scheduled_date;
    }

    private static function checkValidVatAndValidType($livewire)
    {
        $scheduleService = new LeasePaymentService();
        return $scheduleService->checkValidVatForLeaseMembers($livewire->data['owners']) && $livewire->data['lease_type'] == LeaseTypesEnum::COMMERCIAL;
    }

    public static function getOptionView(Model $model): string
    {
        return view('lease::forms.components.bank-account-selecet',['bankAccount' => $model])->render();

    }

    public static function getRelations(): array
    {
        return [
            LeaseCommissionRelationManager::class,
            LeaseServicesRelationManager::class,
            LeaseMembersRelationManager::class,
            LeaseDocumentsRelationManager::class,
            LeaseInvoiceSchedulesRelationManager::class,
            LeaseInvoiceRelationManager::class,
            CommercialLeaseServicesRelationManager::class,
            LeaseRenewalRelationManager::class,
            SyncLeaseStepsRelationManager::class,
            LeaseRenewalRelationManager::class
        ];
    }

    public static function getWidgets(): array
    {
        return [
            LeaseListOverview::class
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        return $query;
    }

    public static function getBreadcrumb() : string
    {
        return __('Lease');
    }
    public static function getModelLabel(): string
    {
        return __('Lease');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Leases');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'force_delete',
            'force_delete_any',
            'restore',
            'restore_any',
            'replicate',
            'reorder',
            'sync',
            'view_sync'
        ];
    }
}
