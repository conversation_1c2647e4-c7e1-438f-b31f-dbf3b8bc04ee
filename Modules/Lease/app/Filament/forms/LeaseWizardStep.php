<?php
namespace Modules\Lease\app\Filament\forms;

use Filament\Forms;
use Illuminate\Support\Str;
use Filament\Support\Colors\Color;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Textarea;
use Modules\Lease\Enums\LeaseTypesEnum;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components\Actions\Action;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Wizard;
use Illuminate\Support\HtmlString;

class LeaseWizardStep
{
    protected const RADIO_DECK_COLOR = '#0f2c24';

    public static function TermsConditionsStep()
    {
        return
        Wizard\Step::make(__('Terms and Conditions'))
        ->description(__('Manage terms and conditions'))
        ->icon('heroicon-o-document-duplicate')
        ->completedIcon('heroicon-o-check-badge')
        ->schema([
            Section::make(__('Residential Terms and Conditions'))
                ->schema([
                    Section::make(__('Payment of the fee by'))
                        ->description(__('Pay the financial fee to document the contract through'))
                        ->hiddenLabel()
                        ->schema([
                            RadioDeck::make('terms.fee_by')
                                ->required()
                                ->hiddenLabel()
                                ->options([
                                    'MediationOffice' => __('Mediation office'),
                                    'TheLessor' => __('The lessor'),
                                ])
                                ->default('MediationOffice')
                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                    'class' => 'rounded-xl radio-deck-cards-color'
                                ])
                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                    'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                ])
                                ->live()
                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                            View::make('lease::forms.components.terms-table')
                        ]),
                    Section::make(__('Sublease'))
                        ->description(__('The tenant is permitted to sublease this property with a new sublease'))
                        ->hiddenLabel()
                        ->schema([
                            Toggle::make('terms.is_sublease')
                                ->hiddenLabel()
                                ->extraFieldWrapperAttributes([
                                    'class' => 'lease-custom-toggle',
                                ]),

                        ])
                        ->extraAttributes([
                            'class' => 'lease-custom-section'
                        ]),

                    Section::make(__('Reviewing government and official agencies'))
                        ->description(__('The tenant has the right to review governmental and official agencies and concerned authorities regarding the rented real estate unit'))
                        ->hiddenLabel()
                        ->schema([
                            Toggle::make('terms.is_reviewing_government')
                                ->hiddenLabel()
                                ->extraFieldWrapperAttributes([
                                    'class' => 'lease-custom-toggle',
                                ])
                        ])
                        ->extraAttributes([
                            'class' => 'lease-custom-section'
                        ]),

                    Section::make(__('Repairs and improvements to the rental unit'))
                        ->description(__('The tenant has the right to make repairs and improvements to the rental unit that do not cause fundamental changes to the property'))
                        ->hiddenLabel()
                        ->schema([
                            Toggle::make('terms.is_repairs_improvements')
                                ->hiddenLabel()
                                ->extraFieldWrapperAttributes([
                                    'class' => 'lease-custom-toggle',
                                ])
                        ])
                        ->extraAttributes([
                            'class' => 'lease-custom-section'
                        ]),

                    Section::make(__('Modify the rental unit'))
                        ->description(__('The tenant has the right to modify the rental unit, whether by increasing, decreasing or amending it'))
                        ->hiddenLabel()
                        ->schema([
                            Toggle::make('terms.is_modify_rental_unit')
                                ->hiddenLabel()
                                ->extraFieldWrapperAttributes([
                                    'class' => 'lease-custom-toggle',
                                ])
                        ])
                        ->extraAttributes([
                            'class' => 'lease-custom-section'
                        ]),

                    Section::make(__('Governing Law and Dispute Resolution'))
                        ->description(__('Except for the rights and obligations that are subject to the jurisdiction of the Enforcement Court in accordance with the Enforcement System, any dispute or disagreement arising'))
                        ->hiddenLabel()
                        ->schema([
                            RadioDeck::make('terms.governing_law')
                                ->required()
                                ->hiddenLabel()
                                ->options([
                                    'SaudiCenter' => __('Saudi Center for Real Estate Arbitration in accordance with the Center’s procedural rules'),
                                    'JudicialAuthority' => __('The competent judicial authority in the Kingdom of Saudi Arabia'),
                                ])
                                ->default('SaudiCenter')
                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                    'class' => 'rounded-xl radio-deck-cards-color'
                                ])
                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                    'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                ])
                                ->live()
                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),

                        ]),

                    Section::make(__('Additional Terms and Conditions'))
                        ->description(__('With the inclusion of additional terms and conditions. The contract will remain in effect as an executory instrument'))
                        ->hiddenLabel()
                        ->schema([
                            Repeater::make('terms.additional_terms')
                                ->label(__('Additional Terms and Conditions'))
                                ->reorderable(false)
                                ->defaultItems(0)
                                ->addActionLabel(__('add additional terms'))
                                ->schema([
                                    Textarea::make('additional_terms')
                                        ->maxLength(255)
                                        ->required()
                                        ->placeholder(__('add other terms and conditions'))
                                        ->extraInputAttributes([
                                            'maxlength' => 255
                                        ])
                                        ->label(__('Additional Terms and Conditions'))
                                ]),
                        ])
                ])
                ->visible(function($livewire) {
                    if($livewire instanceof ViewRecord) {
                        return false;
                    }
                    return $livewire->data['lease_type'] === LeaseTypesEnum::RESIDENTIAL;
                }),

            Section::make(__('Commercial Terms and Conditions'))
                ->schema([
                    Section::make(__('Contract Type'))
                        ->description(__('Terms and conditions to which the contracting will be subject'))
                        ->hiddenLabel()
                        ->schema([
                            RadioDeck::make('commercialTerms.contract_type')
                                ->required()
                                ->hiddenLabel()
                                ->options([
                                    'UnifiedContract' => __('Unified contract'),
                                    'OpenContract' => __('Open contract (non-standardized)'),
                                ])
                                ->descriptions([
                                    'UnifiedContract' => __('A unified contract that allows its parties to easily view and select multiple types of obligations while preserving the executive nature of the document.'),
                                    'OpenContract' => __('A contract that allows its parties to formulate its clauses and obligations according to the agreement during the contract, including determining amicable periods for resolving the dispute and determining the court.'),
                                ])
                                ->default('UnifiedContract')
                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                    'class' => 'rounded-xl radio-deck-cards-color'
                                ])
                                ->extraDescriptionsAttributes ([
                                    'class' => 'text-xs font-light radio-deck-options-description-color pt-2'
                                ])
                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                    'class' => 'text-sm leading-none w-full flex flex-col justify-center p-1 radio-deck-options-color'
                                ])
                                ->live()
                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                        ]),
                        Tabs::make('Tabs')
                            ->tabs([
                                    Tabs\Tab::make('Tenant and lessor commitments')
                                        ->label(__('Tenant and lessor commitments'))
                                        ->schema([
                                            Section::make(__('Tenant Rights and Obligations'))
                                                ->schema([

                                                    Section::make(__('Subletting or Lease Transfer'))
                                                    ->description(__('The obligations are determined based on choosing one of the following materials'))
                                                    ->collapsed()
                                                    ->schema([
                                                        RadioDeck::make('commercialTerms.tenantAndLessor.subletting_or_lease_transfer')
                                                            ->hiddenLabel()
                                                            ->options([
                                                                'WholeRental' => __('The lessee has the right to rent the whole rental unit to others'),
                                                                'PartOfRental' => __('The lessee has the right to rent part of the rental unit space to others after obtaining the approval of the lessor'),
                                                                'AssignContract' => __('The sub-tenant has the right to assign the contract to others, in whole or in part, after obtaining the approval of the lessor'),
                                                            ])
                                                            ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                            ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                                'class' => 'rounded-xl radio-deck-cards-color'
                                                            ])
                                                            ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                                'class' => 'text-sm leading-none w-full flex flex-col justify-center p-1 radio-deck-options-color'
                                                            ])
                                                            ->live()
                                                            ->color(Color::hex(self::RADIO_DECK_COLOR)),
                                                    ]),
                                                    Section::make(__('Review government and official agencies'))
                                                        ->description(__('The lessee has the right to review the government and official agencies, the authorities concerned with issuing the shop license, and others.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_reviewing_government')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Safety and security precautions'))
                                                        ->description(__('The tenant is obligated to take the necessary precautions to prevent exposure to the rental unit and all its accessories and those present in it.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.safety_and_security_precautions')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Change of rental unit'))
                                                        ->description(__('The lessee has the right to change the rental unit, whether by increase, decrease or amendment.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.change_of_rental_unit')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Doing decoration work'))
                                                        ->description(__('The lessee has the right to carry out decoration works, paintings, facades, electrical loads, or construction works for the rental unit that do not make major changes to the property.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.doing_decoration_work')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Daily fee in case the unit opening is delayed'))
                                                        ->description(__('The tenant is obligated to pay a specified fee daily in case of delay in opening the rental unit after the completion of the procedures The official contracting and.'))
                                                        ->collapsed()
                                                        ->schema([
                                                            TextInput::make('commercialTerms.tenantAndLessor.daily_value_of_the_delay')
                                                            ->label(__('Daily value of the delay'))
                                                            ->placeholder(__('Daily value of the delay'))
                                                            ->numeric()
                                                            ->prefix(                                                new HtmlString(
                                                                
                                                                ' <span class="icon-saudi_riyal"></span>' 
                                                           ))
                                                        ]),
                                                    Section::make(__('Proof of trademark registration'))
                                                        ->description(__('The lessee is obligated to provide the lessor with proof of trademark registration or his right or authority to sell the trademark products for the used trade name.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_proof_of_trademark_registration')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Obtaining a comprehensive cooperative insurance policy'))
                                                        ->description(__('The lessee is obligated to obtain a comprehensive cooperative insurance policy (in accordance with the provisions of Islamic law) for the rental unit and all his possessions, including decor and.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_obtaining_a_comprehensive')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Maintaining Mechanical and Electrical Equipment'))
                                                        ->description(__('The lessee is obligated to maintain all mechanical and electrical equipment, and take into account the electrical loads in the complex and other equipment that the lessor has placed in.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_maintaining_mechanical_and_electrical_equipment')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Abstaining from Smoking in Restricted Areas'))
                                                        ->description(__('The tenant and their employees are prohibited from smoking in the hallways and lobbies of the premises, and are expected to only smoke in designated areas.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_abstaining_from_smoking_in_restricted_areas')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Waste Removal'))
                                                        ->description(__('The tenant is obligated to throw the waste in the designated area, and not to throw any solid materials or oils into the sewage streams.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.waste_removal')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Determine the Period of Closing the Rental Unit'))
                                                        ->description(__('The lessee is obligated not to close the rental unit for the inventory or others for more than a specific period continuously or for more than a specified period intermittently throughout.'))
                                                        ->collapsed()
                                                        ->schema([
                                                            Grid::make('2')
                                                                ->schema([
                                                                    TextInput::make('commercialTerms.tenantAndLessor.maximum_time_closed_continuously')
                                                                        ->label(__('Maximum time closed continuously'))
                                                                        ->placeholder(__('Maximum time closed continuously'))
                                                                        ->numeric()
                                                                        ->suffix(__('Days')),
                                                                    TextInput::make('commercialTerms.tenantAndLessor.maximum_total_time_closed_in_year')
                                                                        ->label(__('Maximum total time closed in a year'))
                                                                        ->placeholder(__('Maximum total time closed in a year'))
                                                                        ->numeric()
                                                                        ->suffix(__('Days'))
                                                                ])
                                                        ]),
                                                    Section::make(__('Removal of Improvements After the Lease Term Expires'))
                                                        ->description(__('The obligations are determined based on choosing one of the following materials.'))
                                                        ->collapsed()
                                                        ->schema([
                                                            RadioDeck::make('commercialTerms.tenantAndLessor.removal_of_improvements')
                                                                ->hiddenLabel()
                                                                ->options([
                                                                    'RemoveImprovements' => __('Remove Improvements'),
                                                                    'NotRemoveImprovements' => __('Not Removing Improvements Without the Consent of the Lessor'),
                                                                ])
                                                                ->descriptions([
                                                                    'RemoveImprovements' => __('The lessee is obligated to hand over the rental units to the lessor upon the end of the lease period of this contract, in the state in which they were received, and remove any new improvements.'),
                                                                    'NotRemoveImprovements' => __('The lessee is obligated not to remove the improvements, fixed decor works that cannot be moved, or any additions made to the rental units without obtaining written approval from the lessor or their agent.'),
                                                                ])
                                                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                                ])
                                                                ->extraDescriptionsAttributes ([
                                                                    'class' => 'text-xs font-light radio-deck-options-description-color pt-2'
                                                                ])
                                                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                                    'class' => 'text-sm leading-none w-full flex flex-col justify-center p-1 radio-deck-options-color'
                                                                ])
                                                                ->live()
                                                                ->color(Color::hex(self::RADIO_DECK_COLOR)),
                                                        ]),
                                                    Section::make(__('Cancel the Municipal License'))
                                                        ->description(__("The tenant is obligated to cancel the municipality's license for the rental unit upon the expiration or termination of the contract. The following options apply:"))
                                                        ->collapsed()
                                                        ->schema([
                                                            RadioDeck::make('commercialTerms.tenantAndLessor.cancel_the_municipal_license')
                                                                ->hiddenLabel()
                                                                ->options([
                                                                    'CancelTheMunicipalLicense' => __('Cancel the Municipal License'),
                                                                    'CancelTheMunicipalLicenseAndSetDailyCharges' => __('Cancellation and Set Daily Charges in Case of Delay'),
                                                                ])
                                                                ->descriptions([
                                                                    'CancelTheMunicipalLicense' => __("The tenant is obligated to cancel the municipality's license for the rental unit upon expiration or termination of the contract."),
                                                                    'CancelTheMunicipalLicenseAndSetDailyCharges' => __("The tenant is obligated to cancel the municipality's license for the rental unit upon expiry or termination of the contract. Otherwise, the tenant shall be deemed bound by a specific daily fee."),
                                                                ])
                                                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                                ])
                                                                ->extraDescriptionsAttributes ([
                                                                    'class' => 'text-xs font-light radio-deck-options-description-color pt-2'
                                                                ])
                                                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                                    'class' => 'text-sm leading-none w-full flex flex-col justify-center p-1 radio-deck-options-color'
                                                                ])
                                                                ->live()
                                                                ->color(Color::hex(self::RADIO_DECK_COLOR)),
                                                                TextInput::make('commercialTerms.tenantAndLessor.cancel_municipal_license_daily_value_of_delay')
                                                                    ->label(__('Daily value of the delay'))
                                                                    ->placeholder(__('Daily value of the delay'))
                                                                    ->numeric()
                                                                    ->prefix(                                                new HtmlString(
                                                                        ' <span class="icon-saudi_riyal"></span>' 
                                                                   ))
                                                                    ->visible(fn($livewire) => $livewire->data['commercialTerms']['tenantAndLessor']['cancel_the_municipal_license'] === 'CancelTheMunicipalLicenseAndSetDailyCharges'),

                                                        ]),
                                                    Section::make(__('Daily Charge in Case of Delayed Delivery of the Unit'))
                                                        ->description(__("The lessor is obligated to pay a daily fee in the event that he is late in delivering the rental unit to the tenant after completing the formal contracting procedures."))
                                                        ->collapsed()
                                                        ->schema([
                                                            TextInput::make('commercialTerms.tenantAndLessor.daily_charge_daily_value_of_delay')
                                                                ->label(__('Daily value of the delay'))
                                                                ->placeholder(__('Daily value of the delay'))
                                                                ->numeric()
                                                                ->prefix(
                                                                    new HtmlString(
                                                                        ' <span class="icon-saudi_riyal"></span>' 
                                                                   )
                                                                )
                                                        ]),
                                                    Section::make(__('Approval of the User Guide'))
                                                        ->description(__('The lessee acknowledges receipt and has access to the details and contents of the Tenant Manual and the conditions, specifications.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_approved_user_guide')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Unit Opening and Unit Termination Periods'))
                                                        ->description(__('The lessee is obligated to open and start his commercial activity within a period not exceeding (days) days from the date of the contract:'))
                                                        ->schema([
                                                            Grid::make('2')
                                                                ->schema([
                                                                    TextInput::make('commercialTerms.tenantAndLessor.specify_opening_period_activity')
                                                                        ->label(__('Specify opening period for intended activity'))
                                                                        ->placeholder(__('Specify opening period for intended activity'))
                                                                        ->numeric()
                                                                        ->required()
                                                                        ->suffix(__('Days')),
                                                                    TextInput::make('commercialTerms.tenantAndLessor.specify_termination_unit_not_opened')
                                                                        ->label(__('Specify period for terminating if unit not opened'))
                                                                        ->placeholder(__('Specify period for terminating if unit not opened'))
                                                                        ->numeric()
                                                                        ->required()
                                                                        ->suffix(__('Days'))
                                                                ])
                                                        ]),
                                                ]),
                                            Section::make(__('Malls/Retail Sector'))
                                                ->schema([

                                                    Section::make(__('Adhere to the Tenant Guide'))
                                                        ->description(__('Under this contract, the lessor and the lessee have agreed to abide by the Tenant Manual of the property (the commercial center) as a reference guide for them that regulates the tenant’s.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_adhere_to_tenant_guide')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Adherence to the opening and closing dates of the rental unit'))
                                                        ->description(__('The tenant is committed to the opening and closing dates of the rental unit mentioned in the Tenant Manual, which is applicable in the commercial center, and in the event of.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_adherence_to_opening_and_closing_dates')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Commercial discounts'))
                                                        ->description(__("The obligations are determined based on choosing one of the following materials"))
                                                        ->collapsed()
                                                        ->schema([
                                                            RadioDeck::make('commercialTerms.tenantAndLessor.commercial_discounts')
                                                                ->hiddenLabel()
                                                                ->options([
                                                                    'NoticeOfLassor' => __('Notice of the Lessor and Obtaining Written Approval'),
                                                                    'ObtainingRequiredApprovals' => __('Obtaining Required Approvals from Competent Official Authorities'),
                                                                ])
                                                                ->descriptions([
                                                                    'NoticeOfLassor' => __("The lessee is obligated not to announce the commercial discounts before notifying the lessor and obtaining the written approval."),
                                                                    'ObtainingRequiredApprovals' => __("The lessee shall not announce commercial discounts until after obtaining the required approvals from."),
                                                                ])
                                                                ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                                ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                                ])
                                                                ->extraDescriptionsAttributes ([
                                                                    'class' => 'text-xs font-light radio-deck-options-description-color pt-2'
                                                                ])
                                                                ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                                    'class' => 'text-sm leading-none w-full flex flex-col justify-center p-1 radio-deck-options-color'
                                                                ])
                                                                ->live()
                                                                ->color(Color::hex(self::RADIO_DECK_COLOR)),

                                                        ]),
                                                    Section::make(__('Isolation of the floor for any activity that requires the use of water and sanitation'))
                                                        ->description(__('The tenant is obligated to isolate the floor of the rental unit according to the specifications mentioned in the Tenant Manual, in the event that it is used as toilets, a restaurant.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_isolation_of_floor')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Loading and unloading operations'))
                                                        ->description(__('The tenant shall abide by the times and dates mentioned in the Tenant Manual for loading and unloading operations.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_loading_and_unloading_operations')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Cancellation or transfer of phone lines'))
                                                        ->description(__('The obligation to cancel or transfer the fixed telephone lines of the rental unit upon the end of the contract.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_cancellation_or_transfer_of_phone_lines')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                ]),
                                            Section::make(__('Lessor Rights and Obligations'))
                                                ->schema([
                                                    Section::make(__('Replacing the rental unit'))
                                                        ->description(__('The lessor is obligated to change the location of the rental unit in the event that there are repairs that would partially or completely stop.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.tenantAndLessor.is_replacing_the_rental_unit')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('Change in rental value'))
                                                        ->description(__("The lessor has the right to increase or decrease the rent upon renewing the contract, provided that the tenant is notified of the new rental value for a specified."))
                                                        ->collapsed()
                                                        ->schema([
                                                            TextInput::make('commercialTerms.tenantAndLessor.number_of_reporting_days')
                                                                ->label(__('Number of reporting days'))
                                                                ->placeholder(__('Number of reporting days'))
                                                                ->numeric()
                                                                ->suffix(__('Days'))
                                                        ]),
                                                ])

                                        ])->visible(fn($livewire) => $livewire->data['commercialTerms']['contract_type'] === 'UnifiedContract'),
                                    Tabs\Tab::make('Mutual rights and commitments')
                                        ->label(__('Mutual rights and commitments'))
                                        ->schema([
                                            Section::make(__('Mutual Rights'))
                                                ->description(__('The party that bears the following obligations is determined by choosing between the tenant and the lessor.'))
                                                ->schema([
                                                    Section::make(__('Responsible for paying fees for the services of the competent authorities'))
                                                        ->description(__('Complying with any service fees that may be imposed on rental units by the competent authorities'))
                                                        ->schema([
                                                            RadioDeck::make('commercialTerms.mutualRights.responsible_for_paying_fees_services')
                                                                ->required()
                                                                ->hiddenLabel()
                                                                ->options([
                                                                    LeaseMemberTypesEnum::LESSOR => __('Lessor'),
                                                                    LeaseMemberTypesEnum::TENANT => __('Tenant'),
                                                                ])
                                                                ->default(LeaseMemberTypesEnum::LESSOR)
                                                                ->direction('column')
                                                                ->extraCardsAttributes([
                                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                                ])
                                                                ->extraOptionsAttributes([
                                                                    'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                                ])
                                                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                                ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                                                        ]),
                                                    Section::make(__('Responsible for providing the basic requirements for issuing the shop license'))
                                                        ->description(__('Commitment to providing the basic requirements for government and official agencies and all competent authorities with regard to assistance in issuing a shop license.'))
                                                        ->schema([
                                                            RadioDeck::make('commercialTerms.mutualRights.responsible_for_providing_the_basic_requirements')
                                                                ->required()
                                                                ->hiddenLabel()
                                                                ->options([
                                                                    LeaseMemberTypesEnum::LESSOR => __('Lessor'),
                                                                    LeaseMemberTypesEnum::TENANT => __('Tenant'),
                                                                ])
                                                                ->default(LeaseMemberTypesEnum::LESSOR)
                                                                ->direction('column')
                                                                ->extraCardsAttributes([
                                                                    'class' => 'rounded-xl radio-deck-cards-color'
                                                                ])
                                                                ->extraOptionsAttributes([
                                                                    'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                                ])
                                                                ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                                ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                                                        ])

                                                ]),
                                            Section::make(__('Common obligations'))
                                                ->schema([
                                                    Section::make(__('The validity of the rental contract on owner change'))
                                                        ->description(__("In the event that the ownership of the real estate or rental units is transferred to another owner, the tenant's contract will remain and will not be terminated until the end of its term."))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.mutualRights.is_validity_of_the_rental_contract_on_owner_change')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),
                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                    Section::make(__('The obligation to pay the remaining rental value'))
                                                        ->description(__('The lessor or the lessee is obligated to pay (20%) of the remaining rental value of the lease contract in effect in the desire of either of them to terminate the contract.'))
                                                        ->hiddenLabel()
                                                        ->schema([
                                                            Toggle::make('commercialTerms.mutualRights.is_obligation_to_pay_the_remaining_rental_value')
                                                                ->hiddenLabel()
                                                                ->extraFieldWrapperAttributes([
                                                                    'class' => 'lease-custom-toggle',
                                                                ]),

                                                        ])
                                                        ->extraAttributes([
                                                            'class' => 'lease-custom-section'
                                                        ]),
                                                ])


                                        ])->visible(fn($livewire) => $livewire->data['commercialTerms']['contract_type'] === 'UnifiedContract'),
                                    Tabs\Tab::make(__('General items'))
                                        ->label(__('General items'))
                                        ->schema([
                                            Section::make(__('Ejar Fees Paid By'))
                                                ->description(__('Set who should pay the annual contract registration fee on the Ejar platform:'))
                                                ->hiddenLabel()
                                                ->schema([
                                                    RadioDeck::make('commercialTerms.generalItems.ejar_fee_by')
                                                        ->required()
                                                        ->hiddenLabel()
                                                        ->options([
                                                            'BrokerageOffice' => __('Brokerage Office'),
                                                            'Lessor' => __('Lessor'),
                                                        ])
                                                        ->default('BrokerageOffice')
                                                        ->direction('column') // Column | Row (Allows to place the Icon on top)
                                                        ->extraCardsAttributes([ // Extra Attributes to add to the card HTML element
                                                            'class' => 'rounded-xl radio-deck-cards-color'
                                                        ])
                                                        ->extraOptionsAttributes([ // Extra Attributes to add to the option HTML element
                                                            'class' => 'text-xs leading-none w-full flex flex-col items-center justify-center p-1 radio-deck-options-color'
                                                        ])
                                                        ->live()
                                                        ->color(Color::hex(self::RADIO_DECK_COLOR))
                                                        ->columns(['default' => 1, 'sm' => 2, 'lg' => 1, 'xl' => 2]),
                                                ]),
                                            Forms\Components\Section::make(__('The Contract is Subject to Condition'))
                                            ->description(__('So that if the condition attached to the contract is fulfilled, it is done according to the start date of the lease.'))
                                            ->schema([
                                                    Forms\Components\Checkbox::make('commercialTerms.generalItems.construction_completion')
                                                        ->label(__('Construction completion certificate'))
                                                        ->helperText(__('Issuing a certificate from the Ministry of Municipal and Rural Affairs to complete construction')),
                                                    Forms\Components\Checkbox::make('commercialTerms.generalItems.civil_defense')
                                                        ->label(__('Civil defense requirements'))
                                                        ->helperText(__('A list of the safety conditions, fire prevention, alarm and extinguishing equipment from the Civil Defense Authority')),
                                                    Forms\Components\Checkbox::make('commercialTerms.generalItems.electricity_safety')
                                                        ->label(__('Electricity Safety Requirements'))
                                                        ->helperText(__('A receipt for the safety of electrical panels and switches from the Saudi Electricity Company')),
                                                    Forms\Components\Section::make('')
                                                    ->schema(function (Forms\Get $get) {
                                                        $conditions = $get('commercialTerms.generalItems.conditions_checkbox') ?? [];
                                                        return collect($conditions)->map(function ($condition) {
                                                            $key = Str::snake($condition['label']['en']);
                                                            return Grid::make(2)
                                                                ->schema([
                                                                    Forms\Components\Checkbox::make('commercialTerms.generalItems.conditions_checkbox.' . $key . '.value')
                                                                        ->label($condition['label'][app()->getLocale()])
                                                                        ->helperText($condition['description'][app()->getLocale()])
                                                                        ->default(true)
                                                                        ->live()
                                                                        ->afterStateUpdated(function ($state,$set,$get) use ($condition) {
                                                                            $key = 'commercialTerms.generalItems.conditions_checkbox.' . Str::snake($condition['label']['en']);
                                                                            $current = $get($key);
                                                                            $set($key, [
                                                                                'label' => $condition['label'],
                                                                                'description' => $condition['description'],
                                                                                'value' => $state,
                                                                            ]);
                                                                        })
                                                                        ->columnSpan(1),
                                                                    Forms\Components\Actions::make([
                                                                            Forms\Components\Actions\Action::make('delete'. $key)
                                                                                ->label(__('Remove'))
                                                                                ->icon('heroicon-m-trash')
                                                                                ->color('danger')
                                                                                ->size('sm')
                                                                                ->action(function (Forms\Set $set,$get) use ($key) {
                                                                                    $existingConditions = $get('commercialTerms.generalItems.conditions_checkbox') ?? [];
                                                                                    unset($existingConditions[$key]);
                                                                                    $set('commercialTerms.generalItems.conditions_checkbox', $existingConditions);

                                                                                    Notification::make()
                                                                                        ->success()
                                                                                        ->title(__('Success'))
                                                                                        ->body(__('Condition deleted successfully'))
                                                                                        ->send();
                                                                                })
                                                                                ->requiresConfirmation()
                                                                                ->modalHeading(__('Delete Condition'))
                                                                                ->modalDescription(__('Are you sure you want to delete this condition? This action cannot be undone.'))
                                                                                ->modalSubmitActionLabel(__('Yes, delete it'))
                                                                        ])
                                                                        ->alignEnd()
                                                                        ->columnSpan(1),
                                                                ])->columnSpan('full');
                                                        })->toArray();
                                                    })
                                                    ->collapsible(false)
                                                    ->extraAttributes([
                                                        'class' => 'custom-conditions-section'
                                                    ]),
                                                ])
                                                ->footerActions([
                                                    Action::make('add_custom_condition')
                                                    ->hidden(fn($livewire) => $livewire instanceof ViewRecord)
                                                        ->label(__('Add a custom condition'))
                                                        ->color('primary')
                                                        ->icon('heroicon-o-plus')
                                                        ->modalHeading(__('Add a custom condition'))
                                                        ->modalSubmitAction(
                                                            fn ($action) => $action->label(__('Confirm'))
                                                        )
                                                        ->modalCancelAction(
                                                            fn ($action) => $action->label(__('Close'))
                                                        )
                                                        ->form([
                                                            Tabs::make('Tabs')
                                                                ->tabs([
                                                                    Tabs\Tab::make(__('Arabic'))
                                                                        ->schema([
                                                                            TextInput::make('label.ar')
                                                                                ->label(__('Label'))
                                                                                ->required(),
                                                                            Textarea::make('description.ar')
                                                                                ->label(__('Description'))
                                                                                ->required(),
                                                                        ]),
                                                                    Tabs\Tab::make(__('English'))
                                                                        ->schema([
                                                                            TextInput::make('label.en')
                                                                                ->label(__('Label'))
                                                                                ->required(),
                                                                            Textarea::make('description.en')
                                                                            ->label(__('Description'))
                                                                            ->required(),
                                                                        ]),

                                                                ]),
                                                        ])
                                                        ->action(function (array $data,$set,$get) {
                                                            $key = 'commercialTerms.generalItems.conditions_checkbox.' . Str::snake($data['label']['en']);

                                                            $existingConditions = $get('commercialTerms.generalItems.conditions_checkbox') ?? [];
                                                            $labelExists = collect($existingConditions)->contains(function ($condition) use ($data) {
                                                                return Str::snake($condition['label']['en']) === Str::snake($data['label']['en']);
                                                            });

                                                            if ($labelExists) {
                                                                Notification::make()
                                                                    ->danger()
                                                                    ->title(__('Error'))
                                                                    ->body(__('A condition with this label already exists'))
                                                                    ->send();
                                                                return;
                                                            }

                                                            $set($key, [
                                                                'label' => $data['label'],
                                                                'description' => $data['description'],
                                                                'value' => true,
                                                            ]);

                                                            Notification::make()
                                                                ->success()
                                                                ->title(__('Success'))
                                                                ->body(__('Custom condition added successfully'))
                                                                ->send();
                                                        }),

                                                ]),
                                                Section::make(__('Change Commercial Activity'))
                                                    ->description(__("The lessee has the right to change the commercial activity registered in the contract after lessor approval."))
                                                    ->hiddenLabel()
                                                    ->schema([
                                                        Toggle::make('commercialTerms.generalItems.is_change_commercial_activity')
                                                            ->hiddenLabel()
                                                            ->extraFieldWrapperAttributes([
                                                                'class' => 'lease-custom-toggle',
                                                            ]),
                                                    ])
                                                    ->extraAttributes([
                                                        'class' => 'lease-custom-section'
                                                    ]),
                                                Section::make(__('Additional Terms and Conditions'))
                                                    ->description(__('By adding additional terms and conditions, the contract will not lose the status of an executive document.'))
                                                    ->hiddenLabel()
                                                    ->schema([
                                                        Repeater::make('commercialTerms.generalItems.additional_terms')
                                                            ->label(__('Additional Terms and Conditions'))
                                                            ->reorderable(false)
                                                            ->defaultItems(0)
                                                            ->addActionLabel(__('add additional terms'))
                                                            ->schema([
                                                                Textarea::make('additional_terms')
                                                                    ->maxLength(255)
                                                                    ->required()
                                                                    ->placeholder(__('add other terms and conditions'))
                                                                    ->extraInputAttributes([
                                                                        'maxlength' => 255
                                                                    ])
                                                                    ->label(__('Additional Terms and Conditions'))
                                                            ]),
                                                    ])
                                        ]),
                                ]),
                ])
                ->visible(function($livewire) {
                    if($livewire instanceof ViewRecord) {
                        return false;
                    }
                    return $livewire->data['lease_type'] === LeaseTypesEnum::COMMERCIAL;
                })
        ]);
    }
}
