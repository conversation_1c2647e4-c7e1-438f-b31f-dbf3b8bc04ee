<?php

namespace Modules\Lease\app\Filament;

use Filament\Panel;
use Filament\Contracts\Plugin;
use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Modules\Lease\app\Filament\Resources\LeaseSettingResource;

class LeasePlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Lease';
    }

    public function getId(): string
    {
        return 'lease';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                LeaseResource::class,
                LeaseSettingResource::class,
            ]);
    }
}
