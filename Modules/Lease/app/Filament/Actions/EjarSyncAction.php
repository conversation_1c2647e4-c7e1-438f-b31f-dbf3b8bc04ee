<?php
namespace Modules\Lease\app\Filament\Actions;

use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\Lease\Services\LeaseSyncService;
use Modules\Property\Enums\EjarSyncStatus;
use Carbon\Carbon;

class EjarSyncAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'ejar_sync';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->button()
            ->label(function (Model $record): string {
                return
                $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? __('Sync with ejar') :
                EjarSyncStatus::tryFrom($record->ejar_sync_status?->value)?->getLabel();
            })
            ->tooltip(function (Model $record): ?string {
                return $record->last_synced_at
                    ? __('last synced') . ' ' . $record->last_synced_at->diffForHumans()
                    : null;
            })
            ->icon(function (Model $record): string {
                return
                    $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? 'heroicon-o-arrow-path' :
                        EjarSyncStatus::tryFrom($record->ejar_sync_status->value)?->getIcon();
            })
            ->color(function (Model $record): string {
                return
                $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? 'success' :
                        EjarSyncStatus::tryFrom($record->ejar_sync_status->value)?->getColor();
            })
            ->visible(auth()->user()->can('view_sync_lease'))
            ->action(function (Model $record): void {
                // Check if any unit is not synced with Ejar
                if ($record->units->contains('ejar_uuid', null)) {
                    $this->notifyUnsyncedUnits();
                    return;
                }

                // Check if lease start date is in the past
                if (Carbon::parse($record->start_date)->lessThan(Carbon::today())) {
                    $this->notifyHistoricalLease();
                    return;
                }

                if (count($record->units) > 1) {
                    $this->notifyFailed(__("Ejar does't support syncing multi unit lease , only you can sync leases which has single unit"));
                    return;
                }

                //check action permission
                if (!auth()->user()->can('sync_lease')) {
                    $this->needPermission();
                    return;
                }
                //check subscription
                if (!SyncHelper::syncAvailableLeaseInActivePlan()) {
                    $this->needUpgrade();
                    return;
                }


                $record->update([
                    'ejar_sync_status' => EjarSyncStatus::SYNCING,
                ]);
                try {
                    $syncService = new LeaseSyncService($record);
                    $syncService->sync();

                    if(!$syncService->sync()['status'])
                    {
                        $this->notifyFailed($syncService->sync()['message']);
                        return;
                    }
                    $this->notifySuccess();
                }catch (\Exception $e) {
                    $this->notifyFailed($e->getMessage());
                }
            })
            ->after(function ($livewire) {
                $livewire->dispatch('refreshSteps');
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-o-arrow-path')
            ->modalHeading(__("Sync with ejar"))
            ->modalDescription(__("Are you sure you want to sync this lease with ejar?"))
            ->modalSubmitActionLabel(__('Yes, sync it'));
    }

    protected function notifyUnsyncedUnits(): void
    {
        Notification::make()
            ->warning()
            ->title(__("Unsynced Units"))
            ->body(__("Some lease units are not synced with Ejar. Please sync all units first before proceeding with lease sync."))
            ->icon('heroicon-o-exclamation-triangle')
            ->iconColor('warning')
            ->persistent()
            ->send();
    }

    protected function notifyHistoricalLease(): void
    {
        Notification::make()
            ->warning()
            ->title(__("Cannot Sync Historical Lease"))
            ->body(__("Ejar does not support syncing leases with start dates in the past."))
            ->icon('heroicon-o-exclamation-triangle')
            ->iconColor('warning')
            ->persistent()
            ->send();
    }

    protected function needPermission()
    {
        Notification::make()
            ->title(__('Permission Denied'))
            ->body(__('You do not have permission to do this action.'))
            ->danger()
            ->persistent()
            ->send();
    }
    protected function needUpgrade()
    {
        Notification::make()
            ->title(__('Access Denied'))
            ->body(__('You do not have access to this feature. Please upgrade your subscription.'))
            ->danger()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('upgrade')
                    ->label(__('Upgrade Subscription'))
                    ->url(fn (): string => url('/admin/subscription-plans'))
                    ->button()
                    ->color('success'),

                \Filament\Notifications\Actions\Action::make('back')
                    ->label(__('Go Back'))
                    ->url(url()->previous())
                    ->color('gray')
                    ->button(),
            ])
            ->send();
    }

    protected function notifySuccess(): void
    {
        Notification::make()
            ->success()
            ->title(__("lease synced with ejar successfully"))
            ->icon('heroicon-o-check-circle')
            ->iconColor('success')
            ->duration(5000)
            ->send();
    }

    protected function notifyFailed($message): void
    {
        Notification::make()
            ->danger()
            ->title(__('Could not sync this lease with ejar'))
            ->body($message)
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
}