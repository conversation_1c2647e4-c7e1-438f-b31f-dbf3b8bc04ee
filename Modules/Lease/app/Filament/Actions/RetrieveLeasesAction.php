<?php
namespace Modules\Lease\app\Filament\Actions;

use App\Enums\RoleEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\Lease\Services\LeaseRetrievalService;
use Modules\Lease\Services\RetrieveCompanyLeasesService;

class RetrieveLeasesAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'retrieve_leases';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->button()
            ->color('info')
            ->visible(auth()->user()->can('view_sync_lease') && !auth()->user()->hasRole(RoleEnum::ADMIN->value)) //has permission and not admin
            ->form([
                Radio::make('retrieve_type')
                    ->label(__('Retrieve Type'))
                    ->options([
                        'all' => __('Retrieve All Leases'),
                        'single' => __('Retrieve Single Lease'),
                    ])
                    ->default('all')
                    ->required()
                    ->live(),

                Grid::make(1)
                    ->schema([
                        TextInput::make('lease_number')
                            ->label(__('Lease Number'))
                            ->placeholder(__('Enter lease number'))
                            ->required()
                            ->visible(fn (Get $get): bool => $get('retrieve_type') === 'single'),
                    ])
            ])
            ->action(function (array $data): void {
                try {
                    //check permission
                    if (!auth()->user()->can('sync_lease')) {
                        $this->needPermission();
                        return;
                    }
                    //check subscription
                    if (!SyncHelper::syncAvailableInActivePlan()) {
                        $this->needUpgrade();
                        return;
                    }
                    //get user company
                    $user = auth()->user();
                    if (is_null($user->company)) {
                        $this->notifyUndefinedCompany();
                        return;
                    }

                    // Determine which retrieval method to use based on the selection
                    if ($data['retrieve_type'] === 'all') {
                        // Retrieve all leases
                        $retrievalService = new RetrieveCompanyLeasesService($user->company);
                        $retrievalService->retrieve();
                    } else {
                        // Retrieve single lease
                        $retrievalService = new LeaseRetrievalService(auth()->user()->company);

                        $lease = $retrievalService->getLeaseByNumber($data['lease_number']);

                    }
                } catch (\Exception $e) {
                    $this->notifyFailed($e->getMessage());
                }
            })
            ->requiresConfirmation();
    }

    protected function notifyFailed($message): void
    {
        Notification::make()
            ->danger()
            ->title(__('Could not retrieve company leases'))
            ->body($message)
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
    protected function notifyUndefinedCompany(): void
    {
        Notification::make()
            ->danger()
            ->title(__('Undefined company'))
            ->body(__('The company of leases is not found.'))
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
    protected function needPermission(): void
    {
        Notification::make()
            ->title(__('Permission Denied'))
            ->body(__('You do not have permission to do this action.'))
            ->danger()
            ->persistent()
            ->send();
    }
    protected function needUpgrade(): void
    {
        Notification::make()
            ->title(__('Access Denied'))
            ->body(__('You do not have access to this feature. Please upgrade your subscription.'))
            ->danger()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('upgrade')
                    ->label(__('Upgrade Subscription'))
                    ->url(fn (): string => url('/admin/subscription-plans'))
                    ->button()
                    ->color('success'),

                \Filament\Notifications\Actions\Action::make('back')
                    ->label(__('Go Back'))
                    ->url(url()->previous())
                    ->color('gray')
                    ->button(),
            ])
            ->send();
    }

    protected function notifySingleLeaseSuccess(string $leaseNumber): void
    {
        Notification::make()
            ->success()
            ->title(__('Lease Retrieved Successfully'))
            ->body(__('Lease :number has been successfully retrieved.', ['number' => $leaseNumber]))
            ->icon('heroicon-o-check-circle')
            ->iconColor('success')
            ->send();
    }
}
