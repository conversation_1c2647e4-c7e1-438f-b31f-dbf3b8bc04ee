<?php

namespace Modules\Lease\app\Observers;

use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Services\AccountRoleService;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\Enums\LeaseMemberTypesEnum;

class LeaseMemberObserver
{
    protected AccountRoleService $accRoleService;
    public function __construct(AccountRoleService $accountRoleService)
    {
        $this->accRoleService = $accountRoleService;
    }
    /**
     * Handle the LeaseMember "created" event.
     */
    public function created(LeaseMember $leasemember): void
    {
        $this->syncLeaseMemberRoleWithAccountRole($leasemember);
    }

    /**
     * Handle the LeaseMember "updated" event.
     */
    public function updated(LeaseMember $leasemember): void
    {
        $this->syncLeaseMemberRoleWithAccountRole($leasemember);
    }

    /**
     * Handle the LeaseMember "deleted" event.
     */
    public function deleted(LeaseMember $leasemember): void
    {
        //
    }

    /**
     * Handle the LeaseMember "restored" event.
     */
    public function restored(LeaseMember $leasemember): void
    {
        //
    }

    /**
     * Handle the LeaseMember "force deleted" event.
     */
    public function forceDeleted(LeaseMember $leasemember): void
    {
        //
    }

    public function syncLeaseMemberRoleWithAccountRole(LeaseMember $leasemember): void
    {
        //todo refactor this check
        $current_role = $leasemember->member_role;
        if ($current_role == LeaseMemberTypesEnum::LESSOR){
            $current_role = AccountRolesEnum::OWNER;
        } elseif ($current_role == LeaseMemberTypesEnum::LESSOR_REPRESENTER) {
            $current_role = AccountRolesEnum::OWNER_REPRESENTER;
        }
        if (in_array($current_role, AccountRolesEnum::getAccountRoleTypes()) && !$this->accRoleService->accountRoleExist($leasemember->member_id, $current_role) && $leasemember->member_type !== LeaseMemberTypesEnum::ORGANIZATION_TYPE)
        {
            $account_role_data = ['account_id' => $leasemember->member_id, 'role' => $current_role];
            $this->accRoleService->create($account_role_data);
        }
    }
}
