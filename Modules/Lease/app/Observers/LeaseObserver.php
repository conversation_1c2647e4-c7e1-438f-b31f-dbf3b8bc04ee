<?php
namespace Modules\Lease\app\Observers;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Property\app\Events\PropertyStatusChange;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;
use Modules\Request\Enums\UnitStatusEnum;
class LeaseObserver
{
    /**
     * Handle the Lease "created" event.
     */
    public function created(Lease $lease): void
    {
        //
    }
    /**
     * Handle the Lease "updated" event.
     */
    public function updated(Lease $lease): void
    {
        if($lease->isDirty('status') && $lease->status == LeaseEnum::PUBLISHED){
            //update units
            foreach ($lease->leaseUnits as $leaseUnit){
                event(new PropertyStatusChange(
                    property: $leaseUnit->unit,
                    newStatus: PropertyStatus::RENTED
                ));
            }
        }
        if($lease->isDirty('status') && $lease->status == LeaseEnum::TERMINATED){
            //update units
            foreach ($lease->leaseUnits as $leaseUnit){
                event(new PropertyStatusChange(
                    property: $leaseUnit->unit,
                    newStatus: PropertyStatus::ACTIVE
                ));
            }
        }
        if($lease->isDirty('status') && $lease->status == LeaseEnum::CLOSED && $lease->approvedRequest->unit_status == UnitStatusEnum::NEED_MAINTENANCE){
            //update units
            foreach ($lease->leaseUnits as $leaseUnit){
                event(new PropertyStatusChange(
                    property: $leaseUnit->unit,
                    newStatus: PropertyStatus::IN_MAINTENANCE
                ));
            }
        }

        if($lease->isDirty('status') && $lease->status == LeaseEnum::CLOSED && $lease->approvedRequest->unit_status == UnitStatusEnum::GOOD){
            //update units
            foreach ($lease->leaseUnits as $leaseUnit){
                event(new PropertyStatusChange(
                    property: $leaseUnit->unit,
                    newStatus: PropertyStatus::ACTIVE
                ));
            }
        }
    }
    /**
     * Handle the Lease "deleted" event.
     */
    public function deleting(Lease $lease): void
    {
        // Store units that need processing directly on the lease model
        $units = Property::whereIn('id', function($query) use ($lease) {
            $query->select('unit_id')
                ->from('lease_units')
                ->where('lease_id', $lease->id);
        })
            ->where('status', PropertyStatus::RESERVED)
            ->get();

        if ($units->count() > 0) {
            $lease->unitsToProcess = $units->pluck('id')->toArray();

            Log::info('Units stored for processing', [
                'count' => $units->count(),
                'unit_ids' => $lease->unitsToProcess,
                'unit_statuses' => $units->pluck('status', 'id')->toArray()
            ]);
        }
    }

    public function deleted(Lease $lease): void
    {
        if (!empty($lease->unitsToProcess)) {
            $units = Property::whereIn('id', $lease->unitsToProcess)->get();

            foreach ($units as $unit) {
                try {
                    event(new PropertyStatusChange(
                        property: $unit,
                        newStatus: PropertyStatus::ACTIVE
                    ));

                    $unit->refresh();

                } catch (\Exception $e) {
                }
            }
        }
    }
    /**
     * Handle the Lease "restored" event.
     */
    public function restored(Lease $lease): void
    {
        //
    }
    /**
     * Handle the Lease "force deleted" event.
     */
    public function forceDeleted(Lease $lease): void
    {
        //
    }
}
