<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Service\app\Http\Resources\Api\ServicesResource;

class LeaseUnitServiceResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "lease_unit_id" => $this->lease_unit_id ?? null,
            "meter_current_reading" => $this->meter_current_reading ?? null,
            "to_be_paid_amount" => $this->to_be_paid_amount ?? null,
            "number_of_service" => $this->number_of_service ?? null,
            "to_be_paid_per" => $this->to_be_paid_per ?? null,
            "to_be_paid_by" => $this->to_be_paid_by ?? null,
            "service" => $this->whenLoaded('service', function () {
                return ServicesResource::make($this->service);
            }),
        ];

        return $data;
    }
}
