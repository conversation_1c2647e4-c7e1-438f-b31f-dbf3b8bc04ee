<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\app\Http\Resources\Api\CompanyResource;
use Modules\Request\app\Http\Resources\Api\AutoRenewalrequestDetailsResource;
use Modules\Request\app\Http\Resources\Api\requestDetailsResource;
use Modules\Request\Enums\RequestTypeEnum;

class LeaseResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "lease_type" => $this->lease_type ?? null,
            "start_date" => $this->start_date ?? null,
            "end_date" => $this->end_date ?? null,
            "lease_number" => $this->lease_number ?? null,
            "ejar_uuid" => $this->ejar_uuid ?? null,
            "status" => $this->status ?? null,
            'auto_renewal' => $this->auto_renewal ?? null,
            "lease_file" => $this->getMediaImage('lease') ?? null,
            "property" => $this->whenLoaded('property', function () use ($request) {
                return array_merge(
                    ListLeasePropertyResource::make($this->property)->toArray($request),
                    [
                        'units' => $this->whenLoaded('units', function () {
                            return ListLeaseUnitResource::collection($this->units);
                        })
                    ]
                );
            }),
            "renewd_at" => Carbon::parse($this->end_date)->addDay()->format('Y-m-d'),
            "members" => $this->whenLoaded('leaseMembers', function () {
                return LeaseMemberResource::collection($this->leaseMembers);
            }),
            "is_semi_ended"=>($this->end_date),
            "request_id" => $this->request 
                ? ($this->request->request_type === RequestTypeEnum::AUTO_RENEWAL
                    ? new AutoRenewalrequestDetailsResource($this->request)
                    : new requestDetailsResource($this->request))
                : null,
            "company" => CompanyResource::make($this->company),
        ];

        return $data;
    }
}
