<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class LeaseUnitResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "lease_id" => $this->lease_id ?? null,
            "unit_id" => $this->unit_id ?? null,
            "lease_unit_services" => $this->whenLoaded('leaseUnitServices', function () {
                return LeaseUnitServiceResource::collection($this->leaseUnitServices);
            }),
        ];

        return $data;
    }
}
