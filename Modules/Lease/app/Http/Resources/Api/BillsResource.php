<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class BillsResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "invoice_id"                    =>     $this->id ?? null,
            "property_name"                 =>     $this->property_name() ?? null,
            "units_name"                    =>     $this->lease_units() ?? null,
            "tenant"                        =>     LeaseMemberResource::make($this->lease_tenant()) ?? null,
            "status"                        =>     $this->status ?? null,
            "total_amount"                  =>     ($this->total  + $this->tax - $this->discount) ?? null,
            "remaining_amount"              =>     $this->remaining,
            "for_id"                        =>     $this->for_id,
            "for_type"                      =>     $this->for_type,
            "installment_amount"            =>     $this->invoice_schedule->installment_amount ?? null,
            "installment_service_amount"    =>     $this->invoice_schedule->installment_service_amount ?? null,
            "bill_number"                   =>     $this->invoice_schedule->bill_number ?? null,
            "total_bills"                   =>     $this->invoice_schedule->installment_count ?? null,
            "installment_date"              =>     $this->release_date ?? null,
            "due_date"                      =>     $this->due_date?? null,
            "type"                          =>     $this->invoice_type,
            "vat"                           =>     $this->vat,
            "download_link"                 =>     url('invoices/' . $this->uuid . '/pdf/download'),
        ];
        return $data;
    }
}
