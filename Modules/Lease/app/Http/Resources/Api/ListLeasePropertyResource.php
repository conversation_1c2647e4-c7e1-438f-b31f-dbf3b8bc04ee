<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\app\Http\Resources\Api\CompanyResource;
use Modules\Property\app\Resources\Api\BrokerResource;
use Modules\Property\app\Resources\Api\PropertyTypeResource;
use Modules\Property\app\Resources\Api\UsabilityResource;


class ListLeasePropertyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "name" => $this->name == null ? $this->property_type->name . '-' . $this->number : $this->name,
            "number" => $this->number,
            "address" => $this->address,
            "broker" => $this->whenLoaded('brokers', function () {
                return BrokerResource::collection($this->brokers);
            }),
            "owners" => $this->whenLoaded('owners', function () {
                return BrokerResource::collection($this->owners);
            }),
            "property_type" => $this->whenLoaded('property_type', function () {
                return PropertyTypeResource::make($this->property_type);
            }),
            "usability" => $this->whenLoaded('usability', function () {
                return UsabilityResource::make($this->usability);
            }),
            "company" => CompanyResource::make($this->company),
        ];

        return $data;
    }
}
