<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class SettledPaymentResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "invoice_id"                    =>     $this->id ?? null,
            "property_name"                 =>     $this->property_name() ?? null,
            "status"                        =>     $this->status ?? null,
            "total_amount"                  =>     $this->total ?? null,
            "remaining_amount"              =>     $this->remaining ?? null,
            "installment_amount"            =>     $this->invoice_schedule->installment_amount ?? null,
            "installment_service_amount"    =>     $this->invoice_schedule->installment_service_amount ?? null,
            "bill_number"                   =>     $this->invoice_schedule->bill_number ?? null,
            "total_bills"                   =>     $this->invoice_schedule->installment_count ?? null,
            "installment_date"              =>     $this->release_date ?? null,
            "due_date"                      =>     $this->due_date?? null,
            "type"                          =>     $this->invoice_type,
            "download_link"                 =>     url('invoices/' . $this->uuid . '/pdf/download'),
        ];
        return $data;
    }
}
