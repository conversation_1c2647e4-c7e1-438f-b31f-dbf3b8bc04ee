<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;

class LeaseMemberResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "lease_id" => $this->lease_id ?? null,
            "member_id" => $this->member_id ?? null,
            "member_type" => $this->member_type ?? null,
            "member_role" => $this->member_role ?? null,
            "bank_account" => $this->bank_account ?? null,
            "percentage" => $this->percentage ?? null,
            "member" => new AccountCustomerResource($this->memberWithTrashed),
        ];

        return $data;
    }
}
