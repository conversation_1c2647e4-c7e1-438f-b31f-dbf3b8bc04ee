<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FutureInstallmentResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id"                         =>      $this->id ?? null,
            "property_name"              =>      $this->lease->property->name,
            "installment_amount"         =>      $this->installment_amount ?? null,
            "installment_service_amount" =>      $this->installment_service_amount ?? null,
            "total_amount"               =>      $this->total_amount ?? null,
            "bill_number"                =>      $this->bill_number ?? null,
            "total_bills"                =>      $this->installment_count ?? null,
            "installment_date"           =>      $this->installment_date ?? null,
            "due_date"                   =>      $this->due_date?? null,
        ];

        return $data;
    }
}
