<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\app\Filament\Resources\AccountsResource;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Property\app\Resources\Api\PropertyResource;

class OwnerScheduleResource extends JsonResource
{

    public function toArray($request)
    {
        $data = [
            "id"                         =>   $this->id ?? null,
            "property"                   =>   new PropertyResource($this->lease?->property),
            "tenant"                     =>   new AccountCustomerResource($this->lease?->tenant?->member),
            "installment_amount"         =>   $this->installment_amount ?? null,
            "installment_service_amount" =>   $this->installment_service_amount ?? null,
            "total_amount"               =>   $this->total_amount ?? null,
            "bill_number"                =>   $this->bill_number ?? null,
            "total_bills"                =>   $this->installment_count ?? null,
            "installment_date"           =>   $this->installment_date ?? null,
            "status"                     =>   $this->invoice_id == null ? 'unpaid' : $this->invoice->status,
            "due_date"                   =>   $this->due_date?? null,
        ];

        return $data;
    }
}
