<?php

namespace Modules\Lease\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\app\Http\Resources\Api\CompanyResource;
use Modules\Property\app\Resources\Api\AttributeResource;
use Modules\Property\app\Resources\Api\PropertyTypeResource;
use Modules\Property\app\Resources\Api\PropertyUsabilityResource;


class ListLeaseUnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "lease_unit_id" => $this->whenPivotLoaded('lease_units', function (){
                return $this->pivot->id;
            }) ?? null,
            "name" => $this->name == null ? $this->property_type->name . '-' . $this->number : $this->name,
            "number" => $this->number,
            "image" => $this->getMediaImage('property_images'),
            "usability" => $this->whenLoaded('usability', function (){
                return PropertyUsabilityResource::make($this->usability);
            }) ,
            "property_type" => $this->whenLoaded('property_type', function (){
                return PropertyTypeResource::make($this->property_type);
            }) ,
            "attributes" => $this->whenLoaded('attributes', function (){
                return AttributeResource::collection($this->attributes);
            }) ,
            "company" => CompanyResource::make($this->company),
        ];

        return $data;
    }
}
