<?php

namespace Modules\Lease\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\LeaseContractPdfService;

class LeaseContractController extends Controller
{
    protected $pdfService;

    public function __construct(LeaseContractPdfService $pdfService)
    {
        $this->pdfService = $pdfService;
    }

    public function downloadPdf(Lease $record)
    {
        $lease = $record->load(['tenant', 'property', 'leaseUnits']);
        $pdf = $this->pdfService->generatePdf($lease);

        return $pdf->download("lease_contract_{$lease->id}.pdf");
    }
}
