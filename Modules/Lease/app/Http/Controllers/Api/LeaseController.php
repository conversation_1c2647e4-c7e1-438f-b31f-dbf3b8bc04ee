<?php

namespace Modules\Lease\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Invoice\Services\InvoiceScheduleService;
use Modules\Lease\app\Http\Requests\LeaseUnitServiceListRequest;
use Modules\Lease\app\Http\Resources\Api\LeaseResource;
use Modules\Lease\app\Http\Resources\Api\LeaseScheduleResource;
use Modules\Lease\app\Http\Resources\Api\LeaseUnitResource;
use Modules\Lease\app\Http\Resources\Api\OwnerScheduleResource;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Services\LeasePaymentService;
use Modules\Lease\Services\LeaseService;
use Modules\Lease\Services\LeaseScheduleService;
use Modules\Lease\app\Http\Resources\Api\BillsResource;
use Modules\Lease\app\Http\Resources\Api\FutureInstallmentResource;
use Modules\Lease\app\Models\LeaseMember;
use Illuminate\Pagination\Paginator;

class LeaseController extends ControllerAbstract
{
    protected string $jsonResourceClass = LeaseResource::class;
    protected string $leaseUnitResource = LeaseUnitResource::class;
    protected $LeaseScheduleService;
    protected $InvoiceScheduleService;

    public function __construct(LeaseService $service)
    {
        parent::__construct($service);
        $this->InvoiceScheduleService = new InvoiceScheduleService();
    }
    public function index(Request $request)
    {
        $this->with = ['property', 'units', 'leaseMembers', 'units.attributes', 'units.usability', 'units.property_type','property.brokers'];
        return parent::index($request);
    }

    public function show(int $id)
    {
        $this->with = ['property', 'units', 'leaseMembers', 'property.brokers', 'property.usability', 'property.property_type','property.brokers'];
        return parent::show($id);
    }

    public function leaseUnitServices(LeaseUnitServiceListRequest $request)
    {
        $this->with = ['leaseUnitServices', 'leaseUnitServices.service'];
        $this->filter['lease_id'] = $request->get('lease_id');
        $this->filter['unit_id'] = $request->get('unit_id');
        return ApiResponse::data(
            $this->leaseUnitResource::make($this->service->getUnitServices(
                $this->filter,
                $this->select,
                $this->with)
            )
        );
    }

    //todo remove it after testing
    public function testPaymentSchedule(){
        $payService = new LeasePaymentService();
        return ApiResponse::data($payService->generatePaymentSchedule(
            '2025-03-01',
            '2026-02-28',
            12000,
            0,
            1000,
            'repeated',
            'monthly'
        ));
    }
    //todo remove it after testing
    public function testIsValidPaidAmount(){
        $payService = new LeasePaymentService();
        return ApiResponse::data($payService->isPaidAmountValid(
            '2024-11-15',
            '2025-11-15',
            5001,
            'repeated',
            'monthly',
            60000,
        ));
    }
    //todo remove it after testing
    public function testLeaseBalance(){
        $payService = new LeasePaymentService();
        return ApiResponse::data($payService->getTotalLeaseBalance(
            '2023-11-01',
            '2025-10-31',
            'repeated',
            'annually',
            12000,
            [
                'unit_id_1' => [
                    'services' => [
                        '1' => [
                            'lease_service_amount' => 100,
                            'to_be_paid_amount' => null
                        ],
                        '2' => [
                            'lease_service_amount' => 100,
                            'to_be_paid_amount' => null
                        ],
                        '3' => [
                            'lease_service_amount' => 100,
                            'to_be_paid_amount' => null
                        ],
                    ],
                    'other_services' => []
                ],
                'unit_id_2' => [
                    'services' => [
                        '1' => [
                            'lease_service_amount' => 100,
                            'to_be_paid_amount' => null
                        ],
                    ],
                    'other_services' => [
                        '3' => [
                            'lease_service_amount' => 100,
                            'to_be_paid_amount' => null
                        ],
                    ]
                ],
            ],
        ));
    }
    //todo remove it after testing
    public function testStartScheduleDate(){
        $payService = new LeasePaymentService();
        return ApiResponse::data($payService->getStartScheduleDate(
            '2023-01-07',
            'repeated',
            'monthly',
        ));
    }

    public function getLeaseBills($lease_id)
    {
        $this->LeaseScheduleService = new LeaseScheduleService($lease_id);
        $limit = request()->get('limit', 10);
        $data = $this->LeaseScheduleService->getLeaseBills($limit);

        return response()->json([
            'status' => 'success',
            'data' => BillsResource::collection($data)
        ], 200);
    }

    public function getLeaseFutureSchedule($lease_id)
    {
        $this->LeaseScheduleService = new LeaseScheduleService($lease_id);
        $limit = request()->get('limit', 10);
        $data = $this->LeaseScheduleService->getLeaseFuturePayments($limit);

        return response()->json([
            'status' => 'success',
            'data' => FutureInstallmentResource::collection($data)
        ], 200);
    }

    public function getLeaseSchedule($lease_id)
    {
        $this->LeaseScheduleService = new LeaseScheduleService($lease_id);
        $limit = request()->get('limit', 10);
        $start_date = request()->get('from', null);
        $end_date = request()->get('to', null);
        $data = $this->LeaseScheduleService->getLeaseSchedule($limit , $start_date , $end_date);

        return response()->json([
            'status' => 'success',
            'data' => LeaseScheduleResource::collection($data)
        ], 200);
    }
    public function getLeasePastSchedule($lease_id)
    {
        $this->LeaseScheduleService = new LeaseScheduleService($lease_id);
        $limit = request()->get('limit', 10);
        $start_date = request()->get('from', null);
        $end_date = request()->get('to', null);
        $data = $this->LeaseScheduleService->getLeasePastSchedule($limit , $start_date , $end_date);

        return response()->json([
            'status' => 'success',
            'data' => BillsResource::collection($data)
        ], 200);
    }

    public function getOwnerSchedule()
    {
        $limit = request()->get('limit', 10); // Get 'limit' from the request, default to 10 if not provided
        $from = request()->get('from', null);
        $to = request()->get('to', null);
        $data = $this->InvoiceScheduleService->getOwnerSchedule($limit , $from  , $to);
        // Return the paginated data with the necessary pagination details
        return response()->json([
            'status' => 'success',
            'data' => OwnerScheduleResource::collection($data),
        ], 200);
    }

    public function deleteLease(Lease $lease)
    {
        try {
            $this->service->deleteLease($lease);

            return response()->json([
                'message' => 'Lease and related records deleted successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete lease.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

