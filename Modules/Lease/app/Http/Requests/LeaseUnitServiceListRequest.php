<?php

namespace Modules\Lease\app\Http\Requests;
use Illuminate\Foundation\Http\FormRequest;


class LeaseUnitServiceListRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'lease_id'   => 'required|exists:leases,id',
            'unit_id'   => 'required|exists:properties,id',
        ];
        return $rules;
    }
}
