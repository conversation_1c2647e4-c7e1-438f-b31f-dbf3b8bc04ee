<?php

namespace Modules\Lease\app\Http\Requests;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class LeaseAutoRenewalRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'lease_id' => [
                'required',
                'exists:leases,id',
                function ($attribute, $value, $fail) {
                    // Check if there is a pending request for this lease
                    $exists = DB::table('requests')
                        ->where('requestable_type', 'Modules\Lease\app\Models\Lease')
                        ->where('requestable_id', $value)
                        ->where('status', 'pending') // Adjust this to the actual status column for "pending"
                        ->exists();

                    if ($exists) {
                        $fail(__('A pending request already exists for this lease.'));
                    }
                },
            ],
        ];
    }
}
