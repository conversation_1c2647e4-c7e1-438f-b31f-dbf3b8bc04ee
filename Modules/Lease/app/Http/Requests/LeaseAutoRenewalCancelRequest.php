<?php

namespace Modules\Lease\app\Http\Requests;

use App\Helpers\LeaseSettingHelper;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Modules\Lease\Enums\LeaseSettingEnum;

class LeaseAutoRenewalCancelRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'lease_id' => [
                'required',
                'exists:leases,id',
                function ($attribute, $value, $fail) {
                    // Check if the lease exists with auto_renewal set to ON
                    $lease = DB::table('leases')
                        ->where('id', $value)
                        ->where('auto_renewal', 'on') // Adjust this to the actual value representing "ON"
                        ->first();

                    if (!$lease) {
                        $fail(__('The selected lease does not exist or auto-renewal is not enabled.'));
                        return;
                    }

                    // Validate against end_date minus notice_period days
                    $endDate = Carbon::parse($lease->end_date);
                    $noticePeriodDays = LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD); // Assuming 'notice_period' is the column name
                    $cutoffDate = $endDate->subDays($noticePeriodDays);

                    if (Carbon::now()->greaterThanOrEqualTo($cutoffDate)) {
                        $fail(__('The lease cannot be canceled as it is within the notice period.'));
                    }
                },
            ],
        ];
    }
}

