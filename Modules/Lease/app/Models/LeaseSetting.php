<?php

namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Models\BaseModel;

class LeaseSetting extends BaseModel
{
    use HasFactory;

    protected $guarded = ['id'];
    
    protected $casts = [
        'name' => 'array',   // JSON column for the 'name' field
        'value' => 'integer', // Use 'integer' if value is numeric
    ];
}
