<?php

namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Khaleds\Shared\Models\BaseModel;

class CommercialLeaseMeta extends BaseModel
{

    public $translatable = [];
    protected $table = 'commercial_lease_meta';

    protected $guarded = ['id'];

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }
}
