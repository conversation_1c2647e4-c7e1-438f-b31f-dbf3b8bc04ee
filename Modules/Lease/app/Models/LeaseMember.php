<?php

namespace Modules\Lease\app\Models;

use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\app\Models\BankAccount;
use Modules\Property\app\Models\Property;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Modules\Organization\app\Models\Organization;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;

class LeaseMember extends BaseModel
{
    use HasFactory,InteractsWithMedia;

    public $translatable = [];
    protected $table = 'lease_members';

    protected $guarded = ['id'];
    
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }
    public function member(): MorphTo
    {
        return $this->morphTo('member', 'memberable_type', 'member_id');
    }

    public function memberWithTrashed(): MorphTo
    {
        return $this->morphTo('member', 'memberable_type', 'member_id')
            ->withTrashed(function ($query, $morphClass) {
                if ($morphClass === Account::class) {
                    return $query->withTrashed();
                }
                return $query;
            });
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class, 'bank_account_id');
    }
    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }
}
