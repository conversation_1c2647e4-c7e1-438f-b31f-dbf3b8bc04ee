<?php

namespace Modules\Lease\app\Models;

use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Lease\Database\Factories\LeaseTermFactory;

class LeaseTerm extends BaseModel
{
    protected $guarded = ['id'];

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class);
    }
}
