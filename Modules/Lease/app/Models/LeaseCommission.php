<?php

namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Khaleds\Shared\Models\BaseModel;
use Modules\Account\app\Models\Account;

class LeaseCommission extends BaseModel
{

    public $translatable = [];
    protected $table = 'lease_commissions';

    protected $guarded = ['id'];

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'commission_paid_by');
    }
}
