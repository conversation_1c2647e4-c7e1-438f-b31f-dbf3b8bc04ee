<?php
namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use Modules\Property\app\Events\PropertyStatusChange;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;
use Modules\Service\app\Models\Service;

class LeaseService extends BaseModel
{
    public $translatable = ['name'];
    protected $table = 'lease_services';
    protected $guarded = ['id'];


    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class, 'service_id');
    }

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }
}
