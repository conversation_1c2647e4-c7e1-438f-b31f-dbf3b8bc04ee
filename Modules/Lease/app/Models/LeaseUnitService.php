<?php

namespace Modules\Lease\app\Models;

use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Service\app\Models\Service;

class LeaseUnitService extends BaseModel
{
    public $translatable = [];
    protected $table = 'lease_unit_services';
    protected $guarded = ['id'];

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class, 'service_id');
    }

    public function leaseUnit(): BelongsTo
    {
        return $this->belongsTo(LeaseUnit::class, 'lease_unit_id');
    }
}
