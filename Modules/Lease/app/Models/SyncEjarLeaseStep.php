<?php

namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Khale<PERSON>\Shared\Models\BaseModel;
use Modules\Lease\Enums\LeaseSyncStatus;
use Modules\Lease\Enums\LeaseSyncStep;

class SyncEjarLeaseStep extends BaseModel
{
    protected $guarded = ['id'];

    protected $translatable=['error_message'];

    protected $casts = [
        'step' => LeaseSyncStep::class,
        'status' => LeaseSyncStatus::class,
        'response_data' => 'array',
        'error_message' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class);
    }
}
