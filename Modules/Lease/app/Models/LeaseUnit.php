<?php
namespace Modules\Lease\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use Modules\Lease\Services\LeaseService;
use Modules\Property\app\Events\PropertyStatusChange;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;

class LeaseUnit extends BaseModel
{
    public $translatable = [];
    protected $table = 'lease_units';
    protected $guarded = ['id'];


    public function unit(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'unit_id');
    }

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class, 'lease_id');
    }
    public function leaseUnitServices(): HasMany
    {
        return $this->hasMany(LeaseUnitService::class, 'lease_unit_id')->whereHas('service', function ($query) {
            $query->where('is_fixed_lease', true);
        });
    }

    public function AllleaseUnitServices(): HasMany
    {
        return $this->hasMany(LeaseUnitService::class, 'lease_unit_id')->with('service');
    }

    public function otherLeaseUnitServices(): HasMany
    {
        return $this->hasMany(LeaseUnitService::class, 'lease_unit_id')->whereHas('service', function ($query) {
            $query->where('is_fixed_lease', false);
        });
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($lease) {
            event(new PropertyStatusChange(
                property: $lease->unit,
                newStatus: PropertyStatus::RESERVED
            ));
        });
    }

}
