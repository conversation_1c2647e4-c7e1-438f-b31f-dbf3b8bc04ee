<?php
namespace Modules\Lease\app\Jobs;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Modules\Company\app\Models\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\EjarIntegration\LeaseRetrieving\RetrieveLeases;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\RetrieveSingleLeaseService;
use Modules\Property\Services\RetrieveSinglePropertyService;
use Throwable;

class ProcessRetrievalLeases implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 1000; // 5 minutes

    public $tries = 10; // Set the retry attempts to 10

    protected array $processableLeases = [];
    protected array $includedData = [];
    protected Company $company;
    protected User $user;
    protected RetrieveLeases $leaseRetrieving;
    protected RetrieveSinglePropertyService $retrieveSinglePropertyService;
    protected RetrieveSingleLeaseService $retrieveSingleLeaseService;

    public function __construct(Company $company , User $user)
    {
        $this->company = $company;
        $this->user = $user;
        $this->leaseRetrieving = new RetrieveLeases($this->company);
        $this->retrieveSinglePropertyService = new RetrieveSinglePropertyService($this->company);
        $this->retrieveSingleLeaseService = new RetrieveSingleLeaseService($this->company);
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->retrieveLeasesFromEjar();
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Notification::make()
            ->title(__("Failed to complete retrieving process"))
            ->body(__("Failed to complete retrieving process, please try again later or contact support"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }

    public function getLeasesCount()
    {
        return $this->leaseRetrieving->getLeasesCount();
    }

    /**
     * @throws \Exception
     */
    public function retrieveLeasesFromEjar(): void
    {
        $size = $this->getLeasesCount();
        $perPage = 5;
        $totalPages = (int) ceil($size / $perPage);

        for ($page = 1; $page <= $totalPages; $page++) {
            $existingEjarIDs = $this->getCompanyLeases($this->company)->pluck('ejar_uuid')->toArray();
            $Retrievedleases = $this->leaseRetrieving->retrieveLeases($this->company , $this->user , $page , $perPage);
            foreach ($Retrievedleases as $lease) {
                $lease_uuid = $lease->Body->data->id;
                $invoices = $this->leaseRetrieving->getLeaseInvoices($lease_uuid)->Body;
                if (!in_array($lease_uuid, $existingEjarIDs)) {
                    $lease->Body->company_id = $this->company->id;
                    $lease->Body->broker_id = $this->user->id;

                    $this->processRetrieving($lease->Body , $invoices);
                }
            }
        }
    }

    protected function processRetrieving($Retrievedleases , $invoices): void
    {
        $this->processLeaseRetrieve($Retrievedleases , $invoices);
        $this->notifyAfterProcess();
    }

    public function processLeaseRetrieve($leaseData , $invoices): void
    {
        try {
            DB::beginTransaction();
            $lease = $this->retrieveSingleLeaseService->saveLeaseDetails($leaseData);
            $this->retrieveSingleLeaseService->saveLeaseMemebers($lease->id , $leaseData);
            $invoices = $this->retrieveSingleLeaseService->getLeaseSchedule($invoices , $leaseData , $lease);
            if($invoices !== false){
                RetrievalLogger::success($this->company->id, $lease->lease_number, __('Lease retrieved successfully'));
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error("Lease processing failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            RetrievalLogger::failed($this->company->id,$leaseData->Body->data->id,$e->getMessage(),$e->getTrace());
            $this->fail($e);
        }
    }

    protected function getCompanyLeases(Company $company): Builder
    {
        return Lease::where(['company_id' => $company->id]);
    }

    protected function notifyAllLeasesUpdated(): void
    {
        Notification::make()
            ->title(__("All Properties are updated"))
            ->body(__("All Properties are synced and retrieved from Ejar, You do not need to retrieve properties"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }

    protected function notifyAfterProcess(): void
    {
        $processCount = count($this->processableLeases);
        Notification::make()
            ->title(__("Retrieving {$processCount} Properties is Done from ejar, please see the retrieval log for more info"))
            ->body(__("Retrieve Properties are synced and retrieved from Ejar, if you faced any problem please contact us via support"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }
}
