<?php
namespace Modules\Lease\app\Jobs;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\EjarIntegration\LeaseRetrieving\RetrieveLeases;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Services\RetrieveSingleLeaseService;
use Modules\Property\Services\RetrieveSinglePropertyService;
use Throwable;

class ProcessSingleLeaseInRetrieve implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes

    protected $leaseData;
    protected Company $company;
    protected User $user;
    protected $count;
    protected $lease_number;
    protected RetrieveLeases $leaseRetrieving;
    protected RetrieveSingleLeaseService $retrieveSingleLeaseService;

    public function __construct(Company $company, User $user , $count , $lease_number)
    {
        $this->company = $company;
        $this->user = $user;
        $this->leaseRetrieving = new RetrieveLeases($this->company);
        $this->count = $count;
        $this->lease_number = $lease_number;
        $this->retrieveSingleLeaseService = new RetrieveSingleLeaseService($this->company);
    }

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle()
    {
        try {
            $lease = $this->processLeaseRetrieve();
            
            Log::info("Successfully processed lease", [
                'lease_id' => $lease->id,
                'lease_number' => $lease->lease_number
            ]);
            
            // Notify the user about successful retrieval
            $this->notifySuccess($lease->lease_number);
            
            return $lease;
        } catch (\Exception $e) {
            Log::error("Failed to process lease: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->notifyFailed($e->getMessage());
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Log::error("Job failed: " . $exception->getMessage(), [
            'trace' => $exception->getTraceAsString()
        ]);
        
        Notification::make()
            ->title(__("Failed to retrieve lease"))
            ->body(__("Failed to retrieve lease, please try again later or contact support"))
            ->danger()
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase($this->user)
            ->toDatabase();
    }

    /**
     * Process the lease data and save it to the database.
     *
     * @return Lease
     * @throws \Exception
     */
    public function processLeaseRetrieve(): Lease
    {
        try {
            DB::beginTransaction();
            
            $leaseData = $this->leaseRetrieving->retrieveLeaseByNumber( $this->lease_number, 1 , $this->count);
            $leaseData->Body->company_id = $this->company->id;
            $leaseData->Body->broker_id = $this->user->id;

            $lease_uuid = $leaseData->Body->data->id;
            $invoices = $this->leaseRetrieving->getLeaseInvoices($lease_uuid)->Body;
            $lease = $this->retrieveSingleLeaseService->saveLeaseDetails($leaseData->Body);
            $members = $this->retrieveSingleLeaseService->saveLeaseMemebers($lease->id , $leaseData->Body);
            $invoices = $this->retrieveSingleLeaseService->getLeaseSchedule($invoices , $leaseData->Body , $lease);

            DB::commit();
            
            // Log successful retrieval
            RetrievalLogger::success(
                $this->company->id, 
                $lease->lease_number, 
                __('Lease retrieved successfully')
            );
            
            return $lease;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Lease processing failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }


    /**
     * Notify the user about successful lease retrieval.
     *
     * @param string $leaseNumber
     * @return void
     */
    protected function notifySuccess(string $leaseNumber): void
    {
        Notification::make()
            ->title(__("Lease retrieved successfully"))
            ->body(__("Lease :number has been successfully retrieved", ['number' => $leaseNumber]))
            ->success()
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase($this->user)
            ->toDatabase();
    }

    /**
     * Notify the user about failed lease retrieval.
     *
     * @param string $message
     * @return void
     */
    protected function notifyFailed(string $message): void
    {
        Notification::make()
            ->title(__("Failed to retrieve lease"))
            ->body($message)
            ->danger()
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase($this->user)
            ->toDatabase();
    }
}