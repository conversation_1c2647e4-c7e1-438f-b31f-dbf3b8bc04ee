<?php

namespace Modules\Lease\database\seeders;

use Illuminate\Database\Seeder;
use Modules\Lease\app\Models\LeaseSetting;
use Modules\Lease\Enums\LeaseSettingEnum;

class LeaseSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $settings = [
            LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD => [
                'name' => [
                    'en' => 'Renewal Notice Period',
                    'ar' => 'فترة السماح لتتحكم في النحديد',
                ],
                'value' => 60,
            ],
            LeaseSettingEnum::DAYS_TO_REMIND_BEFORE_RENEWAL => [
                'name' => [
                    'en' => 'Days To Notify User Before Renewal',
                    'ar' => 'عدد الايام لتنبيه المستخدمين قبل التجديد',
                ],
                'value' => 30,
            ],
        ];

        foreach ($settings as $setting => $data) {
            $existingSetting = LeaseSetting::where('key', $setting)->first();

            if (!$existingSetting) {
                // Merge the key with the data, and ensure 'name' is saved as JSON
                $dataToInsert = array_merge($data, ['key' => $setting]);

                // Now create the setting in the database
                LeaseSetting::create($dataToInsert);
            }
        }
    }
}
