<?php

namespace Modules\Lease\database\seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseMember;
use Modules\Lease\app\Models\LeaseStep;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\app\Models\LeaseUnitService;
use Modules\Lease\Enums\EjarEnum;
use Modules\Property\app\Models\Property;

class LeaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            $first_prop = Property::where(['parent_id' => null, 'ejar_synced' => 1])->first();
            $second_prop = Property::find(16);
            if (empty($second_prop)) {
                $second_prop = Property::where(['parent_id' => null, 'ejar_synced' => 0])->orderByDesc('id')->first();
            }
            $lease_data = [
                [
                    'start_date' => '2024-10-10',
                    'end_date' => '2026-10-10',
                    'property_id' => $first_prop->id,
                    'lease_number' => '********',
                    'ejar_uuid' => time(),
                    'EJAR_registration_status' => 1,
                    'status' => 'draft',
                    'total_amount' => '100000.00',
                    'paid_amount' => '40000.00',
                    'due_amount' => '60000.00',
                    'rent_amount' => '2500.00',
                    'days_permitted' => 3,
                    'daily_penalty' => 3,
                    'payment_method' => 'online',
                    'payment_type' => 'repeated',
                    'payment_repeated_type' => 'monthly',
                    'start_scheduled_date' => '2024-11-01',
                    'created_by' => User::orderByDesc('id')->first()->id
                ],
                [
                    'start_date' => '2024-10-10',
                    'end_date' => '2026-10-10',
                    'property_id' => $second_prop->id,
                    'lease_number' => '20202020',
                    'status' => 'draft',
                    'total_amount' => '100000.00',
                    'paid_amount' => '40000.00',
                    'due_amount' => '60000.00',
                    'rent_amount' => '2500.00',
                    'days_permitted' => 3,
                    'daily_penalty' => 3,
                    'payment_method' => 'online',
                    'payment_type' => 'repeated',
                    'payment_repeated_type' => 'monthly',
                    'start_scheduled_date' => '2024-11-01',
                    'created_by' => User::orderByDesc('id')->first()->id
                ],
            ];
            $member_data = [
                [
                    'member_type' => 'individual',
                    'member_role' => 'lessor',
                    'member_id' => Account::first()->id,
                    'percentage' => 30,
                    'created_at' => '2024-10-21 11:35:36',
                ],
                [
                    'member_type' => 'individual',
                    'member_role' => 'tenant',
                    'member_id' => Account::find(7)?->id ?? Account::orderByDesc('id')->first()->id,
                    'created_at' => '2024-10-21 11:35:36',
                ]
            ];
            $lease_steps_data = [
                [
                    'step' => EjarEnum::INITIAL_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::PROPERTY_UNITS_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::SERVICES_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::TERMS_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::FINANCIAL_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::PARTIES_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::SIGNED_DOCUMENTS_STEP,
                    'done' => true,
                ],
                [
                    'step' => EjarEnum::SUBMIT_STEP,
                    'done' => true,
                ],
            ];
            $lease_unit_services = [
                [
                    'service_id' => 1,
                    'to_be_paid_per' => 'monthly',
                    'to_be_paid_by' => 'fixed_fee',
                    'lease_service_amount' => 0,
                ],
                [
                    'service_id' => 2,
                    'to_be_paid_per' => 'monthly',
                    'to_be_paid_by' => 'fixed_fee',
                    'lease_service_amount' => 0,
                ],
                [
                    'service_id' => 3,
                    'to_be_paid_per' => 'monthly',
                    'to_be_paid_by' => 'fixed_fee',
                    'lease_service_amount' => 0,
                ],
            ];
            DB::beginTransaction();
            foreach ($lease_data as $lease_item) {
                $lease = Lease::where(['lease_number' => $lease_item['lease_number']])->first();

                if (empty($lease)) {
                    //create lease
                    $lease = Lease::create($lease_item);
                    //create lease members
                    foreach ($member_data as $member) {
                        $member['lease_id'] = $lease->id;
                        LeaseMember::create($member);
                    }
                    //lease steps
                    if ($lease->EJAR_registration_status) {
                        foreach ($lease_steps_data as $lease_step) {
                            $lease_step['lease_id'] = $lease->id;
                            LeaseStep::create($lease_step);
                        }
                    }
                    //lease units
                    $units = Property::where(['parent_id' => $lease->property_id])->get();
                    //lease unit services
                    foreach ($units as $unit) {
                        $lease_unit = LeaseUnit::create(['lease_id' => $lease->id, 'unit_id' => $unit->id]);
                        foreach ($lease_unit_services as $lease_unit_service) {
                            $lease_unit_service['lease_unit_id'] = $lease_unit->id;
                            LeaseUnitService::create($lease_unit_service);
                        }
                    }
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }
}
