<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $newValues = implode("', '", LeaseAutoRenewalEnum::getValues());
            DB::statement("ALTER TABLE leases MODIFY COLUMN auto_renewal ENUM('$newValues')");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $oldValues = implode("', '", [LeaseAutoRenewalEnum::OFF , LeaseAutoRenewalEnum::ON]);
            DB::statement("ALTER TABLE leases MODIFY COLUMN auto_renewal ENUM('$oldValues')");
        });
    }
};
