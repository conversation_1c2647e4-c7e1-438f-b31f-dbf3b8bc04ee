<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->boolean('is_organization')->default(0);
            $table->string('account_identity')->nullable();
            $table->string('account_identity_type')->nullable();
            $table->string('cr_number')->nullable();
            $table->string('registration_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->dropColumn('is_organization');
            $table->dropColumn('account_identity');
            $table->dropColumn('account_identity_type');
            $table->dropColumn('cr_number');
            $table->dropColumn('registration_date');
        });
    }
};
