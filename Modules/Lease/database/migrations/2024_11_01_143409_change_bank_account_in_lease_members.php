<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            if (Schema::hasColumn('lease_members', 'bank_account')) {
                $table->dropColumn('bank_account');
            }
            $table->foreignId('bank_account_id')->nullable()->constrained()->references('id')->on('bank_accounts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->dropForeign(['bank_account_id']);
            $table->dropColumn('bank_account_id');
            // Restore the old column
            $table->string('bank_account')->nullable();
        });
    }
};
