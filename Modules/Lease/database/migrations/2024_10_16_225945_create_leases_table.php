<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeasePaymentEnum;
use Modules\Lease\Enums\LeaseTypesEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leases', function (Blueprint $table) {
            $table->id();
            $table->date('start_date');
            $table->date('end_date');
            $table->unsignedBigInteger('property_id');
            $table->foreign('property_id')->references('id')->on('properties')->onDelete('cascade')->onUpdate('cascade');
            $table->string('lease_number')->nullable();
            $table->enum('lease_type', LeaseTypesEnum::getLeaseTypes())->default(LeaseTypesEnum::RESIDENTIAL);
            $table->string('lease_sub_type')->default('main')->nullable();
            $table->integer('version')->default(1)->nullable();
            $table->integer('previous_version_contract_id')->nullable();
            $table->foreignId('main_contract_id')->nullable()->constrained()->references('id')->on('leases')->cascadeOnDelete()->cascadeOnUpdate();
            $table->boolean('EJAR_registration_status')->default(0);
            $table->enum('status', LeaseEnum::getLeaseStatus())->nullable();
            $table->decimal('total_amount')->nullable();
            $table->decimal('paid_amount')->nullable();
            $table->decimal('due_amount')->nullable();
            $table->decimal('rent_amount')->nullable();
            $table->integer('days_permitted')->default(0);
            $table->integer('daily_penalty')->default(0);
            $table->foreignId('created_by')->constrained()->references('id')->on('users')->cascadeOnDelete()->cascadeOnUpdate();
            $table->enum('payment_method',LeasePaymentEnum::getLeasePaymentMethodTypes())->nullable();
            $table->enum('payment_type', LeasePaymentEnum::getLeasePaymentTypes())->nullable();
            $table->enum('payment_repeated_type', LeasePaymentEnum::getLeasePaymentRepeatedTypes())->nullable();
            $table->date('start_scheduled_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
            //todo handle commission data
            //todo handle notification data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leases');
    }
};
