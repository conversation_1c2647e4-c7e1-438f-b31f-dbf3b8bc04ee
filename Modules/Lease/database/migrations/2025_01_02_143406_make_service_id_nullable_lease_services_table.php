<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_services', function (Blueprint $table) {
            $table->unsignedBigInteger('service_id')->nullable()->change(); // Make 'service_id' nullable
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_services', function (Blueprint $table) {
            $table->unsignedBigInteger('service_id')->nullable(false)->change(); // Revert 'service_id' to not nullable
        });
    }
};
