<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove the columns from the leases table
        Schema::table('leases', function (Blueprint $table) {
            $table->dropColumn('engineering_supervision');
            $table->dropColumn('unit_finishing');
            $table->dropColumn('waste_removal');
        });

        // Create the commercial_lease_meta table
        Schema::create('commercial_lease_meta', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lease_id'); // Foreign key to leases table
            $table->integer('engineering_supervision')->default(0);
            $table->integer('unit_finishing')->default(0);
            $table->integer('waste_removal')->default(0);
            $table->timestamps();

            // Add foreign key constraint
            $table->foreign('lease_id')->references('id')->on('leases')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the commercial_lease_meta table
        Schema::dropIfExists('commercial_lease_meta');

        // Add the columns back to the leases table
        Schema::table('leases', function (Blueprint $table) {
            $table->integer('engineering_supervision')->default(0);
            $table->integer('unit_finishing')->default(0);
            $table->integer('waste_removal')->default(0);
        });
    }
};