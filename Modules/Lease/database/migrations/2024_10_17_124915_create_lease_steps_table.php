<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\EjarEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lease_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lease_id')->constrained()->references('id')->on('leases')->cascadeOnDelete()->cascadeOnUpdate();
            $table->enum('step', EjarEnum::getEjarContractSteps());
            $table->boolean('done')->default(0);
            $table->string('failure')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lease_steps');
    }
};
