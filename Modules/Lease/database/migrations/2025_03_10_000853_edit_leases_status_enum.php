<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        DB::statement("ALTER TABLE leases MODIFY COLUMN status ENUM(
            'draft',
            'published',
            'reserved',
            'terminate_request',
            'terminated',
            'close_request',
            'closed',
            'near_to_expire',
            'ended',
            'renewed'
        ) DEFAULT 'draft'");
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        DB::statement("ALTER TABLE leases MODIFY COLUMN status ENUM(
            'draft',
            'published',
            'terminated',
            'terminate_request',
            'closed',
            'close_request',
            'ended'
        ) DEFAULT 'draft'");
    }
};