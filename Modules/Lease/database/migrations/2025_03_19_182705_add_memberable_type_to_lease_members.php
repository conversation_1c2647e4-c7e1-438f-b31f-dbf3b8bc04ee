<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Account\app\Models\Account;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Organization\app\Models\Organization;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->string('memberable_type')->after('id');
        });

        $leaseMembers = DB::table('lease_members')->get();
        
        foreach ($leaseMembers as $member) {
            $memberableType = null;
            
            if (isset($member->member_type)) {
                if ($member->member_type === LeaseMemberTypesEnum::INDIVIDUAL_TYPE) {
                    $memberableType = Account::class;
                } elseif ($member->member_type === LeaseMemberTypesEnum::ORGANIZATION_TYPE) {
                    $memberableType = Organization::class;
                }
            }
            
            if ($memberableType) {
                DB::table('lease_members')
                    ->where('id', $member->id)
                    ->update(['memberable_type' => $memberableType]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            //
        });
    }
};
