<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\LeasePaymentEnum;
use Modules\Service\Enums\ServicePaymentEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->enum('to_be_paid_by', ServicePaymentEnum::getServicePaymentTypes())->nullable()->after('service_id');
            $table->enum('to_be_paid_per', LeasePaymentEnum::getLeasePaymentRepeatedTypes())->nullable()->after('service_id');
            $table->integer('number_of_service')->default(1)->after('service_id');
            $table->decimal('to_be_paid_amount')->nullable()->after('service_id');
            $table->decimal('meter_current_reading')->nullable()->after('service_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->dropColumn('to_be_paid_by');
            $table->dropColumn('to_be_paid_per');
            $table->dropColumn('number_of_service');
            $table->dropColumn('to_be_paid_amount');
            $table->dropColumn('meter_current_reading');
        });
    }
};
