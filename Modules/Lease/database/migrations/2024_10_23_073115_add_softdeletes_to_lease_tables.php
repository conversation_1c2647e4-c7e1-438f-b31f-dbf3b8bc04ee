<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_units', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->softDeletes();
        });
        Schema::table('lease_steps', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_units', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
        Schema::table('lease_steps', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
