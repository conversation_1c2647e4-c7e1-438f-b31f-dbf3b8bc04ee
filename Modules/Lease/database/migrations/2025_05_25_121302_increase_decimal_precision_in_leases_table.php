<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->decimal('total_amount', 12, 2)->change();
            $table->decimal('due_amount', 12, 2)->change();
            $table->decimal('paid_amount', 12, 2)->change();
            $table->decimal('rent_amount', 12, 2)->change();
            $table->decimal('total_services_amount', 12, 2)->change();
            $table->decimal('insurance_amount', 12, 2)->change();
            $table->decimal('retainer_amount', 12, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->decimal('total_amount', 8, 2)->change();
            $table->decimal('due_amount', 8, 2)->change();
            $table->decimal('paid_amount', 8, 2)->change();
            $table->decimal('rent_amount', 8, 2)->change();
            $table->decimal('total_services_amount', 8, 2)->change();
            $table->decimal('insurance_amount', 8, 2)->change();
            $table->decimal('retainer_amount', 8, 2)->nullable()->change();
        });
    }
};