<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lease_commissions', function (Blueprint $table) {
            $table->id();
            $table->decimal('commission_amount')->default(0);
            $table->decimal('commission_percentage')->nullable();
            $table->enum('commission_type', \Modules\Lease\Enums\LeaseCommissionTypesEnum::getLeaseCommissionTypes())->nullable();
            $table->foreignId('commission_paid_by')->nullable()->references('id')->on('accounts');
            $table->foreignId('lease_id')->references('id')->on('leases')->cascadeOnDelete()->cascadeOnUpdate();
            $table->date('commission_due_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lease_commissions');
    }
};
