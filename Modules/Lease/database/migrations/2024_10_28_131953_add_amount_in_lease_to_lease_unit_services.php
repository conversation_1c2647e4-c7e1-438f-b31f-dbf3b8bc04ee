<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->decimal('lease_service_amount')->after('service_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->dropColumn('lease_service_amount');
        });
    }
};
