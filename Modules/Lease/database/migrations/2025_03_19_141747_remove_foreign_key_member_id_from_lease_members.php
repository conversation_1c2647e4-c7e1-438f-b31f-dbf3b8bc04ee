<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            $table->dropForeign(['member_id']);
        });
        $leaseMembers = DB::table('lease_members')->get();
    
        foreach ($leaseMembers as $member) {
            if (isset($member->member_type) && $member->member_type === LeaseMemberTypesEnum::ORGANIZATION_TYPE) {
                try {
                    $organizationId = null;
                    
                    // Check if organization already exists with the same unified_number
                    $existingOrg = DB::table('organizations')
                        ->where('unified_number', $member->cr_number)
                        ->first();
                    
                    if (!$existingOrg) {
                        // Create new organization record
                        $organizationId = DB::table('organizations')->insertGetId([
                            'name' => $member->organization_name,
                            'unified_number' => $member->cr_number,
                            'ownership_document_number' => $member->ownership_document_number,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    } else {
                        $organizationId = $existingOrg->id;
                    }
                    
                    if ($organizationId) {
                        // Update the member_id to point to the organization
                        DB::table('lease_members')
                            ->where('id', $member->id)
                            ->update([
                                'member_id' => $organizationId,
                            ]);
                    }
                } catch (\Exception $e) {
                    \Log::error('Failed to migrate lease member organization record: ' . $e->getMessage());
                }
            }
        }
        Schema::table('lease_members', function (Blueprint $table) {
            $table->dropColumn([
                'organization_name',
                'account_identity',
                'account_identity_type',
                'cr_number',
                'ownership_document_number'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_members', function (Blueprint $table) {
            //
        });
    }
};
