<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lease_terms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lease_id')->constrained()->references('id')->on('leases')->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('key',255);
            $table->string('value',255);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lease_terms');
    }
};
