<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Property\Enums\EjarSyncStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->dropColumn('EJAR_registration_status'); // Replace with the actual column name
            $table->enum('ejar_sync_status', EjarSyncStatus::getOptions())
                  ->nullable()
                  ->default(EjarSyncStatus::NOT_SYNCED->value);
            $table->timestamp('last_synced_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->dropColumn(['ejar_sync_status', 'last_synced_at']);
            $table->string('EJAR_registration_status')->nullable();
        });
    }
};
