<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Lease\Enums\LeaseMemberTypesEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lease_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lease_id')->constrained()->references('id')->on('leases')->cascadeOnDelete()->cascadeOnUpdate();
            $table->enum('member_type', LeaseMemberTypesEnum::getLeaseMemberTypes());
            $table->enum('member_role', LeaseMemberTypesEnum::getLeaseMemberRoles());
            $table->foreignId('member_id')->constrained()->references('id')->on('accounts')->cascadeOnDelete()->cascadeOnUpdate();;
            $table->string('bank_account')->nullable();
            $table->decimal('percentage')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lease_members');
    }
};
