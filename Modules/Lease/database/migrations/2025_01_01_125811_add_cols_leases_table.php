<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->integer('engineering_supervision')->default(0);
            $table->integer('unit_finishing')->default(0);
            $table->integer('waste_removal')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->dropColumn('engineering_supervision');
            $table->dropColumn('unit_finishing');
            $table->dropColumn('waste_removal');
        });
    }
};
