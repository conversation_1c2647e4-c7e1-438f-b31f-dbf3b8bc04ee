<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //unit_number => unit_id
        DB::statement("
            CREATE OR REPLACE VIEW tenant_units_view AS
            SELECT
                l.id as lease_id,
                lu.unit_id,
                lm.member_id,
                l.status as lease_status,
                u.number as unit_number,
                a.first_name as account_name,
                lm.member_role as member_role
            FROM leases l
            JOIN lease_units lu ON lu.lease_id = l.id
            JOIN properties u ON u.id = lu.unit_id
            JOIN lease_members lm ON lm.lease_id = l.id
            JOIN accounts a ON a.id = lm.member_id
            WHERE l.status IN ('published', 'terminate_request')
                AND lm.member_role IN ('tenant', 'tenant_representer')
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS tenant_units_view');
    }
};
