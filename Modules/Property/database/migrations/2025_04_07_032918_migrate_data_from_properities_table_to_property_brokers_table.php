<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class MigrateDataFromProperitiesTableToPropertyBrokersTable extends Migration
{
    /**
     * Run the migration.
     *
     * @return void
     */
    public function up()
    {
        $properties = DB::table('properties')
            ->whereNotNull('broker_id')
            ->select('id', 'broker_id')
            ->get();

        // Insert records into property_brokers table
        foreach ($properties as $property) {
            // Check if the relationship already exists to avoid duplicates
            $exists = DB::table('property_brokers')
                ->where('property_id', $property->id)
                ->where('broker_id', $property->broker_id)
                ->exists();

            if (!$exists) {
                DB::table('property_brokers')->insert([
                    'property_id' => $property->id,
                    'broker_id' => $property->broker_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migration.
     *
     * @return void
     */
    public function down()
    {
        $propertyBrokers = DB::table('property_brokers')
            ->select('property_id', 'broker_id')
            ->get();

        // If you removed the broker_id column, you need to add it back first
        if (!Schema::hasColumn('properties', 'broker_id')) {
            Schema::table('properties', function (Blueprint $table) {
                $table->unsignedBigInteger('broker_id')->nullable()->after('description');
            });
        }

        // Update properties table with broker_id values
        foreach ($propertyBrokers as $relation) {
            DB::table('properties')
                ->where('id', $relation->property_id)
                ->update(['broker_id' => $relation->broker_id]);
        }
    }
}