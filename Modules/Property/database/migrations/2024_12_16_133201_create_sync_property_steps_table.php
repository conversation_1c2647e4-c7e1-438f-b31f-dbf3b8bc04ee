<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_property_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->cascadeOnDelete();
            $table->enum('step', \Modules\Property\Enums\PropertySyncStep::getOptions())->nullable()->default(null);
            $table->enum('status', \Modules\Property\Enums\PropertySyncStatus::getOptions()); // pending, processing, completed, failed
            $table->text('error_message')->nullable();
            $table->json('response_data')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_property_steps');
    }
};
