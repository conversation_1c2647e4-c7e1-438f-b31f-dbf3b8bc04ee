<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {


        Schema::create('properties', function (Blueprint $table) {
            $table->id();
            $table->json('name');
            $table->json('description')->nullable();
            $table->foreignId('broker_id')->references('id')->on('users')->cascadeOnUpdate()->cascadeOnDelete();
            $table->float('total_price');
            $table->text('address')->nullable();
            $table->float('lat')->nullable();
            $table->float('lng')->nullable();
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->string('region')->nullable();
            $table->string('street')->nullable();
            $table->string('building_date');
            $table->string('number');
            $table->string('postal_code');
            $table->foreignId('property_type_id')->nullable()->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('parent_id')->nullable()->constrained()->references('id')->on('properties')->cascadeOnDelete()->cascadeOnUpdate();
            $table->boolean('is_active')->default(1);
            $table->string('status')->default('New');
            $table->boolean('is_available')->default(1);
            $table->foreignId('usability_id')->nullable()->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->softDeletes();

            $table->timestamps();
        });


        Schema::create('property_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('attribute_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('value');
            $table->softDeletes();

            $table->timestamps();

        });



        Schema::create('property_amenities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('amenity_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->softDeletes();

            $table->timestamps();

        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
        Schema::dropIfExists('types');
        Schema::dropIfExists('property_attribute');
        Schema::dropIfExists('amenities');
        Schema::dropIfExists('attributes');
        Schema::dropIfExists('properties');
        Schema::dropIfExists('property_amenities');

    }
};
