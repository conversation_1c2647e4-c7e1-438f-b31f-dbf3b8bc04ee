<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Property\Enums\PropertySyncStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sync_property_steps', function (Blueprint $table) {
            $table->enum('status', PropertySyncStatus::getOptions())->change(); // pending, processing, completed, failed, need_sync
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Change any new status to something safe before modifying enum
        \Modules\Property\app\Models\SyncPropertyStep::query()
            ->where('status', PropertySyncStatus::NEED_SYNC->value)
        ->update(['status' => PropertySyncStatus::PENDING->value]);

        Schema::table('sync_property_steps', function (Blueprint $table) {
            $table->enum('status', [
                PropertySyncStatus::PENDING->value,
                PropertySyncStatus::PROCESSING->value,
                PropertySyncStatus::COMPLETED->value,
                PropertySyncStatus::FAILED->value
            ])->change(); // pending, processing, completed, failed
        });
    }
};
