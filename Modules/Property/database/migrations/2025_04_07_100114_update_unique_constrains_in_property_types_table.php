<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('property_types', function (Blueprint $table) {
            //drop the existing unique constraint on 'key'
            $table->dropUnique(['key']);
            // Then add a new composite unique constraint
            $table->unique(['key', 'property_type']);
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('property_types', function (Blueprint $table) {
            // Revert changes - drop composite constraint and add back single column constraint
            $table->dropUnique(['key', 'property_type']);
            $table->unique(['key']);
        });
    }
};
