<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('usabilities', function (Blueprint $table) {
            $table->string('key')->unique()->nullable()->after('icon');
            $table->boolean('from_ejar')->default(false)->after('key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('usabilities', function (Blueprint $table) {
            $table->dropColumn('key');
            $table->dropColumn('from_ejar');
        });
    }
};
