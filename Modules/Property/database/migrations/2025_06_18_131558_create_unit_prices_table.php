<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Modules\Property\Enums\UnitPeriodType;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained('properties')->cascadeOnDelete();
            $table->enum('period_type', UnitPeriodType::getPeriodTypes());
            $table->decimal('price', 10, 2)->nullable(); 
            $table->decimal('annual_price', 10, 2)->nullable(); 

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_prices');
    }
};
