<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->string('ejar_uuid')->nullable()->after('name');
            $table->date('reception_date')->nullable()->after('building_date');
            $table->boolean('ejar_synced')->default(0)->after('ejar_uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->dropColumn('ejar_uuid');
            $table->dropColumn('reception_date');
            $table->dropColumn('ejar_synced');
        });
    }
};
