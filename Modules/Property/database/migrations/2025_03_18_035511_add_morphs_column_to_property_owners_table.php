<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('property_owners', function (Blueprint $table) {
            $table->string('ownerable_type')->after('id');
            $table->unsignedBigInteger('ownerable_id')->after('ownerable_type');
            $table->index(['ownerable_type', 'ownerable_id']);
        });

        $propertyOwners = DB::table('property_owners')->get();
        
        foreach ($propertyOwners as $owner) {
            if (isset($owner->is_organization) && $owner->is_organization) {
                try {
                    $organizationId = null;
                    
                    $existingOrg = DB::table('organizations')
                        ->where('unified_number', $owner->cr_number)
                        ->first();
                    
                    if (!$existingOrg) {
                        $organizationId = DB::table('organizations')->insertGetId([
                            'name' => $owner->organization_name,
                            'unified_number' => $owner->cr_number,
                            'ownership_document_number' => $owner->ownership_document_number,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    } else {
                        $organizationId = $existingOrg->id;
                    }
                    
                    if ($organizationId) {
                        DB::table('property_owners')
                            ->where('id', $owner->id)
                            ->update([
                                'ownerable_type' => 'Modules\\Organization\\app\\Models\\Organization',
                                'ownerable_id' => $organizationId,
                            ]);
                    }
                } catch (\Exception $e) {
                    \Log::error('Failed to migrate organization record: ' . $e->getMessage());
                }
            } else if (isset($owner->user_id) && !$owner->is_organization) {
                DB::table('property_owners')
                    ->where('id', $owner->id)
                    ->update([
                        'ownerable_type' => 'Modules\\Account\\app\\Models\\Account',
                        'ownerable_id' => $owner->user_id,
                    ]);
            }
        }

        Schema::table('property_owners', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropColumn(['user_id', 'is_organization', 'organization_name', 'cr_number', 'ownership_document_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('property_owners', function (Blueprint $table) {
            //
        });
    }
};
