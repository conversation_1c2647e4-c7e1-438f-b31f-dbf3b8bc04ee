<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('property_brokers', function (Blueprint $table) {
            
            $table->timestamp('delete_time')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('property_brokers', function (Blueprint $table) {
            // Remove the custom delete_time column
            $table->dropColumn('delete_time');
        });
    }
};