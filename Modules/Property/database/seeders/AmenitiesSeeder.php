<?php

namespace Modules\Property\database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Http\File;
use Illuminate\Support\Facades\DB;
use Modules\Property\app\Models\Amenities;

class AmenitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = [
            [
                'name' => [
                    'ar' => 'مسبح',
                    'en' => 'Swimming Pool',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-172276891866af5e1618bdb.png',
                'key' => 'swimming_pool'
            ],
            [
                'name' => [
                    'ar' => 'نادي رياضي',
                    'en' => 'GYM',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-172276896266af5e4217808.png',
                'key' => 'gym'
            ],
            [
                'name' => [
                    'ar' => 'مواقف سيارات',
                    'en' => 'Parking Spaces',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276899066af5e5e67d72.png',
                'key' => 'parking_spaces'
            ],
            [
                'name' => [
                    'ar' => 'مغسل ملابس',
                    'en' => 'Laundry',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-1719501430667d82769664c.png',
                'key' => 'laundry'
            ],
            [
                'name' => [
                    'ar' => 'خدمة حراسة',
                    'en' => 'security service',
                ],
                'amenities_category_id' => 2,
                'icon_file' => 'file-1719501786667d83da36661.png',
                'key' => 'security_service'
            ],
            [
                'name' => [
                    'ar' => 'مصاعد',
                    'en' => 'Elevators',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276932566af5fad3cd59.png',
                'key' => 'elevators'
            ],
            [
                'name' => [
                    'ar' => 'خدمة نقل',
                    'en' => 'Transfer service',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276951666af606ca0ea3.png',
                'key' => 'transfer_service'
            ],
            [
                'name' => [
                    'ar' => 'دكان بقالة',
                    'en' => 'Grocery store',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-1719502713667d87799b43e.png',
                'key' => 'grocery_store'
            ],
            [
                'name' => [
                    'ar' => 'ملعب أطفال',
                    'en' => 'children\'s playground',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-1719502960667d8870ad7ee.png',
                'key' => 'childrens_playground'
            ],
            [
                'name' => [
                    'ar' => 'مداخل أمن',
                    'en' => 'security entries',
                ],
                'amenities_category_id' => 2,
                'icon_file' => 'file-1719503085667d88ed611e8.png',
                'key' => 'security_entries'
            ],
            [
                'name' => [
                    'ar' => 'معلب كرة سلة',
                    'en' => 'basketball court',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-1719503164667d893c09b83.png',
                'key' => 'basketball_court'
            ],
            [
                'name' => [
                    'ar' => 'ملعب كرة المضرب',
                    'en' => 'Tennis court',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-1719503240667d89886ce41.png',
                'key' => 'tennis_court'
            ],
            [
                'name' => [
                    'ar' => 'مطعم وجبات خفيفة',
                    'en' => 'Cafeteria',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-1719503322667d89da14ca9.png',
                'key' => 'cafeteria'
            ],
            [
                'name' => [
                    'ar' => 'حضانة أطفال',
                    'en' => 'Baby nursery',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-1719503490667d8a82ecac8.png',
                'key' => 'baby_nursery'
            ],
            [
                'name' => [
                    'ar' => 'صالة ألعاب',
                    'en' => 'Games room',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-1719504360667d8de8582c3.png',
                'key' => 'games_room'
            ],
            [
                'name' => [
                    'ar' => 'ملعب كرة قدم',
                    'en' => 'Football yard',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-1719503693667d8b4d042ad.png',
                'key' => 'football_yard'
            ],
            [
                'name' => [
                    'ar' => 'ملعب كرة الطائرة',
                    'en' => 'Volleyball court',
                ],
                'amenities_category_id' => 1,
                'icon_file' => 'file-1719503754667d8b8a89aaf.png',
                'key' => 'volleyball_court'
            ],
            [
                'name' => [
                    'ar' => 'قاعة مناسبات',
                    'en' => 'Banquet hall',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-1719503836667d8bdc7dc4b.png',
                'key' => 'banquet_hall'
            ],
            [
                'name' => [
                    'ar' => 'شبكة انترنت',
                    'en' => 'WiFi',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276901966af5e7bf25c7.png',
                'key' => 'wifi'
            ],
            [
                'name' => [
                    'ar' => 'تدفئة مركزية',
                    'en' => 'Central Heating',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276906366af5ea7af161.png',
                'key' => 'central_heating'
            ],
            [
                'name' => [
                    'ar' => 'مكيف',
                    'en' => 'Air Conditioning',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276910966af5ed5e4117.png',
                'key' => 'air_conditioning'
            ],
            [
                'name' => [
                    'ar' => 'يسمح بالحيوانات الاليفة',
                    'en' => 'Pet Friendly',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276917366af5f156f2a4.png',
                'key' => 'pet_friendly'
            ],
            [
                'name' => [
                    'ar' => 'حدائق',
                    'en' => 'Garden Area',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-172276925366af5f65eec01.png',
                'key' => 'garden_area'
            ],
            [
                'name' => [
                    'ar' => 'شرفة',
                    'en' => 'Balcony',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276928066af5f806445c.png',
                'key' => 'balcony'
            ],
            [
                'name' => [
                    'ar' => 'منطقة شواء',
                    'en' => 'BBQ Area',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-172276937866af5fe27f37d.png',
                'key' => 'bbq_area'
            ],
            [
                'name' => [
                    'ar' => 'مدفأة',
                    'en' => 'Fireplace',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276940066af5ff8d001f.png',
                'key' => 'fireplace'
            ],
            [
                'name' => [
                    'ar' => 'الألواح الشمسية',
                    'en' => 'Solar Panels',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276947066af603e87cbc.png',
                'key' => 'solar_panels'
            ],
            [
                'name' => [
                    'ar' => 'حوض استحمام ساخن',
                    'en' => 'Hot Tub',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-172276954366af608784f06.png',
                'key' => 'hot_tub'
            ],
            [
                'name' => [
                    'ar' => 'ساونا',
                    'en' => 'Sauna',
                ],
                'amenities_category_id' => 4,
                'icon_file' => 'file-172276957666af60a865e8d.png',
                'key' => 'sauna'
            ],
            [
                'name' => [
                    'ar' => 'مساحة تخزينية فالقبو',
                    'en' => 'Basement Storage',
                ],
                'amenities_category_id' => 3,
                'icon_file' => 'file-172276960066af60c012ea0.png',
                'key' => 'basement_storage'
            ],
        ];

        foreach ($amenities as $amenityData) {
            $existingAmenity = Amenities::where(function ($query) use ($amenityData) {
                $query->where(DB::raw("JSON_EXTRACT(name, '$.en')"), $amenityData['name']['en'])
                    ->orWhere(DB::raw("JSON_EXTRACT(name, '$.ar')"), $amenityData['name']['ar']);
            })->first();

            if (!$existingAmenity) {
                $amenity = Amenities::create([
                    'name' => $amenityData['name'],
                    'key' => $amenityData['key'],
                    'amenities_category_id' => $amenityData['amenities_category_id'],
                ]);

                // Handle icon upload using Spatie Media Library
                if (isset($amenityData['icon_file']) && file_exists(public_path('images/amenities/' . $amenityData['icon_file']))) {
                    $file = new File(public_path('images/amenities/' . $amenityData['icon_file']));
                    $amenity->addMedia($file)->preservingOriginal()->toMediaCollection('amenities');
                    unset($amenityData['icon_file']);
                }
            } elseif (is_null($existingAmenity->key)) {
                $existingAmenity->key = $amenityData['key'];
                $existingAmenity->save();
            }
        }

    }
}
