<?php

namespace Modules\Property\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Http\File;
use Modules\Property\app\Models\Usability;

class UsabilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $usabilities = [
            [
                'name' => [
                    'ar' => 'سكني أفراد',
                    'en' => 'Residential Singles',
                ],
                'icon_file' => 'file-172276879266af5d987d8e7.png',
                'key' => 'residential_singles'
            ],
            [
                'name' => [
                    'ar' => 'تجاري',
                    'en' => 'Commercial',
                ],
                'icon_file' => 'file-172276883566af5dc3c1485.png',
                'key' => 'commercial'
            ],
            [
                'name' => [
                    'ar' => 'سكني - تجاري',
                    'en' => 'Mixed',
                ],
                'icon_file' => 'file-172276885366af5dd55be2a.png',
                'key' => 'mixed'
            ],
            [
                'name' => [
                    'ar' => 'سكني عائلات',
                    'en' => 'Residential Families',
                ],
                'icon_file' => 'file-172276876766af5d7f4820c.png',
                'key' => 'residential_families'
            ],
            [
                'name' => [
                    'ar' => 'السكن الجماعي',
                    'en' => 'Communal Housing',
                ],
                'icon_file' => 'file-1719760872668177e8bc03c.png',
                'key' => 'communal_housing'
            ],
        ];

        foreach ($usabilities as $usabilityData) {
            $usability = Usability::firstOrNew([
                'name->en' => $usabilityData['name']['en']
            ]);

            $usability->name = $usabilityData['name'];
            $usability->key = $usabilityData['key'];
            $usability->save();

            if (isset($usabilityData['icon_file']) && !empty($usabilityData['icon_file'])) {
                $iconPath = public_path('images/usability/' . $usabilityData['icon_file']);

                if (file_exists($iconPath)) {
                    // Check if media already exists
                    if (!$usability->media()->where('file_name', $usabilityData['icon_file'])->exists()) {
                        $file = new File($iconPath);
                        $usability->addMedia($file)->preservingOriginal()->toMediaCollection('usability');
                    }
                } else {
                    \Log::warning('Usability icon file does not exist: ' . $iconPath);
                }
            }
        }
    }
}
