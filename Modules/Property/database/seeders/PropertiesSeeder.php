<?php

namespace Modules\Property\database\seeders;

use App\Models\Document;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\PropertyAmenity;
use Modules\Property\app\Models\PropertyAttribute;
use Modules\Property\app\Models\PropertyOwners;

class PropertiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            $data = [
                [
                    'name' => [
                        'ar' => 'مبني واحد',
                        'en' => 'Building one',
                    ],
                    'description' => [
                        'ar' => 'تفاصيل مبني واحد',
                        'en' => 'description for Building one',
                    ],
                    'address' => '59 Gamal Zain, Al Manteqah Ath Thamenah, Nasr City, Cairo Governorate 4441504, Egypt',
                    'ejar_uuid' => null,
                    'lat' => '30.0461588',
                    'lng' => '31.3477529',
                    'city' => 'Nasr City',
                    'district' => 'Al Manteqah Ath Thamenahy',
                    'region' => 'Cairo Governorate',
                    'street' => 'Gamal Zain',
                    'build_number' => '59',
                    'postal_code' => '4441504',
                    'building_date' => '2022-02-15',
                    'reception_date' => '2022-08-08',
                    'number' => '333333',
                    'property_type_id' => 1,
                    'usability_id' => 1,
                    'created_at' => '2024-10-21 11:35:36',
                ],
                [
                    'name' => [
                        'ar' => 'مبني اثنين',
                        'en' => 'Building two',
                    ],
                    'description' => [
                        'ar' => 'تفاصيل مبني اثنين',
                        'en' => 'description for Building two',
                    ],
                    'address' => '59 Gamal Zain, Al Manteqah Ath Thamenah, Nasr City, Cairo Governorate 4441504, Egypt',
                    'ejar_uuid' => '9b988e18–d3fa–51ba–b25f–θ5e2d425255d',
                    'ejar_synced' => true,
                    'lat' => '30.0461588',
                    'lng' => '31.3477529',
                    'city' => 'Nasr City',
                    'district' => 'Al Manteqah Ath Thamenahy',
                    'region' => 'Cairo Governorate',
                    'street' => 'Gamal Zain',
                    'build_number' => '60',
                    'postal_code' => '4441511',
                    'building_date' => '2022-02-15',
                    'reception_date' => '2022-08-08',
                    'number' => '444444',
                    'property_type_id' => 1,
                    'usability_id' => 1,
                    'created_at' => '2024-10-21 11:35:36',
                ],
            ];
            $unit_data = [
                'building_date' => '2022-10-08',
                'number' => '123123',
                'property_type_id' => 12,
                'usability_id' => 1,
                'price' => 1000.00,
                'created_at' => '2024-10-21 11:35:36',
            ];

            DB::beginTransaction();
            foreach ($data as $propertyData){
                $property = Property::where(['ejar_uuid' => $propertyData['ejar_uuid'], 'number' => $propertyData['number']])->first();
                if (empty($property))
                {
                    $property = Property::create($propertyData);
                    $property_amenities = [
                        [
                            'property_id' => $property->id,
                            'amenity_id' => 5,
                        ],
                        [
                            'property_id' => $property->id,
                            'amenity_id' => 19,
                        ],
                        [
                            'property_id' => $property->id,
                            'amenity_id' => 23,
                        ],
                        [
                            'property_id' => $property->id,
                            'amenity_id' => 27,
                        ],
                    ];
                    $property_attributes = [
                        [
                            'property_id' => $property->id,
                            'attribute_id' => 5,
                            'value' => 5,
                        ],
                        [
                            'property_id' => $property->id,
                            'attribute_id' => 4,
                            'value' => 90,
                        ],
                        [
                            'property_id' => $property->id,
                            'attribute_id' => 2,
                            'value' => 4,
                        ],
                    ];
                    //TODO edit the property owners object to match the new scheme
                    $property_owners = [
                        'property_id' => $property->id,
                        'user_id' => User::first()->id,
                        'percentage' => 100,
                    ];
                    $property_document = [
                        'document_type_id' => 1,
                        'morphable_type' => 'Modules\Property\app\Models\Property',
                        'morphable_id' => $property->id,
                        'metadata' => json_encode(["document_name_and_type"=>"doc one","document_number"=>"132","release_date"=>"2024-10-21","expiration_date"=>"2024-10-27","released_by"=>"owner one","released_in"=>"riyadh"]),
                    ];
                    Document::create($property_document);
                    PropertyOwners::create($property_owners);
                    foreach ($property_amenities as $prop_amenity){
                        PropertyAmenity::create($prop_amenity);
                    }
                    foreach ($property_attributes as $prop_attribute){
                        PropertyAttribute::create($prop_attribute);
                    }
                    //property units
                    for ($i=0; $i<3; $i++) {
                        $unit_data['parent_id'] = $property->id;
                        $unit_data['number'] = '12312' . $i;
                        Property::create($unit_data);
                    }
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage());
        }
    }
}
