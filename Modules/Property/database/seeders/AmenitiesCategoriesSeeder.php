<?php

namespace Modules\Property\database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Property\app\Models\AmenitiesCategory;

class AmenitiesCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => [
                    'ar' => 'الرياضة',
                    'en' => 'Sport',
                ],
            ],
            [
                'name' => [
                    'ar' => 'الأمن',
                    'en' => 'Security',
                ],
            ],
            [
                'name' => [
                    'ar' => 'خدمات',
                    'en' => 'Services',
                ],
            ],
            [
                'name' => [
                    'ar' => 'الترفيه',
                    'en' => 'entertainment',
                ],
            ],
        ];

        foreach ($categories as $category) {
            $existingCategory = AmenitiesCategory::where(function ($query) use ($category) {
                $query->where(DB::raw("JSON_EXTRACT(name, '$.en')"), $category['name']['en'])
                    ->orWhere(DB::raw("JSON_EXTRACT(name, '$.ar')"), $category['name']['ar']);
            })->first();

            if (!$existingCategory) {
                AmenitiesCategory::create($category);
            }
        }
    }
}
