<?php

namespace Modules\Property\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Property\app\Events\PropertyStatusChange;
use Modules\Property\app\Listeners\PropertyStatusListener;
use Modules\Property\app\Events\PropertyNeedSync;
use Modules\Property\app\Listeners\PropertyNeedSyncListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event handler mappings for the application.
     *
     * @var array<string, array<int, string>>
     */
    protected $listen = [
        PropertyStatusChange::class => [PropertyStatusListener::class],
        PropertyNeedSync::class => [PropertyNeedSyncListener::class]
    ];

    /**
     * Indicates if events should be discovered.
     *
     * @var bool
     */
    protected static $shouldDiscoverEvents = true;

    /**
     * Configure the proper event listeners for email verification.
     */
    protected function configureEmailVerification(): void
    {
        //
    }
}
