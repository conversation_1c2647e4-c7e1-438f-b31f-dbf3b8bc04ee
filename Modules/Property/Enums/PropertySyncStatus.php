<?php

namespace Modules\Property\Enums;

enum PropertySyncStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case NEED_SYNC = 'need_sync';


    public function getLabel(): string
    {
        return match($this) {
            self::PENDING => __('Pending'),
            self::PROCESSING => __('Processing'),
            self::COMPLETED => __('Completed'),
            self::FAILED => __('Failed'),
            self::NEED_SYNC => __('Sync Required!'),
        };
    }

    public static function getOptions(): array
    {
        return [
            self::PENDING->value,
            self::PROCESSING->value,
            self::COMPLETED->value,
            self::FAILED->value,
            self::NEED_SYNC->value,
        ];
    }

    public function getColor(): string
    {
        return match($this) {
            self::PENDING => 'gray',
            self::PROCESSING => 'primary',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::NEED_SYNC => 'warning',
        };
    }
}
