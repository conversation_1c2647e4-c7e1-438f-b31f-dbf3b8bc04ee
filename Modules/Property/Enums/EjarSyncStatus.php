<?php
namespace Modules\Property\Enums;

enum EjarSyncStatus: string
{
    case SYNCING = 'syncing';
    case NEED_SYNC = 'need_sync';
    case SYNCED = 'synced';
    case FAILED = 'failed';
    case NOT_SYNCED = 'not_synced';

    public function getLabel(): string
    {
        return match($this) {
            self::SYNCING => __('Syncing'),
            self::NEED_SYNC => __('Sync Required'),
            self::SYNCED => __('Ejar Synced Successful'),
            self::FAILED => __('Ejar Sync failed'),
            self::NOT_SYNCED => __('Not Synced'),
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::NEED_SYNC => 'info',
            self::SYNCING => 'warning',
            self::SYNCED => 'success',
            self::FAILED => 'danger',
            self::NOT_SYNCED => 'warning',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::NEED_SYNC => 'heroicon-o-exclamation-circle',
            self::SYNCING => 'heroicon-o-arrow-path',
            self::SYNCED => 'heroicon-o-check-circle',
            self::FAILED => 'heroicon-o-x-circle',
            self::NOT_SYNCED => 'heroicon-o-x-circle',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::NEED_SYNC->value,
            self::SYNCING->value,
            self::SYNCED->value,
            self::FAILED->value,
            self::NOT_SYNCED->value,
        ];
    }
}
