<?php

namespace Modules\Property\Enums;

enum UnitPeriodType: string
{
    const QUARTERLY = 'quarterly';
    const HALF_ANNUALLY = 'half_annually';
    const MONTHLY = 'monthly';
    const ANNUALLY = 'annually';


    public static function getPeriodTypes(): array
    {
        return [
            self::QUARTERLY,
            self::HALF_ANNUALLY,
            self::MONTHLY,
        ];
    }

    public static function repeatedLabels(): array
    {
        return [
            self::MONTHLY => __('Monthly'),
            self::QUARTERLY => __('Quarterly'),
            self::HALF_ANNUALLY => __('Half Annually'),
        ];
    }

    public static function trans(?string $status): ?string
    {
        return match($status) {
            self::MONTHLY => __('Monthly'),
            self::QUARTERLY => __('Quarterly'),
            self::HALF_ANNUALLY => __('Half Annually'),
            default => $status,
        };
    }

}
