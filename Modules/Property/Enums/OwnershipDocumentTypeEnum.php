<?php

namespace Modules\Property\Enums;

enum OwnershipDocumentTypeEnum: string
{
    case Electronic = 'electronic_title_deed';
    case PaperTitleDeed = 'paper_title_deed';
    case HojjatEsthkam = 'hojjat';
    case RealEstateRegistryTitleDeed = 'real_estate_registry_title_deed';
    case Other = 'other';

    public  function getLabel():string
    {
        return match($this) {
            self::Electronic => __('Electronic title deed'),
            self::PaperTitleDeed => __('Paper title deed'),
            self::HojjatEsthkam => __('Hojjat esthkam'),
            self::RealEstateRegistryTitleDeed => __('Real Estate registry title deed'),
            self::Other => __('Other'),
    };

    }

    public static function toArray():array
    {
        return
            collect(self::cases())->mapWithKeys(fn ($type) => [$type->value => $type->getLabel()])->toArray();
    }
}
