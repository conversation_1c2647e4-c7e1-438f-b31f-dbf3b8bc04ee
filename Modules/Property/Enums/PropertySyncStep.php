<?php

namespace Modules\Property\Enums;

enum PropertySyncStep: string
{
    /**
     * please keep the ordering of this enum [OWNERSHIP_DOCS, INDIVIDUAL_ENTITY, IBAN, LINK_OWNER_TO_DOCUMENT, PROPERTY, PROPERTY_UNITS]
     * the operation of sequential syncing of property depends on this ordering
     */
    case OWNERSHIP_DOCS = 'ownership_docs';
    case INDIVIDUAL_ENTITY = 'individual_entity';
    case IBAN = 'iban';
    case LINK_OWNER_TO_DOCUMENT = 'link_owner_to_document';
    case PROPERTY = 'property';
    case PROPERTY_UNITS = 'property_units';

    public function getLabel(): string
    {
        return match($this) {
            static::OWNERSHIP_DOCS => __('Ownership Docs'),
            static::INDIVIDUAL_ENTITY => __('Individual Entities'),
            static::IBAN => __('IBAN'),
            static::LINK_OWNER_TO_DOCUMENT => __('Link Owner To Document'),
            static::PROPERTY => __('Property'),
            static::PROPERTY_UNITS => __('Property Units'),
        };
    }

    public static function getOptions(): array
    {
        return [
            self::OWNERSHIP_DOCS->value,
            self::INDIVIDUAL_ENTITY->value,
            self::IBAN->value,
            self::LINK_OWNER_TO_DOCUMENT->value,
            self::PROPERTY->value,
            self::PROPERTY_UNITS->value,
        ];
    }

    public function getClass(): string
    {
        return match($this) {
            self::OWNERSHIP_DOCS => "SyncOwnershipDocs",
            self::INDIVIDUAL_ENTITY => "SyncIndividualEntities",
            self::IBAN => "SyncIBANs",
            self::LINK_OWNER_TO_DOCUMENT => "SyncDocsWithOwners",
            self::PROPERTY => "SyncProperty",
            self::PROPERTY_UNITS => "SyncPropertyUnits",
            default => throw new \Exception('Unexpected match value')
        };
    }
}
