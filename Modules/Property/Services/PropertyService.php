<?php

namespace Modules\Property\Services;

use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Property\Repositories\PropertyRepository;
use Illuminate\Support\Facades\Schema;
use Modules\Account\app\Models\Account;
use Modules\Property\Enums\UnitPeriodType;

class PropertyService extends ServiceAbstract
{
    public function __construct(PropertyRepository $repository)
    {
        parent::__construct($repository);
    }

    public function paginate(array $filter = [], array $with = [], array $select = ['*'])
    {
        return $this->repository->getAllWithVal($filter, $with, $select);

    }

    public function getTenantUnits(array $filter = [], array $with = [], array $select = ['*'])
    {
        return $this->repository->getAllTenantUnitsBy($filter, $with, $select);
    }


    public function getTenantUnit($filter, $with)
    {
        return $this->repository->tenantUnit->where($filter)->with($with)->first();
    }

    public function getAddress(array $request)
    {
        $filterLocation = $request['location'];
        $select[] = $filterLocation;

        $result = [];
        if(!Schema::hasColumn($this->repository->getTable(),$filterLocation)){
            return ['state'=>false,'message'=>__('This column not found')];
        }
        $AllData = $this->repository->getAllByQuery(select: $select)->distinct($select)->whereNotNull($filterLocation)->get();

        foreach ($AllData as $data) {
            $result[] = $data->$filterLocation;
        }

        return ['state'=>true,'data'=>$result];
    }

    public function propertyOwner()
    {
        return $this->repository->getAllByQuery()->whereHas('owners',function($q){
             $q->where('ownerable_id',auth()->id())->where('ownerable_type' , Account::class );
        })->with(["documentOwnership"])->get();
    }

    public function owners(array $filter)
    {
        return $this->repository->getAllByQuery($filter)
            ->with('onlyOwner')
            ->first()?->owners;
    }

    public function getUnitPrice($unit_id){
        $unit = $this->repository->getAllPrices($unit_id);
        if(!$unit){
            return null;
        }
        $prices = $unit->prices;
        if ($prices->isEmpty()) {
            return null;
        }
        $highestAnnualPrice = $prices->first()->annual_price;
        if ($unit->price > $highestAnnualPrice) {
            $highestAnnualPrice = $unit->price;
        }
        $prices->transform(function ($price) use ($highestAnnualPrice) {
            $price->saved_amount = $highestAnnualPrice - $price->annual_price;
            return $price;
        });
        $annualPrice = [
            'id' => 0,
            'period_type' => UnitPeriodType::ANNUALLY,
            'price' => $unit->price,
            'saved_amount' => $highestAnnualPrice - $unit->price
        ];
        
        $prices->push($annualPrice);
        return $prices;
    }

    public function getPropertiesStats($propertyId = null)
    {
        return $this->repository->getPropertiesStats($propertyId);
    }

    public function getLeasesStats($propertyId = null)
    {
        return $this->repository->getLeasesStats($propertyId);
    }

    public function getFinancialStats($propertyId = null)
    {
        return $this->repository->getFinancialStats($propertyId);
    }
}
