<?php
namespace Modules\Property\Services;

use App\Enums\DocumentTypeEnum;
use App\Enums\PropertyTypeEnum;
use App\Models\Document;
use App\Models\DocumentType;
use GeniusTS\HijriDate\Hijri;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Account\app\Models\Account;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\EjarIntegration\Enums\EjarEntityTypes;
use Modules\EjarIntegration\Enums\EjarPropertyOwners;
use Modules\EjarIntegration\PropertyRetrieving\RetrieveProperties;
use Modules\Organization\app\Models\Organization;
use Modules\Property\app\Jobs\ProcessPropertyUnitInRetrieve;
use Modules\Property\app\Models\Amenities;
use Modules\Property\app\Models\AmenitiesCategory;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\PropertyAmenity;
use Modules\Property\app\Models\PropertyAttribute;
use Modules\Property\app\Models\PropertyOwners;
use Modules\Property\app\Models\PropertyType;
use Modules\Property\app\Models\Usability;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertyStatus;

class RetrieveSinglePropertyService
{
    protected Company $company;
    protected array $Properties = [];
    protected array $propertyData = [];
    public array $includedData = [];
    protected bool $processingPropertyIsDraft = false;

    public array $params;
    public function __construct(Company $company)
    {
        $this->company = $company;
        //TODO Make the count dynamic
        $this->params = ['page[number]' => 1, 'page[size]' => 10000000];
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function retrieve(array $data)
    {
        $this->setRetrieveParams($data);
        if ($this->checkIfPropertyExistsByData($data)) {
            throw new \Exception(__('Property already exists'));
        }
        //retrieve properties from ejar
        $propertyRetrieving = new RetrieveProperties($this->company);
        $propertyRetrieving->setParams($this->params);
        $propertyRetrieving->retrieveProperties();
        $this->Properties = $propertyRetrieving->properties;
        if (count($this->Properties) < 1) {
            throw new \Exception(__('Property not found'));
        }
        $this->propertyData = $this->getPropertyData($this->Properties, $data);
        $this->includedData = $propertyRetrieving->units;
        return $this->processPropertyRetrieve($this->propertyData);
    }

    /**
     * @throws \Exception
     */
    public function processPropertyRetrieve(array $propertyData): Property
    {
        try {
            DB::beginTransaction();
            //save property details
            $property = $this->handleProcessSteps($propertyData);
            SyncHelper::markPropertySyncStepsAsCompleted($property->id); //mark property sync steps as completed
            DB::commit();
            return $property;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function handleProcessSteps(array $propertyData): Property
    {
        //save property details
        $property = $this->savePropertyDetails($propertyData);
        //save document ownership
        $documentEjarId = intval($propertyData['relationships']['ownership_document']['data']['id']);
        $this->savePropertyDocumentData($property, $documentEjarId);
        //save an owner if not exists and save property owners
        $this->savePropertyOwners($property->id, $propertyData['relationships']['property_owners']['data']);
        //set property status
        $this->setPropertyStatus($property);
        //units
        $this->savePropertyUnits($property, $propertyData['relationships']['units']['data']);
        return $property;
    }

    public function setPropertyStatus(Property $property): void
    {
        //by default, the property is draft
        if (!$this->processingPropertyIsDraft) {
            $property->status = PropertyStatus::ACTIVE->value; //todo get status of units and property
            $property->save();
        }
    }

    protected function getAddressDetails(array $propertyData): array
    {
        $addressData = $propertyData['attributes']['address'];
        $cityObj = $propertyData['attributes']['address']['attributes']['city_obj'];
        $districtObj = $propertyData['attributes']['address']['attributes']['district_obj'];
        $regionObj = $propertyData['attributes']['address']['attributes']['region_obj'];


        $region = !empty($regionObj) ? $regionObj['name_en'] : null;
        $city = !empty($cityObj) ? $cityObj['name_en'] : null;
        $district = !empty($districtObj) ? $districtObj['name_en'] : null;
        $street = $addressData['attributes']['street_name'] ?? null;
        $postal_code = $addressData['attributes']['postal_code'] ?? null;
        $lat = $addressData['attributes']['latitude'] ?? null;
        $lng = $addressData['attributes']['longitude'] ?? null;
        $region_id = !empty($regionObj) ? $regionObj['region_id'] : null;
        $city_id = !empty($cityObj)? $cityObj['city_id'] : null;
        $district_id = !empty($districtObj) ? $districtObj['id'] : null;
        $address_title = $propertyData['attributes']['property_name'] . $propertyData['attributes']['property_number'] . '، ' . $street
            . ', ' . $region . ' ' . $city. ' ' . $district . ' ' . $postal_code . ', Saudi Arabia';

        return [
            'address_title' => $address_title,
            'region' => $region,
            'city' => $city,
            'district' => $district,
            'street' => $street,
            'postal_code' => $postal_code,
            'lat' => $lat,
            'lng' => $lng,
            'region_id' => $region_id,
            'city_id' => $city_id,
            'district_id' => $district_id,
        ];

    }

    public function savePropertyDetails($propertyData): Property
    {
        $addressDetails = $this->getAddressDetails($propertyData);
        $property = Property::create([
            'ejar_uuid' => $propertyData['id'],
            'name' => $propertyData['attributes']['property_name'],
            'usability_id' => $this->getUsability($propertyData['attributes']['property_usage'])->id,
            'property_type_id' => $this->getPropertyType($propertyData['attributes']['property_type'], PropertyTypeEnum::Property->value)->id,
            'ejar_sync_status' => EjarSyncStatus::SYNCED->value,
            'ejar_synced' => true,
            'retrieved_from_ejar' => true,
            'last_synced_at' => now(),
            'company_id' => $this->company->id,
            'broker_id' => $this->company->user_id,
            'number' => $propertyData['attributes']['property_number'],
            'build_number' => $propertyData['attributes']['address']['attributes']['building_number'],
            'building_date' => $propertyData['attributes']['established_date'] ?? null,
            'address' => $addressDetails['address_title'],
            'region' => $addressDetails['region'],
            'region_id' => $addressDetails['region_id'],
            'city' => $addressDetails['city'],
            'city_id' => $addressDetails['city_id'],
            'district' => $addressDetails['district'],
            'district_id' => $addressDetails['district_id'],
            'street' => $addressDetails['street'],
            'postal_code' => $addressDetails['postal_code'],
            'lat' => $addressDetails['lat'],
            'lng' => $addressDetails['lng'],
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        //save property attributes
        $propertyAttributes = [
            'total_floors' => $propertyData['attributes']['total_floors'],
            'compound_name' => $propertyData['attributes']['compound_name'],
            'units_per_floor' => $propertyData['attributes']['units_per_floor']
        ];
        $this->savePropertyAttributes($property, $propertyAttributes);
        $this->savePropertyAmenities($property, $propertyData['attributes']['associated_facilities']);
        return $property;
    }

    /**
     * @throws \Exception
     */
    public function savePropertyDocumentData(Property $property, int $documentEjarId): Document
    {
        $documentRetrieving = new \Modules\EjarIntegration\PropertyRetrieving\RetrieveOwnershipDocument($this->company);
        $documentRetrieving->retrieveDocumentDetails($documentEjarId);

        $metadata = $this->getDocumentMetadata($documentRetrieving->ownershipDocument);
        $document = Document::create([
            'document_type_id' => DocumentType::where(['key' => DocumentTypeEnum::Ownership->value])->first()->id,
            'morphable_id' => $property->id,
            'morphable_type' => Property::class,
            'ejar_id' => $documentRetrieving->ownershipDocument['id'],
            'metadata' => json_decode(json_encode($metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), true),
            'company_id' => $property->company_id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        //save media documents
        //add media in valid staging
        $nonProductionStages = ['local', 'develop', 'testing'];
        if (env('APP_STAGE') && !in_array(env('APP_STAGE'), $nonProductionStages)) {
            foreach ($documentRetrieving->ownershipDocument['attributes']['scanned_documents'] as $scanned_document) {
                $document->addMediaFromUrl($scanned_document['file_url'])->toMediaCollection(DocumentTypeEnum::OWNERSHIP_DOCUMENT->value);
            }
        }

        return $document;
    }

    /**
     * @throws \Exception
     */
    public function savePropertyOwners(int $propertyID, array $propertyOwnersList): void
    {
        $owners = collect($propertyOwnersList);
        $included = collect($this->includedData);
        $insertable = [];
        $percentage = 0;
        $hasRepresenter = false;

        foreach ($owners as $index => $ownerData) {
            $propertyOwnerData = $included->where('id', $ownerData['id'])
                ->whereIn('type', [EjarPropertyOwners::OWNERS->value, EjarPropertyOwners::REPRESENTATIVE->value])
                ->first();
            if (!isset($propertyOwnerData['relationships']['entity'])) {
                break;
            }
            $entityData = $included->where('id', $propertyOwnerData['relationships']['entity']['data']['id'])
                ->whereIn('type', [EjarEntityTypes::INDIVIDUAL_ENTITIES->value, EjarEntityTypes::ORGANIZATION_ENTITIES->value])
                ->first();
            $entityType = $propertyOwnerData['relationships']['entity']['data']['type'];
            $is_representer = $propertyOwnerData['attributes']['is_representative'];

            if ($entityType == EjarEntityTypes::INDIVIDUAL_ENTITIES->value) {
                $ownerable = $this->getAccountUsingEjarEntityDetails($entityData, $is_representer ? AccountRolesEnum::OWNER_REPRESENTER : AccountRolesEnum::OWNER);
                $ownerType = Account::class;
            } elseif ($entityType == EjarEntityTypes::ORGANIZATION_ENTITIES->value) {
                $ownerable = $this->getOrganizationUsingEjarEntityDetails($entityData); //get org object
                $ownerType = Organization::class;
            } else {
                throw new \Exception("Unknown entity type");
            }

            if (is_null($ownerable)) {
                break;
            }

            if ($is_representer) {
                $hasRepresenter = true;
                $percentage = 0;
            } elseif ($percentage != 100) {
                $percentage = 100;
            } else {
                $percentage = 0;
            }

            $propOwnerObject = [
                'property_id' => $propertyID,
                'ownerable_type' => $ownerType,
                'ownerable_id' => $ownerable->id,
                'percentage' => $percentage, //todo how to get percentage from ejar ????
                'ejar_uuid' => $ownerData['id'],
                'is_representer' => $is_representer,
            ];

            $insertable[] = $propOwnerObject;
        }

        if (count($insertable) == 0) {
            $this->processingPropertyIsDraft = true;
        } else {
            if (!$hasRepresenter) {
                if (count($insertable) == 1) {
                    $insertable[0]['is_representer'] = true;
                } else {
                    $this->processingPropertyIsDraft = true;
                }
            }
            PropertyOwners::insert($insertable);
        }
    }

    /**
     * @throws \Exception
     */
    protected function getAccountUsingEjarEntityDetails($entityDetails, $role): Account|null
    {
        $account = Account::where(['ejar_uuid' => $entityDetails['attributes']['entity_uuid']])->first();
        if (empty($account)) {
            if ($entityDetails['attributes']['verification_status'] != 'verification_succeed') {
                return null;
            }
            $email = $entityDetails['attributes']['email'];
            $phone = $entityDetails['attributes']['phone_number'];
            Log::info('phone'. $phone);
            $accountNameParts = $this->getAccountNameParts($entityDetails['attributes']['full_name']);
            $data = [
                'name' => $entityDetails['attributes']['full_name'] ?? null,
                'first_name' => !is_null($entityDetails['attributes']['first_name']) ? $entityDetails['attributes']['first_name'] : $accountNameParts['first_name'],
                'second_name' => !is_null($entityDetails['attributes']['father_name']) ? $entityDetails['attributes']['father_name'] : $accountNameParts['second_name'],
                'third_name' => !is_null($entityDetails['attributes']['grand_father_name']) ? $entityDetails['attributes']['grand_father_name'] : $accountNameParts['third_name'],
                'last_name' => !is_null($entityDetails['attributes']['family_name']) ? $entityDetails['attributes']['family_name'] : $accountNameParts['last_name'],
                'phone' => $phone,
                'email' => $email == null ? 'ddummy' . rand(10000, 99999) . '@gmail.com' : $email,
                'national_id' => $entityDetails['attributes']['id_number'], //todo handle id types
                'birth_date' => $entityDetails['attributes']['date_of_birth'] ?? null,
                'ejar_uuid' => $entityDetails['attributes']['entity_uuid'],
                'lang' => 'ar',
                'ejar_verified' => true,
                'is_active' => true,
                'otp_activated_at' => now(),
                'password' => rand(1000, 9999),
            ];
            $account =  Account::create($data);
            $account->accountRoles()->create([
                'role' => $role,
                'is_default' => true,
            ]);
        }

        return $account;
    }

    protected function getOrganizationUsingEjarEntityDetails($entityDetails): Organization|null
    {
        $org = Organization::where(['ejar_uuid' => $entityDetails['attributes']['entity_uuid']])->first();
        if (empty($org)) {
            if (is_null($entityDetails['attributes']['unified_number'])) {
                return null;
            }
            $data = [
                'name' => $entityDetails['attributes']['name'],
                'unified_number' => $entityDetails['attributes']['unified_number'],
                'ownership_document_number' => $entityDetails['attributes']['registration_number'],
                'ejar_uuid' => $entityDetails['attributes']['entity_uuid'],
                'organization_type' => $entityDetails['attributes']['organization_type'],
                'registration_number' => $entityDetails['attributes']['registration_number'],
                'registration_date' => $entityDetails['attributes']['registration_date']
            ];
            $org =  Organization::create($data);
        }
        return $org;
    }

    protected function yieldPortfolioUnits(array $relatedUnits): \Generator
    {
        foreach ($relatedUnits as $unit) {
            if ($unit['type'] === 'portfolio_units') {
                yield $unit;
            }
        }
    }

    public function savePropertyUnits(Property $property, array $relatedUnits): void
    {
        foreach ($this->yieldPortfolioUnits($relatedUnits) as $unit) {
            $unitData = collect($this->includedData)->where('id',$unit['id'])->first();
            if (!empty($unitData)) {
                ProcessPropertyUnitInRetrieve::dispatchSync($property, $unitData, $this->company);
            }
        }
    }

    protected function savePropertyAmenities(Property $property, array $propertyAmenities): void
    {
        $insertable = [];
        foreach ($propertyAmenities as $availableAmenityKey => $value) {

            $amenity = Amenities::firstOrCreate(
                ['key' => $availableAmenityKey],
                ['name' => Str::title($availableAmenityKey), 'amenities_category_id' => AmenitiesCategory::query()->latest()->first()->id, 'key' => $availableAmenityKey, 'from_ejar' => true]
            );
            $insertable[] = [
                'amenity_id' => $amenity->id,
                'property_id' => $property->id,
            ];
        }

        PropertyAmenity::insert($insertable);
    }

    protected function savePropertyAttributes(Property $property, array $unitAttributes): void
    {
        $insertable = [];
        foreach ($unitAttributes as $availableAttributeKey => $value) {

            $attribute = Attribute::firstOrCreate(
                ['key' => $availableAttributeKey],
                ['name' => Str::title($availableAttributeKey), 'key' => $availableAttributeKey, 'is_required' => true, 'from_ejar' => true]
            );
            if (!is_null($value)) {
                $insertable[] = [
                    'attribute_id' => $attribute->id,
                    'property_id' => $property->id,
                    'value' => $value
                ];
            }
        }

        PropertyAttribute::insert($insertable);
    }

    protected function getPropertyType(string $propertyTypeKey, string $type): PropertyType
    {
        return PropertyType::firstOrCreate(
            ['key' => $propertyTypeKey, 'property_type' => $type],
            ['name' => Str::title($propertyTypeKey), 'property_type' => $type, 'key' => $propertyTypeKey, 'from_ejar' => true]
        );
    }

    protected function getUsability(string $usabilityKey): Usability
    {
        return Usability::firstOrCreate(
            ['key' => $usabilityKey],
            ['name' => Str::title($usabilityKey), 'key' => $usabilityKey, 'from_ejar' => true]
        );
    }

    protected function getAccountNameParts(string $full_name): array
    {
        $parts = explode(' ', $full_name);

        // Assign parts safely
        $first_name = $parts[0] ?? null;
        $second_name = $parts[1] ?? $first_name;
        $third_name = $parts[2] ?? $second_name;

        // For last name, if there are more than 3 parts, combine the rest
        if (count($parts) > 3) {
            $last_name = implode(' ', array_slice($parts, 3));
        } else {
            $last_name = $parts[3] ?? $third_name;
        }

        return [
            'first_name' => $first_name,
            'second_name' => $second_name,
            'third_name' => $third_name,
            'last_name' => $last_name,
        ];
    }

    protected function getDocumentMetadata(array $ownershipDocument): array
    {
        return [
            'document_type' => $ownershipDocument['attributes']['ownership_document_type'] ?? null,
            'ownership_reference_no' => $ownershipDocument['attributes']['document_number'] ?? null,
            'legal_document_type_name' => $ownershipDocument['attributes']['legal_document_type_name'] ?? null,
            "issue_date" => $this->getGorgianIssueDate($ownershipDocument['attributes']['issued_date']) ?? null,
        ];
    }

    protected function getGorgianIssueDate($issue_date): string
    {
        $issue_date = date('Y-m-d', strtotime($issue_date));
        $date = Carbon::parse($issue_date);
        try {
            // Try to convert Hijri to Gregorian
            return Hijri::convertToGregorian($date->format('d'), $date->format('m'), $date->format('Y'))->format('Y-m-d');
        } catch (\Exception $e) {
            return $date->format('Y-m-d');
        }
    }

    public function checkIfPropertyExists(string $propertyNumber): bool
    {
        return Property::query()->where('number', $propertyNumber)->exists();
    }

    public function checkIfPropertyExistsByData(array $data): bool
    {
        if (isset($data['ejar_uuid'])) {
            return Property::query()->whereNull('parent_id')->where('ejar_uuid', $data['ejar_uuid'])->exists();
        } elseif (isset($data['reference_number'])) {
            // Check if property exists by reference number in document metadata
            return Property::query()
                ->whereHas('documentOwnership', function ($query) use ($data) {
                    $query->whereJsonContains('metadata->ownership_reference_no', $data['reference_number']);
                })
                ->exists();
        }

        return false;
    }

    public function getPropertyData(array $retrievedProperties, array $params)
    {
        if (isset($params['ejar_uuid'])) {
            $propertyData = collect($retrievedProperties)->where('id', $params['ejar_uuid'])->first();
            if (!$propertyData) {
                throw new \Exception(__('Property not found'));
            }
        } elseif (isset($params['reference_number'])) {
            $propertyData = $retrievedProperties[0]; //get the first according to business
        } else {
            throw new \Exception(__('missing parameters of property'));
        }

        return $propertyData;
    }

    public function setRetrieveParams(array $data): void
    {
        if (count($data) == 0) {
            throw new \Exception('Undefined property number or reference number');
        }
        if (isset($data['property_number'])) {
            $this->params['filter[property_number]'] = $data['property_number'];
        } elseif (isset($data['reference_number'])) {
            $this->params['filter[reference_number]'] = $data['reference_number'];
        }
    }
}
