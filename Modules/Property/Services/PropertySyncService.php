<?php
namespace Modules\Property\Services;

use Filament\Notifications\Notification;
use Modules\EjarIntegration\Factories\SyncPropertyFactory;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\SyncPropertyStep;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class PropertySyncService
{
    protected Property $property;
    public function __construct(Property $property) {
        $this->property = $property;
    }

    public function sync(): void
    {
        $this->prepareSteps();
        foreach (PropertySyncStep::cases() as $step) {
            $this->executeStep($step);
        }
        $this->successSync();
    }

    /**
     * @throws \Exception
     */
    protected function executeStep(PropertySyncStep $step): void
    {
        $syncStep = $this->initializeStep($step);
        $syncPropertyFactory = new SyncPropertyFactory($this->property, $syncStep);
        $response = $syncPropertyFactory->make($step->getClass())->sync();
        $this->handleExecuteStepResponse($syncStep, $response);
    }

    protected function initializeStep(PropertySyncStep $step): SyncPropertyStep
    {
        return SyncPropertyStep::where(['property_id' => $this->property->id, 'step' => $step->value])->first();
    }

    protected function completeStep(SyncPropertyStep $step, array $response): void
    {
        $step->update([
            'status' => PropertySyncStatus::COMPLETED->value,
            'response_data' => json_encode($response['data']),
            'error_message' => null,
            'completed_at' => now(),
        ]);
    }

    protected function failStep(SyncPropertyStep $step, array $response): void
    {
        $err_msg = $response['data']['Body']['errors'][0]['detail'] ?? $response['message'];
        $step->update([
            'status' => PropertySyncStatus::FAILED->value,
            'error_message' => $err_msg,
            'response_data' => json_encode($response['data']),
            'completed_at' => now(),
        ]);
    }

    public function failedSync(): void
    {
        $this->property->update([
            'ejar_sync_status' => EjarSyncStatus::FAILED->value,
            'last_synced_at' => now(),
        ]);
    }

    protected function successSync(): void
    {
        $this->property->update([
            'ejar_sync_status' => EjarSyncStatus::SYNCED->value,
            'last_synced_at' => now(),
        ]);
    }

    protected function prepareSteps(): void
    {
        $PropertySteps = SyncPropertyStep::where(['property_id' => $this->property->id])->get();
        if ($PropertySteps->isEmpty()) {
            $bulkSteps = [];
            foreach (PropertySyncStep::cases() as $step) {
                $bulkSteps[] = [
                    'property_id' => $this->property->id,
                    'step' => $step->value,
                    'status' => PropertySyncStatus::PENDING->value,
                    'started_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            SyncPropertyStep::insert($bulkSteps);
        }
    }

    protected function handleExecuteStepResponse(SyncPropertyStep $syncStep, array $response): void
    {
        if ($response['status']) {
            $this->completeStep($syncStep, $response);
            Notification::make()
                ->success()
                ->title($response['message'])
                ->icon('heroicon-o-check-circle')
                ->iconColor('success')
                ->duration(5000)
                ->send();
        } else {
            $this->failStep($syncStep, $response);
            $this->failedSync();
            throw new \Exception($response['message']);
        }
    }
}
