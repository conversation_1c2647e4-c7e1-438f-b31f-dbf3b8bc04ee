<?php
namespace Modules\Property\Services;

use Filament\Notifications\Notification;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\Lease\app\Jobs\ProcessRetrievalLeases;
use Modules\Property\app\Jobs\ProcessRetrievalProperties;

class RetrieveCompanyPropertiesService
{
    protected Company $company;
    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function retrieve(): void
    {
        RetrievalLogger::clearPreviousLogs($this->company->id); //clear previous logs
        $this->processRetrievalProperties();
    }

    /**
     * @throws \Throwable
     */
    public function processRetrievalProperties(): void
    {
        $this->notifyProcessing();
        ProcessRetrievalProperties::dispatch($this->company);
    }
    protected function notifyProcessing(): void
    {
        Notification::make()
            ->title(__('Properties Retrieve Started'))
            ->body(__('Processing properties in the background.'))
            ->info()
            ->duration(5000)
            ->send();
    }
}
