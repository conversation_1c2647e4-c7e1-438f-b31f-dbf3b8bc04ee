<?php

use Illuminate\Support\Facades\Route;
use Modules\Property\app\Http\Controllers\Api\OwnerUnitController;
use Modules\Property\app\Http\Controllers\Api\UsabilityController;
use Modules\Property\app\Http\Controllers\Api\PropertyController;
use Modules\Property\app\Http\Controllers\Api\PropertyTypeController;
use Modules\Property\app\Http\Controllers\Api\AmenitiesController;
/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/
Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::get('tenant-units', [PropertyController::class, 'getTenantUnits']);
    Route::get('tenant-unit/{id}/{unit}', [PropertyController::class, 'getTenantUnit']);
    Route::get('owner-properties', [PropertyController::class,'getOwnerProperties']);
    Route::get('owners/{property_id}', [PropertyController::class,'getOwners']);
    Route::get('owner-unit/{id}', [OwnerUnitController::class,'getOwnerUnit']);
});
Route::apiResource('usabilities', UsabilityController::class);
Route::apiResource('properties', PropertyController::class)->only(['index', 'show'])->middleware(['auth.optional']);

Route::get('location-address', [PropertyController::class,'getAddress']);

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(static function(){
    Route::get('dashboard', [PropertyController::class,'dashboard']);
});

Route::apiResource('property-type', PropertyTypeController::class)->only(['index']);
Route::apiResource('amenities', AmenitiesController::class)->only(['index']);

