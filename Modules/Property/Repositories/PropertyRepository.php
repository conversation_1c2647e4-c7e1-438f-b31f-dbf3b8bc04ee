<?php

namespace Modules\Property\Repositories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Pagination\LengthAwarePaginator;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\TenantUnitView;
use Modules\Property\Enums\PropertyStatus;
use Spatie\QueryBuilder\QueryBuilder;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Illuminate\Support\Facades\Auth;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Service\app\Models\Service;

class PropertyRepository extends RepositoriesAbstract
{

    public TenantUnitView $tenantUnit;
    public function __construct(Property $model, TenantUnitView $tenantUnit)
    {
        $this->tenantUnit = $tenantUnit;
        parent::__construct($model);
    }

    public function getAllWithVal(array $condition = [], array $with = [], array $select = ['*']): LengthAwarePaginator
    {

        return QueryBuilder::for($this->model::class)
            ->select($select)
            ->with($with)
            ->where($condition)
            ->where(['parent_id' => null, 'status' => PropertyStatus::ACTIVE])
            // Where has active published units with active vals
            ->whereHas('publishedUnits', function ($query) {
                $query->whereHas('vals', function ($q) {
                    $today = Carbon::today()->format('Y-m-d');
                    $q->where('active', 1)
                        ->whereDate('start_date', '<=', $today)
                        ->whereDate('end_date', '>=', $today);
                });
            })
            // Where the related company has active vals with the specified date range
            ->whereHas('company', function ($companyQuery) {
                $companyQuery->whereHas('vals', function ($valQuery) {
                    $today = Carbon::today()->format('Y-m-d');
                    $valQuery->where('active', 1)
                        ->whereDate('start_date', '<=', $today)
                        ->whereDate('end_date', '>=', $today);
                });
            })
            ->allowedFilters($this->model->getAllowedFilters())
            ->orderBy('id', request()->input('sort') ?? 'DESC')
            ->paginate(request()->limit ?? 10)
            ->appends(request()->query());
    }

    public function getAllTenantUnitsBy(array $condition=[], array $with = [], array $select = ['*']): LengthAwarePaginator
    {
        $account = auth()->user();
        $defaultRole = $account->accountRoles()
            ->where('is_default', true)
            ->first();
        return QueryBuilder::for($this->tenantUnit::class)
            ->select($select)
            ->with($with)
            ->withoutGlobalScope(SoftDeletingScope::class)
            ->where($condition)
            ->where('member_role', $defaultRole->role) //todo change to enum values
            ->paginate(request()->limit ?? 10)
            ->appends(request()->query());
    }

    public function getAllPrices ($unit_id){
        return $this->model->with(['prices' => function($query) {
            $query->orderByDesc('annual_price');
        }])->find($unit_id);
    }

    private function getUserPropertiesQuery($propertyId = null)
    {
        $propertiesQuery = Property::whereNull('parent_id')
            ->whereHas('owners', function($q) {
                $q->where('ownerable_id', Auth::user()->id)
                ->where('ownerable_type', 'Modules\\Account\\app\\Models\\Account');
            });
        
        if ($propertyId) {
            $propertiesQuery->where('id', $propertyId);
        }
        
        return $propertiesQuery;
    }

    public function getPropertiesStats($propertyId = null)
    {
        $properties = $this->getUserPropertiesQuery($propertyId)->with('units')->get();
        $totalProperties = $properties->count();
        
        $allUnits = collect();
        foreach ($properties as $property) {
            $allUnits = $allUnits->merge($property->units);
        }
        
        $totalUnits = $allUnits->count();
        
        $availableUnits = $allUnits->where('status', PropertyStatus::ACTIVE->value)->count();
        
        $rentedUnits = $allUnits->where('status', PropertyStatus::RENTED->value)->count();
        
        $nonActiveUnits = $allUnits->where('status', '!=', PropertyStatus::ACTIVE->value)->count();
        

        return [
            'total_properties' => $totalProperties,
            'total_units' => $totalUnits,
            'available_units' => $availableUnits,
            'rented_units' => $rentedUnits,
            'non_active_units' => $nonActiveUnits,
        ];
    }

    public function getLeasesStats($propertyId = null)
    {
        $leases = Lease::select('status', 'lease_type','property_id');
        

        if($propertyId){
            $leases->where('property_id', $propertyId);
        }
        $leases = $leases->get();
        $totalLeases = $leases->count();
        
        $activeLeases = $leases->whereIn('status', LeaseEnum::getActiveStatus())
        ->count();
        $nonActiveLeases = $totalLeases - $activeLeases;
        
        $residentialLeases = $leases->where('lease_type', LeaseTypesEnum::RESIDENTIAL)
            ->count();
        $commercialLeases = $leases ->where('lease_type', LeaseTypesEnum::COMMERCIAL)
            ->count();

        
        return [
            'total_leases' => $totalLeases,
            'active_leases' => $activeLeases,
            'non_active_leases' => $nonActiveLeases,
            'residential_leases' => $residentialLeases,
            'commercial_leases' => $commercialLeases,
        ];
    }

    public function getFinancialStats($propertyId = null)
    {
        $today = now()->format('Y-m-d');
        $endDate = now()->endOfMonth()->format('Y-m-d');
        
        $properties = $this->getUserPropertiesQuery($propertyId)->get();
        $propertyIds = $properties->pluck('id')->toArray();
    
        $leaseIds = Lease::whereIn('property_id', $propertyIds)->pluck('id')->toArray();
    
        $invoices = Invoice::select('id','invoice_type','paid','total','due_date','release_date','remaining','extra')
            ->with('items')
            ->where(function($query) use ($leaseIds) {
                $query->whereHas('invoice_schedule', function($q) use ($leaseIds) {
                    $q->whereIn('lease_id', $leaseIds);
                })
                ->orWhere(function($q) use ($leaseIds) {
                    foreach ($leaseIds as $leaseId) {
                        $q->orWhereJsonContains('extra->lease_id', $leaseId);
                    }
                });
            })
            ->get();

    
        $upcomingInvoices = InvoiceSchedule::select('installment_date','total_amount')
            ->whereIn('lease_id', $leaseIds)
            ->get();
    
        $totalInvoiceAmount = $upcomingInvoices->sum('total_amount');
    
        $totalEarnings = $invoices->sum('paid');
    
        $delayedInvoices = $invoices->filter(function ($invoice) use ($today) {
            return $today > $invoice->due_date && $invoice->remaining > 0;
        });
        $totalDelayedInvoices = $delayedInvoices->sum('remaining');
    
        $upcomingMonthlyInvoices = $upcomingInvoices->whereBetween('installment_date', [$today, $endDate]);
        $totalUpcomingMonthly = $upcomingMonthlyInvoices->sum('total_amount');
    
        $dueInvoices = $invoices->filter(function ($invoice) use ($today) {
            return $invoice->release_date <= $today && 
                   $today <= $invoice->due_date && 
                   $invoice->remaining > 0;
        });
        $totalDue = $dueInvoices->sum('remaining');
    
        return [
            'total_earnings' => $totalEarnings,
            'total_invoice_amount' => $totalInvoiceAmount,
            'delayed_invoices' => $totalDelayedInvoices,
            'upcoming_monthly' => $totalUpcomingMonthly,
            'total_due' => $totalDue,
        ];
    }
}
