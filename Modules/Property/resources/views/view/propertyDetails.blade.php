{{-- @dd($unitCount) --}}
<div class="w-full flex flex-col gap-6">
    <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 w-full p-6">
        <div class="w-full flex flex-col gap-1 border-b-[0.4px] dark:border-b-[#ffffff2d] pb-4">
            <p class="text-[#112c24] font-semibold text-lg dark:text-primary-400 flex justify-between w-full items-center">
                <span class=" flex items-center">
                    {{$record->name}}
                    <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90 hidden sm:block"/>
                    <span class="text-[#6F6F6F] dark:text-white text-base font-semibold">
                        {{$record->number}}
                    </span>
                </span>
                <span @class([
                    'text-white rounded-3xl px-6 py-1 text-[12px] font-bold',
                    'bg-gray-500' => $record->status->getColor() === 'gray',
                    'bg-[#31846C]' => $record->status->getColor() === 'success',
                    'bg-red-500' => $record->status->getColor() === 'danger',
                    'bg-yellow-500' => $record->status->getColor() === 'warning',
                    'bg-blue-500' => $record->status->getColor() === 'info',
                ])>
                    {{\Modules\Property\Enums\PropertyStatus::getLabel($record->status)}}
                </span>
            </p>
            <div class="w-full flex gap-1 justify-between flex-wrap">
                <div class="flex flex-wrap gap-2 sm:gap-0">
                    <div class="flex items-center gap-1">
                        <x-heroicon-o-map-pin class="text-primary-600 w-4 h-4 dark:text-primary-400"/>
                        <span class="text-[#112c24] text-sm dark:text-primary-300">
                            {{__('Addresss')}}:
                            <span class="text-[#6F6F6F] text-sm dark:text-white">
                                {{  $record->address }}
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-full flex gap-1 pt-4 justify-between flex-wrap">
            <div class="flex flex-wrap gap-2 sm:gap-0 pt-4">
                <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M13.2004 14.3116V15.2262C13.2004 15.5312 12.9511 15.7805 12.6461 15.7805H1.63501C1.32825 15.7805 1.07959 15.5309 1.07959 15.2262V0.775146C1.07959 0.468636 1.3285 0.219727 1.63501 0.219727H10.9414C11.0939 0.219727 11.2259 0.27513 11.333 0.382299L13.0378 2.08708C13.145 2.19425 13.2004 2.32627 13.2004 2.47877V7.79516C13.5404 7.71055 13.9133 7.73923 14.2324 7.92348C14.9086 8.31391 15.088 9.26917 14.7499 9.85474C14.4326 10.4043 13.983 10.5835 13.4877 10.5621C13.3408 10.6796 13.2509 10.8305 13.1777 10.9905C13.1089 11.141 13.0541 11.2997 12.9942 11.4553L13.5434 11.7724C13.6634 11.8417 13.7046 11.9952 13.6353 12.1152L13.3391 12.6282L14.1643 13.1046C14.2219 13.1379 14.264 13.1927 14.2812 13.257C14.2984 13.3213 14.2894 13.3898 14.2561 13.4475L13.7435 14.3354C13.6742 14.4554 13.5207 14.4965 13.4006 14.4272L13.2004 14.3116ZM10.8155 0.721687H1.63501C1.60552 0.721687 1.58155 0.745625 1.58155 0.775146V15.2262C1.58155 15.2553 1.60577 15.2786 1.63501 15.2786H12.6461C12.6749 15.2786 12.6984 15.255 12.6984 15.2262V14.559C12.4827 14.7092 12.1995 14.7245 11.9623 14.5876L9.59617 13.2215C9.3136 13.0583 9.18547 12.7305 9.28263 12.419L9.3802 12.106L9.01593 11.8957C8.89587 11.8264 8.85474 11.6729 8.92404 11.5528L9.43667 10.6649C9.46996 10.6073 9.52477 10.5652 9.58908 10.548C9.65336 10.5308 9.72188 10.5398 9.77951 10.5731L10.6047 11.0495L10.9009 10.5365C10.9341 10.4788 10.989 10.4368 11.0533 10.4195C11.1175 10.4023 11.1861 10.4114 11.2437 10.4446L11.7928 10.7617C11.8831 10.6497 11.9773 10.5399 12.0633 10.4256C12.1724 10.2805 12.2687 10.1278 12.3093 9.94315C12.3133 9.92504 12.3172 9.90541 12.3202 9.88975C12.309 9.8713 12.2937 9.84608 12.2803 9.82274C12.049 9.41982 12.0003 8.96203 12.3012 8.44097C12.3954 8.27777 12.533 8.13373 12.6984 8.02007V2.6046H11.2187C10.9977 2.6046 10.8155 2.42242 10.8155 2.20141V0.721687ZM12.3435 2.10264L11.3175 1.07664V2.10264H12.3435ZM12.5549 11.2016C12.6083 11.0605 12.659 10.9178 12.7212 10.7818C12.8278 10.5486 12.966 10.3341 13.1819 10.164C13.2732 10.0921 13.3103 10.0762 13.358 10.0657C13.3871 10.0593 13.4239 10.056 13.4911 10.0598C13.8152 10.078 14.1082 9.96222 14.3152 9.60376C14.5332 9.2261 14.4176 8.60997 13.9815 8.35817C13.6947 8.19262 13.3413 8.23184 13.0698 8.38026C12.9295 8.45697 12.81 8.56363 12.7359 8.69195C12.5441 9.02415 12.5681 9.31588 12.7156 9.57276C12.7762 9.67824 12.8039 9.72338 12.8162 9.77132C12.8308 9.82835 12.834 9.89449 12.7996 10.051C12.7155 10.4329 12.4655 10.7194 12.2305 11.0143L12.5549 11.2016ZM12.9044 12.3772C12.9852 12.2374 13.0751 12.0816 13.0751 12.0816C13.0751 12.0816 12.8492 11.9511 12.5546 11.7811C12.553 11.7802 12.5514 11.7793 12.5498 11.7783C12.2952 11.6313 11.99 11.4551 11.7353 11.3081C11.7337 11.3072 11.7322 11.3063 11.7306 11.3053C11.436 11.1353 11.2101 11.0048 11.2101 11.0048L11.0394 11.3004L12.9044 12.3772ZM9.48426 11.5865L13.4343 13.867L13.6959 13.4138L9.74591 11.1333L9.48426 11.5865ZM9.82579 12.3633L9.76182 12.5684C9.7354 12.6532 9.77029 12.7424 9.84721 12.7868L12.2133 14.1529C12.2902 14.1973 12.3849 14.1829 12.4451 14.1176L12.5908 13.9596L9.82579 12.3633ZM3.20856 3.93628L2.71529 3.91717C2.54023 3.91039 2.38764 3.79601 2.33198 3.62989C2.27633 3.46377 2.32922 3.28056 2.46484 3.16969L4.64728 1.38556C4.80251 1.25869 5.0256 1.25869 5.1808 1.38556L7.36323 3.16969C7.49888 3.28056 7.55178 3.46377 7.49612 3.62989C7.44047 3.79601 7.28784 3.91039 7.11278 3.91717L6.61954 3.93628V6.07676C6.61954 6.21537 6.50717 6.32774 6.36856 6.32774H3.45954C3.32094 6.32774 3.20856 6.21537 3.20856 6.07676V3.93628ZM6.11758 3.85424C6.11586 3.85286 6.11413 3.85148 6.11244 3.8501L4.91404 2.87042L3.71567 3.8501C3.71397 3.85148 3.71225 3.85286 3.71052 3.85424V5.82578H4.17813V4.52611C4.17813 4.38748 4.29048 4.27513 4.42911 4.27513H5.39899C5.5376 4.27513 5.64997 4.38748 5.64997 4.52611V5.82578H6.11758V3.85424ZM4.68009 5.82578H5.14801V4.77709H4.68009V5.82578ZM2.94707 3.4238L3.42155 3.44219L4.7552 2.35196C4.84762 2.27639 4.98048 2.27639 5.07291 2.35196L6.40652 3.44219L6.881 3.4238L4.91404 1.81584L2.94707 3.4238ZM2.56357 14.2284C2.42503 14.2284 2.31259 14.116 2.31259 13.9775C2.31259 13.8389 2.42503 13.7265 2.56357 13.7265H9.64125C9.77976 13.7265 9.89223 13.8389 9.89223 13.9775C9.89223 14.116 9.77976 14.2284 9.64125 14.2284H2.56357ZM8.37951 4.74939C8.241 4.74939 8.12853 4.63692 8.12853 4.49841C8.12853 4.3599 8.241 4.24743 8.37951 4.24743H11.7138C11.8523 4.24743 11.9647 4.3599 11.9647 4.49841C11.9647 4.63692 11.8523 4.74939 11.7138 4.74939H8.37951ZM2.56357 11.9482C2.42503 11.9482 2.31259 11.8357 2.31259 11.6972C2.31259 11.5587 2.42503 11.4462 2.56357 11.4462H8.37951C8.51802 11.4462 8.63049 11.5587 8.63049 11.6972C8.63049 11.8357 8.51802 11.9482 8.37951 11.9482H2.56357ZM2.56357 10.8081C2.42503 10.8081 2.31259 10.6956 2.31259 10.5571C2.31259 10.4186 2.42503 10.3061 2.56357 10.3061H8.67611C8.81462 10.3061 8.92709 10.4186 8.92709 10.5571C8.92709 10.6956 8.81462 10.8081 8.67611 10.8081H2.56357ZM2.56357 13.0883C2.42503 13.0883 2.31259 12.9759 2.31259 12.8373C2.31259 12.6988 2.42503 12.5863 2.56357 12.5863H8.67611C8.81462 12.5863 8.92709 12.6988 8.92709 12.8373C8.92709 12.9759 8.81462 13.0883 8.67611 13.0883H2.56357ZM8.37951 5.88953C8.241 5.88953 8.12853 5.77706 8.12853 5.63855C8.12853 5.50001 8.241 5.38757 8.37951 5.38757H11.7138C11.8523 5.38757 11.9647 5.50001 11.9647 5.63855C11.9647 5.77706 11.8523 5.88953 11.7138 5.88953H8.37951ZM2.56357 7.3877C2.42503 7.3877 2.31259 7.27523 2.31259 7.13672C2.31259 6.99817 2.42503 6.88574 2.56357 6.88574H11.7165C11.855 6.88574 11.9674 6.99817 11.9674 7.13672C11.9674 7.27523 11.855 7.3877 11.7165 7.3877H2.56357ZM2.56357 8.52781C2.42503 8.52781 2.31259 8.41537 2.31259 8.27682C2.31259 8.13832 2.42503 8.02585 2.56357 8.02585H11.7165C11.855 8.02585 11.9674 8.13832 11.9674 8.27682C11.9674 8.41537 11.855 8.52781 11.7165 8.52781H2.56357ZM8.37951 3.60928C8.241 3.60928 8.12853 3.49681 8.12853 3.3583C8.12853 3.21976 8.241 3.10732 8.37951 3.10732H11.7138C11.8523 3.10732 11.9647 3.21976 11.9647 3.3583C11.9647 3.49681 11.8523 3.60928 11.7138 3.60928H8.37951ZM2.56357 9.66795C2.42503 9.66795 2.31259 9.55548 2.31259 9.41697C2.31259 9.27843 2.42503 9.16599 2.56357 9.16599H11.3857C11.5242 9.16599 11.6366 9.27843 11.6366 9.41697C11.6366 9.55548 11.5242 9.66795 11.3857 9.66795H2.56357Z"
                        class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                    </svg>
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Document Type')}}:
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ $document->metadata['document_name_and_type'] ?? $document->metadata['document_type'] ?? '' }}
                        </span>
                    </span>
                </div>
                <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90 hidden sm:block"/>
                <div class="flex items-center gap-1">
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Document Number')}}
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ $document->metadata['real_estate_number'] ?? $document->metadata['ownership_reference_no'] ??'' }}
                        </span>
                    </span>
                </div>
            </div>
            <div class="flex flex-wrap gap-2 sm:gap-0 pt-4">
                <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                        <path d="M15.9775 9.30585C15.9775 9.30577 15.9775 9.30573 15.9775 9.30565L12.9566 1.72052C12.8925 1.55935 12.7098 1.48067 12.5486 1.54479C12.5485 1.54483 12.5484 1.54487 12.5483 1.54494L0.293041 6.42487C0.243062 6.44646 0.199718 6.48094 0.167407 6.52475C0.0654866 6.57771 0.00109929 6.68254 0 6.79737V14.9636C0 15.137 0.140631 15.2776 0.314084 15.2776H13.5056C13.6791 15.2776 13.8197 15.137 13.8197 14.9636V10.5036L15.8022 9.71396C15.9633 9.64969 16.0418 9.46697 15.9775 9.30585ZM13.1915 14.6495H0.628168V7.11146H13.1915V14.6495ZM5.2405 6.48486L10.4995 4.38929C10.9689 4.88264 11.6192 5.16304 12.3002 5.16571L12.8247 6.48486H5.2405ZM13.8197 9.82891V6.79737C13.8197 6.62392 13.6791 6.48329 13.5056 6.48329H13.5006L12.8351 4.81173C12.8277 4.7978 12.8191 4.78453 12.8093 4.77216C12.7756 4.62089 12.6355 4.51767 12.4811 4.53031C11.8475 4.58807 11.2277 4.32117 10.8343 3.82111C10.7365 3.70031 10.5644 3.67027 10.4314 3.75076C10.4164 3.75252 10.4016 3.75527 10.3871 3.75892L3.96657 6.31557C3.88887 6.34604 3.82672 6.40646 3.79414 6.48329H1.8443L12.4892 2.24472L15.278 9.24723L13.8197 9.82891Z" class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                        <path d="M1.4806 12.5292C2.0904 12.7095 2.56757 13.186 2.74856 13.7956C2.78825 13.9287 2.91063 14.0198 3.04945 14.0199C3.06575 14.0188 3.08192 14.0164 3.09782 14.0126C3.11192 14.0166 3.12633 14.0196 3.14085 14.0214H10.6789C10.6918 14.0197 10.7046 14.0171 10.7172 14.0136C10.8741 14.0517 11.0323 13.9555 11.0705 13.7986C11.0706 13.798 11.0707 13.7974 11.0709 13.7969C11.2514 13.1867 11.7286 12.7094 12.3388 12.5289C12.4868 12.4837 12.5787 12.3362 12.5543 12.1834C12.5585 12.1682 12.5615 12.1526 12.5634 12.1369V9.62425C12.5615 9.60851 12.5584 9.59296 12.554 9.57777C12.5786 9.42493 12.4866 9.27738 12.3385 9.23227C11.7283 9.05179 11.2512 8.57435 11.0712 7.964C11.0257 7.81501 10.8768 7.72282 10.7232 7.74854C10.7086 7.7445 10.6938 7.74155 10.6789 7.73975H3.14085C3.12503 7.74163 3.10937 7.74477 3.09405 7.74917C2.94129 7.72483 2.79391 7.81674 2.74856 7.96463C2.56792 8.5747 2.09071 9.05179 1.4806 9.23227C1.33263 9.2775 1.24068 9.42496 1.26514 9.57777C1.26106 9.59304 1.25811 9.60855 1.25635 9.62425V12.1369C1.25815 12.1517 1.2611 12.1663 1.26514 12.1806C1.23939 12.3344 1.33153 12.4835 1.4806 12.5292ZM1.88452 9.75177C2.5152 9.49846 3.01506 8.9986 3.26837 8.36791H10.551C10.8045 8.9986 11.3045 9.49846 11.9352 9.75177V12.0094C11.3047 12.263 10.8049 12.7628 10.5514 13.3933H3.26837C3.01479 12.7628 2.515 12.263 1.88452 12.0094V9.75177Z" class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                        <path d="M6.9099 12.7651C7.95069 12.7651 8.7944 11.9214 8.7944 10.8806C8.7944 9.8398 7.95069 8.99609 6.9099 8.99609C5.8691 8.99609 5.02539 9.8398 5.02539 10.8806C5.02641 11.921 5.86953 12.7641 6.9099 12.7651ZM6.9099 9.62426C7.60375 9.62426 8.16623 10.1867 8.16623 10.8806C8.16623 11.5745 7.60375 12.1369 6.9099 12.1369C6.21604 12.1369 5.65356 11.5745 5.65356 10.8806C5.65356 10.1867 6.21604 9.62426 6.9099 9.62426Z" class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                        <path d="M3.45501 11.3514C3.71519 11.3514 3.92614 11.1405 3.92614 10.8803C3.92614 10.6201 3.71519 10.4092 3.45501 10.4092C3.19483 10.4092 2.98389 10.6201 2.98389 10.8803C2.98389 11.1405 3.19483 11.3514 3.45501 11.3514ZM3.45501 10.7233C3.54174 10.7233 3.61206 10.7936 3.61206 10.8803C3.61206 10.967 3.54174 11.0373 3.45501 11.0373C3.36829 11.0373 3.29797 10.967 3.29797 10.8803C3.29797 10.7936 3.36829 10.7233 3.45501 10.7233Z" class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                        <path d="M10.3647 11.3514C10.6249 11.3514 10.8358 11.1405 10.8358 10.8803C10.8358 10.6201 10.6249 10.4092 10.3647 10.4092C10.1045 10.4092 9.89355 10.6201 9.89355 10.8803C9.89355 11.1405 10.1045 11.3514 10.3647 11.3514ZM10.3647 10.7233C10.4514 10.7233 10.5217 10.7936 10.5217 10.8803C10.5217 10.967 10.4514 11.0373 10.3647 11.0373C10.278 11.0373 10.2076 10.967 10.2076 10.8803C10.2076 10.7936 10.278 10.7233 10.3647 10.7233Z" class="fill-primary-600 w-4 h-4 dark:fill-primary-400"/>
                    </svg>
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        {{__('Price')}}:
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ number_format($record->getMinUnitPrice()) }} <span class="icon-saudi_riyal"></span>
                        </span>
                    </span>
                </div>
                <x-heroicon-o-minus class="text-primary-600 w-6 h-6 dark:text-primary-400 rotate-90"/>
                <div class="flex items-center gap-1">
                    <span class="text-[#112c24] text-sm dark:text-primary-300">
                        <span class="text-[#6F6F6F] text-sm dark:text-white">
                            {{ number_format($record->getMaxUnitPrice()) }} <span class="icon-saudi_riyal"></span>
                        </span>
                    </span>
                </div>
            </div>
        </div>

    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none" class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3">
                    <path d="M54.7334 44.6753V21.0337H36.0262V30.6337L25.895 33.6417V58.9153H11.6422C8.44225 58.9153 5.82465 56.9953 5.82465 54.6721C5.82465 52.5793 7.84705 50.8833 10.5286 50.4929V53.5201C10.5286 56.7201 15.6486 57.6353 18.0486 56.1953C19.2902 55.4785 19.6166 54.5633 19.6166 53.1937V11.2417H52.4358V17.3793H54.7334V8.9569H19.4182C18.7654 5.9489 15.495 3.6001 11.5078 3.6001C7.13025 3.6001 3.53345 6.5377 3.53345 10.1281V54.6721C3.53345 58.3265 7.06625 61.2001 11.6422 61.2001H61.1334V44.6753H54.7334ZM46.9446 53.1041H49.2358V49.5777H46.9446V53.1041ZM41.5174 53.1041H43.8086V49.5777H41.5174V53.1041ZM46.9446 45.7249H49.2358V42.1985H46.9446V45.7249ZM41.5174 45.7249H43.8086V42.1985H41.5174V45.7249ZM46.9446 38.3457H49.2358V34.8193H46.9446V38.3457ZM41.5174 38.3457H43.8086V34.8193H41.5174V38.3457ZM46.9446 30.9665H49.2358V27.4337H46.9446V30.9665ZM41.5174 30.9665H43.8086V27.4337H41.5174V30.9665ZM15.239 54.5377C13.927 54.5377 12.8198 54.0193 12.8198 53.4945V50.4929C14.3077 50.61 15.7125 51.2256 16.807 52.2401C18.087 53.6801 16.807 54.5249 15.239 54.5249V54.5377ZM11.5078 5.8977C14.7078 5.8977 17.3318 7.8177 17.3318 10.1409V49.6801C15.6224 48.6283 13.6491 48.0845 11.6422 48.1121C9.53744 48.0886 7.48677 48.7789 5.82465 50.0705V10.1281C5.82465 7.3249 9.15905 5.8849 11.5078 5.8849V5.8977ZM33.2166 58.9153V40.9953H30.9254V58.9153H28.1798V35.3377L36.0262 33.0401V58.9153H33.2166ZM52.4166 58.9153H38.3174V23.3249H52.4358L52.4166 58.9153ZM58.8166 58.9153H54.7334V54.0833H57.2166V51.7985H54.7334V46.9665H58.8486L58.8166 58.9153Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"  />
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400 ">
                    {{$unitCount}}
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white ">
                    {{__('Total Units')}}
                </span>
            </p>
        </div>
            <!-- Available Units -->
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">

                <svg class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3" xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none">
                    <g clip-path="url(#clip0_8908_47553)">
                        <path d="M63.6001 62.2665H59.3335V27.0665C59.3335 26.4774 58.8559 25.9999 58.2669 25.9999H40.1334V1.46653C40.1334 0.877402 39.6558 0.399902 39.0667 0.399902H7.06675C6.47762 0.399902 6.00012 0.877527 6.00012 1.46653V62.2665H1.73337C1.14425 62.2665 0.666748 62.7442 0.666748 63.3332C0.666748 63.9222 1.14437 64.3999 1.73337 64.3999H7.06675H39.0667H58.2667H63.6001C64.1893 64.3999 64.6667 63.9223 64.6667 63.3333C64.6667 62.7443 64.1891 62.2665 63.6001 62.2665ZM38.0001 27.0665V62.2665H8.13337V2.53328H38V27.0665H38.0001ZM57.2001 62.2665H40.1334V28.1333H57.2V62.2665H57.2001Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M13.4668 15.3331H19.8668C20.4559 15.3331 20.9334 14.8554 20.9334 14.2664V7.86643C20.9334 7.2773 20.4558 6.7998 19.8668 6.7998H13.4668C12.8776 6.7998 12.4001 7.27743 12.4001 7.86643V14.2664C12.4001 14.8554 12.8778 15.3331 13.4668 15.3331ZM14.5334 8.93305H18.8V13.1997H14.5334V8.93305Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M26.2668 15.3331H32.6668C33.2559 15.3331 33.7334 14.8554 33.7334 14.2664V7.86643C33.7334 7.2773 33.2558 6.7998 32.6668 6.7998H26.2668C25.6777 6.7998 25.2002 7.27743 25.2002 7.86643V14.2664C25.2002 14.8554 25.6777 15.3331 26.2668 15.3331ZM27.3334 8.93305H31.6001V13.1997H27.3334V8.93305Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M13.4668 28.1333H19.8668C20.4559 28.1333 20.9334 27.6557 20.9334 27.0667V20.6667C20.9334 20.0776 20.4558 19.6001 19.8668 19.6001H13.4668C12.8776 19.6001 12.4001 20.0777 12.4001 20.6667V27.0667C12.4001 27.6557 12.8778 28.1333 13.4668 28.1333ZM14.5334 21.7333H18.8V26H14.5334V21.7333Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M26.2668 28.1333H32.6668C33.2559 28.1333 33.7334 27.6557 33.7334 27.0667V20.6667C33.7334 20.0776 33.2558 19.6001 32.6668 19.6001H26.2668C25.6777 19.6001 25.2002 20.0777 25.2002 20.6667V27.0667C25.2002 27.6557 25.6777 28.1333 26.2668 28.1333ZM27.3334 21.7333H31.6001V26H27.3334V21.7333Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M13.4668 40.9332H19.8668C20.4559 40.9332 20.9334 40.4555 20.9334 39.8665V33.4665C20.9334 32.8774 20.4558 32.3999 19.8668 32.3999H13.4668C12.8776 32.3999 12.4001 32.8775 12.4001 33.4665V39.8665C12.4001 40.4555 12.8778 40.9332 13.4668 40.9332ZM14.5334 34.5331H18.8V38.7998H14.5334V34.5331Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M26.2668 40.9332H32.6668C33.2559 40.9332 33.7334 40.4555 33.7334 39.8665V33.4665C33.7334 32.8774 33.2558 32.3999 32.6668 32.3999H26.2668C25.6777 32.3999 25.2002 32.8775 25.2002 33.4665V39.8665C25.2002 40.4555 25.6777 40.9332 26.2668 40.9332ZM27.3334 34.5331H31.6001V38.7998H27.3334V34.5331Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M13.4668 53.7334H19.8668C20.4559 53.7334 20.9334 53.2558 20.9334 52.6668V46.2668C20.9334 45.6777 20.4558 45.2002 19.8668 45.2002H13.4668C12.8776 45.2002 12.4001 45.6778 12.4001 46.2668V52.6668C12.4001 53.2558 12.8778 53.7334 13.4668 53.7334ZM14.5334 47.3334H18.8V51.6001H14.5334V47.3334Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M26.2668 53.7334H32.6668C33.2559 53.7334 33.7334 53.2558 33.7334 52.6668V46.2668C33.7334 45.6777 33.2558 45.2002 32.6668 45.2002H26.2668C25.6777 45.2002 25.2002 45.6778 25.2002 46.2668V52.6668C25.2002 53.2558 25.6777 53.7334 26.2668 53.7334ZM27.3334 47.3334H31.6001V51.6001H27.3334V47.3334Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M45.4668 40.9332H51.8668C52.4559 40.9332 52.9334 40.4555 52.9334 39.8665V33.4665C52.9334 32.8774 52.4558 32.3999 51.8668 32.3999H45.4668C44.8776 32.3999 44.4001 32.8775 44.4001 33.4665V39.8665C44.4001 40.4555 44.8778 40.9332 45.4668 40.9332ZM46.5334 34.5331H50.8V38.7998H46.5334V34.5331Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                        <path d="M45.4668 53.7334H51.8668C52.4559 53.7334 52.9334 53.2558 52.9334 52.6668V46.2668C52.9334 45.6777 52.4558 45.2002 51.8668 45.2002H45.4668C44.8776 45.2002 44.4001 45.6778 44.4001 46.2668V52.6668C44.4001 53.2558 44.8778 53.7334 45.4668 53.7334ZM46.5334 47.3334H50.8V51.6001H46.5334V47.3334Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    </g>
                    <defs>
                        <clipPath id="clip0_8908_47553">
                            <rect width="64" height="64" fill="white" transform="translate(0.666748 0.399902)"/>
                        </clipPath>
                    </defs>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400">
                    {{ $record->publishedUnits()->whereDoesntHave('activeLease')->count() }}
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white">
                    {{ __('Available Units') }}
                </span>
            </p>
        </div>

        <!-- Rented Units -->
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">
                <svg class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3" xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.21191 62.5175V2.28224C1.21191 1.72783 1.66142 1.27832 2.21584 1.27832H62.4511C63.0056 1.27832 63.4551 1.72783 63.4551 2.28224V62.5175C63.4551 63.072 63.0056 63.5215 62.4511 63.5215H2.21584C1.66142 63.5215 1.21191 63.072 1.21191 62.5175ZM31.3296 3.28616H3.21976V31.396H31.3296V3.28616ZM61.4472 31.396V3.28616H33.3374V31.396H61.4472ZM33.3374 61.5136H61.4472V33.4038H33.3374V61.5136ZM3.21976 33.4038V61.5136H31.3296V33.4038H3.21976ZM52.1583 6.24033H53.5591C54.1136 6.24033 54.5631 6.68983 54.5631 7.24425V30.0712C54.5631 30.6256 54.1136 31.0751 53.5591 31.0751H41.2255C40.6711 31.0751 40.2215 30.6256 40.2215 30.0712V7.24425C40.2215 6.68983 40.6711 6.24033 41.2255 6.24033H42.6263V4.61096C42.6263 4.05655 43.0758 3.60704 43.6302 3.60704H51.1544C51.7088 3.60704 52.1583 4.05655 52.1583 4.61096V6.24033ZM51.1544 8.24817H42.2294V29.0672H52.5552V8.24817H51.1544ZM50.1505 6.24033V5.61489H44.6342V6.24033H50.1505ZM50.6789 28.896H44.1057C43.5513 28.896 43.1018 28.4464 43.1018 27.892V9.59543C43.1018 9.04089 43.5513 8.59151 44.1057 8.59151H50.6789C51.2333 8.59151 51.6828 9.04089 51.6828 9.59543V27.892C51.6828 28.4464 51.2333 28.896 50.6789 28.896ZM46.3884 10.5994C45.6651 10.5994 45.1096 10.5994 45.1096 10.5994V26.8881H46.3884V10.5994ZM48.3962 26.8881H49.675V10.5994C49.675 10.5994 49.1196 10.5994 48.3962 10.5994V26.8881ZM29.2182 59.12C24.43 59.1232 5.33188 59.12 5.33188 59.12C4.77734 59.12 4.32796 58.6705 4.32796 58.1161V45.3856C4.32796 44.8312 4.77734 44.3817 5.33188 44.3817H12.2898V38.5342C12.2898 37.9797 12.7392 37.5303 13.2937 37.5303H13.8655V35.9371C13.8655 35.3827 14.315 34.9333 14.8694 34.9333H19.6799C20.2343 34.9333 20.6839 35.3827 20.6839 35.9371V37.5303H21.2556C21.81 37.5303 22.2595 37.9797 22.2595 38.5342V44.3817H29.2174C29.772 44.3817 30.2214 44.8312 30.2214 45.3856V58.1161C30.2214 58.6703 29.7724 59.1196 29.2182 59.12ZM12.2898 46.3895H6.3358V57.1122H12.2898V46.3895ZM20.2517 39.5382H14.2977V57.1122H20.2517V39.5382ZM18.676 37.5303V36.941H15.8733V37.5303H18.676ZM22.2595 57.1146C23.3504 57.1152 25.3351 57.1146 28.2135 57.1128V46.3895H22.2595V57.1146ZM11.8166 48.1336V55.3682C11.8166 55.9227 11.3671 56.3722 10.8127 56.3722H7.81295C7.25841 56.3722 6.80903 55.9227 6.80903 55.3682V48.1336C6.80903 47.5792 7.25841 47.1297 7.81295 47.1297H10.8127C11.3671 47.1297 11.8166 47.5792 11.8166 48.1336ZM9.80875 49.1375H8.81687V54.3643H9.80875V49.1375ZM19.7784 41.3801V55.3695C19.7784 55.9239 19.3289 56.3734 18.7745 56.3734H15.7748C15.2204 56.3734 14.7709 55.9239 14.7709 55.3695V41.3801C14.7709 40.8257 15.2204 40.3762 15.7748 40.3762H18.7745C19.3289 40.3762 19.7784 40.8257 19.7784 41.3801ZM17.7706 42.384H16.7787V54.3656H17.7706V42.384ZM27.7403 48.1349V55.3695C27.7403 55.9239 27.2908 56.3734 26.7364 56.3734H23.7367C23.1822 56.3734 22.7327 55.9239 22.7327 55.3695V48.1349C22.7327 47.5804 23.1822 47.1309 23.7367 47.1309H26.7364C27.2908 47.1309 27.7403 47.5804 27.7403 48.1349ZM25.7324 49.1388H24.7406V54.3656H25.7324V49.1388ZM9.4831 17.7736L7.02035 17.6783C6.27293 17.6494 5.62126 17.161 5.38358 16.4517C5.14603 15.7424 5.37191 14.9601 5.95105 14.4868L16.1357 6.16089C16.7984 5.61915 17.7509 5.61915 18.4136 6.16089L28.5983 14.4868C29.1774 14.9601 29.4033 15.7424 29.1657 16.4517C28.9281 17.161 28.2764 17.6494 27.529 17.6783L25.0662 17.7736V27.9235C25.0662 28.4781 24.6167 28.9275 24.0623 28.9275H10.487C9.9326 28.9275 9.4831 28.4781 9.4831 27.9235V17.7736ZM23.0584 17.4686C23.0294 17.4476 23.0009 17.4258 22.973 17.4029L17.2747 12.7446L11.5763 17.4029C11.5484 17.4258 11.5199 17.4476 11.4909 17.4686V26.9196H14.0076V20.6871C14.0076 20.1327 14.4572 19.6832 15.0116 19.6832H19.5378C20.0922 19.6832 20.5417 20.1327 20.5417 20.6871V26.9196H23.0584V17.4686ZM16.0155 26.9196H18.5338V21.6911H16.0155V26.9196ZM7.64755 15.6933L10.3665 15.7985L16.6393 10.6708C17.009 10.3685 17.5403 10.3685 17.91 10.6708L24.1828 15.7985L26.9018 15.6933L17.2747 7.82326L7.64755 15.6933ZM37.2798 46.0058L34.826 44.9661C34.4774 44.8184 34.2414 44.4871 34.216 44.1093C34.1905 43.7315 34.3797 43.3715 34.7054 43.1784L35.884 42.4791V38.4995C35.5081 38.3972 35.1618 38.1983 34.8814 37.9179C34.4584 37.4949 34.2207 36.9209 34.2207 36.3226C34.2207 35.7243 34.4584 35.1504 34.8814 34.7272C35.3046 34.3042 35.8785 34.0665 36.4768 34.0665H41.1127C41.711 34.0665 42.2847 34.3042 42.7079 34.7272C43.1309 35.1504 43.3687 35.7243 43.3687 36.3226C43.3687 36.9209 43.1309 37.4949 42.7079 37.9179C42.4277 38.1983 42.0812 38.3972 41.7053 38.4995V39.0258L46.8801 35.9559C47.1959 35.7686 47.5887 35.7686 47.9046 35.9559L60.0792 43.1784C60.4049 43.3715 60.5941 43.7315 60.5687 44.1093C60.5432 44.4871 60.3073 44.8184 59.9586 44.9661L57.5048 46.0058V54.9044H59.567C60.1214 54.9044 60.5709 55.3539 60.5709 55.9083V58.3417C60.5709 58.8961 60.1214 59.3456 59.567 59.3456H35.2176C34.6632 59.3456 34.2137 58.8961 34.2137 58.3417V55.9083C34.2137 55.3539 34.6632 54.9044 35.2176 54.9044H37.2798V46.0058ZM37.8918 41.2881L39.6975 40.2168V38.5788H37.8918V41.2881ZM47.3923 37.9866L37.4377 43.892L38.2102 44.2194L46.8834 39.1195C47.1975 38.9348 47.5871 38.9348 47.9012 39.1195L56.5744 44.2194L57.3469 43.892L47.3923 37.9866ZM36.4768 36.5709H41.1127C41.1784 36.5709 41.2415 36.5448 41.2881 36.4983C41.3346 36.4517 41.3609 36.3885 41.3609 36.3226C41.3609 36.2568 41.3346 36.1936 41.2881 36.147C41.2415 36.1005 41.1784 36.0744 41.1127 36.0744H36.4768C36.4109 36.0744 36.3478 36.1005 36.3012 36.147C36.2547 36.1936 36.2286 36.2568 36.2286 36.3226C36.2286 36.3885 36.2547 36.4517 36.3012 36.4983C36.3478 36.5448 36.4109 36.5709 36.4768 36.5709ZM55.497 54.9044C55.497 52.3703 55.497 45.9152 55.497 45.9152L47.3923 41.1496L39.2876 45.9152V54.9044H55.497ZM36.2215 57.3378H58.5631V56.9123H36.2215V57.3378ZM53.6771 45.9242V49.1969C53.6771 49.7513 53.2276 50.2008 52.6732 50.2008H42.1114C41.557 50.2008 41.1075 49.7513 41.1075 49.1969V45.9242C41.1075 45.3697 41.557 44.9203 42.1114 44.9203H52.6732C53.2276 44.9203 53.6771 45.3697 53.6771 45.9242ZM51.6693 46.9281H43.1154V48.193H51.6693V46.9281Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400">
                    {{ $record->publishedUnits()->whereHas('activeLease')->count() }}
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white">
                    {{ __('Rented Units') }}
                </span>
            </p>
        </div>

        <!-- Total Property Income -->
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">

                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="65" viewBox="0 0 64 65" fill="none" class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3">
                    <path d="M13.625 40.75C11.7214 40.75 9.86046 41.3145 8.27764 42.3721C6.69482 43.4297 5.46116 44.9329 4.73266 46.6917C4.00417 48.4504 3.81356 50.3857 4.18495 52.2527C4.55633 54.1198 5.47302 55.8348 6.8191 57.1809C8.16518 58.527 9.88019 59.4437 11.7473 59.8151C13.6143 60.1864 15.5496 59.9958 17.3083 59.2673C19.0671 58.5388 20.5703 57.3052 21.6279 55.7224C22.6855 54.1395 23.25 52.2786 23.25 50.375C23.2471 47.8232 22.2321 45.3767 20.4277 43.5723C18.6233 41.7679 16.1768 40.7529 13.625 40.75ZM13.625 58.25C12.0675 58.25 10.5449 57.7881 9.24989 56.9228C7.95485 56.0575 6.94549 54.8276 6.34945 53.3886C5.75341 51.9497 5.59746 50.3663 5.90132 48.8387C6.20518 47.3111 6.9552 45.9079 8.05654 44.8065C9.15788 43.7052 10.5611 42.9552 12.0887 42.6513C13.6163 42.3475 15.1997 42.5034 16.6386 43.0994C18.0776 43.6955 19.3075 44.7048 20.1728 45.9999C21.0381 47.2949 21.5 48.8175 21.5 50.375C21.4976 52.4628 20.6671 54.4645 19.1908 55.9408C17.7145 57.4171 15.7128 58.2476 13.625 58.25Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M15.1517 47.2659C15.2808 47.3879 15.3603 47.5533 15.3747 47.7303C15.3693 47.9586 15.4534 48.1798 15.6088 48.347C15.7643 48.5141 15.9789 48.6139 16.2069 48.625C16.4369 48.6291 16.6598 48.5453 16.8301 48.3907C17.0004 48.2362 17.1053 48.0224 17.1234 47.7932C17.1279 47.1468 16.8764 46.5249 16.424 46.0633C15.9007 45.5601 15.2227 45.2487 14.5001 45.1795V45.125C14.5001 44.8929 14.4079 44.6704 14.2438 44.5063C14.0797 44.3422 13.8572 44.25 13.6251 44.25C13.393 44.25 13.1705 44.3422 13.0064 44.5063C12.8423 44.6704 12.7501 44.8929 12.7501 45.125V45.1993C12.0244 45.294 11.3579 45.6496 10.8751 46.1995C10.3922 46.7495 10.126 47.4564 10.126 48.1882C10.126 48.9201 10.3922 49.627 10.8751 50.177C11.3579 50.7269 12.0244 51.0825 12.7501 51.1772V53.7933C12.5093 53.7499 12.2844 53.6431 12.0985 53.4841C11.975 53.3667 11.8961 53.2101 11.8751 53.041C11.8848 52.8101 11.8027 52.5851 11.6468 52.4145C11.4909 52.2439 11.2739 52.1418 11.043 52.1307C10.8121 52.1195 10.5862 52.2001 10.4146 52.3549C10.243 52.5097 10.1395 52.7261 10.1269 52.9568C10.1224 53.6032 10.3738 54.2251 10.8263 54.6867C11.3494 55.19 12.0274 55.5016 12.7501 55.5707V55.625C12.7501 55.8571 12.8423 56.0796 13.0064 56.2437C13.1705 56.4078 13.393 56.5 13.6251 56.5C13.8572 56.5 14.0797 56.4078 14.2438 56.2437C14.4079 56.0796 14.5001 55.8571 14.5001 55.625V55.5507C15.2258 55.456 15.8923 55.1004 16.3751 54.5505C16.858 54.0005 17.1242 53.2936 17.1242 52.5618C17.1242 51.8299 16.858 51.123 16.3751 50.573C15.8923 50.0231 15.2258 49.6675 14.5001 49.5728V46.9575C14.7408 47.0006 14.9657 47.1071 15.1517 47.2659ZM11.876 48.1879C11.8759 47.9226 11.9606 47.6641 12.1179 47.4504C12.2752 47.2367 12.4967 47.0788 12.7501 47V49.3763C12.4967 49.2973 12.2753 49.1393 12.118 48.9256C11.9608 48.7118 11.876 48.4533 11.876 48.1879ZM15.3742 52.5621C15.3743 52.8274 15.2896 53.0859 15.1323 53.2996C14.975 53.5133 14.7535 53.6712 14.5001 53.75V51.3737C14.7535 51.4527 14.9749 51.6107 15.1322 51.8244C15.2894 52.0382 15.3742 52.2967 15.3742 52.5621Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M59.1242 53.875H54.7492V17.125C54.7492 17.0101 54.7266 16.8963 54.6826 16.7901C54.6387 16.6839 54.5742 16.5875 54.493 16.5062C54.4117 16.425 54.3152 16.3605 54.2091 16.3165C54.1029 16.2726 53.9891 16.25 53.8742 16.25H46.8742C46.7593 16.25 46.6455 16.2726 46.5393 16.3165C46.4331 16.3605 46.3366 16.425 46.2554 16.5062C46.1741 16.5875 46.1097 16.6839 46.0657 16.7901C46.0218 16.8963 45.9991 17.0101 45.9992 17.125V53.875H42.4992V22.375C42.4992 22.2601 42.4766 22.1463 42.4326 22.0401C42.3887 21.9339 42.3242 21.8375 42.243 21.7562C42.1617 21.675 42.0652 21.6105 41.9591 21.5665C41.8529 21.5226 41.7391 21.5 41.6242 21.5H34.6242C34.5093 21.5 34.3955 21.5226 34.2893 21.5665C34.1831 21.6105 34.0866 21.675 34.0054 21.7562C33.9241 21.8375 33.8597 21.9339 33.8157 22.0401C33.7718 22.1463 33.7491 22.2601 33.7492 22.375V53.875H30.2492V27.625C30.2492 27.3929 30.157 27.1704 29.9929 27.0063C29.8288 26.8422 29.6062 26.75 29.3742 26.75H22.3742C22.1421 26.75 21.9196 26.8422 21.7555 27.0063C21.5914 27.1704 21.4992 27.3929 21.4992 27.625V41.8087C21.4992 42.0408 21.5914 42.2634 21.7555 42.4275C21.9196 42.5916 22.1421 42.6837 22.3742 42.6837C22.6062 42.6837 22.8288 42.5916 22.9929 42.4275C23.157 42.2634 23.2492 42.0408 23.2492 41.8087V28.5H28.4992V53.875H25.0603C24.8282 53.875 24.6056 53.9672 24.4416 54.1313C24.2775 54.2954 24.1853 54.5179 24.1853 54.75C24.1853 54.9821 24.2775 55.2046 24.4416 55.3687C24.6056 55.5328 24.8282 55.625 25.0603 55.625H58.2492V58.25H22.1904C21.9584 58.25 21.7358 58.3422 21.5717 58.5063C21.4076 58.6704 21.3154 58.8929 21.3154 59.125C21.3154 59.3571 21.4076 59.5796 21.5717 59.7437C21.7358 59.9078 21.9584 60 22.1904 60H59.1242C59.2391 60 59.3529 59.9774 59.4591 59.9334C59.5652 59.8895 59.6617 59.825 59.743 59.7438C59.8242 59.6625 59.8887 59.566 59.9326 59.4599C59.9766 59.3537 59.9992 59.2399 59.9992 59.125V54.75C59.9992 54.6351 59.9766 54.5213 59.9326 54.4151C59.8887 54.3089 59.8242 54.2125 59.743 54.1312C59.6617 54.0499 59.5652 53.9855 59.4591 53.9415C59.3529 53.8976 59.2391 53.875 59.1242 53.875ZM47.7492 18H52.9992V53.875H47.7492V18ZM35.4992 23.25H40.7492V53.875H35.4992V23.25Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M10.125 39.5076C10.3571 39.5076 10.5796 39.4154 10.7437 39.2513C10.9078 39.0872 11 38.8646 11 38.6326V32.875H16.25V38.6326C16.25 38.8646 16.3422 39.0872 16.5063 39.2513C16.6704 39.4154 16.8929 39.5076 17.125 39.5076C17.3571 39.5076 17.5796 39.4154 17.7437 39.2513C17.9078 39.0872 18 38.8646 18 38.6326V32C18 31.7679 17.9078 31.5454 17.7437 31.3813C17.5796 31.2172 17.3571 31.125 17.125 31.125H10.125C9.89294 31.125 9.67038 31.2172 9.50628 31.3813C9.34219 31.5454 9.25 31.7679 9.25 32V38.6326C9.25001 38.8646 9.3422 39.0872 9.50629 39.2513C9.67039 39.4154 9.89294 39.5076 10.125 39.5076Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M13.6241 25.875L13.6664 25.8741C29.3463 25.1272 44.7043 11.5887 49.5 6.97854V12.75C49.5 12.9821 49.5922 13.2046 49.7563 13.3687C49.9204 13.5328 50.1429 13.625 50.375 13.625C50.607 13.625 50.8296 13.5328 50.9937 13.3687C51.1578 13.2046 51.25 12.9821 51.25 12.75V4.875L51.248 4.86485C51.2401 4.63659 51.1453 4.41997 50.9831 4.25923C50.8208 4.09849 50.6033 4.00576 50.375 4H42.5C42.2679 4 42.0454 4.09219 41.8813 4.25628C41.7172 4.42038 41.625 4.64294 41.625 4.875C41.625 5.10706 41.7172 5.32962 41.8813 5.49372C42.0454 5.65781 42.2679 5.75 42.5 5.75H48.2345C43.5194 10.229 28.4301 23.4191 13.5836 24.1259C13.3555 24.1368 13.1406 24.2364 12.985 24.4035C12.8293 24.5706 12.7452 24.7919 12.7505 25.0202C12.7558 25.2485 12.8501 25.4657 13.0133 25.6254C13.1766 25.7851 13.3958 25.8747 13.6241 25.875Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400">
                    {{ number_format($record->getTotalUnitsIncome()) }} <span class="icon-saudi_riyal"></span>
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white">
                    {{ __('Total Property Income') }}
                </span>
            </p>
        </div>

        <!-- Current Property Income -->
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">

                <svg class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3" xmlns="http://www.w3.org/2000/svg" width="64" height="65" viewBox="0 0 64 65" fill="none">
                    <path d="M26.059 27.363C25.8556 27.5664 25.7385 27.8486 25.7385 28.1362C25.7385 28.4239 25.8556 28.7061 26.059 28.9095C26.2624 29.1141 26.5446 29.23 26.8323 29.23C27.1199 29.23 27.4021 29.1141 27.6056 28.9095C27.809 28.7061 27.926 28.425 27.926 28.1362C27.926 27.8486 27.809 27.5664 27.6056 27.363C27.4021 27.1595 27.1199 27.0425 26.8323 27.0425C26.5446 27.0425 26.2624 27.1596 26.059 27.363Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M58.9063 27.0434H31.2073C30.6032 27.0434 30.1135 27.5331 30.1135 28.1372C30.1135 28.7412 30.6032 29.2309 31.2073 29.2309H57.8126V50.8048V51.8V53.9176H25.8376C25.6128 53.4897 25.3161 53.1052 24.9628 52.7812C25.1524 52.6073 25.326 52.4164 25.4808 52.2103H51.5645C52.1686 52.2103 52.6583 51.7206 52.6583 51.1166C52.6583 49.8254 53.7087 48.7749 55 48.7749C55.6041 48.7749 56.0938 48.2853 56.0938 47.6812V35.4674C56.0938 34.8633 55.6041 34.3736 55 34.3736C53.7088 34.3736 52.6583 33.3232 52.6583 32.0319C52.6583 31.4278 52.1686 30.9381 51.5645 30.9381H18.3255C17.7214 30.9381 17.2318 31.4278 17.2318 32.0319C17.2318 33.3231 16.1813 34.3736 14.89 34.3736C14.286 34.3736 13.7963 34.8633 13.7963 35.4674V39.4375H12.0938V33.3985L16.2613 29.231H22.4573C23.0614 29.231 23.551 28.7413 23.551 28.1373C23.551 27.5332 23.0614 27.0435 22.4573 27.0435H18.4488L26.7276 18.7647L30.6022 22.6394C30.8073 22.8445 31.0856 22.9598 31.3756 22.9598C31.6657 22.9598 31.9439 22.8445 32.149 22.6394L41.0648 13.7236L42.6347 15.2936C42.9197 15.5787 43.3385 15.684 43.7244 15.5672C44.1103 15.4506 44.4008 15.1313 44.4803 14.736L46.3774 5.30958C46.4497 4.95061 46.3375 4.57928 46.0786 4.32039C45.8197 4.0615 45.4486 3.94917 45.0894 4.02158L35.663 5.91858C35.2678 5.99821 34.9484 6.2886 34.8318 6.67458C34.7152 7.06057 34.8204 7.47926 35.1053 7.76429L36.3806 9.03971L31.3754 14.0449L27.5008 10.1702C27.2957 9.96514 27.0175 9.84985 26.7274 9.84985C26.4373 9.84985 26.1591 9.96514 25.954 10.1702L19.3816 16.7426C18.9545 17.1697 18.9545 17.8622 19.3816 18.2895C19.8088 18.7166 20.5013 18.7166 20.9285 18.2895L26.7275 12.4905L30.6021 16.3652C30.8072 16.5703 31.0854 16.6856 31.3755 16.6856C31.6656 16.6856 31.9438 16.5703 32.1489 16.3652L38.7009 9.81321C39.128 9.3861 39.128 8.69365 38.7009 8.26643L38.0952 7.6606L43.9084 6.49073L42.7386 12.3036L41.8383 11.4033C41.6333 11.1982 41.355 11.083 41.0649 11.083C40.7749 11.083 40.4966 11.1982 40.2916 11.4033L31.3757 20.3191L27.5011 16.4444C27.296 16.2393 27.0178 16.1241 26.7277 16.1241C26.4377 16.1241 26.1594 16.2393 25.9543 16.4444L8.26946 34.1292L6.67904 32.5389L14.7415 24.4763C15.1686 24.0492 15.1686 23.3566 14.7415 22.9295C14.3144 22.5024 13.6218 22.5025 13.1947 22.9295L4.35886 31.7655C4.15378 31.9705 4.0385 32.2488 4.0385 32.5389C4.0385 32.8289 4.15378 33.1072 4.35886 33.3122L7.49607 36.4493C7.70968 36.663 7.98957 36.7697 8.26946 36.7697C8.54935 36.7697 8.82935 36.6628 9.04285 36.4493L9.90626 35.5859V39.4374H8.15626C5.86452 39.4374 4 41.3019 4 43.5936C4 44.8045 4.52074 45.8957 5.3498 46.6561C4.52084 47.4165 4 48.5078 4 49.7187C4 50.9296 4.52074 52.0209 5.3498 52.7812C4.52084 53.5415 4 54.6329 4 55.8437C4 58.1355 5.86452 60 8.15626 60H11H22.1563H58.9063C59.5104 60 60.0001 59.5103 60.0001 58.9062V55.0114V51.8V50.8048V28.1372C60.0001 27.5331 59.5104 27.0434 58.9063 27.0434ZM15.9838 36.4276C17.6042 36.0243 18.8825 34.746 19.2857 33.1257H50.6042C51.0075 34.746 52.2857 36.0243 53.9061 36.4276V46.7209C52.2857 47.1241 51.0075 48.4023 50.6042 50.0227H26.3003C26.3076 49.9222 26.3126 49.821 26.3126 49.7186C26.3126 48.5077 25.7919 47.4165 24.9628 46.6561C25.7918 45.8957 26.3126 44.8045 26.3126 43.5936C26.3126 41.3019 24.4481 39.4374 22.1564 39.4374H15.9839L15.9838 36.4276ZM6.1875 43.5937C6.1875 42.5082 7.07071 41.625 8.15626 41.625H22.1563C23.2418 41.625 24.125 42.5082 24.125 43.5937C24.125 44.6793 23.2418 45.5625 22.1563 45.5625H8.15626C7.07071 45.5625 6.1875 44.6793 6.1875 43.5937ZM6.1875 49.7187C6.1875 48.6332 7.07071 47.75 8.15626 47.75H22.1563C23.2418 47.75 24.125 48.6332 24.125 49.7187C24.125 50.8043 23.2418 51.6875 22.1563 51.6875H8.15626C7.07071 51.6875 6.1875 50.8043 6.1875 49.7187ZM11 57.8125H8.15626C7.07071 57.8125 6.1875 56.9293 6.1875 55.8437C6.1875 54.7582 7.07071 53.875 8.15626 53.875H22.1563C23.2418 53.875 24.125 54.7582 24.125 55.8437C24.125 56.9293 23.2418 57.8125 22.1563 57.8125H11ZM57.8126 57.8125H25.8156C26.0932 57.2985 26.2651 56.7196 26.3035 56.1052H57.8126V57.8125Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M45.75 42.6685H47.3079C47.912 42.6685 48.4017 42.1788 48.4017 41.5747C48.4017 40.9706 47.912 40.481 47.3079 40.481H45.75C45.1459 40.481 44.6562 40.9706 44.6562 41.5747C44.6562 42.1788 45.1459 42.6685 45.75 42.6685Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M34.9118 47.4011C38.1243 47.4011 40.7378 44.7872 40.7378 41.5743C40.7378 38.3614 38.1243 35.7476 34.9118 35.7476C31.6994 35.7476 29.0859 38.3614 29.0859 41.5743C29.0859 44.7872 31.6995 47.4011 34.9118 47.4011ZM34.9118 37.9351C36.9181 37.9351 38.5503 39.5676 38.5503 41.5743C38.5503 43.581 36.9181 45.2136 34.9118 45.2136C32.9057 45.2136 31.2734 43.581 31.2734 41.5743C31.2734 39.5676 32.9057 37.9351 34.9118 37.9351Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M17.0769 21.7173C17.3646 21.7173 17.6468 21.6014 17.8502 21.3968C18.0536 21.1934 18.1707 20.9112 18.1707 20.6235C18.1707 20.3359 18.0536 20.0537 17.8502 19.8503C17.6468 19.6468 17.3646 19.5298 17.0769 19.5298C16.7893 19.5298 16.5071 19.6468 16.3036 19.8503C16.0991 20.0548 15.9832 20.3359 15.9832 20.6235C15.9832 20.9112 16.0991 21.1934 16.3036 21.3968C16.5071 21.6014 16.7893 21.7173 17.0769 21.7173Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400">
                    {{ number_format($record->getOccupiedUnitsIncome()) }} <span class="icon-saudi_riyal"></span>
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white">
                    {{ __('Current Property Income') }}
                </span>
            </p>
        </div>

        <!-- Active Rent -->
        <div class="fi-in-tabs flex flex-col fi-contained rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 p-4">
            <p class="flex flex-col gap-4 w-full relative">

                <svg class="absolute ltr:right-[-10px] rtl:left-[-10px] top-3" xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none">
                    <path d="M62.3335 45.3999H54.3335C54.0683 45.3999 53.8139 45.5053 53.6264 45.6928C53.4389 45.8803 53.3335 46.1347 53.3335 46.3999V47.9999C51.7114 46.4427 49.5796 45.5267 47.3335 45.4219V2.3999C47.3335 2.13469 47.2281 1.88033 47.0406 1.6928C46.8531 1.50526 46.5987 1.3999 46.3335 1.3999H2.3335C2.06828 1.3999 1.81393 1.50526 1.62639 1.6928C1.43885 1.88033 1.3335 2.13469 1.3335 2.3999V62.3999C1.3335 62.6651 1.43885 62.9195 1.62639 63.107C1.81393 63.2945 2.06828 63.3999 2.3335 63.3999H46.3335C46.5987 63.3999 46.8531 63.2945 47.0406 63.107C47.2281 62.9195 47.3335 62.6651 47.3335 62.3999V60.0509L53.3335 60.3509V62.3999C53.3335 62.6651 53.4389 62.9195 53.6264 63.107C53.8139 63.2945 54.0683 63.3999 54.3335 63.3999H62.3335C62.5987 63.3999 62.8531 63.2945 63.0406 63.107C63.2281 62.9195 63.3335 62.6651 63.3335 62.3999V46.3999C63.3335 46.1347 63.2281 45.8803 63.0406 45.6928C62.8531 45.5053 62.5987 45.3999 62.3335 45.3999ZM32.8055 48.7999L29.3335 45.8679V43.9999C29.3372 41.5517 29.9118 39.138 31.0117 36.9508C32.1116 34.7635 33.7064 32.8629 35.6695 31.3999H38.9975C40.9606 32.8628 42.5555 34.7634 43.6554 36.9507C44.7553 39.138 45.3299 41.5516 45.3335 43.9999V45.4109C44.6253 45.4436 43.9343 45.6403 43.315 45.9855C42.6957 46.3306 42.1649 46.8148 41.7645 47.3999H35.3335C34.8283 47.4015 34.3317 47.5307 33.8898 47.7755C33.4479 48.0202 33.0749 48.3726 32.8055 48.7999ZM22.2965 49.8539L27.2885 55.3999H9.3335V27.3999H32.7155L34.0655 30.0999C33.554 30.506 33.0652 30.94 32.6015 31.3999H11.3335V33.3999H30.8685C30.3909 34.0367 29.9566 34.7049 29.5685 35.3999H11.3335V37.3999H28.6195C28.3573 38.0528 28.1339 38.7206 27.9505 39.3999H11.3335V41.3999H27.5335C27.4026 42.2604 27.3357 43.1295 27.3335 43.9999V44.1559C26.7776 43.7352 26.1152 43.4782 25.421 43.4138C24.7268 43.3495 24.0285 43.4803 23.4047 43.7916C22.7809 44.1028 22.2564 44.5822 21.8905 45.1756C21.5245 45.7689 21.3316 46.4528 21.3335 47.1499V47.3419C21.3325 48.2696 21.6756 49.1646 22.2965 49.8539ZM38.7155 29.3999H35.9515L33.9515 25.3999H40.7155L38.7155 29.3999ZM45.3335 61.3999H3.3335V3.3999H45.3335V35.8499C44.1809 33.6196 42.5676 31.6597 40.6005 30.0999L43.2335 24.8469C43.3099 24.694 43.3458 24.5241 43.3379 24.3534C43.3299 24.1827 43.2784 24.0169 43.1881 23.8717C43.0979 23.7266 42.9719 23.607 42.8223 23.5244C42.6727 23.4418 42.5044 23.399 42.3335 23.3999H32.3335C32.163 23.3998 31.9954 23.4433 31.8464 23.5263C31.6975 23.6092 31.5722 23.7289 31.4826 23.8739C31.3929 24.0189 31.3418 24.1844 31.3341 24.3547C31.3263 24.525 31.3623 24.6944 31.4385 24.8469L31.7155 25.4009H8.3335C8.06828 25.4009 7.81393 25.5063 7.62639 25.6938C7.43885 25.8813 7.3335 26.1357 7.3335 26.4009V56.3999C7.3335 56.6651 7.43885 56.9195 7.62639 57.107C7.81393 57.2945 8.06828 57.3999 8.3335 57.3999H29.1445C30.5508 58.6772 32.3797 59.3895 34.2795 59.3999L45.3335 59.9519V61.3999ZM34.3335 57.3999C33.5294 57.3991 32.7344 57.2295 31.9999 56.9023C31.2654 56.575 30.6078 56.0972 30.0695 55.4999L23.7835 48.5159C23.4935 48.1944 23.3332 47.7768 23.3335 47.3439V47.1519C23.3334 46.7955 23.4421 46.4475 23.6451 46.1545C23.8481 45.8615 24.1356 45.6374 24.4693 45.5121C24.803 45.3869 25.167 45.3665 25.5127 45.4537C25.8583 45.5408 26.1691 45.7314 26.4035 45.9999C26.4369 46.0371 26.4726 46.0722 26.5105 46.1049L32.4225 51.0939C32.5783 51.7493 32.9502 52.3332 33.4783 52.7515C34.0063 53.1698 34.6598 53.3982 35.3335 53.3999H42.3335V51.3999H35.3335C35.0683 51.3999 34.8139 51.2945 34.6264 51.107C34.4389 50.9195 34.3335 50.6651 34.3335 50.3999C34.3335 50.1347 34.4389 49.8803 34.6264 49.6928C34.8139 49.5053 35.0683 49.3999 35.3335 49.3999H42.3335C42.5202 49.4009 42.7034 49.3497 42.8625 49.2519C43.0215 49.1542 43.1501 49.0139 43.2335 48.8469C43.4489 48.4116 43.7819 48.0453 44.1947 47.7896C44.6076 47.5338 45.0838 47.3988 45.5695 47.3999H46.8635C47.8302 47.397 48.7879 47.5855 49.6814 47.9545C50.5749 48.3235 51.3865 48.8657 52.0695 49.5499C52.5301 50.2276 52.9521 50.9307 53.3335 51.6559V58.3489L34.3335 57.3999ZM61.3335 61.3999H55.3335V47.3999H61.3335V61.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M24.3335 23.3999C26.1135 23.3999 27.8536 22.8721 29.3336 21.8831C30.8137 20.8942 31.9672 19.4886 32.6484 17.8441C33.3296 16.1995 33.5078 14.3899 33.1606 12.6441C32.8133 10.8983 31.9561 9.29462 30.6975 8.03594C29.4388 6.77727 27.8351 5.92011 26.0893 5.57284C24.3435 5.22557 22.5339 5.4038 20.8893 6.08499C19.2448 6.76618 17.8392 7.91973 16.8503 9.39977C15.8613 10.8798 15.3335 12.6199 15.3335 14.3999C15.3361 16.786 16.2852 19.0737 17.9725 20.7609C19.6597 22.4482 21.9474 23.3973 24.3335 23.3999ZM24.3335 7.39991C25.718 7.39991 27.0713 7.81045 28.2225 8.57962C29.3736 9.34879 30.2708 10.442 30.8007 11.7211C31.3305 13.0002 31.4691 14.4077 31.199 15.7655C30.9289 17.1234 30.2622 18.3707 29.2832 19.3497C28.3043 20.3286 27.057 20.9953 25.6991 21.2654C24.3413 21.5355 22.9338 21.3969 21.6547 20.8671C20.3756 20.3372 19.2824 19.44 18.5132 18.2889C17.744 17.1378 17.3335 15.7844 17.3335 14.3999C17.3356 12.544 18.0738 10.7648 19.3861 9.4525C20.6984 8.1402 22.4776 7.40202 24.3335 7.39991Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M24.3335 17.3999C24.0683 17.3999 23.8139 17.2945 23.6264 17.107C23.4389 16.9195 23.3335 16.6651 23.3335 16.3999H21.3335C21.3361 17.0182 21.5296 17.6205 21.8876 18.1246C22.2456 18.6287 22.7506 19.0098 23.3335 19.2159V20.3999H25.3335V19.2159C26.0002 18.9811 26.5622 18.5179 26.9201 17.9084C27.278 17.2988 27.4087 16.5823 27.2889 15.8857C27.1691 15.1891 26.8067 14.5574 26.2657 14.1024C25.7248 13.6474 25.0403 13.3986 24.3335 13.3999C24.1357 13.3999 23.9424 13.3413 23.7779 13.2314C23.6135 13.1215 23.4853 12.9653 23.4096 12.7826C23.3339 12.5999 23.3141 12.3988 23.3527 12.2048C23.3913 12.0108 23.4865 11.8326 23.6264 11.6928C23.7662 11.5529 23.9444 11.4577 24.1384 11.4191C24.3324 11.3805 24.5335 11.4003 24.7162 11.476C24.8989 11.5517 25.0551 11.6799 25.165 11.8443C25.2748 12.0088 25.3335 12.2021 25.3335 12.3999H27.3335C27.3309 11.7816 27.1374 11.1793 26.7794 10.6752C26.4214 10.1711 25.9164 9.78999 25.3335 9.5839V8.3999H23.3335V9.5839C22.6668 9.81872 22.1048 10.2819 21.7469 10.8914C21.389 11.501 21.2583 12.2175 21.3781 12.9141C21.4979 13.6107 21.8603 14.2424 22.4013 14.6974C22.9422 15.1524 23.6267 15.4012 24.3335 15.3999C24.5987 15.3999 24.8531 15.5053 25.0406 15.6928C25.2281 15.8803 25.3335 16.1347 25.3335 16.3999C25.3335 16.6651 25.2281 16.9195 25.0406 17.107C24.8531 17.2945 24.5987 17.3999 24.3335 17.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                    <path d="M36.3335 45.3999H38.3335V44.2159C39.0002 43.9811 39.5622 43.5179 39.9201 42.9084C40.278 42.2988 40.4087 41.5823 40.2889 40.8857C40.1691 40.1891 39.8067 39.5574 39.2657 39.1024C38.7248 38.6474 38.0403 38.3986 37.3335 38.3999C37.1357 38.3999 36.9424 38.3413 36.7779 38.2314C36.6135 38.1215 36.4853 37.9653 36.4096 37.7826C36.3339 37.5999 36.3141 37.3988 36.3527 37.2048C36.3913 37.0108 36.4865 36.8326 36.6264 36.6928C36.7662 36.5529 36.9444 36.4577 37.1384 36.4191C37.3324 36.3805 37.5335 36.4003 37.7162 36.476C37.8989 36.5517 38.0551 36.6799 38.165 36.8443C38.2748 37.0088 38.3335 37.2021 38.3335 37.3999H40.3335C40.3309 36.7816 40.1374 36.1793 39.7794 35.6752C39.4214 35.1711 38.9164 34.79 38.3335 34.5839V33.3999H36.3335V34.5839C35.6668 34.8187 35.1048 35.2819 34.7469 35.8914C34.389 36.501 34.2583 37.2175 34.3781 37.9141C34.4979 38.6107 34.8603 39.2424 35.4013 39.6974C35.9422 40.1524 36.6267 40.4012 37.3335 40.3999C37.5313 40.3999 37.7246 40.4586 37.8891 40.5684C38.0535 40.6783 38.1817 40.8345 38.2574 41.0172C38.3331 41.1999 38.3529 41.401 38.3143 41.595C38.2757 41.789 38.1805 41.9672 38.0406 42.107C37.9008 42.2469 37.7226 42.3421 37.5286 42.3807C37.3346 42.4193 37.1335 42.3995 36.9508 42.3238C36.7681 42.2481 36.6119 42.1199 36.502 41.9555C36.3921 41.791 36.3335 41.5977 36.3335 41.3999H34.3335C34.3361 42.0182 34.5296 42.6205 34.8876 43.1246C35.2456 43.6287 35.7506 44.0098 36.3335 44.2159V45.3999Z" class="fill-[#E6E6E6] dark:fill-[#e6e6e63d]"/>
                </svg>
                <span class="text-[#31846C] font-bold text-lg dark:text-primary-400">
                    {{ number_format($record->getOccupiedUnitsIncome()) }} <span class="icon-saudi_riyal"></span>
                </span>
                <span class="text-[#112C24] font-bold text-lg dark:text-white">
                    {{ __('Active Rent') }}
                </span>
            </p>
        </div>
    </div>
</div>

