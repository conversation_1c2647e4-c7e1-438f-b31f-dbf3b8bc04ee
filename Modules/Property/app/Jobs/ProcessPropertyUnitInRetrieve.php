<?php
namespace Modules\Property\app\Jobs;

use App\Enums\PropertyTypeEnum;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Modules\Company\app\Models\Company;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\PropertyAttribute;
use Modules\Property\app\Models\PropertyType;
use Modules\Property\app\Models\Usability;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertyStatus;
use Throwable;

class ProcessPropertyUnitInRetrieve implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected Property $property, protected array $unitData = [], protected Company $company)
    {
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->storeUnitDataUsingEjarID($this->property, $this->unitData);
    }
    protected function storeUnitDataUsingEjarID(Property $property, array $unitData): void
    {
        $unit = Property::create([
            'ejar_uuid' => $unitData['id'],
            'ejar_synced' => true,
            'parent_id' => $property->id,
            'broker_id' => $property->broker_id,
            'retrieved_from_ejar' => true,
            'number' => $unitData['attributes']['unit_number'] ?? null,
            'property_type_id' => $this->getUnitType($unitData['attributes']['unit_type'], PropertyTypeEnum::Unit->value)->id,
            'building_date' => $unitData['attributes']['established_date'] ?? null,
            'is_active' => true,
            'status' => $this->getUnitStatus($unitData),
            'is_available' => $unitData['attributes']['availability'] == 'available',
            'usability_id' => $this->getUsability($unitData['attributes']['unit_usage'])->id,
            'created_at' => now(),
            'updated_at' => now(),
            'company_id' => $this->company->id,
            'ejar_sync_status' => EjarSyncStatus::SYNCED->value,
            'last_synced_at' => now(),
        ]);

        //save unit attributes
        $defaultAttributes = [
            'area' => $unitData['attributes']['area'],
            'rooms' => $unitData['attributes']['rooms'],
            'floor_number' => $unitData['attributes']['floor_number']
        ];
        //all attributes of unit are merged between amenities and default attributes
        $unitAttributes = array_merge($defaultAttributes, $unitData['attributes']['amenities']);
        $this->saveUnitAttributes($unit, $unitAttributes);
    }

    protected function getUnitStatus(array $unitData): string
    {
        $status = PropertyStatus::ACTIVE->value;
        if ($unitData['attributes']['availability'] == 'reserved') {
            $status = PropertyStatus::RESERVED->value;
        } elseif ($unitData['attributes']['availability'] != 'available') {
            $status = PropertyStatus::DRAFT->value;
        }
        //todo get from docs
        return $status;
    }

    protected function saveUnitAttributes(Property $property, array $unitAttributes): void
    {
        $insertable = [];
        foreach ($unitAttributes as $availableAttributeKey => $value) {

            $attribute = Attribute::firstOrCreate(
                ['key' => $availableAttributeKey],
                ['name' => Str::title($availableAttributeKey), 'key' => $availableAttributeKey, 'is_required' => true, 'from_ejar' => true]
            );
            if (!is_null($value)) {
                $insertable[] = [
                    'attribute_id' => $attribute->id,
                    'property_id' => $property->id,
                    'value' => $value
                ];
            }
        }

        PropertyAttribute::insert($insertable);
    }

    protected function getUnitType(string $propertyTypeKey, string $type): PropertyType
    {
        return PropertyType::query()->firstOrCreate(
            ['key' => $propertyTypeKey, 'property_type' => $type],
            ['name' => Str::title($propertyTypeKey), 'property_type' => $type, 'key' => $propertyTypeKey, 'from_ejar' => true]
        );
    }

    protected function getUsability(string $usabilityKey): Usability
    {
        return Usability::firstOrCreate(
            ['key' => $usabilityKey],
            ['name' => Str::title($usabilityKey), 'key' => $usabilityKey, 'from_ejar' => true]
        );
    }

    public function failed(Throwable $exception): void
    {
        Notification::make()
            ->title(__("Failed to complete retrieving unit : " . $this->unitData['attributes']['unit_number']))
            ->body(__("Failed to complete retrieving this unit, please try again later or contact support"))
            ->status('danger')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }
}
