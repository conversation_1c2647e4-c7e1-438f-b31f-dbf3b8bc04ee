<?php
namespace Modules\Property\app\Jobs;

use App\Enums\DocumentTypeEnum;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\Property\app\Helpers\PropertyHelper;
use Modules\Property\app\Models\Property;
use Modules\Property\Services\RetrieveSinglePropertyService;
use Throwable;

class ProcessSinglePropertyInRetrieve implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected RetrieveSinglePropertyService $retrieveSinglePropertyService;

    public function __construct(protected array $propertyData, protected array $includedData = [], protected Company $company)
    {
        $this->retrieveSinglePropertyService = new RetrieveSinglePropertyService($this->company);
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->processPropertyRetrieve($this->propertyData);
    }

    public function processPropertyRetrieve(array $propertyData): void
    {
        try {
            DB::beginTransaction();
            $this->retrieveSinglePropertyService->includedData = $this->includedData;
            //check if property data is already exists by document number
            $existingProperty = $this->findPropertyIfExists($propertyData);
            if (!is_null($existingProperty)) {
                $this->linkRetrievedProperty($propertyData, $existingProperty);
                return;
            }
            $property = $this->retrieveSinglePropertyService->handleProcessSteps($propertyData);
            DB::commit();
            SyncHelper::markPropertySyncStepsAsCompleted($property->id); //mark property sync steps as completed
            RetrievalLogger::success($this->company->id, $property->name, __('Property retrieved successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            RetrievalLogger::failed($this->company->id, $propertyData['attributes']['property_name'], Str::limit($e->getMessage(), 200), $e->getTrace());
        }
    }

    public function findPropertyIfExists(array $propertyData): Property|null
    {
        $documentData = collect($this->includedData)
            ->where('id', '=', $propertyData['attributes']['ownership_document_id'])
            ->where('type', '=', DocumentTypeEnum::OWNERSHIP_DOCUMENT->value)->first();

        return PropertyHelper::checkPropertyExistsByDocumentNumber($documentData['attributes']['document_number'], $documentData['attributes']['ownership_document_type']);
    }


    public function failed(Throwable $exception): void
    {
        Notification::make()
            ->title(__("Failed to complete retrieving " . $this->propertyData['attributes']['property_name']))
            ->body(__("Failed to complete retrieving this property, please try again later or contact support"))
            ->status('danger')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }

    protected function linkRetrievedProperty(array $propertyData, Property $existingProperty): void
    {
        $existingProperty->ejar_uuid = $propertyData['id'];
        $existingProperty->save();
    }

}
