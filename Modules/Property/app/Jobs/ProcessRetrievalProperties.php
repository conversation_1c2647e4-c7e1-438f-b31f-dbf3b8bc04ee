<?php
namespace Modules\Property\app\Jobs;

use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Company\app\Models\Company;
use Modules\EjarIntegration\app\Helpers\RetrievalLogger;
use Modules\Property\app\Models\Property;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

class ProcessRetrievalProperties implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $processableProperties = [];
    protected array $includedData = [];
    protected Company $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->retrievePropertiesFromEjar();
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Notification::make()
            ->title(__("Failed to complete retrieving process"))
            ->body(__("Failed to complete retrieving process, please try again later or contact support"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }

    /**
     * @throws \Exception
     */
    public function retrievePropertiesFromEjar(): void
    {
        //retrieve properties from ejar
        $propertyRetrieving = new \Modules\EjarIntegration\PropertyRetrieving\RetrieveProperties($this->company);
        $params = ['page[number]' => 1, 'page[size]' => 10000000];
        $propertyRetrieving->setParams($params);
        $propertyRetrieving->retrieveProperties();

        //get properties to compare with retrieved properties
        $existingEjarIDs = $this->getCompanyProperties($this->company)->pluck('ejar_uuid')->toArray();
        foreach ($propertyRetrieving->properties as $property) {
            if (!in_array($property['id'], $existingEjarIDs)) {
                $property['company_id'] = $this->company->id;
                $property['broker_id'] = $this->company->user_id; //at this point, no broker defined from ejar
                $this->processableProperties[] = $property;
            }
        }

        if (count($this->processableProperties) > 0) {
            $this->includedData = $propertyRetrieving->units;
            $this->processRetrieving();
        } else{
            $this->notifyAllPropertiesUpdated();
            RetrievalLogger::success($this->company->id, __('Retrieving Process'), __('All Properties are updated'));
        }
    }

    protected function processRetrieving(): void
    {
        foreach ($this->processableProperties as $property) {
            ProcessSinglePropertyInRetrieve::dispatch($property, $this->includedData, $this->company);
        }
        $this->notifyAfterProcess();
    }

    protected function getCompanyProperties(Company $company): Builder
    {
        return Property::whereNull('parent_id')->where(['company_id' => $company->id]);
    }

    protected function notifyAllPropertiesUpdated(): void
    {
        Notification::make()
            ->title(__("All Properties are updated"))
            ->body(__("All Properties are synced and retrieved from Ejar, You do not need to retrieve properties"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }

    protected function notifyAfterProcess(): void
    {
        $processCount = count($this->processableProperties);
        Notification::make()
            ->title(__("Retrieving {$processCount} Properties is Done from ejar"))
            ->body(__("Retrieve Properties are synced and retrieved from Ejar, if you faced any problem please contact us via support"))
            ->status('info')
            ->date(now()->format('Y-m-d H:i:s'))
            ->sendToDatabase(User::find($this->company->user_id))
            ->toDatabase();
    }
}
