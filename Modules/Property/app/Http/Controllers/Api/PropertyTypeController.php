<?php

namespace Modules\Property\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Property\app\Resources\Api\PropertyTypeResource;
use Modules\Property\Services\PropertyTypeService;

class PropertyTypeController extends ControllerAbstract
{
    protected string $jsonResourceClass = PropertyTypeResource::class;

    public function __construct(PropertyTypeService $service)
    {
        parent::__construct($service);
    }
}
