<?php

namespace Modules\Property\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Property\app\Resources\Api\AmenityResource;
use Modules\Property\Services\AmenityService;


class AmenitiesController extends ControllerAbstract
{
    protected string $jsonResourceClass = AmenityResource::class;

    public function __construct(AmenityService $service)
    {
        parent::__construct($service);
    }
}
