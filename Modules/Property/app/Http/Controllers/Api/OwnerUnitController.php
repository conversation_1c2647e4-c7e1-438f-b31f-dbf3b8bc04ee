<?php

namespace Modules\Property\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Property\app\Resources\Api\OwnersPropertyResource;
use Modules\Property\app\Resources\Api\PropertyResource;
use Modules\Property\app\Resources\Api\TenantUnitResource;
use Modules\Property\app\Resources\Api\UnitResource;
use Modules\Property\Services\PropertyService;

class OwnerUnitController extends ControllerAbstract
{
    protected string $jsonResourceClass = UnitResource::class;

    public function __construct(PropertyService $service)
    {
        parent::__construct($service);
    }

    public function getOwnerUnit(int $id)
    {
        $this->with = ['activeLease','usability', 'property_type','attributes'];
        return parent::show($id);
    }
}
