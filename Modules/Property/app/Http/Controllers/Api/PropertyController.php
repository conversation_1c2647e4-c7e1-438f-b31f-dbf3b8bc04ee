<?php

namespace Modules\Property\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Property\app\Resources\Api\OwnersPropertyResource;
use Modules\Property\app\Resources\Api\PropertyResource;
use Modules\Property\app\Resources\Api\TenantUnitResource;
use Modules\Property\Services\PropertyService;

class PropertyController extends ControllerAbstract
{
    protected string $jsonResourceClass = PropertyResource::class;
    protected string $tenantUnitResource = TenantUnitResource::class;
    protected string $ownerPropertyResource = OwnersPropertyResource::class;

    public function __construct(PropertyService $service)
    {
        $this->with= ["documentOwnership"];
        parent::__construct($service);
    }

    public function getTenantUnits(){
        $this->filter['member_id'] = Auth::id();
        $this->with = ['lease', 'lease.leaseMembers', 'unit', 'unit.usability', 'unit.property_type','unit.attributes'];

        return ApiResponse::data($this->tenantUnitResource::collection($this->service->getTenantUnits($this->filter, $this->with, $this->select)));
    }

    public function getTenantUnit($id,$unit)
    {
        $this->filter['member_id'] = Auth::id();
        $this->filter['lease_id'] = $id;
        $this->filter['unit_id'] = $unit;
        $this->with = ['lease', 'lease.leaseMembers', 'unit', 'unit.usability', 'unit.property_type','unit.attributes'];
        return ApiResponse::data($this->tenantUnitResource::make($this->service->getTenantUnit($this->filter,$this->with)));

    }
    public function getAddress(Request $request)
    {
        $result = $this->service->getAddress($request->all());

        if($result['state']){
            return ApiResponse::data($result['data']);
        }else{
            return ApiResponse::error($result['message']);
        }
    }

    public function getOwnerProperties()
    {
        return ApiResponse::data($this->jsonResourceClass::collection($this->service->propertyOwner()));
    }

    public function getOwners($property_id)
    {
        $this->filter = ['id'=>$property_id];
        return ApiResponse::data($this->ownerPropertyResource::collection($this->service->owners($this->filter)));
    }

    public function dashboard(Request $request)
    {
        $propertyId = $request->get('property_id');
        
        $dashboardData = [
            'properties' => $this->service->getPropertiesStats($propertyId),
            'leases' => $this->service->getLeasesStats($propertyId),
            'financial' => $this->service->getFinancialStats($propertyId)
        ];

        return ApiResponse::data($dashboardData);
    }
}
