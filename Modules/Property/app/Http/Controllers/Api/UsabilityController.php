<?php

namespace Modules\Property\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Property\app\Resources\Api\UsabilityResource;
use Modules\Property\Services\UsabilityService;


class UsabilityController extends ControllerAbstract
{
    protected string $jsonResourceClass = UsabilityResource::class;

    public function __construct(UsabilityService $service)
    {
        parent::__construct($service);
    }
}
