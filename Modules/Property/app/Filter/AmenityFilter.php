<?php

namespace Modules\Property\app\Filter;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class AmenityFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        // If value is array of amenity IDs
        if (is_array($value)) {
            return $query->whereHas('amenities', function ($query) use ($value) {
                $query->whereIn('amenities.id', $value);
            });
        }

        // If value is single amenity ID
        return $query->whereHas('amenities', function ($query) use ($value) {
            $query->where('amenities.id', $value);
        });
    }
}
