<?php

namespace Modules\Property\app\Filter;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class PriceFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        // Ensure value is array and has valid numeric values
        if (!is_array($value)) {
            return $query;
        }

        $min = isset($value['min']) && is_numeric($value['min']) ? floatval($value['min']) : null;
        $max = isset($value['max']) && is_numeric($value['max']) ? floatval($value['max']) : null;

        return $query->whereHas('units', function ($query) use ($min, $max) {
            if ($min !== null && $max !== null) {
                $query->whereBetween('price', [$min, $max]);
            } elseif ($min !== null) {
                $query->where('price', '>=', $min);
            } elseif ($max !== null) {
                $query->where('price', '<=', $max);
            }
        });
    }
}
