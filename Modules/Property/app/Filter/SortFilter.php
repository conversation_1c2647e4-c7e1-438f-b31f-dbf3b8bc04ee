<?php

namespace Modules\Property\app\Filter;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class SortFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        $sortParams = request()->input('filter.sort');
        foreach ($sortParams as $column => $direction) {
            $direction = strtolower($direction);
            if (!in_array($direction, ['asc', 'desc'])) {
                $direction = 'desc'; // default direction
            }
            $query->orderBy($column, $direction);
        }
        return  $query;
    }
}
