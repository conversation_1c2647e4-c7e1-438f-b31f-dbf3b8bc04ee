<?php

namespace Modules\Property\Observers;

use Mo<PERSON>les\Property\app\Models\SyncPropertyStep;
use Modules\Property\Enums\PropertySyncStatus;

class SyncPropertyStepObserver
{
    /**
     * Handle the SyncPropertyStep "created" event.
     */
    public function created(SyncPropertyStep $syncpropertystep): void {}

    /**
     * Handle the SyncPropertyStep "updated" event.
     */
    public function updated(SyncPropertyStep $syncpropertystep): void {
    }

    /**
     * Handle the SyncPropertyStep "deleted" event.
     */
    public function deleted(SyncPropertyStep $syncpropertystep): void {}

    /**
     * Handle the SyncPropertyStep "restored" event.
     */
    public function restored(SyncPropertyStep $syncpropertystep): void {}

    /**
     * Handle the SyncPropertyStep "force deleted" event.
     */
    public function forceDeleted(SyncPropertyStep $syncpropertystep): void {}
}
