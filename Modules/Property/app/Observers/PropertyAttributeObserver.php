<?php

namespace Modules\Property\app\Observers;

use Modules\Property\app\Models\PropertyAttribute;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class PropertyAttributeObserver
{
    /**
     * Handle the PropertyAttribute "created" event.
     */
    public function created(PropertyAttribute $propertyattribute): void {
        $this->handleUpdateEjarSyncing($propertyattribute);
    }

    /**
     * Handle the PropertyAttribute "updated" event.
     */
    public function updated(PropertyAttribute $propertyattribute): void {
        $this->handleUpdateEjarSyncing($propertyattribute);
    }

    /**
     * Handle the PropertyAttribute "deleted" event.
     */
    public function deleted(PropertyAttribute $propertyattribute): void {
        $this->handleUpdateEjarSyncing($propertyattribute);
    }

    /**
     * Handle the PropertyAttribute "restored" event.
     */
    public function restored(PropertyAttribute $propertyattribute): void {}

    /**
     * Handle the PropertyAttribute "force deleted" event.
     */
    public function forceDeleted(PropertyAttribute $propertyattribute): void {}

    protected function handleUpdateEjarSyncing(PropertyAttribute $propertyattribute): void
    {
        $relatedProperty = $propertyattribute->property;
        if (is_null($relatedProperty->parent_id)) {
            $parentProperty = $relatedProperty;
            $syncStep = PropertySyncStep::PROPERTY->value;

            if ($parentProperty->ejar_sync_status != EjarSyncStatus::NOT_SYNCED) {
                // Update the property sync step status to NEED_SYNC
                $parentProperty->syncSteps()
                    ->where('step', $syncStep)
                    ->update(['status' => PropertySyncStatus::NEED_SYNC->value]);
                // Update the property's ejar_sync_status to NEED_SYNC
                $parentProperty->update(['ejar_sync_status' => EjarSyncStatus::NEED_SYNC->value]);
            }
        }
    }
}
