<?php

namespace Modules\Property\app\Observers;

use Modules\Property\app\Models\PropertyOwners;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class PropertyOwnersObserver
{
    /**
     * Handle the PropertyOwners "created" event.
     */
    public function created(PropertyOwners $propertyowners): void {
        $this->handleUpdateEjarSyncing($propertyowners);
    }

    /**
     * Handle the PropertyOwners "updated" event.
     */
    public function updated(PropertyOwners $propertyowners): void {
        $this->handleUpdateEjarSyncing($propertyowners);
    }

    /**
     * Handle the PropertyOwners "deleted" event.
     */
    public function deleted(PropertyOwners $propertyowners): void {
        $this->handleUpdateEjarSyncing($propertyowners);
    }

    /**
     * Handle the PropertyOwners "restored" event.
     */
    public function restored(PropertyOwners $propertyowners): void {}

    /**
     * Handle the PropertyOwners "force deleted" event.
     */
    public function forceDeleted(PropertyOwners $propertyowners): void {}

    protected function handleUpdateEjarSyncing(PropertyOwners $propertyowners): void
    {
        $property = $propertyowners->property;
        if ($property->ejar_sync_status != EjarSyncStatus::NOT_SYNCED) {
            // Update the property sync step status to NEED_SYNC
            $property->syncSteps()
                ->whereIn('step', [PropertySyncStep::LINK_OWNER_TO_DOCUMENT->value, PropertySyncStep::IBAN->value, PropertySyncStep::INDIVIDUAL_ENTITY->value])
                ->update(['status' => PropertySyncStatus::NEED_SYNC->value]);
            // Update the property's ejar_sync_status to NEED_SYNC
            $property->update(['ejar_sync_status' => EjarSyncStatus::NEED_SYNC->value]);
        }
    }
}
