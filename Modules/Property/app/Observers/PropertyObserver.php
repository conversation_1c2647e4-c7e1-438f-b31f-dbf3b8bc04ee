<?php

namespace Modules\Property\app\Observers;

use App\Enums\MediaCollectionEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class PropertyObserver
{
    /**
     * Handle the Property "created" event.
     */
    public function created(Property $property): void
    {
        $this->handlePropertyLocationImage($property);
    }

    /**
     * Handle the Property "updated" event.
     */
    public function updated(Property $property): void
    {
        if ($property->isDirty(['lat', 'lng'])) {
            $this->handlePropertyLocationImage($property);
        }
        //handle ejar syncing
        $this->handleSyncPropertyWithEjar($property);
    }

    /**
     * Handle the Property "deleted" event.
     */
    public function deleted(Property $property): void
    {
    }

    /**
     * Handle the Property "restored" event.
     */
    public function restored(Property $property): void
    {
        //
    }

    /**
     * Handle the Property "force deleted" event.
     */
    public function forceDeleted(Property $property): void
    {
        //
    }

    protected function handlePropertyLocationImage(Property $property): void {
        try {
            $google_maps_key = env('GOOGLE_MAPS_API_KEY');
            $url = "https://maps.googleapis.com/maps/api/staticmap?zoom=13&size=600x300&maptype=roadmap&markers=color:red%7Clabel:P%7c{$property->lat},{$property->lng}&key={$google_maps_key}";
            DB::beginTransaction();
            // Add the media from the URL
            $property->clearMediaCollection(MediaCollectionEnum::PROPERTY_LOCATION_IMAGE->value);
            $property->addMediaFromUrl($url)->toMediaCollection(MediaCollectionEnum::PROPERTY_LOCATION_IMAGE->value);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e->getMessage());
        }
    }

    protected function handleSyncPropertyWithEjar(Property $property): void {
        if ($property->ejar_sync_status != EjarSyncStatus::NOT_SYNCED) {
            if ($property->isDirty(['price', 'building_date', 'number', 'attributes', 'property_type_id', 'usability_id', 'lng', 'lat'])) {
                //price, building_date, number, attributes, property_type_id, usability_id, lng, lat
                if (is_null($property->parent_id)) {
                    $property->syncSteps()
                        ->where('step', PropertySyncStep::PROPERTY->value)
                        ->update(['status' => EjarSyncStatus::NEED_SYNC->value]);
                }
            }
        }
    }
}
