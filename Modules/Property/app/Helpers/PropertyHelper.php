<?php
namespace Modules\Property\app\Helpers;

use App\Models\Document;
use Modules\Property\app\Models\Property;

class PropertyHelper
{

    public static function checkPropertyExistsByDocumentNumber(string $documentNumber, string $documentType): Property|null
    {
        $document = Document::query()
            ->where('morphable_type', '=', Property::class)
            ->where('metadata->ownership_reference_no', $documentNumber)
            ->where('metadata->document_type', $documentType)
            ->first();

        if (!$document) {
            return null;
        }

        return $document->morphable;
    }
}
