<?php

namespace Modules\Property\app\Filament\Actions;

use App\Enums\RoleEnum;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertyStatus;
use Modules\Property\Services\PropertySyncService;

class EjarSyncAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'ejar_sync';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->button()
            ->label(function (Model $record): string {
                return
                    $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? __('Sync with ejar') :
                        EjarSyncStatus::tryFrom($record->ejar_sync_status?->value)?->getLabel();
            })
            ->tooltip(function (Model $record): ?string {
                return $record->last_synced_at
                    ? __('last synced') . ' ' . $record->last_synced_at->diffForHumans()
                    : null;
            })
            ->icon(function (Model $record): string {
                return
                    $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? 'heroicon-o-arrow-path' :
                        EjarSyncStatus::tryFrom($record->ejar_sync_status?->value)?->getIcon();
            })
            ->color(function (Model $record): string {
                return
                    $record->ejar_sync_status->value == EjarSyncStatus::NOT_SYNCED->value ? 'success' :
                        EjarSyncStatus::tryFrom($record->ejar_sync_status?->value)?->getColor();
            })
            ->visible(function (Model $record): bool {
                if ($record->status == PropertyStatus::ACTIVE) {
                    if (auth()->user()->hasRole(RoleEnum::ADMIN)) {
                        return true;
                    }
                    return (auth()->user()->can('view_sync_property'));
                }
                return false;
            })
            ->disabled(fn(Model $record): bool => ($record->ejar_sync_status == EjarSyncStatus::SYNCING))
            ->action(function (Model $record): void {
                //check action permission
                if (!auth()->user()->can('sync_property')) {
                    if (!auth()->user()->hasRole(RoleEnum::ADMIN)) {
                        $this->needPermission();
                        return;
                    }
                }
                //check subscription
                if (!SyncHelper::syncAvailableInActivePlan()) {
                    $this->needUpgrade();
                    return;
                }
                // Only allow sync if not syncing now
                if ($record->ejar_sync_status !== EjarSyncStatus::SYNCING) {
                    try {
                        $syncService = new PropertySyncService($record);
                        $syncService->sync();
                        $this->notifySuccess();
                    } catch (\Exception $e) {
                        $this->notifyFailed($e->getMessage());
                    }
                }
            })
            ->after(function ($livewire) {
                $livewire->dispatch('refreshSteps'); //fire event to refresh sync steps
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-o-arrow-path')
            ->modalHeading(__("Sync with ejar"))
            ->modalDescription(__("Are you sure you want to sync this property with ejar?"))
            ->modalSubmitActionLabel(__('Yes, sync it'));
    }

    protected function needPermission()
    {
        Notification::make()
            ->title(__('Permission Denied'))
            ->body(__('You do not have permission to do this action.'))
            ->danger()
            ->persistent()
            ->send();
    }
    protected function needUpgrade()
    {
        Notification::make()
            ->title(__('Access Denied'))
            ->body(__('You do not have access to this feature. Please upgrade your subscription.'))
            ->danger()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('upgrade')
                    ->label(__('Upgrade Subscription'))
                    ->url(fn(): string => url('/admin/subscription-plans'))
                    ->button()
                    ->color('success'),

                \Filament\Notifications\Actions\Action::make('back')
                    ->label(__('Go Back'))
                    ->url(url()->previous())
                    ->color('gray')
                    ->button(),
            ])
            ->send();
    }
    protected function notifySuccess(): void
    {
        Notification::make()
            ->success()
            ->title(__("Property synced with ejar successfully"))
            ->icon('heroicon-o-check-circle')
            ->iconColor('success')
            ->duration(5000)
            ->send();
    }
    protected function notifyFailed($message): void
    {
        Notification::make()
            ->danger()
            ->title(__('Could not sync this property with ejar'))
            ->body($message)
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
}
