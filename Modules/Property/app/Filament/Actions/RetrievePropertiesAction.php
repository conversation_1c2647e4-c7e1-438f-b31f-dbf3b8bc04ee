<?php
namespace Modules\Property\app\Filament\Actions;

use App\Enums\RoleEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Modules\EjarIntegration\app\Helpers\SyncHelper;
use Modules\Property\Services\RetrieveCompanyPropertiesService;
use Modules\Property\Services\RetrieveSinglePropertyService;

class RetrievePropertiesAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'retrieve_properties';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->button()
            ->color('info')
            ->visible(auth()->user()->can('view_sync_property') && !auth()->user()->hasRole(RoleEnum::ADMIN->value)) //has permission and not admin
            ->form([
                Radio::make('retrieve_type')
                    ->label(__('Retrieve Type'))
                    ->options([
                        'all' => __('Retrieve All Properties'),
                        'single' => __('Retrieve Single Property'),
                    ])
                    ->default('all')
                    ->required()
                    ->live(),

                Grid::make(1)
                    ->schema([
                        TextInput::make('reference_number')
                            ->label(__('Property Document Number'))
                            ->placeholder(__('Enter property Document number'))
                            ->required()
                            ->visible(fn (Get $get): bool => $get('retrieve_type') === 'single'),
                    ])
            ])
            ->action(function (array $data): void {
                try {
                    //check permission
                    if (!auth()->user()->can('sync_property')) {
                        $this->needPermission();
                        return;
                    }
                    //check subscription
                    if (!SyncHelper::syncAvailableInActivePlan()) {
                        $this->needUpgrade();
                        return;
                    }
                    //get user company
                    $user = auth()->user();
                    if (is_null($user->company)) {
                        $this->notifyUndefinedCompany();
                        return;
                    }

                    // Determine which retrieval method to use based on the selection
                    if ($data['retrieve_type'] === 'all') {
                        // Retrieve all properties
                        $retrievalService = new RetrieveCompanyPropertiesService($user->company);
                        $retrievalService->retrieve();
                    } else {
                        // Retrieve single property
                        $retrievalService = new RetrieveSinglePropertyService($user->company);

                        // Prepare the data with the reference number
                        $retrieveData = [
                            'reference_number' => $data['reference_number']
                        ];

                        $property = $retrievalService->retrieve($retrieveData);

                        // Notify success for single property retrieval
                        $this->notifySinglePropertySuccess($property->name);
                    }
                } catch (\Exception $e) {
                    $this->notifyFailed($e->getMessage());
                }
            });
    }

    protected function notifyFailed($message): void
    {
        Notification::make()
            ->danger()
            ->title(__('Could not retrieve company properties'))
            ->body($message)
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
    protected function notifyUndefinedCompany(): void
    {
        Notification::make()
            ->danger()
            ->title(__('Undefined company'))
            ->body(__('The company of properties is not found.'))
            ->icon('heroicon-o-x-circle')
            ->iconColor('danger')
            ->persistent()
            ->send();
    }
    protected function needPermission(): void
    {
        Notification::make()
            ->title(__('Permission Denied'))
            ->body(__('You do not have permission to do this action.'))
            ->danger()
            ->persistent()
            ->send();
    }
    protected function needUpgrade(): void
    {
        Notification::make()
            ->title(__('Access Denied'))
            ->body(__('You do not have access to this feature. Please upgrade your subscription.'))
            ->danger()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('upgrade')
                    ->label(__('Upgrade Subscription'))
                    ->url(fn (): string => url('/admin/subscription-plans'))
                    ->button()
                    ->color('success'),

                \Filament\Notifications\Actions\Action::make('back')
                    ->label(__('Go Back'))
                    ->url(url()->previous())
                    ->color('gray')
                    ->button(),
            ])
            ->send();
    }

    protected function notifySinglePropertySuccess(string $propertyName): void
    {
        Notification::make()
            ->success()
            ->title(__('Property Retrieved Successfully'))
            ->body(__('Property :name has been successfully retrieved.', ['name' => $propertyName]))
            ->icon('heroicon-o-check-circle')
            ->iconColor('success')
            ->send();
    }
}
