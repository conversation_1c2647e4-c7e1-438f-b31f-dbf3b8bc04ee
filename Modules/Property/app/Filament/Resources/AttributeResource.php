<?php

namespace Modules\Property\app\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Modules\Property\app\Filament\Resources\AttributeResource\Pages;
use Modules\Property\app\Models\Attribute;
use Filament\Notifications\Notification;

class AttributeResource extends Resource
{
    protected static ?string $model = Attribute::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Forms\Components\Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->translateLabel('ar')
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->translateLabel('en')
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function ($state, $set) {
                                                $set('key', Str::slug($state, '_'));
                                            }),
                                    ])
                            ]),
                        Forms\Components\Section::make(__('Attribute Icon'))
                            ->schema([
                                Forms\Components\SpatieMediaLibraryFileUpload::make('icon')
                                    ->label(__('Label'))
                                    ->image()
                                    ->imageEditor()
                                    ->maxSize(5120)
                                    ->collection('attribute')
                                    ->imageEditorAspectRatios([
                                        '16:9',
                                        '4:3',
                                        '1:1',
                                    ])->required(),

                            ])->columns(1)
                            ->columnSpan(1),


                        Forms\Components\Select::make('property_types')
                            ->label(__('Property Type'))
                            ->multiple()
                            ->relationship('propertyTypes', 'name')
                            ->preload(),
                        // Hidden field for key
                        Forms\Components\Hidden::make('key'),
                        Forms\Components\Section::make("")->schema([
                            Forms\Components\Toggle::make('is_required')
                                ->label(__('Required'))
                                ->default(true)
                                ->visible(fn ($record) => $record?->deletable !== 0),
                            Forms\Components\Toggle::make('is_active')
                                ->label(__('Active'))
                                ->default(true),
                        ])->columns(1)
                            ->columnSpan(1),


                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('propertyTypes.name')
                    ->label(__('Property Types'))
                    ->badge(),

                Tables\Columns\IconColumn::make('is_required')
                    ->label(__('Required'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('Active'))
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('icon')
                    ->label(__('Icon'))
                    ->collection('attribute')
                    ->circular(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(tooltip: fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->sortable()
                    ->tooltip(tooltip: fn($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('property_types')
                    ->label(__('Property Types'))
                    ->relationship('propertyTypes', 'name')
                    ->searchable()
                    ->preload()
                    ->native(false),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->before(function ($record) {
                            if ($record->deletable === 0) {
                                Notification::make()
                                    ->danger()
                                    ->title(__('Cannot Delete'))
                                    ->body(__('This attribute cannot be deleted.'))
                                    ->send();

                                return false;
                            }
                        }),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAttributes::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }


    public static function getNavigationLabel(): string
    {
        return __("attributes");
    }

    public static function getNavigationGroup(): string
    {
        return __('Properties Management');
    }
    public static function getBreadcrumb() : string
    {
        return __('attributes');
    }
    public static function getModelLabel(): string
    {
        return __('attribute');
    }

    public static function getPluralModelLabel(): string
    {
        return __('attributes');
    }
}
