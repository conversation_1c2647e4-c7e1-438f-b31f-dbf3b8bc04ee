<?php

namespace Modules\Property\app\Filament\Resources;

//use App\Filament\Resources\AmenitiesCategoryResource\Pages;
//use App\Filament\Resources\AmenitiesCategoryResource\RelationManagers;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Form\AmenitiesCategoryForm;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Table\AmenitiesCategoryTable;
use Modules\Property\app\Models\AmenitiesCategory;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\RelationManagers\AmenitiesRelationManager;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages\ListAmenitiesCategories;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages\ViewAmenitiesCategory;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages\EditAmenitiesCategory;
class AmenitiesCategoryResource extends Resource
{
    protected static ?string $model = AmenitiesCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';
    protected static ?string $navigationGroup = 'Amenities Management';



    public static function form(Form $form): Form
    {
        return AmenitiesCategoryForm::form($form);
    }

    public static function table(Table $table): Table
    {
        return AmenitiesCategoryTable::table($table);
    }

    public static function getRelations(): array
    {
        return [
            AmenitiesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAmenitiesCategories::route('/'),
            // 'create' => Pages\CreateAmenitiesCategory::route('/create'),
            'view' => ViewAmenitiesCategory::route('/{record}'),
            'edit' => EditAmenitiesCategory::route('/{record}/edit'),
        ];
    }
    public static function getNavigationLabel(): string
    {
        return __("amenities categories");
    }

    public static function getNavigationGroup(): string
    {
        return  __('Amenities Management');
    }

    public static function getBreadcrumb() : string
    {
        return __('Amenities Category');
    }
    public static function getModelLabel(): string
    {
        return __('Amenities Category');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Amenities Category');
    }
}
