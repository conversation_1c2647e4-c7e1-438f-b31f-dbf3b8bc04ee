<?php

namespace Modules\Property\app\Filament\Resources\UsabilityResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Property\app\Filament\Resources\UsabilityResource;

class EditUsability extends EditRecord
{
    protected static string $resource = UsabilityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
