<?php

namespace Modules\Property\app\Filament\Resources\UsabilityResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Modules\Property\app\Filament\Resources\UsabilityResource;

class ViewUsability extends ViewRecord
{
    protected static string $resource = UsabilityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
