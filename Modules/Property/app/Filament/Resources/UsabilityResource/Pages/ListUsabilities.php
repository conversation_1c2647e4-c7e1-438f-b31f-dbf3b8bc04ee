<?php

namespace Modules\Property\app\Filament\Resources\UsabilityResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\UsabilityResource;

class ListUsabilities extends ListRecords
{
    protected static string $resource = UsabilityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Usablility')),
        ];
    }
}
