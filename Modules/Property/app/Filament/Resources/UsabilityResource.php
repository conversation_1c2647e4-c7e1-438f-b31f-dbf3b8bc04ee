<?php

namespace Modules\Property\app\Filament\Resources;

use Filament\Forms;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Str;
use Modules\Property\app\Filament\Resources\UsabilityResource\Pages;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Property\app\Models\Usability;

class UsabilityResource extends Resource
{
    protected static ?string $model = Usability::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
    protected static ?string $navigationGroup = 'Properties Management';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Usability Name'))
                    ->schema([
                        Forms\Components\Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->translateLabel('ar')
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->translateLabel('en')
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function ($state, $set) {
                                                $set('key', Str::slug($state, '_'));
                                            }),
                                    ]),
                            ]),
                    ]),
                // Hidden field for key
                Forms\Components\Hidden::make('key'),
                Forms\Components\Section::make(__('Usability Icon'))
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('icon')
                            ->label(__('Icon'))
                            ->collection('usability')
                            ->image()
                            ->maxSize(5120)
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('icon')
                    ->label(__('Icon'))
                    ->collection('usability')
                    ->circular()
                    ->limit(1),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->datetime()->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->tooltip(fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Property\app\Filament\Resources\UsabilityResource\Pages\ListUsabilities::route('/'),
            // 'create' => Pages\CreateUsability::route('/create'),
            // 'view' => Pages\ViewUsability::route('/{record}'),
            // 'edit' => Pages\EditUsability::route('/{record}/edit'),
        ];
    }
    public static function getNavigationLabel(): string
    {
        return __("usabilities");
    }
    public static function getNavigationGroup(): string
    {
        return  __('Properties Management');
    }

    public static function getBreadcrumb() : string
    {
        return __('Usability');
    }

    public static function getPluralModelLabel(): string
    {
        return __('usabilities');
    }

    public static function getModelLabel(): string
    {
        return __('Usability');
    }
}
