<?php

namespace Modules\Property\app\Filament\Resources\AttributeResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Modules\Property\app\Filament\Resources\AttributeResource;

class ManageAttributes extends ManageRecords
{
    protected static string $resource = AttributeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Attribute')),
        ];
    }
}
