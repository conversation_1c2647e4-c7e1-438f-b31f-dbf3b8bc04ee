<?php

namespace Modules\Property\app\Filament\Resources;

use App\Enums\PropertyTypeEnum;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Property\app\Models\PropertyType;

class PropertyTypeResource extends Resource
{
    protected static ?string $model = PropertyType::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationGroup = 'Properties Management';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Property Type Name'))
                    ->schema([
                        Forms\Components\Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->translateLabel('ar')
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->translateLabel('en')
                                            ->required()
                                            ->live()
                                            ->afterStateUpdated(function ($state, $set) {
                                                $set('key', Str::slug($state, '_'));
                                            }),
                                    ]),
                            ]),
                        // Hidden field for key
                        Forms\Components\Hidden::make('key'),
                        Forms\Components\Select::make('property_type')
                            ->label(__('Property Type'))
                            ->native(false)
                            ->options(PropertyTypeEnum::class)
                            ->required()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('property_type')
                    ->label(__('Property Type'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->sortable()
                    ->tooltip(tooltip: fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make(__('property_type'))
                    ->options(PropertyTypeEnum::class)
                    ->native(false),
                //
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => PropertyTypeResource\Pages\ListPropertyTypes::route('/'),
            // 'create' => Pages\CreatePropertyType::route('/create'),
            // 'view' => Pages\ViewPropertyType::route('/{record}'),
            // 'edit' => Pages\EditPropertyType::route('/{record}/edit'),
        ];
    }



    public static function getNavigationLabel(): string
    {
        return __("Property Types");
    }
    public static function getNavigationGroup(): string
    {
        return  __('Properties Management');
    }

    public static function getBreadcrumb() : string
    {
        return __('Property Type');
    }
    public static function getModelLabel(): string
    {
        return __('PropertyType');
    }

    public static function getPluralModelLabel(): string
    {
        return __('PropertyTypes');
    }
}
