<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesResource\Table;

use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource;
use Modules\Property\app\Models\AmenitiesCategory;
use Filament\Tables;
class AmenitiesTable
{

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\SpatieMediaLibraryImageColumn::make('icon')
                    ->label(__('Icon'))
                    ->collection('amenities')
                    ->circular(),
                Tables\Columns\TextColumn::make('amenitiesCategory.name')
                    ->label(__('Category Name'))
                    ->badge()
                    ->sortable()
                    ->searchable()
                    ->url(
                        fn ($record) => AmenitiesCategoryResource::getUrl('view', ['record' => $record->amenitiesCategory]),
                        shouldOpenInNewTab: true
                    ),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->tooltip(fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                //
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('amenities_category_id')
                    ->label(__('Amenities Category'))
                    ->options(AmenitiesCategory::select('id','name')->get()->pluck('name', 'id'))
                    ->relationship('amenitiesCategory', 'name')
                    ->native(false)
                    ->searchable()
                    ->preload()
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),

                ]),
            ])
            ;
    }
}
