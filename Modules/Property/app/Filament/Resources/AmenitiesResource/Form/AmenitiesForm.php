<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesResource\Form;

use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Illuminate\Support\Str;
use Modules\Property\app\Models\AmenitiesCategory;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
class AmenitiesForm
{

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make([
                    Forms\Components\Section::make(__('Amenities Name'))
                        ->schema([
                            Forms\Components\Tabs::make('Tabs')
                                ->tabs([
                                    Tabs\Tab::make(__('Arabic'))
                                        ->schema([
                                            Forms\Components\TextInput::make('name.ar')
                                                ->label(__('Name'))
                                                ->translateLabel('ar')
                                                ->required(),
                                        ]),
                                    Tabs\Tab::make(__('English'))
                                        ->schema([
                                            Forms\Components\TextInput::make('name.en')
                                                ->label(__('Name'))
                                                ->translateLabel('en')
                                                ->required()
                                                ->live()
                                                ->afterStateUpdated(function ($state, $set) {
                                                    $set('key', Str::slug($state, '_'));
                                                }),
                                        ]),
                                ])
                        ]),
                    // Hidden field for key
                    Forms\Components\Hidden::make('key'),
                    Forms\Components\Section::make(__('Amenities Category'))
                        ->schema([
                            Forms\Components\Select::make('amenities_category_id')
                                ->label(__('Amenities Category'))
                                ->options(AmenitiesCategory::select('id','name')->get()->pluck('name', 'id'))
                                ->required()
                                ->native()
                                ->searchable()
                                ->preload()
                                ->searchDebounce(500)
                                ->createOptionForm([
                                    Forms\Components\Tabs::make('Tabs')
                                        ->tabs([
                                            Tabs\Tab::make(__('Arabic'))
                                                ->schema([
                                                    Forms\Components\TextInput::make('name.ar')
                                                        ->label(__('Name'))
                                                        ->translateLabel('ar')
                                                        ->required(),
                                                ]),
                                            Tabs\Tab::make(__('English'))
                                                ->schema([
                                                    Forms\Components\TextInput::make('name.en')
                                                        ->label(__('Name'))
                                                        ->translateLabel('en')
                                                        ->required(),
                                                ])
                                        ])
                                ])->createOptionAction(
                                    fn (Action $action) => $action->visible(fn () => auth()->user()->can('create', AmenitiesCategory::class))
                                        ->modalWidth('2xl')
                                )->createOptionUsing(function (array $data) {
                                    $category = AmenitiesCategory::create(['name' => $data['name']]);
                                    return $category->id;
                                })
                            // ->editOptionForm([
                            //     Forms\Components\Tabs::make('Tabs')
                            //         ->tabs([
                            //             Tabs\Tab::make('Arabic')
                            //             ->schema([
                            //                 Forms\Components\TextInput::make('name.ar')
                            //                     ->label('Name')
                            //                     ->translateLabel('ar')
                            //                     ->required(),
                            //             ]),
                            //             Tabs\Tab::make('English')
                            //             ->schema([
                            //                 Forms\Components\TextInput::make('name.en')
                            //                     ->label('Name')
                            //                     ->translateLabel('en')
                            //                     ->required(),
                            //             ])
                            //         ])
                            // ])->editOptionAction(
                            //     fn (Action $action) => $action->visible(fn () => auth()->user()->can('update', [AmenitiesCategory::class,new AmenitiesCategory ]))
                            //     ->modalWidth('2xl')
                            // )

                        ]),
                ]),
                Forms\Components\Section::make(__('Amenities Icon'))
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('icon')
                            ->label(__('Icon'))
                            ->image()
                            ->imageEditor()
                            ->maxSize(5120)
                            ->collection('amenities')
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ]),

                    ])->columns(1)
                    ->columnSpan(1),
            ]);
    }
}
