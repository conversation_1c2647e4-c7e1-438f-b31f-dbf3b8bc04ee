<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\AmenitiesResource;

class ListAmenities extends ListRecords
{
    protected static string $resource = AmenitiesResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Amenity')),
        ];
    }
}
