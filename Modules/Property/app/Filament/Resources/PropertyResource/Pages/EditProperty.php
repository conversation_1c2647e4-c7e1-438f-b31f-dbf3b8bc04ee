<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Pages;

use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Property\Enums\PropertyStatus;
use Modules\Property\app\Filament\Resources\PropertyResource;
use Filament\Notifications\Notification;
use Modules\Property\app\Models\Property;

class EditProperty extends EditRecord
{
    protected static string $resource = PropertyResource::class;

    public function mount($record): void
    {
        parent::mount($record);

        if ($this->record->status !== PropertyStatus::DRAFT && $this->record->status !== PropertyStatus::ACTIVE) {
            redirect()->route('filament.admin.resources.properties.index')->with('error', __('Cannot edit a published property.'));
        }
    }

    protected function beforeSave(): void
    {
        $addRepresenter = $this->data['add_representer'];
        $representer = $this->record->onlyRepresentative?->exists();

        if(!$addRepresenter && $representer){
            $this->record->onlyRepresentative()->delete();
            if ($this->record->document()->whereNull('deleted_at')->exists()) {
                $this->record->document()->whereNull('deleted_at')->first()->delete();
            }
        }

        $hasRepresenter = $this->record->owners()->where([
            ['percentage', '>', 0],
            ['is_representer', '=', 1]
        ])->first();

        if($addRepresenter && $hasRepresenter){
            $hasRepresenter->update(['is_representer' => 0]);
        }
    }

    protected function afterSave(): void
    {
        $hasRepresenter = $this->record->owners()->where('is_representer', true)->exists();

        if (!$hasRepresenter && $this->record->document()->whereNull('deleted_at')->exists()) {
            $this->record->document()->whereNull('deleted_at')->first()->delete();
        }

        $property = Property::find($this->record->id);
        
        if (!$property) {
            return;
        }
    
        // Get current broker IDs before changes
        $oldBrokerIds = $property->units
            ->flatMap(fn ($unit) => $unit->propertyBroker()->pluck('broker_id'))
            ->unique()
            ->values();
    
        // New broker IDs (after editing property)
        $newBrokerIds = $property->brokers->pluck('id');

        // Get added and removed brokers
        $addedBrokers = $newBrokerIds->diff($oldBrokerIds)->values()->all();
        $removedBrokers = $oldBrokerIds->diff($newBrokerIds)->values()->all();
        // Notify new brokers
        if (!empty($addedBrokers)) {
            $this->notifyBrokerAdded($addedBrokers);
        }
    
        // Notify removed brokers
        if (!empty($removedBrokers)) {
            $this->notifyBrokerRemoved($removedBrokers);
        }
    
        // Reassign brokers to units
        $property->units->each(function ($unit) use ($newBrokerIds) {
            $unit->propertyBroker()
                ->whereNull('delete_time')
                ->update([
                    'delete_time' => now(),
                ]);
    
            $newBrokerIds->each(function ($brokerId) use ($unit) {
                $unit->propertyBroker()->create([
                    'broker_id' => $brokerId,
                    'created_by' => auth()->id()
                ]);
            });
        });
    }

    protected function notifyBrokerAdded(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'broker_new_assigned'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-web','email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }

    protected function notifyBrokerRemoved(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'broker_remove'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-web','email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
            // Actions\ForceDeleteAction::make(),
            // Actions\RestoreAction::make(),
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getFormActions(): array
    {
        return [
                Actions\Action::make('customAction')
                ->action(function () {
                    try{
                        $this->save();
                        Notification::make()
                            ->success()
                            ->title(__('Success'))
                            ->body(__('Property updated successfully.'))
                            ->send();
                    }catch (\Illuminate\Validation\ValidationException $e) {
                        $validator = $e->validator;
                        $messages = $validator->getMessageBag()->getMessages();
                        $formattedMessages = collect($messages)->map(function ($message) {
                            return  $message;
                        })->flatten()->implode(' <hr class="my-2"/>');
                        Notification::make()
                            ->danger()
                            ->title(__('Validation Error'))
                            ->body($formattedMessages)
                            ->send();
                    }
                })
                ->label(__('Update Property')),
        ];
    }
}
