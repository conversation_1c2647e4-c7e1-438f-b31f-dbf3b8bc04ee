<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Forms\Form;
use Filament\Forms\Components\Wizard;
use Filament\Resources\Pages\ViewRecord;
use Modules\Property\app\Filament\Actions\EjarSyncAction;
use Modules\Property\app\Filament\Resources\PropertyResource;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;

class ViewProperty extends ViewRecord
{
    protected static string $resource = PropertyResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                $this->modifyFormSchema(
                    PropertyResource::form($form)->getComponents()
                )
            );
    }

    protected function modifyFormSchema(array $schema): array
    {
        return collect($schema)->map(function ($field) {
            if ($field instanceof Wizard) {
                return $field->submitAction(null)->skippable();
            }
            return $field;
        })->toArray();
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT),
            Actions\EditAction::make()
                ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE),
            Actions\Action::make('publish')
                ->label(__('Publish'))
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading(__('Publish Property'))
                ->modalDescription(__('Are you sure you want to publish this property?'))
                ->modalSubmitActionLabel(__('Yes, publish it'))
                ->action(function (Property $record): void {
                    $record->update([
                        'status' => PropertyStatus::ACTIVE
                    ]);

                    Notification::make()
                        ->title(__('Property published successfully'))
                        ->success()
                        ->send();
                })
                ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT),
            EjarSyncAction::make(),
        ];
    }
}
