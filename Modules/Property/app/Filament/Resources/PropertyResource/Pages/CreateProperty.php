<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Pages;

use App\Models\District;
use App\Models\User;
use App\Enums\RoleEnum;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Property\app\Filament\Resources\PropertyResource;
use Modules\Property\app\Models\Property;

class CreateProperty extends CreateRecord
{
    protected static string $resource = PropertyResource::class;
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['user_id'] = auth()->id();
        if (is_null($data['district'])) {
            $data['district'] = District::find($data['district_id'])?->name ?? '';
        }
        return $data;
    }

    protected function getFormActions(): array
    {
        return [];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordCreation(array $data): Model
    {
        $record = new ($this->getModel())($data);
        $record->save();

        $user = auth()->user();
        $isBroker = $user->hasRole(RoleEnum::BROKER);
        // If you're handling a broker relationship here
        if ($isBroker) {
            $record->brokers()->sync([auth()->id()]);
        }
        
        return $record;
    }

    protected function afterCreate(): void
    {
        $property = Property::find($this->record->id);
        $brokerIds = $property->brokers->pluck('id')->toArray();
        $this->notifyBroker($brokerIds);
        $units = $property->units()->withoutGlobalScopes()->get();

        foreach($units as $unit)
        {
            $unit->brokers()->sync($brokerIds);
        }
    }

    protected function notifyBroker(array $members): void
    {
        $template = NotificationsTemplate::where(['key' => 'broker_new_assigned'])->first();
        if ($template) {
            foreach ($members as $key => $memberId) {
                SendNotification::make(['fcm-web','email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($memberId)
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
    }
}
