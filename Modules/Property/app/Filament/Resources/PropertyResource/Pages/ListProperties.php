<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\PropertyResource;
use Modules\Property\app\Filament\Actions\RetrievePropertiesAction;
use Modules\Property\app\Filament\Resources\PropertyResource\Widgets\PropertyListOverview;

class ListProperties extends ListRecords
{
    protected static string $resource = PropertyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            RetrievePropertiesAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            PropertyListOverview::class
        ];
    }
}
