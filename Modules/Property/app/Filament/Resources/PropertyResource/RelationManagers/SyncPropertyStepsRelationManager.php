<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class SyncPropertyStepsRelationManager extends RelationManager
{
    protected static string $relationship = 'syncSteps';
    //protected Table $table;
    protected $listeners = ['refreshSteps' => 'refresh'];
    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Sync Steps');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Property Sync Steps');
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('step')
                    ->label(__('Sync Step'))
                    ->badge()
                    ->formatStateUsing(fn (PropertySyncStep $state): string => PropertySyncStep::tryFrom($state->value)?->getLabel() ?? $state->value),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->formatStateUsing(fn (PropertySyncStatus $state): string => PropertySyncStatus::tryFrom($state->value)->getLabel() ?? $state->value)
                    ->color(fn (PropertySyncStatus $state): string => PropertySyncStatus::tryFrom($state->value)?->getColor() ?? $state->value),
                Tables\Columns\TextColumn::make('started_at')
                    ->label(__('Started At'))
                    ->dateTime(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->label(__('Completed At'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('error_message')
                    ->label(__('Error Message'))
                    ->formatStateUsing(fn (Model $record): string =>
                        $record->getTranslation('error_message', app()->getLocale(), false) ?? ''
                    )
                    ->wrap()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->emptyStateDescription(__('Create a unit to get started.'))
            ->emptyStateIcon('heroicon-o-building-office');
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(2)
                ->schema([
                    TextInput::make('step')
                        ->label(__('Sync Step'))
                        ->disabled()
                        ->formatStateUsing(fn ($state): string => PropertySyncStep::tryFrom($state)?->getLabel() ?? $state),

                    TextInput::make('status')
                        ->label(__('Status'))
                        ->disabled()
                        ->formatStateUsing(fn ($state): string => PropertySyncStatus::tryFrom($state)->getLabel() ?? $state),

                ]),
            Grid::make(2)
                ->schema([
                    DatePicker::make('started_at')
                        ->label(__('Started At'))
                        ->disabled()
                        ->native(false),
                    DatePicker::make('completed_at')
                        ->label(__('Completed At'))
                        ->disabled()
                        ->native(false),
                ]),
            Grid::make(3)
                ->schema([
                    Textarea::make('error_message')
                        ->label(__('Error Message'))
                        ->disabled()
                        ->formatStateUsing(fn (Model $record): string =>
                            $record->getTranslation('error_message', app()->getLocale(), false) ?? ''
                        )
                        ->columnSpanFull(),
                ]),
            ]);
    }

    public function refresh(): void
    {
        $this->resetTable();
    }

    public function isReadOnly(): bool
    {
        return false;
    }

}
