<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;

class OwnersRelationManager extends RelationManager
{
    protected static string $relationship = 'owners';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Owners');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Owners');
    }


    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('Owner Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label(__('Email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.phone')
                    ->label(__('Phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.national_id')
                    ->label(__('National ID'))
                    ->badge()
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ]);
    }
}
