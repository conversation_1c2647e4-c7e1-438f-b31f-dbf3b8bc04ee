<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers;

use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;


class BrokersLogRelationManager extends RelationManager
{
    protected static string $relationship = 'PropertyBroker';

    // Add property to store unit brokers
    protected $unitBrokers = null;

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Brokers Log');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Brokers Log');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public function mount(): void
    {
        parent::mount();
        
        // Get the first unit's brokers if they exist
        $firstUnit = $this->getOwnerRecord()->units->first();
        $this->unitBrokers = $firstUnit ? $firstUnit->PropertyBroker : collect();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function () {
                // Instead of modifying the existing query, replace it entirely with the unit brokers
                if ($this->unitBrokers && $this->unitBrokers->count() > 0) {
                    // Use the model of PropertyBroker to create a new query
                    $model = $this->unitBrokers->first()::query();
                    return $model->whereIn('id', $this->unitBrokers->pluck('id'));
                }
                
                // Fall back to the default relationship query
                return $this->getRelationship()->getQuery();
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('Id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('broker.name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('broker.email')
                    ->label(__('Email'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('broker.userProfile.phone_number')
                    ->label(__('Phone'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('broker.userProfile.national_id')
                    ->label(__('National Id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->getStateUsing(function ($record) {
                        return $record->delete_time ? 'deleted' : 'added';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'deleted' => 'danger',
                        'added' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('delete_time')
                    ->label(__('Deleted at'))
                    ->since()
                    ->sortable()
            ])
            ->filters([
                //
            ])
            ->headerActions([
            ])
            ->actions([])
            ->emptyStateDescription(__('No Brokers Log Found'))
            ->emptyStateIcon('heroicon-o-building-office');
    }

    public function isReadOnly(): bool
    {
        return false;
    }

}
