<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\RelationManagers\RelationManager;

class AmenitiesRelationManager extends RelationManager
{
    protected static string $relationship = 'amenities';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('amenities');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('amenities');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('amenitiesCategory.name')
                    ->label(__('Category')),
            ])
            ->filters([
                //
            ]);
    }
}
