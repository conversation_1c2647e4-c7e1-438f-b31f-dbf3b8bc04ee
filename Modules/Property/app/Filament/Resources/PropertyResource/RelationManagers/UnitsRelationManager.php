<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\RelationManagers;

use Filament\Forms\Components\Group;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\PropertyTypeEnum;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\Usability;
use Modules\Property\Enums\PropertyStatus;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Toggle;
use Modules\Property\app\Filament\Resources\UsabilityResource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Actions\ActionGroup;
use App\Forms\Components\HijriDatePicker;
use Modules\Property\Enums\UnitPeriodType;


use Modules\Property\Enums\PropertySyncStep;
use Modules\Property\app\Events\PropertyNeedSync;


class UnitsRelationManager extends RelationManager
{
    protected static string $relationship = 'units';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Units');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Units');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(3)
                    ->schema([
                        Hidden::make('company_id')
                            ->default(fn ($livewire) => $livewire->getOwnerRecord()->company_id),
                        TextInput::make('number')
                            ->label(__('Unit Number'))
                            ->placeholder(__('Unit Number'))
                            ->required(),
                        Select::make('property_type_id')
                            ->label(__('Unit Type'))
                            ->required()
                            ->relationship(
                                name: 'property_type',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn($query) => $query->where('property_type', PropertyTypeEnum::Unit->value)
                            )
                            ->searchable(['name'])
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $set('attributes', []);

                                if ($state) {
                                    $requiredAttributes = Attribute::whereHas('propertyTypes', function ($query) use ($state) {
                                        $query->where('property_type_id', $state);
                                    })->where('is_required', true)->get();

                                    foreach ($requiredAttributes as $index => $attribute) {
                                        $set("attributes.{$index}.attribute_id", $attribute->id);
                                    }
                                }
                            }),

                        Select::make('usability_id')
                            ->relationship(name: 'usability', titleAttribute: 'name')
                            ->label(__('Usability'))
                            ->searchable(['name'])
                            ->preload()
                            ->required()
                            ->createOptionForm(fn(Form $form) => UsabilityResource::form($form))
                            ->createOptionAction(
                                fn(Action $action) => $action->visible(fn() => auth()->user()->can('create', Usability::class))
                                    ->modalWidth('2xl')
                            ),

                        HijriDatePicker::make('building_date')
                            ->showConvertedDate()
                            ->label(__('Building Date'))
                            ->placeholder(__('Building Date'))
                            ->minDate(fn(RelationManager $livewire) => $livewire->getOwnerRecord()->building_date)
                            ->default(function (RelationManager $livewire) {
                                return $livewire->getOwnerRecord()->building_date;
                            })
                            ->native(false),
                        TextInput::make('price')
                            ->label(__('Price'))
                            ->placeholder(__('Price'))
                            ->required()
                            ->live(true)
                            ->debounce(700)
                            ->numeric() // Ensures that the input is treated as a number
                            ->rules([
                                'numeric',
                                'decimal:0,2', // Allows up to 2 decimal places
                                'max:99999999.99', // Maximum value for decimal(10,2)
                                'min:0',
                                'regex:/^\d{1,8}(\.\d{0,2})?$/' // Ensures max 8 digits before decimal and 2 after
                            ])
                            ->placeholder(__('Enter unit price'))
                            ->helperText(__('The Price should be in Annually period'))
                            ->afterStateUpdated(function($get, $set,$state){
                                $pricePlans = $get('pricePlans');
                                foreach ($pricePlans as $key => $pricePlan) {
                                    if($pricePlan['period_type'] == UnitPeriodType::MONTHLY){
                                        $price = $state / 12;
                                        $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                    }elseif($pricePlan['period_type'] == UnitPeriodType::HALF_ANNUALLY){
                                        $price = $state / 2;
                                        $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                    }elseif($pricePlan['period_type'] == UnitPeriodType::QUARTERLY){
                                        $price = $state / 4;
                                        $pricePlans[$key]['price'] = number_format($price, 2, '.', '');
                                    }
                                    $pricePlans[$key]['annual_price'] = $state;
                                }
                                $set('pricePlans', $pricePlans);
                            }),
                            Repeater::make('pricePlans')
                                ->relationship('prices')
                                ->reorderable(false)
                                ->label(__('Price Plans'))
                                ->columnSpanFull()
                                ->defaultItems(0)
                                ->collapsed(false)
                                ->columns(2)
                                ->grid(2)
                                ->maxItems(3)
                                ->collapsible(true)
                                ->itemLabel(fn (array $state): ?string => UnitPeriodType::trans($state['period_type'] ?? null))
                                ->schema([
                                    Select::make('period_type')
                                        ->label(__('Period Type'))
                                        ->options(fn() => UnitPeriodType::repeatedLabels())
                                        ->native(false)
                                        ->required()
                                        ->live()
                                        ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                        ->afterStateUpdated(function($state,$get,$set){
                                            $annualPrice = $get('../../price');
                                            if($state == UnitPeriodType::MONTHLY){
                                                $price = $annualPrice / 12;
                                                $set('price', number_format($price, 2, '.', ''));
                                            }elseif($state == UnitPeriodType::HALF_ANNUALLY){
                                                $price = $annualPrice / 2;
                                                $set('price', number_format($price, 2, '.', ''));
                                            }elseif($state == UnitPeriodType::QUARTERLY){
                                                $price = $annualPrice / 4;
                                                $set('price', number_format($price, 2, '.', ''));
                                            }
                                            $set('annual_price', $annualPrice);
                                        }),
                                    
                                    TextInput::make('price')
                                        ->label(__('Price'))
                                        ->placeholder(__('Enter price'))
                                        ->numeric()
                                        ->required()
                                        ->live(true)
                                        ->debounce(700)
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            $period = $get('period_type');
                                            if($period == UnitPeriodType::MONTHLY){
                                                $annualPrice = $state * 12;
                                            }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                                $annualPrice = $state * 2;
                                            }elseif($period == UnitPeriodType::QUARTERLY){
                                                $annualPrice = $state * 4;
                                            }
                                            $set('annual_price', $annualPrice);
                                        })
                                        ->rules([
                                            'numeric',
                                            'decimal:0,2',
                                            'min:0',
                                        ])
                                        ->helperText(function ($get){
                                            $period = $get('period_type');
                                            $basePrice = $get('../../price');
                                            $annualPrice = $get('annual_price');
                                            $diff = $annualPrice - $basePrice;
                                            if($annualPrice){
                                                if($period == UnitPeriodType::MONTHLY){
                                                    return __('the difference between the Monthly price and Annually price is'). ' ' .$diff;
                                                }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                                    return __('the difference between the Half Annually price and Annually price is'). ' ' .$diff;
                                                }elseif($period == UnitPeriodType::QUARTERLY){
                                                    return __('the difference between the Quarterly price and Annually price is').' '.$diff;
                                                }
                                            }
                                        })
                                        ->validationMessages([
                                            'numeric' => __('Static price must be a number'),
                                            'min' => __('Static price must be greater than or equal to 0'),
                                        ]),
                                    Hidden::make('annual_price')
                                        
                                ]),
                    ]),

                Grid::make(2)
                    ->schema([
                        Repeater::make('attributes')
                            ->relationship('attributes')
                            ->label(__('attributes'))
                            ->collapsed(false)
                            ->collapsible()
                            ->itemLabel(function (array $state): ?string {
                                if (!isset($state['attribute_id'])) {
                                    return null;
                                }

                                $attribute = Attribute::find($state['attribute_id']);
                                return $attribute ? $attribute->name : null;
                            })
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        Select::make('attribute_id')
                                            ->options(function (Get $get, $livewire) {
                                                // $propertyTypeId = $livewire->data['property_type_id'] ?? null;
                                                $propertyTypeId = $get('../../property_type_id');
                                                if (!$propertyTypeId) {
                                                    return [];
                                                }
                                                return Attribute::whereHas('propertyTypes', function ($query) use ($propertyTypeId) {
                                                    $query->where('property_types.id', $propertyTypeId);
                                                })
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->label(__('attributes'))
                                            ->searchable(['name'])
                                            ->preload()
                                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                            ->required()
                                            ->disabled(function ($get) {
                                                $attributeId = $get('attribute_id');
                                                if (!$attributeId) {
                                                    return false;
                                                }

                                                $attribute = Attribute::find($attributeId);
                                                return $attribute && $attribute->is_required === 1;
                                            })
                                            ->dehydrated()
                                            ->columnSpan(1),
                                        TextInput::make('value')
                                            ->label(__('Value'))
                                            ->required(),
                                    ])


                            ])
                            ->deleteAction(
                                fn(Action $action) => $action
                                    ->requiresConfirmation()
                                    ->modalHeading(__('Delete Attribute'))
                                    ->modalDescription(__('Are you sure you want to delete this attribute?'))
                                    ->before(function ($record, $state, $arguments) use ($action) {
                                        if (isset($state[$arguments['item']]['attribute_id'])) {
                                            $attributeId = $state[$arguments['item']]['attribute_id'];
                                            $attribute = Attribute::find($attributeId);

                                            if ($attribute && $attribute->is_required) {
                                                Notification::make()
                                                    ->danger()
                                                    ->title(__('Delete Failed'))
                                                    ->body(__('This attribute is required and cannot be deleted.'))
                                                    ->send();
                                                $action->cancel();
                                            }
                                        }
                                    })
                            )
                            ->defaultItems(0),

                        SpatieMediaLibraryFileUpload::make('attachment')
                            ->label(__('Attachment'))
                            ->image()
                            ->imageEditor()
                            ->collection(collection: "property_images")
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->panelLayout('grid')
                            ->reorderable()
                            ->multiple()
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable(),
                        Repeater::make('vals')
                            ->label(__('val'))
                            ->collapsed(false)
                            ->collapsible()
                            ->relationship('vals')
                            ->schema([
                                TextInput::make('value')
                                    ->label(__('License number'))
                                    ->placeholder(__('License number'))
                                    ->required(),

                                Hidden::make('morphable_type')
                                    ->default(Property::class),

                                Hidden::make('morphable_id')
                                    ->default(function (Get $get) {
                                        return $get('../../id');
                                    }),

                                HijriDatePicker::make('start_date')
                                    ->label(__('Start Date'))
                                    ->validationMessages([
                                        'required' => __('Start Date is required'),
                                    ])
                                    ->live()
                                    ->syncWith('vals_date')
                                    ->showConvertedDate()
                                    ->required(),

                                HijriDatePicker::make('end_date')
                                    ->label(__('End Date'))
                                    ->validationMessages([
                                        'required' => __('End Date is required'),
                                    ])
                                    ->syncWith('vals_date')
                                    ->showConvertedDate()
                                    ->hideSwitcher()
                                    ->minDate(fn (Get $get): string =>
                                    $get('start_date')
                                        ? \Carbon\Carbon::parse($get('start_date'))->addDay()->toDateString()
                                        : now()->toDateString()
                                    )
                                    ->required(),

                                Toggle::make('active')
                                    ->label(__('Active'))
                                    ->default(true)
                            ])
                            ->defaultItems(0)
                            ->maxItems(1)
                            ->columns(2)
                            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                return array_merge($data, [
                                    'company_id' => auth()->user()->company_id,
                                ]);
                            }),
                    ]),

                Grid::make(3)
                    ->label(__('Meters'))
                    ->relationship('meters')
                    ->schema([
                        Group::make([
                            TextInput::make('water_meter')
                                ->label(__('Water Meter'))
                                ->placeholder(__('Enter water meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'water_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-beaker')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This water meter number is already in use'),
                                ])

                        ])->columnSpan(1),

                        Group::make([
                            TextInput::make('gas_meter')
                                ->label(__('Gas Meter'))
                                ->placeholder(__('Enter gas meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'gas_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-fire')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This Gas meter number is already in use'),
                                ])
                        ])->columnSpan(1),

                        Group::make([
                            TextInput::make('electronic_meter')
                                ->label(__('Electronic Meter'))
                                ->placeholder(__('Enter electronic meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'electronic_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-bolt')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This electronic meter number is already in use'),
                                ])
                        ])->columnSpan(1),
                    ])
                    ->columns(3)
                    ->extraAttributes([
                        'class' => 'mt-4 mb-4', // Add some vertical spacing
                    ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('number')
                    ->label(__('Unit Number'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('property_type.name')
                    ->label(__('Property Type'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('usability.name')
                    ->label(__('Usability'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('building_date')
                    ->label(__('Building Date')),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (PropertyStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (PropertyStatus $state): string => PropertyStatus::getLabel($state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label(__('New Unit'))
                    ->modalWidth('7xl')
                    ->modalHeading('Create Unit')
                    ->after(function (\Illuminate\Database\Eloquent\Model $record, array $data) {
                        // Assuming $record is the Unit
                        event(new PropertyNeedSync(
                            property: $record->parent,
                            step: PropertySyncStep::PROPERTY_UNITS->value
                        ));
                        $property = $record->parent;
                        if ($property && $property->brokers) {
                            // Assuming a many-to-many relationship: $unit->brokers()
                            $record->brokers()->sync($property->brokers->pluck('id')->toArray());
                        }
                    })
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                        ->modalWidth('7xl'),
                ActionGroup::make([
                    Tables\Actions\DeleteAction::make()
                    ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE)
                    ->after(function (Model $record) {
                        event(new PropertyNeedSync(
                            property: $record->parent,
                            step: PropertySyncStep::PROPERTY_UNITS->value
                        ));
                    }),
                    Tables\Actions\EditAction::make()
                        ->modalWidth('7xl')
                        ->visible(fn (Property $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE)
                        ->after(function (Model $record) {
                            event(new PropertyNeedSync(
                                property: $record->parent,
                                step: PropertySyncStep::PROPERTY_UNITS->value
                            ));
                        }),
                    Tables\Actions\Action::make('publish')
                        ->label(__('Publish'))
                        ->icon('heroicon-o-paper-airplane')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading(__('Publish unit'))
                        ->modalDescription(__('Are you sure you want to publish this unit?'))
                        ->modalSubmitActionLabel(__('Yes, publish it'))
                        ->action(function (Property $record): void {
                            Property::query()
                            ->where(function ($query) use ($record) {
                                $query->where('id', $record->id)
                                    ->orWhere(function ($q) use ($record) {
                                        $q->where('id', $record->parent_id)
                                            ->where('status', PropertyStatus::DRAFT);
                                    });
                            })
                            ->update(['status' => PropertyStatus::ACTIVE]);

                            Notification::make()
                                ->title(__('Unit published successfully'))
                                ->success()
                                ->send();
                        })
                        ->visible(fn (Property $record): bool => in_array($record->status, [PropertyStatus::DRAFT, PropertyStatus::IN_MAINTENANCE])),
                ])
                ->button()
                ->extraAttributes([
                    'class' => 'custom-action-btn',
                ])
                ->color('transparent')
                ->label(__('Commends')),

            ])
            ->emptyStateDescription(__('Create a unit to get started.'))
            ->emptyStateIcon('heroicon-o-building-office');
    }

    public function isReadOnly(): bool
    {
        return false;
    }

}
