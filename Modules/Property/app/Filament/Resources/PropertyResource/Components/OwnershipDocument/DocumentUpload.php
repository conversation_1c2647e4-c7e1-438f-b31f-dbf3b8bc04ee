<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument;

use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class DocumentUpload
{
    private const MAX_FILE_SIZE = 5120; // 5MB (5 * 1024 KB)
    private const IMAGE_PREVIEW_HEIGHT = 250;

    public static function make(): SpatieMediaLibraryFileUpload
    {
        return SpatieMediaLibraryFileUpload::make('upload_contract')
            ->label(__('Upload Contract Authorization'))
            ->acceptedFileTypes(self::acceptedFileTypes())
            ->imageEditor()
            ->downloadable()
            ->openable()
            ->panelLayout('grid')
            ->reorderable()
            ->multiple()
            ->maxSize(5120)
            ->collection('ownership_document')
            ->imageEditorAspectRatios([
                '16:9',
                '4:3',
                '1:1',
            ])
            ->imagePreviewHeight(self::IMAGE_PREVIEW_HEIGHT)
            ->columnSpanFull()
            ->required()
            ->maxSize(self::MAX_FILE_SIZE)
            ->helperText(self::getHelperText())

            ->validationMessages([
                'required' => __('Upload Contract Authorization is required'),
                'max' => __('Each file must not be larger than 5MB'),
                'type' => __('Invalid file type. Please upload only: JPG, JPEG, PNG, WebP, PDF, DOC, DOCX'),
            ]);
    }

    public static function acceptedFileTypes(): array
    {
        return [
            'image/jpeg',     // .jpg and .jpeg
            'image/png',      // .png
            'image/webp',     // .webp
            'application/pdf', // .pdf
            'application/msword', // .doc
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // .docx
        ];
    }

    public static function acceptedExtensions(): array
    {
        return [
            'jpg',
            'jpeg',
            'png',
            'webp',
            'pdf',
            'doc',
            'docx'
        ];
    }

    private static function getHelperText(): string
    {
        return sprintf(
            '%s %s',
            __('Maximum file size: 5MB per file.'),
            __('Accepted formats: :formats', ['formats' => strtoupper(implode(', ', self::acceptedExtensions()))])
        );
    }

    public static function getMaxFileSize(): int
    {
        return self::MAX_FILE_SIZE;
    }
}
