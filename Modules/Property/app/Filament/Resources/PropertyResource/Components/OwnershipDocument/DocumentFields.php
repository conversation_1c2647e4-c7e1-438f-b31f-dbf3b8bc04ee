<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Modules\Property\Enums\OwnershipDocumentTypeEnum;
use App\Forms\Components\HijriDatePicker;

class DocumentFields
{
    public static function make(): array
    {
        return [
            static::ownerIdNumber(),
            static::ownershipReferenceNo(),
            static::realEstateNumber(),
            static::legalDocumentTypeName(),
            static::issueDate(),
        ];
    }

    public static function ownerIdNumber(): TextInput
    {
        return TextInput::make('metadata.owner_id_number')
            ->label(__('Owner ID Number'))
            ->placeholder(__('Enter Owner ID Number'))
            ->required()
//            ->helperText(__('National ID must start with 1 for Saudi citizens or 2 for non-Saudi residents'))
            ->visible(fn (Get $get) => $get('metadata.document_type') === OwnershipDocumentTypeEnum::Electronic->value)
            ->validationMessages([
                'required' => __('Owner ID Number is required'),
            ]);
    }
    public static function ownershipReferenceNo(): TextInput
    {
        return TextInput::make('metadata.ownership_reference_no')
            ->label(__('Ownership Reference No.'))
            ->placeholder(__('Enter Reference Number'))
            ->required()
            ->visible(fn(Get $get) => in_array($get('metadata.document_type'),[
                OwnershipDocumentTypeEnum::Electronic->value,
                OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                OwnershipDocumentTypeEnum::Other->value,
            ]
            ))->validationMessages([
                'required' => __('Ownership Reference No is required'),
            ]);
    }

    public static function realEstateNumber(): TextInput
    {
        return TextInput::make('metadata.real_estate_number')
            ->label(__('Real Estate Number'))
            ->placeholder(__('Enter Real Estate Number'))
            ->required()->extraInputAttributes([
                'maxlength' => '16',
                'minlength' => '16',
                'pattern' => '[0-9]{16}',
                'oninput' => "
                this.value = this.value.replace(/[^0-9]/g, '').substring(0, 16);
                this.dispatchEvent(new Event('input'));
            "
            ])
            ->rules([
                'required',
                'numeric',
                'digits:16',
                'regex:/^[0-9]{16}$/',
            ])
            ->validationMessages([
                'required' => __('Real Estate Number is required'),
                'digits' => __('Real Estate Number must be exactly 16 digits'),
                'numeric' => __('Real Estate Number must contain only numbers'),
                'regex' => __('Real Estate Number must be a valid 16-digit number'),
            ])
            ->visible(fn (Get $get) => $get('metadata.document_type') === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value);
    }


    public static function legalDocumentTypeName(): TextInput
    {
        return TextInput::make('metadata.legal_document_type_name')
            ->label(__('Legal Document Type Name'))
            ->placeholder(__('Enter Legal Document Type Name'))
            ->required()
            ->visible(fn (Get $get) => $get('metadata.document_type') === OwnershipDocumentTypeEnum::Other->value);
    }

    public static function issueDate(): DatePicker
    {
        return HijriDatePicker::make('metadata.issue_date')
            ->label(fn (Get $get) => $get('metadata.document_type') === OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value
                ? __('First Registration Date')
                : __('Issue Date'))
            ->placeholder(__('Select Date'))
            ->maxDate(now())
            ->showConvertedDate()
            ->required()
            ->visible(fn (Get $get) => in_array(
                $get('metadata.document_type'),
                [
                    OwnershipDocumentTypeEnum::Electronic->value,
                    OwnershipDocumentTypeEnum::PaperTitleDeed->value,
                    OwnershipDocumentTypeEnum::HojjatEsthkam->value,
                    OwnershipDocumentTypeEnum::RealEstateRegistryTitleDeed->value,
                    OwnershipDocumentTypeEnum::Other->value,
                ]
            ))->validationMessages([
                'required' => __('Issue Date is required'),
            ]);
    }



}
