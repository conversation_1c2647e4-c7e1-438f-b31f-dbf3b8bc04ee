<?php

namespace Modules\Property\app\Filament\Resources\PropertyResource\Components\OwnershipDocument;

use Filament\Forms\Components\Select;
use Filament\Forms\Set;
use Modules\Property\Enums\OwnershipDocumentTypeEnum;

class DocumentTypeSelect
{
    public static function make(): Select
    {
        return Select::make('metadata.document_type')
            ->label(__('Document Type'))
            ->options(OwnershipDocumentTypeEnum::toArray())
            ->required()
            ->live()
            ->afterStateUpdated(function (Set $set) {
                $set('metadata.document_name_and_type', null);
            })->validationMessages([
                'required' => __('Document Type is required'),
            ]);
    }

}
