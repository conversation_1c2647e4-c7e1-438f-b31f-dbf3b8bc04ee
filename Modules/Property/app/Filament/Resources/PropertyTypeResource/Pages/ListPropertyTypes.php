<?php

namespace Modules\Property\app\Filament\Resources\PropertyTypeResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\PropertyTypeResource;

class ListPropertyTypes extends ListRecords
{
    protected static string $resource = PropertyTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Property Type')),
        ];
    }
}
