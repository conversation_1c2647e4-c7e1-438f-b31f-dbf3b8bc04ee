<?php

namespace Modules\Property\app\Filament\Resources;

//use App\Filament\Resources\AmenitiesResource\Pages;
//use App\Filament\Resources\AmenitiesResource\RelationManagers;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Form\AmenitiesForm;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Table\AmenitiesTable;
use Modules\Property\app\Models\Amenities;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Pages\ListAmenities;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Pages\CreateAmenities;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Pages\ViewAmenities;
use Modules\Property\app\Filament\Resources\AmenitiesResource\Pages\EditAmenities;

class AmenitiesResource extends Resource
{
    protected static ?string $model = Amenities::class;

    protected static ?string $navigationIcon = 'heroicon-o-tv';
    protected static ?string $navigationGroup = 'Amenities Management';



    public static function form(Form $form): Form
    {
        return AmenitiesForm::form($form);
    }

    public static function table(Table $table): Table
    {
        return AmenitiesTable::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAmenities::route('/'),
            'create' => CreateAmenities::route('/create'),
            'view' => ViewAmenities::route('/{record}'),
            'edit' => EditAmenities::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __("amenities");
    }

    public static function getNavigationGroup(): string
    {
        return  __('Amenities Management');
    }
    public static function getBreadcrumb() : string
    {
        return __('amenities');
    }
    public static function getModelLabel(): string
    {
        return __('amenities');
    }

    public static function getPluralModelLabel(): string
    {
        return __('amenities');
    }

}
