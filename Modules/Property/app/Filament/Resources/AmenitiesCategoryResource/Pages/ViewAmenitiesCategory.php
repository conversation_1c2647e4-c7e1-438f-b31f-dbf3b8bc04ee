<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource;

class ViewAmenitiesCategory extends ViewRecord
{
    protected static string $resource = AmenitiesCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
