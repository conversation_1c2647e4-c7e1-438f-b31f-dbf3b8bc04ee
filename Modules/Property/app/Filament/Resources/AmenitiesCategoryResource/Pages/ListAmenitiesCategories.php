<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource;

class ListAmenitiesCategories extends ListRecords
{
    protected static string $resource = AmenitiesCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Amenity Category')),
        ];
    }
}
