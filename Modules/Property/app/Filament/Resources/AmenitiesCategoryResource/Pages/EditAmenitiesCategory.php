<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource;

class EditAmenitiesCategory extends EditRecord
{
    protected static string $resource = AmenitiesCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
