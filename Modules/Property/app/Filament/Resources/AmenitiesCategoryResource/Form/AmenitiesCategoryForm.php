<?php

namespace Modules\Property\app\Filament\Resources\AmenitiesCategoryResource\Form;

use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
class AmenitiesCategoryForm
{

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Amenities Category Name'))
                    ->schema([
                        Forms\Components\Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.ar')
                                            ->label(__('Name Arabic'))
                                            ->translateLabel('ar')
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        Forms\Components\TextInput::make('name.en')
                                            ->label(__('Name English'))
                                            ->translateLabel('en')
                                            ->required(),
                                    ])
                            ])
                    ]),
            ]);
    }
}
