<?php

namespace Modules\Property\app\Listeners;

use Modules\Property\app\Events\PropertyStatusChange;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class PropertyStatusListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PropertyStatusChange $event): void
    {
        $event->property->update(['status' => $event->newStatus]);
    }
}
