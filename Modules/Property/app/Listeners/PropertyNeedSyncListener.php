<?php

namespace Modules\Property\app\Listeners;

use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\app\Events\PropertyNeedSync;

class PropertyNeedSyncListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PropertyNeedSync $event): void
    {
        $event->property->update(['ejar_sync_status' => EjarSyncStatus::NEED_SYNC]);
        //update steps
        $event->property->syncSteps()
            ->where('step', $event->step)
            ->update(['status' => PropertySyncStatus::NEED_SYNC->value]);
    }
}
