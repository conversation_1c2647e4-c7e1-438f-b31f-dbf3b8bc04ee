<?php

namespace Modules\Property\app\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Khaleds\Shared\Helpers\HandleMediaCollection;
use App\Shared\HasRelationshipChecks;


class Amenities extends BaseModel implements HasMedia
{
    use HasFactory,InteractsWithMedia,HandleMediaCollection,SoftDeletes;
    use HasRelationshipChecks;


    public $translatable = ['name'];

    protected $fillable = ['name', 'icon', 'key', 'amenities_category_id'];

    protected $relationsList = ["properties"];


    public function amenitiesCategory(): BelongsTo
    {
        return $this->belongsTo(AmenitiesCategory::class,'amenities_category_id');
    }
    public function properties()
    {
        return $this->belongsToMany(Property::class, 'property_amenities', 'amenity_id', 'property_id');
    }
}
