<?php

namespace Modules\Property\app\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Account\app\Models\Account;

class PropertyOwners extends Model
{
    use HasFactory;


    protected $guarded = ['id'];

    public function ownerable()
    {
        return $this->morphTo();
    }

    public function property()
    {
        return $this->belongsTo(Property::class);
    }
}
