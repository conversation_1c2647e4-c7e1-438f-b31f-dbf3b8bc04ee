<?php

namespace Modules\Property\app\Models;

use App\Enums\DocumentTypeEnum;
use App\Enums\RoleEnum;
use App\Models\Document;
use App\Models\User;
use App\Models\Val;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Schema\Builder;
use Khaleds\Shared\Models\BaseModel;
use Modules\Company\app\Models\Company;
use Modules\Favorite\app\Traits\HasFavorite;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Property\app\Filter\AmenityFilter;
use Modules\Property\app\Filter\PriceFilter;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Property\Enums\PropertyStatus;
use Modules\Property\app\Models\PropertyMeter;
use Modules\Property\app\Models\SyncPropertyStep;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Spatie\QueryBuilder\AllowedFilter;
use Modules\Organization\app\Models\Organization;
use Modules\Property\app\Models\PropertyType;
use Modules\Property\app\Models\Usability;
use Modules\Property\app\Models\PropertyOwners;
use Modules\Property\app\Models\PropertyAttribute;
use Modules\Property\app\Models\Amenities;
use Modules\Property\app\Models\UnitPrice;

/**
 * @property integer $id
 * @property integer $title
 * @property integer $description
 * @property string $link
 * @property string $image
 * @property integer $active
 * @property string $property_id
 * @property string $ejar_sync_status
 * @property string $last_synced_at
 * @property boolean $ejar_synced
 */
class Property extends BaseModel
{
    use HasFavorite;
    use BelongsToTenancy;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $guarded = ['id']; // Guarding the id field

    protected array $allowedFilters=['id','name','usability_id','property_type_id','region','city'];

    protected $casts = [
        'status' => PropertyStatus::class,
        'ejar_sync_status' => EjarSyncStatus::class,
        'last_synced_at' => 'datetime',
    ];

    public function brokers()
    {
        return $this->belongsToMany(User::class, 'property_brokers', 'property_id', 'broker_id')
            ->withTimestamps()
            ->with('userProfile');
    }

    public function PropertyBroker()
    {
        return $this->hasMany(PropertyBroker::class, 'property_id')
            ->with('broker.userProfile');
    }

    public function owners()
    {
        return $this->hasMany(PropertyOwners::class);
    }

    public function onwerOrganization()
    {
        return $this->hasOne(PropertyOwners::class)->where([
            ['percentage', '>', 0],
            ['ownerable_type', '=', Organization::class]
        ]);
    }

    public function viewOwners()
    {
        return $this->hasMany(PropertyOwners::class)->with('ownerable')->orderBy('is_representer');
    }

    public function onlyOwner()
    {
        return $this->hasMany(PropertyOwners::class)->where('percentage','>',0);
    }

    public function onlyRepresentative()
    {
        return $this->hasOne(PropertyOwners::class)->where([
            ['percentage', '=', 0],
            ['is_representer', '=', 1]
        ]);
    }

    public function usability()
    {
        return $this->belongsTo(Usability::class);
    }

    public function property_type()
    {
        return $this->belongsTo(PropertyType::class);
    }

    public function amenities()
    {
        // return $this->hasMany(PropertyAmenity::class);
        return $this->belongsToMany(Amenities::class, 'property_amenities', 'property_id', 'amenity_id');
    }

    public function attributes()
    {
        return $this->hasMany(PropertyAttribute::class);
    }

    public function propertyAttributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'property_attributes', 'property_id', 'attribute_id')->withPivot('value');
    }

    public function units()
    {
        return $this->hasMany(Property::class,"parent_id");
    }

    public function prices()
    {
        return $this->hasMany(UnitPrice::class, 'unit_id');
    }

    public function publishedUnits()
    {
        return $this->hasMany(Property::class,"parent_id")->whereNot('status', PropertyStatus::DRAFT);
    }

    public function publishedUnitsWithVal()
    {
        return $this->hasMany(Property::class, "parent_id")
            ->whereNot('status', PropertyStatus::DRAFT)
            ->whereHas('vals', function ($query) {
                $today = Carbon::today()->format('Y-m-d');
                $query->where('active', 1)
                    ->whereDate('start_date', '<=', $today)
                    ->whereDate('end_date', '>=', $today);
            });
    }

    public function vals()
    {
        return $this->morphMany(Val::class, 'morphable');
    }

    public function document()
    {
        return $this->morphOne(Document::class, 'morphable')->whereHas('documentType', function ($query) {
            $query->where('key', DocumentTypeEnum::Representative->value);
        });
    }

    public function documentOwnership()
    {
        return $this->morphOne(Document::class, 'morphable')->whereHas('documentType', function ($query) {
            $query->where('key', DocumentTypeEnum::Ownership->value);
        });
    }

    public function val()
    {
        return $this->morphOne(Val::class, 'morphable')->where('active', 1);
    }

    public function parent()
    {
        return $this->belongsTo(Property::class, 'parent_id');
    }

    public function leases()
    {
        return $this->belongsToMany(Lease::class, 'lease_units')
            ->withTimestamps();
    }
    public function propertyLeases()
    {
        return $this->hasMany(Lease::class);
    }
    public function activeLease()
    {
        return $this->hasOneThrough(
            Lease::class,
            LeaseUnit::class,
            'unit_id',
            'id',
            'id',
            'lease_id'
        )->whereIn('leases.status', LeaseEnum::getActiveStatus())
            ->orderByDesc('leases.created_at');
    }

    public function getAllowedFilters()
    {
        if (request()->has('filter')){

            if(array_key_exists('amenity',request()->input('filter'))){
                $this->allowedFilters= array_merge($this->allowedFilters,[
                    AllowedFilter::custom('amenity', new AmenityFilter()),
                ]);
            }
            if(array_key_exists('region',request()->input('filter'))) {
                AllowedFilter::callback('region', function (Builder $query, $value) {
                    return $query->where('region', 'LIKE', '%' . $value . '%');
                });
            }
            if(array_key_exists('city',request()->input('filter'))) {
                AllowedFilter::callback('city', function (Builder $query, $value) {
                    return $query->where('city', 'LIKE', '%' . $value . '%');
                });
            }

            if (array_key_exists('price', request()->input('filter'))) {
                $this->allowedFilters = array_merge($this->allowedFilters, [
                    AllowedFilter::custom('price', new PriceFilter()),
                ]);
            }
        }

        return parent::getAllowedFilters();
    }
    public function documents()
    {
        return $this->morphMany(Document::class, 'morphable');
    }

    // In Modules\Property\app\Models\Property
    public function leaseUnits()
    {
        return $this->hasMany(\Modules\Lease\app\Models\LeaseUnit::class, 'unit_id');
    }

    // In Modules\Property\app\Models\Property
    public function propertyType()
    {
        return $this->belongsTo(PropertyType::class, 'property_type_id');
    }
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getTotalUnitsIncome()
    {
        return $this->publishedUnits()
            ->sum('price');
    }

    public function getOccupiedUnitsIncome()
    {
        return $this->publishedUnits()
            ->whereHas('activeLease')
            ->sum('price');
    }

    public function getMinUnitPrice()
    {
        return $this->publishedUnits()
            ->min('price') ?? 0;
    }

    public function getMaxUnitPrice()
    {
        return $this->publishedUnits()
            ->max('price') ?? 0;
    }
    public function syncSteps()
    {
        return $this->hasMany(SyncPropertyStep::class);
    }

    public function meters()
    {
        return $this->hasOne(PropertyMeter::class);
    }
}
