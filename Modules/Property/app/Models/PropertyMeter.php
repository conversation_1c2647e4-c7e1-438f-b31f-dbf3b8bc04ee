<?php

namespace Modules\Property\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Property\app\Models\Property;

class PropertyMeter extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'property_id',
        'electronic_meter',
        'water_meter',
        'gas_meter'
    ];

    public function property()
    {
        return $this->belongsTo(Property::class);
    }
}
