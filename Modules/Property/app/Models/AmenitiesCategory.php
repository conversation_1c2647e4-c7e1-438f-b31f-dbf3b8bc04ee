<?php

namespace Modules\Property\app\Models;

use App\Shared\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Shared\HasRelationshipChecks;

class AmenitiesCategory extends BaseModel
{
    use HasFactory;
    use HasRelationshipChecks;

    protected $relationsList = ["amenities"];

    protected $fillable = ['name'];
    public $translatable = ['name'];

    public function amenities()
    {
        return $this->hasMany(Amenities::class);
    }
}
