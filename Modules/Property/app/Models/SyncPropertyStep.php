<?php

namespace Modules\Property\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Khaleds\Shared\Models\BaseModel;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertySyncStatus;
use Modules\Property\Enums\PropertySyncStep;

class SyncPropertyStep extends BaseModel
{
    protected $guarded = ['id'];

    protected $translatable=['error_message'];

    protected $casts = [
        'step' => PropertySyncStep::class,
        'status' => PropertySyncStatus::class,
        'response_data' => 'array',
        'error_message' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }
}
