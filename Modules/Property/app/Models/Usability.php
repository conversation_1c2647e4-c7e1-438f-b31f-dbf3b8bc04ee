<?php

namespace Modules\Property\app\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use App\Shared\HasRelationshipChecks;

/**
 * @property integer $id
 * @property integer $name
 * @property integer $icon
 */
class Usability extends BaseModel
{
    use SoftDeletes;
    use HasRelationshipChecks;

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['name', 'key'];
    protected $relationsList = ["properties"];
    public function properties()
    {
        return $this->hasMany(Property::class, 'usability_id');
    }
}
