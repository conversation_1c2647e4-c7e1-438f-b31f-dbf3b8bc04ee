<?php

namespace Modules\Property\app\Models;

use Khaleds\Shared\Models\BaseModel;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Models\Lease;

class TenantUnitView extends BaseModel
{
    protected $table = 'tenant_units_view';

    public $timestamps = false;

    protected $guarded = []; // To Make it read-only

    public function lease()
    {
        return $this->belongsTo(Lease::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function unit()
    {
        return $this->belongsTo(Property::class, 'unit_id');
    }
}
