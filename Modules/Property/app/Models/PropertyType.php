<?php

namespace Modules\Property\app\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use App\Shared\HasRelationshipChecks;

class PropertyType extends BaseModel
{
    use HasFactory;
    use SoftDeletes;
    use HasRelationshipChecks;

    public $translatable = ['name'];
    protected $fillable = ['name', 'key', 'property_type'];

    protected array $allowedFilters=['id','name','property_type'];

    protected $relationsList = ["attributes"];

    public function attributes()
    {
        return $this->belongsToMany(Attribute::class, 'attribute_type');
    }
}
