<?php

namespace Modules\Property\app\Models;

use App\Shared\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Helpers\HandleMediaCollection;
use Spatie\MediaLibrary\InteractsWithMedia;

class PropertyAttribute extends BaseModel
{
    use HasFactory, InteractsWithMedia,HandleMediaCollection;

    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    public function attribute()
    {
        return $this->belongsTo(Attribute::class);
    }
}
