<?php

namespace Modules\Property\app\Models;

use App\Shared\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Helpers\HandleMediaCollection;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Shared\HasRelationshipChecks;

class Attribute extends BaseModel implements HasMedia
{
    use HasFactory, InteractsWithMedia,HandleMediaCollection;
    use HasRelationshipChecks;


    protected $relationsList = ["properties"];

    public function propertyTypes()
    {
        return $this->belongsToMany(PropertyType::class, 'attribute_type');
    }

    public function properties()
    {
        return $this->belongsToMany(Property::class, 'property_attributes', 'attribute_id', 'property_id');
    }
}
