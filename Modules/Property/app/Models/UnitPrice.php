<?php

namespace Modules\Property\app\Models;

use Khaleds\Shared\Models\BaseModel;
use Illuminate\Database\Eloquent\Model;
use Modules\Property\app\Models\Property;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Property\Database\Factories\UnitPriceFactory;

class UnitPrice extends BaseModel
{
    protected $guarded=['id'];

    public function unit()
    {
        return $this->belongsTo(Property::class);
    }

}
