<?php

namespace Modules\Property\app\Models;

use App\Shared\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PropertyAmenity extends BaseModel
{
    use HasFactory;

    public function property()
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    public function amenity()
    {
        return $this->belongsTo(Amenities::class, 'amenity_id');
    }
}
