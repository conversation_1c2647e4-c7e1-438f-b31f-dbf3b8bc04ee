<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Lease\app\Http\Resources\Api\LeaseResource;


class TenantUnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [

            'lease_id' => $this->lease_id,
            'member_id' => $this->member_id,
            'member_role' => $this->member_role,
            'unit_id' => $this->unit_id,
            'unit' => $this->whenLoaded('unit', function (){
                return UnitResource::make($this->unit);
            }),
            'lease' => $this->whenLoaded('lease', function (){
                return LeaseResource::make($this->lease);
            })
        ];

        return $data;

    }
}
