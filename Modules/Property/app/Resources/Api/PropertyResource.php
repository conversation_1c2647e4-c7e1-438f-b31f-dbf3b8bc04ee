<?php

namespace Modules\Property\app\Resources\Api;

use App\Enums\MediaCollectionEnum;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Helpers\AccountHelper;
use Modules\Company\app\Http\Resources\Api\CompanyResource;
use Modules\General\app\Http\Resources\Api\DocumentWithoutPropertyResource;
use Modules\Property\Services\PropertyService;
use Modules\Property\app\Resources\Api\UnitPriceResource;

class PropertyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $propertyService = app(PropertyService::class);
        $data = [
            "id" => $this->id,
            //this made to handle the case of unit details to show unit type and name
            "name" => $this->name == null ? $this->property_type->name . '-' . $this->number : $this->name,
            "price" => $this->prices->count() > 0 && $this->prices?->min('annual_price') < $this->price? $this->prices?->min('annual_price') : $this->price,
            "prices_list" => $propertyService->getUnitPrice($this->id),
            "image" => $this->getMediaImage('property_images'),
            "images_urls" => $this->getMedia('property_images')->map(function($media) {
                return [
                    'id' => $media->id,
                    'url' => $media->getUrl(),
                ];
            }),
            "description" => $this->description,
            "broker" => BrokerResource::collection($this->brokers ?? $this->parent?->brokers),
            "total_price" => $this->total_price,
            "min_price" => $this->units->min('price'),
            "max_price" => $this->units->max('price'),
            "location" => [
                "address" => $this->address ?? $this->parent->address,
                "city" => $this->city ?? $this->parent->city,
                "district" => $this->district ?? $this->parent->district,
                "region" => $this->region ?? $this->parent->region,
                "street" => $this->street ?? $this->parent->street,
                "building_date" => $this->building_date,
                "latitude" => $this->lat ?? $this->parent->lat,
                "longitude" => $this->lng ?? $this->parent->lng,
                "image" => $this->getMediaImage(MediaCollectionEnum::PROPERTY_LOCATION_IMAGE->value),
            ],
            "number" => $this->number,
            "postal_code" => $this->postal_code,
            "property_type" => new PropertyTypeResource($this->property_type),
            "parent" => new PropertyResource($this->parent),
            "units" => (AccountHelper::CurrentRole() == AccountRolesEnum::OWNER || AccountHelper::CurrentRole() == AccountRolesEnum::OWNER_REPRESENTER)
                    ? UnitResource::collection($this->publishedUnits)
                    : UnitResource::collection($this->publishedUnitsWithVal),            
            "attributes" => AttributeResource::collection($this->attributes),
            "amenities" => AmenityResource::collection($this->amenities ?? $this->parent->amenities),
            "is_active" => $this->is_active,
            "is_available" => $this->is_available,
            "usability" => new PropertyUsabilityResource($this->usability),
            "val" => new ValResource($this->val ?? $this->parent?->val),
            "is_favourite" => $this->isFavoritedByUser(),
            "status" => $this->status,
            "ownership_document" => new DocumentWithoutPropertyResource($this->documentOwnership),
            "company" => CompanyResource::make($this->company)
        ];

        return $data;

    }
}
