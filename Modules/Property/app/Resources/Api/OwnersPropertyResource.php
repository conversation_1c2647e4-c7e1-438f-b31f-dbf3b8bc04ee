<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Account\app\Models\Account;
use Modules\Organization\app\Http\Resources\Api\OrganizationResource;
use Modules\Organization\app\Models\Organization;

class OwnersPropertyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "member_type" => $this->ownerable_type == Account::class ? 'individual' : 'organization',
            "user"=> ($this->ownerable_type == Account::class) ? AccountCustomerResource::make($this->ownerable) : null,
            "organization"=> ($this->ownerable_type == Organization::class) ? OrganizationResource::make($this->ownerable) : null,
            "percentage"=> $this->percentage,
            "is_representer"=> $this->is_representer,
        ];
    }
}
