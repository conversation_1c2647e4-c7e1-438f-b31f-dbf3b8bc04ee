<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Rates\app\Transformers\RateResource;


class BrokerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "name" => $this->name,
            "first_name" => $this->userProfile->first_name ?? null,
            "second_name" => $this->userProfile->second_name ?? null,
            "third_name" => $this->userProfile->third_name ?? null,
            "last_name" => $this->userProfile->last_name ?? null,
            "email" => $this->email,
            "company" => $this->company ?? null,
            "phone" => $this->userProfile->phone_number ?? null,
            "rate_avg" => $this->averageRate() ?? 0,
            "rate_list" => isset($this->rates) ? RateResource::collection($this->rates) : [],
        ];

        return $data;
    }
}
