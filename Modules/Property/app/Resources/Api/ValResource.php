<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;


class ValResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "model_type" => $this->morphable_type,
            "model_id" => $this->morphable_id,
            "value" => $this->value,
            "start_date" => $this->start_date,
            "end_date" => $this->end_date,
            "active" => $this->active
        ];
        return $data;

    }
}
