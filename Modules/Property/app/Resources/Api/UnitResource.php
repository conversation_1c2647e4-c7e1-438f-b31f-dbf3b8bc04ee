<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\app\Http\Resources\Api\CompanyResource;
use Modules\Lease\app\Http\Resources\Api\LeaseResource;


class UnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [

            "id" => $this->id,
            "name" => $this->property_type->name . '-' . $this->number,
            "image" => $this->getMediaImage('property_images'),
            "images_urls" => $this->getMedia('property_images')->map(function($media) {
                return [
                    'id' => $media->id,
                    'url' => $media->getUrl(),
                ];
            }),
            "address" => $this->parent?->address,
            "parent_id" => $this->parent_id,
            "is_available" => $this->is_available,
            "broker" => BrokerResource::collection($this->brokers ?? $this->parent?->brokers), //todo make it to when loaded
            "location" => [
                "address" => $this->address ?? $this->parent->address,
                "city" => $this->city ?? $this->parent->city,
                "district" => $this->district ?? $this->parent->district,
                "region" => $this->region ?? $this->parent->region,
                "street" => $this->street ?? $this->parent->street,
                "building_date" => $this->building_date ?? $this->parent->building_date,
                "latitude" => $this->lat ?? $this->parent->lat,
                "longitude" => $this->lng ?? $this->parent->lng,
            ],
            "attributes" =>(isset($this->attributes)) ? AttributeResource::collection($this->attributes) : null,
            "usability" => $this->whenLoaded('usability', function (){
                return PropertyUsabilityResource::make($this->usability);
            }),
            "property_type" => $this->whenLoaded('property_type', function (){
                return PropertyTypeResource::make($this->property_type);
            }),
            "is_favourite" => $this->isFavoritedByUser(),
            "lease" => $this->whenLoaded('activeLease', function (){
               return LeaseResource::make($this->activeLease->load('leaseMembers')) ?? null;
            }),
            "status"=>$this->status,
            "company" => CompanyResource::make($this->company)
        ];

        return $data;

    }
}
