<?php

namespace Modules\Property\app\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;


class AmenityCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "name" => $this->name,
        ];
        return $data;

    }
}
