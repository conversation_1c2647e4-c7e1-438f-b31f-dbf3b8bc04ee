<?php

namespace Modules\Service\database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Service\app\Models\Service;

class DefaultServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'name' => ['en' => 'water', 'ar' => 'مياة'],
                'is_fixed_lease' => 1,
                'key' => 'water'
            ],
            [
                'name' => ['en' => 'electric', 'ar' => 'كهرباء'],
                'is_fixed_lease' => 1,
                'key' => 'electricity'
            ],
            [
                'name' => ['en' => 'gas', 'ar' => 'غاز'],
                'is_fixed_lease' => 1,
                'key' => 'gas'
            ],
        ];

        foreach ($data as $item) {
            // Check if service already exists
            $existingService = Service::where(function ($query) use ($item) {
                $query->where(DB::raw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.en'))"), $item['name']['en'])
                    ->orWhere(DB::raw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.ar'))"), $item['name']['ar']);
            })->first();

            if ($existingService) {
                // Update only columns that are NULL
                $updateData = [];

                if (is_null($existingService->key)) {
                    $updateData['key'] = $item['key'];
                }

                if (is_null($existingService->is_fixed_lease)) {
                    $updateData['is_fixed_lease'] = $item['is_fixed_lease'];
                }

                if (!empty($updateData)) {
                    $existingService->update($updateData);
                }
            } else {
                // Insert new service if it doesn't exist
                Service::create([
                    'name' => $item['name'],
                    'is_fixed_lease' => $item['is_fixed_lease'],
                    'key' => $item['key']
                ]);
            }
        }
    }
}
