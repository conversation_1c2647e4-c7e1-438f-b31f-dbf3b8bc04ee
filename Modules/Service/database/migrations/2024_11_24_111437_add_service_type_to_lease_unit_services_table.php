<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Service\Enums\ServiceTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->enum('service_type', ServiceTypeEnum::getServiceTypes())->nullable()->after('to_be_paid_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lease_unit_services', function (Blueprint $table) {
            $table->dropColumn('service_type');
        });
    }
};
