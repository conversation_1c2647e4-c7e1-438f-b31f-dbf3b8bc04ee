<?php

namespace Modules\Service\Enums;

enum ServicePaymentEnum: string
{
    const FIXED_FEE_TYPE = 'fixed_fee';
    const METERED_FEE_TYPE = 'metered_fee';

    public static function labels(): array
    {
        return [
            self::FIXED_FEE_TYPE => __('Fixed Fee'),
            self::METERED_FEE_TYPE => __('Metered Fee'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
    public static function getServicePaymentTypes(): array
    {
        return [
            self::FIXED_FEE_TYPE,
            self::METERED_FEE_TYPE,
        ];
    }
}
