<?php

namespace Modules\Service\Enums;

enum ServiceTypeEnum: string
{
    const FIXED_FEE = 'fixed_value';
    const METERE_FEE = 'meter_reading';
    const ON_LEASE = 'on_lease';
    const ON_DEMAND = 'on_demand';

    public static function labels(): array
    {
        return [
            self::FIXED_FEE => __('Fixed Value'),
            self::METERE_FEE => __('Metered Value'),
            self::ON_LEASE => __('On Lease'),
            self::ON_DEMAND => __('On Demand'),
        ];
    }

    public static function getLabel(?string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
    public static function getServiceTypes(): array
    {
        return [
            self::FIXED_FEE,
            self::METERE_FEE,
            self::ON_LEASE,
            self::ON_DEMAND,
        ];
    }
}
