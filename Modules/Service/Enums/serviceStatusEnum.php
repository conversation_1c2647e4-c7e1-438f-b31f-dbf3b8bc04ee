<?php

namespace Modules\Service\Enums;

enum serviceStatusEnum: string
{
    case Active = 'active';
    case Inactive = 'inactive';

    public function label(): string
    {
        return match($this) {
            self::Active => 'Active',
            self::Inactive => 'Inactive',
        };
    }

    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }
    public function value(): bool
    {
        return match($this) {
            self::Active => true,
            self::Inactive => false,
        };
    }

    public static function getServiceRequestStatusOptions(): array
    {
        return [
            self::Active->value => self::Active->label(),
            self::Inactive->value => self::Inactive->label(),
        ];
    }
}
