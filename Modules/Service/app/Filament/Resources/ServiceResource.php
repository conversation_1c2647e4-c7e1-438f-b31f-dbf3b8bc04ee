<?php

namespace Modules\Service\app\Filament\Resources;

use App\Shared\Components\DateRangeFilter;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Service\app\Filament\Resources\ServiceResource\Components\FormComponent;
use Modules\Service\app\Filament\Resources\ServiceResource\Pages\ViewService;
use Modules\Service\Enums\serviceStatusEnum;
use Modules\Service\Filament\Resources\ServiceResource\Pages;
use Modules\Service\app\Filament\Resources\ServiceResource\Pages\CreateService;
use Modules\Service\app\Filament\Resources\ServiceResource\Pages\EditService;
use Modules\Service\app\Filament\Resources\ServiceResource\Pages\ListServices;
use Modules\Service\app\Models\Service;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;


class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_fixed_lease')
                    ->label(__('Fixed Lease'))
                    ->sortable()
                    ->toggleable()
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state) {
                            $record->update(['is_fixed_commercial_lease' => false]);
                        }
                    }),

                Tables\Columns\ToggleColumn::make('is_fixed_commercial_lease')
                    ->label(__('Fixed Commercial Lease'))
                    ->sortable()
                    ->toggleable()
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state) {
                            $record->update(['is_fixed_lease' => false]);

                        }
                    }),
                Tables\Columns\ToggleColumn::make('active')
                    ->label(__('Active'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->label(__('Created at'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->label(__('Updated at'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->since()
                    ->sortable()
                    ->label(__('Deleted at'))
                    ->tooltip(fn($record) => $record->deleted_at?->format('Y-m-d H:i:s'))
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('active')
                    ->label(__('Active'))
                    ->options(serviceStatusEnum::getServiceRequestStatusOptions())->native(false),
                SelectFilter::make('is_fixed_lease')
                    ->label(__('Fixed Lease'))
                    ->options(serviceStatusEnum::getServiceRequestStatusOptions())->native(false),

                DateRangeFilter::make('created_at')->fromLabel(__('Created From'))
                    ->untilLabel(__('Created Until')),

            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListServices::route('/'),
            'create' => CreateService::route('/create'),
            'edit' => EditService::route('/{record}/edit'),
            'view' => ViewService::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Properties Management');
    }

    public static function getNavigationLabel(): string
    {
        return __('Services');
    }

    public static function getBreadcrumb(): string
    {
        return __('Services');
    }

    public static function getModelLabel(): string
    {
        return __('Services');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Services');
    }

}
