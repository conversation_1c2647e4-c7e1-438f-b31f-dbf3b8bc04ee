<?php

namespace Modules\Service\app\Filament\Resources\ServiceResource\Components;
use App\Models\Val;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Forms\Set;
class FormComponent
{
    public static function getForm() :array{

        return [
                Grid::make(2)
                ->schema([
                    Section::make(__('Name of service'))
                        ->schema([
                            Tabs::make('Tabs')
                                ->tabs([
                                    Tabs\Tab::make(__('Arabic'))
                                        ->schema([
                                            TextInput::make('name.ar')
                                                ->label(__('Name'))
                                                ->translateLabel('ar')
                                                ->required(),
                                        ]),
                                    Tabs\Tab::make(__('English'))
                                        ->schema([
                                            TextInput::make('name.en')
                                                ->label(__('Name'))
                                                ->translateLabel('en')
                                                ->required(),
                                        ]),
                                ]),
                        ])->columnSpan(1),

                    Section::make(__('Options'))
                        ->schema([
                            Group::make([

                                Toggle::make('active')
                                    ->label(__('Active'))
                                    ->default(true),
                                Toggle::make('is_fixed_lease')
                                ->label(__('Fixed Lease'))
                                ->reactive()
                                ->afterStateUpdated(function ($state, $set, $get) {
                                    if ($state) {
                                        $set('is_fixed_commercial_lease', false);
                                    }
                                }),
                                
                                Toggle::make('is_fixed_commercial_lease')
                                    ->label(__('Fixed Commercial Lease'))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $set, $get) {
                                        if ($state) {
                                            $set('is_fixed_lease', false);
                                        }
                                    }),
                            ]),
                        ])->columnSpan(1),
                ]),
        ];

    }
}


