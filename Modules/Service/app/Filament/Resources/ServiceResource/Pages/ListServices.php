<?php

namespace Modules\Service\app\Filament\Resources\ServiceResource\Pages;

use Modules\Service\app\Filament\Resources\ServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListServices extends ListRecords
{
    protected static string $resource = ServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Service')),
        ];
    }
}
