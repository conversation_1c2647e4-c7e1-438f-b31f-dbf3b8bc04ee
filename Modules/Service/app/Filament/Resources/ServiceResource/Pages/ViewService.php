<?php

namespace Modules\Service\app\Filament\Resources\ServiceResource\Pages;

use Modules\Service\app\Filament\Resources\ServiceResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions;

class ViewService extends ViewRecord
{
    protected static string $resource = ServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
        ];
    }
}
