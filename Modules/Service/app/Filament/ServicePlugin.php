<?php

namespace Modules\Service\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Service\app\Filament\Resources\ServiceResource;

class ServicePlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Service';
    }

    public function getId(): string
    {
        return 'service';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                ServiceResource::class,
            ]);
    }
}
