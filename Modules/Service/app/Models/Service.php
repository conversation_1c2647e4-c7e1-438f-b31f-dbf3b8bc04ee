<?php

namespace Modules\Service\app\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use Illuminate\Support\Str;
use Modules\Lease\app\Models\LeaseUnitService;
use App\Shared\HasRelationshipChecks;


class Service extends BaseModel
{
    use HasFactory;
    use SoftDeletes;
    use HasRelationshipChecks;


    protected $guarded = ['id'];

    protected $relationsList = ["leaseUnitServices"];
    protected $translatable = ['name'];

    protected function casts(): array
    {
        return [
            'is_fixed_lease' => 'boolean',
            'active' => 'boolean',
        ];
    }

    public function leaseUnitServices()
    {
        return $this->hasMany(LeaseUnitService::class);
    }

}
