<?php

namespace Modules\Service\app\Http\Controllers\Api;

use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Service\app\Http\Resources\Api\ServicesResource;
use Modules\Service\app\Services\ServiceService;

class ServiceController extends ControllerAbstract
{
    protected string $jsonResourceClass = ServicesResource::class;

    protected array $filter=["active"=>1];

    public function __construct(ServiceService $service)
    {
        parent::__construct($service);
    }


}
