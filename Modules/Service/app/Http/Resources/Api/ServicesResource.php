<?php

namespace Modules\Service\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ServicesResource extends JsonResource
{

    public function toArray($request)
    {
        return [
            "id" => $this->id ?? "",
            "name" => $this->name ?? null,
            "is_fixed_lease" => $this->is_fixed_lease,
            "icon" => $this->getMedia('service')->first() ? $this->getMedia('service')->first()->getUrl() : null,
        ];
    }
}
