# Favorite Module

This module provides functionality to add favorite capabilities to your Laravel application.

## Configuration

To configure the Favorite module, you need to add entries to the `favoriteable` array in the `config/favorite.php` file. 
This configuration is used for validation when creating favorites and for retrieving the appropriate model and resource classes.

### Example Configuration
To add a new favoriteable type:

1. Choose a key for your new type (e.g., "article").
2. Add an entry to the favoriteable array with the following structure:

```php
"article" => [
    "model" => \App\Models\Article::class,
    "resource" => \App\Http\Resources\ArticleResource::class
]
```
3. Replace ``\App\Models\Article::class`` with the fully qualified class name of your model.
4. Replace ``\App\Http\Resources\ArticleResource::class`` with the fully qualified class name of your API resource for this model.

### Using the HasFavorite Trait
The ``HasFavorite`` trait provides methods to add favorite relations to your models.

Adding the Trait to Your Model
```php
use Modules\Favorite\app\Traits\HasFavorite;

class YourModel extends Model
{
    use HasFavorite;

    // ... rest of your model code
}
```
## APIs

This module provides both RESTful APIs for managing favorites and a special toggle API for easy favorite toggling.

### RESTful APIs

The module includes standard RESTful endpoints for managing favorites:

- `GET /api/favorite` - List all favorites for the authenticated user
- `POST /api/favorite` - Create a new favorite
- `GET /api/favorite/{id}` - Get details of a specific favorite
- `DELETE /api/favorite/{id}` - Remove a favorite

### Toggle API

For convenience, a special toggle API is provided to easily add or remove a favorite:

- `POST /api/favorite/toggle`

#### Toggle API Usage

To use the toggle API, send a POST request with the following parameters:

```json
{
  "favoriteable_id": 1,
  "favoriteable_type": "property"
}
