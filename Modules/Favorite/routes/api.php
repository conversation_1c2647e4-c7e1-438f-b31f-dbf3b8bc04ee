<?php

use Illuminate\Support\Facades\Route;
//use Modules\Favorite\Http\Controllers\FavoriteController;
use Modules\Favorite\app\Http\Controllers\Api\FavoriteController;
/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->prefix('favorite')->group(function () {
    Route::apiResource('/', FavoriteController::class)->names('favorite');
    Route::post("/toggle",[FavoriteController::class,'toggle']);
});
