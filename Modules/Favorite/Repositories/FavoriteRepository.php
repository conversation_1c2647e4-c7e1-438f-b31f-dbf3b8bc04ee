<?php

namespace Modules\Favorite\Repositories;

//use Illuminate\Database\Eloquent\Model;
//use Illuminate\Database\Eloquent\ModelNotFoundException;
//use Modules\Favorite\Entities\Favorite;
//use TomatoPHP\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Favorite\app\Models\Favorite;

class FavoriteRepository extends RepositoriesAbstract
{

    public function __construct(Favorite $model)
    {
        parent::__construct($model);
    }


}
