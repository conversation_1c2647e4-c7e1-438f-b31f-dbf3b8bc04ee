<?php

namespace Modules\Favorite\Services;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Favorite\Repositories\FavoriteRepository;

class FavoriteService extends ServiceAbstract
{

    public function __construct(FavoriteRepository $repository)
    {
        parent::__construct($repository);
    }

    public function toggle(array $data)
    {

        $favorite = $this->repository->getFirstBy($data);
        if ($favorite) {
            $this->repository->delete($favorite);
            $result = [
                'state'=>true,
                'message'=>__('unfavorable successfully')
            ];
        } else {
            $this->repository->create($data);
            $result = [
                'state'=>true,
                'message'=>__('favorable successfully')
            ];
        }

        return $result;
    }


}


