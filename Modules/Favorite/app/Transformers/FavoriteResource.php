<?php

namespace Modules\Favorite\app\Transformers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FavoriteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        if($request->query('favoriteable_type')){
            $favoriteable_type = $request->query('favoriteable_type');
        }else{
            $favoriteable_type = $request->input('favoriteable_type');
        }
        
        $resourceClass = Config("favorite.favoriteable.{$favoriteable_type}.resource");
        return [
            "id" => $this->id ?? null,
            "favorite" => $this->favorite ?? null,
            "favoriteable" => $resourceClass::make($this->favoriteable),
        ];
    }
}
