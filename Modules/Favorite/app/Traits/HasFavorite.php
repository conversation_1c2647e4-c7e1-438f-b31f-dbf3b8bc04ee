<?php
namespace Modules\Favorite\app\Traits;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\Auth;
use Modules\Favorite\app\Models\Favorite;

trait HasFavorite
{

    public function favorites(): MorphMany
    {
        return $this->morphMany(Favorite::class, 'favoriteable');
    }

    public function isFavoritedByUser($user = null): bool
    {
        if (!$user && !Auth::check()) {
            return false;
        }

        $user = $user ?: Auth::user();

        return $this->favorites()->where(['favorite_id'=> $user->id,'favorite_type'=>get_class($user)])->exists();
    }
}
