<?php

namespace Modules\Favorite\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class FavoriteStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'favoriteable_type' => 'sometimes|in:'.implode(',',array_keys(config('favorite.favoriteable'))),
            'favoriteable_id' => 'required'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        $validated['favoriteable_type'] = config("favorite.favoriteable.{$this->input('favoriteable_type')}.model") ?? null;
        $validated['favorite_id'] = Auth::id();
        $validated['favorite_type'] = Auth::user()::class;
        return $validated;
    }
}
