## How to use the Rate Module

The Rate Module provides an easy way to add rating functionality to any model in your application. Here's how to use it:

### 1. Make a Model Rateable

To make any model rateable, simply use the `HasRates` trait:

```php
use YourNamespace\RatePlugin\Traits\HasRates;

class User extends Model
{
    use HasRates;

    // ... rest of your model
}
```
This trait provides all the necessary relations for a rateable model.

### 2. Use the API
The module comes with pre-built API endpoints for rating functionality. These are accessible with the prefix "rate". For example:

Create a rate: POST /api/rate
Update a rate: PUT /api/rate/{id}
Delete a rate: DELETE /api/rate/{id}
Get rates: GET /api/rate
### 3. Configuration
You can customize the behavior of the Rate Module through the configuration file. This includes:

Setting the minimum and maximum values for ratings
### 4. Add Rateable Type 
you can validate rateable type by adding the key and class in this file ``Modules\Rates\app\Enums\RateableTypeEnum``
