<?php

namespace Modules\Rates\app\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Modules\Rates\app\Enums\RateableTypeEnum;

class ValidRateableType implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!RateableTypeEnum::isValid($value)) {
            $fail(__('The :attribute must be a valid rateable type.'));
            return;
        }

        $class = RateableTypeEnum::getClass($value);
        if (!class_exists($class)) {
            $fail(__('The class for :attribute does not exist.'));
        }
    }
}
