<?php

namespace Modules\Rates\app\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use phpDocumentor\Reflection\Types\False_;

class UniqueRating implements ValidationRule
{
    private $rateableType;

    public function __construct($rateableType)
    {
        $this->rateableType = $rateableType;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {

        if ($this->rateExists($value)) {
            $fail(__('You have already rated this item.'));
        }

    }

    private function rateExists($rateableId): bool
    {
        return DB::table('rates')
            ->where('user_id', Auth::id())
            ->where('user_type', Auth::user()::class)
            ->where('rateable_type', $this->rateableType)
            ->where('rateable_id',$rateableId)
            ->exists();
    }
}
