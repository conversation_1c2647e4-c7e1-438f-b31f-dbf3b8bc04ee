<?php

namespace Modules\Rates\app\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Rates\app\Http\Requests\UpdateRateRequest;
use Modules\Rates\app\Transformers\RateResource;
use Modules\Rates\app\Http\Requests\CreateRateRequest;
use Modules\Rates\Services\RateService;

class RatesController extends ControllerAbstract
{
    protected string $jsonResourceClass = RateResource::class;
    protected string $storeRequestClass = CreateRateRequest::class;
    protected string $updateRequestClass = UpdateRateRequest::class;


    public function __construct(RateService $service)
    {
        parent::__construct($service);
    }
}
