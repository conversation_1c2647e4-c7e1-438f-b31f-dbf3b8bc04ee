<?php

namespace Modules\Rates\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Modules\Rates\app\Enums\RateableTypeEnum;
use Modules\Rates\app\Rules\UniqueRating;
use Modules\Rates\app\Rules\ValidRateableType;
class CreateRateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'rateable_id' => [
                'required',
                'integer',
                new UniqueRating($this->input('rateable_type')),
            ],
            'rateable_type' => [
                'required',
                'string',
                new ValidRateableType(),
            ],
            'value' => 'required|numeric|min:'.config('rates.min_rate_value').'|max:'.config('rates.max_rate_value'),
            'comment' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validated data from the request.
     *
     * @param  string|null  $key
     * @param  mixed  $default
     * @return mixed
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        $validated['user_id'] = Auth::id();
        $validated['user_type'] = Auth::user()::class;
        $validated['rateable_type'] = RateableTypeEnum::getClass($this->input('rateable_type'));
        return $validated;
    }
}
