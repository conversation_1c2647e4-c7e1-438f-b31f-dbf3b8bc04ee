<?php

namespace Modules\Rates\app\Transformers;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'value' => $this->value,
            'comment' => $this->comment,
            'user' => $this->user,
            'rateable' => $this->rateable,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
