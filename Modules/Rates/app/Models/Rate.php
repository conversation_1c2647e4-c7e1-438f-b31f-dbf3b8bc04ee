<?php

namespace Modules\Rates\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Khaleds\Shared\Models\BaseModel;

// use Modules\Rates\Database\Factories\RateFactory;

class Rate extends BaseModel
{
    use HasFactory;

    protected $fillable = ['value', 'comment', 'user_type', 'user_id','rateable_type','rateable_id'];

    protected $casts = [
        'value' => 'float',
    ];

    public function rateable()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->morphTo();
    }
}
