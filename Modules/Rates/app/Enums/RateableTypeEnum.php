<?php
namespace Modules\Rates\app\Enums;

use App\Models\User;

class RateableTypeEnum
{
    public const TYPES = [
        'user' => User::class,
        // Add more types as needed
    ];

    public static function getClass(string $key): ?string
    {
        return self::TYPES[strtolower($key)] ?? null;
    }

    public static function isValid(string $key): bool
    {
        return isset(self::TYPES[strtolower($key)]);
    }

    public static function getKeys(): array
    {
        return array_keys(self::TYPES);
    }
}
