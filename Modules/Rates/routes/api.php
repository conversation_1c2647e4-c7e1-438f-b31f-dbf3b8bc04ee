<?php

use Illuminate\Support\Facades\Route;
use Modules\Rates\app\Http\Controllers\Api\RatesController;
/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->prefix('rate')->group(function () {
    Route::apiResource('/', RatesController::class)->names('rates');
});
