<?php

namespace Modules\BankAccount\Helpers;

use Filament\Forms\Components\Select;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\app\Models\BankAccount;

class BankAccountHelper
{

    public static function bankAccountField(string $fieldName = 'bank_account_id', string $bankableType,int $bankableId)
    {
        return Select::make($fieldName)
            ->label(__('Bank Account'))
            ->options(function () use ($bankableType, $bankableId) {
                return BankAccount::query()
                    ->where('bankable_type', $bankableType)
                    ->where('bankable_id', $bankableId)
                    ->orderBy('is_primary', 'desc')
                    ->get()
                    ->pluck('account_number', 'id');
            })
            ->searchable()
            ->preload()
            ->reactive();
    }
}
