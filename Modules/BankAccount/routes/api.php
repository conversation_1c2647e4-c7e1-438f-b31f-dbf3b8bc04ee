<?php

use Illuminate\Support\Facades\Route;
use Modules\BankAccount\app\Http\Controllers\Api\BankAccountController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->prefix('bank-account')->group(function () {
    Route::post('/{id}', [BankAccountController::class, 'update'])->name('api.bank-account.update');
    Route::delete('/{id}', [BankAccountController::class, 'destroy'])->name('api.bank-account.destroy');
    Route::apiResource('/', BankAccountController::class)->names('bankaccount');
});
