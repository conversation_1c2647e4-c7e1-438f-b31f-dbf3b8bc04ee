<?php

namespace Modules\BankAccount\app\Http\Controllers\Api;


use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\BankAccount\app\Http\Requests\CreateBankAccountRequest;
use Modules\BankAccount\app\Http\Requests\UpdateBankAccountRequest;
use Modules\BankAccount\Services\BankAccountService;
use Modules\BankAccount\app\Transformers\BankAccountResource;

class BankAccountController extends ControllerAbstract
{
    protected string $jsonResourceClass = BankAccountResource::class;
    protected string $storeRequestClass = CreateBankAccountRequest::class;
    protected string $updateRequestClass = UpdateBankAccountRequest::class;


    public function __construct(BankAccountService $service)
    {
        parent::__construct($service);

        $this->filter = ['bankable_type'=>Auth::user()::class,'bankable_id'=>auth()->id()];
    }

    public function destroy($id): JsonResponse
    {
        try {
            $this->service->delete($id);
            return ApiResponse::success('Bank account deleted successfully');

        } catch (ModelNotFoundException $e) {
            return ApiResponse::error('Bank account not found', 404);

        } catch (\Exception $e) {
            return ApiResponse::error($e->getMessage(), 422);
        }
    }
}
