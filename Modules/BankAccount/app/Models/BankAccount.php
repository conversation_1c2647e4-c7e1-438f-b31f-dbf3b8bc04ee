<?php

namespace Modules\BankAccount\app\Models;


use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Khaleds\Shared\Models\BaseModel;


class BankAccount extends BaseModel
{

    protected $fillable = [
        'bankable_type',
        'bankable_id',
        'bank_name',
        'branch_name',
        'account_name',
        'account_number',
        'swift_code',
        'iban',
        'is_primary',
        'is_active'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function bankable(): MorphTo
    {
        return $this->morphTo();
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('orderByPrimary', function ($query) {
            $query->orderBy('is_primary', 'desc');
        });

        static::creating(function($bankAccount) {
            if ($bankAccount->is_primary) {
                static::where('bankable_type', $bankAccount->bankable_type)
                    ->where('bankable_id', $bankAccount->bankable_id)
                    ->update(['is_primary' => false]);
            }
        });

        static::updating(function ($bankAccount) {
            if($bankAccount->isDirty('is_primary') && $bankAccount->is_primary){
                static::where('id', '!=', $bankAccount->id)
                    ->where('bankable_type', $bankAccount->bankable_type)
                    ->where('bankable_id', $bankAccount->bankable_id)
                    ->update(['is_primary' => false]);
            }
        });

        static::deleting(function ($bankAccount) {
            $references = $bankAccount->hasReferences();

            if ($references) {
                $message = "Cannot delete. Has related records in:\n";
                foreach ($references as $table => $count) {
                    $message .= "- $table: $count\n";
                }

                Notification::make()
                    ->title(__('Delete Failed'))
                    ->danger()
                    ->body(__("Cannot delete the bank account. Please resolve related records first."))
                    ->send();

                return false;
            }

            return true;
        });
    }

    public function hasReferences(): bool|array
    {
        $references = [];

        $tables = config('bankaccount.tables');

        foreach ($tables as $table) {
            $count = DB::table($table)
                ->where('bank_account_id', $this->id)
                ->count();

            if ($count > 0) {
                $references[$table] = $count;
            }
        }

        return empty($references) ? false : $references;
    }
}
