<?php

namespace Modules\BankAccount\Services;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\Repositories\BankAccountRepository;


class BankAccountService extends ServiceAbstract
{

    public function __construct(BankAccountRepository $repository)
    {
        parent::__construct($repository);
    }

    public function create(array $data){

        $bankAccount = $this->repository->create($data);
        $nonActiveBankAccountsForAccount = $this->repository->getFirstBy(['bankable_type' => Account::class, 'bankable_id' => $bankAccount->bankable_id, 'is_primary' => true]);
        if(!$nonActiveBankAccountsForAccount) {
            $this->repository->update($bankAccount, ['is_primary' => true]);
        }
        return $bankAccount;
    }

    public function delete($id)
    {
        try {
            // First try to get the bank account using findOrFail
            $bankAccount = $this->repository->findOrFail($id);

            // Check if bank account is in use
            $isInUse = DB::table('lease_members')
                ->where('bank_account_id', $bankAccount->id)
                ->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('leases')
                        ->whereRaw('leases.id = lease_members.lease_id')
                        ->whereNull('leases.deleted_at');
                })
                ->exists();

            if ($isInUse) {
                throw new \Exception('This bank account is currently in use by an active lease and cannot be deleted.');
            }

            // Use the repository delete method
            $this->repository->delete($bankAccount);
            if ($bankAccount->is_primary) { //check on primary account
                $nonActiveBankAccountsForAccount = $this->repository->getFirstBy(['bankable_type' => Account::class, 'bankable_id' => $bankAccount->bankable_id]);
                if($nonActiveBankAccountsForAccount) {
                    $this->repository->update($nonActiveBankAccountsForAccount, ['is_primary' => true]);
                }
            }

        } catch (ModelNotFoundException $e) {
            throw new ModelNotFoundException('Bank account not found.');
        }
    }


}
