<?php

namespace Modules\BankAccount\Forms;

use Filament\Forms\Components\Card;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

class BankAccountQuickForm
{

    public static function make(): array
    {
        return [
            Card::make()
                ->schema([
                    TextInput::make('bank_name')
                        ->label(__('Bank Name'))
                        ->placeholder(__('Enter bank name'))
                        ->required()
                        ->maxLength(255),
                    TextInput::make('branch_name')
                        ->label(__('Branch Name'))
                        ->placeholder(__('Enter branch name'))
                        ->required()
                        ->maxLength(255),
                    TextInput::make('account_name')
                        ->label(__('Account Name'))
                        ->placeholder(__('Enter account name'))
                        ->required()
                        ->maxLength(255),
                    TextInput::make('account_number')
                        ->label(__('Account Number'))
                        ->placeholder(__('Enter account number'))
                        ->numeric()
                        ->required()
                        ->maxLength(255),
                    TextInput::make('swift_code')
                        ->label(__('SWIFT Code'))
                        ->placeholder(__('Enter SWIFT code'))
                        ->required()
                        ->maxLength(255),
                    TextInput::make('iban')
                        ->label(__('IBAN'))
                        ->placeholder(__('Enter IBAN'))
                        ->required()
                        ->maxLength(255),
                    Toggle::make('is_primary')
                        ->fixIndistinctState()
                        ->label(__('Set as Primary Account')),
                    Hidden::make('is_active')
                        ->label(__('Active'))
                        ->default(true),
                ])
                ->columns(2)
        ];
    }
}
