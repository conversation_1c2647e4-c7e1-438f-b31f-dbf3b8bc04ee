<?php
namespace Modules\BankAccount\Traits;



use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\BankAccount\app\Models\BankAccount;

trait HasBankAccounts
{
    public function bankAccounts(): MorphMany
    {
        return $this->morphMany(BankAccount::class, 'bankable');
    }

    public function primaryBankAccount()
    {
        return $this->bankAccounts()->where('is_primary', true)->first();
    }
}
