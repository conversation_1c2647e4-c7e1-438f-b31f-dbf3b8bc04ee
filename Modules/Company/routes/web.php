<?php

use Illuminate\Support\Facades\Route;
use Modules\Company\app\Http\Controllers\CompanyController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::resource('company', CompanyController::class)->names('company');
});

Route::post('/switch-company', [CompanyController::class, 'switchCompany'])->name('switch-company')->middleware('auth');
