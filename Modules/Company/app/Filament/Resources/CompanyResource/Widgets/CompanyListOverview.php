<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Widgets;

use Modules\Company\app\Models\Company;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class CompanyListOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        $companies = Company::select('id', 'created_at', 'updated_at')->get();
        
        $companyChart = $this->calculateDailyStats($companies, 'created_at');
        $companyCount = $companies->count();
        
        $this->cards[] = Stat::make(__('Total Companies'), $companyCount)
            ->color('primary')
            ->chart(array_reverse($companyChart));
            
        return $this->cards;
    }
}