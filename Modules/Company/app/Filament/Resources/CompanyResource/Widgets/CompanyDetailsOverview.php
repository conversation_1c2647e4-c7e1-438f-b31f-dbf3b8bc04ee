<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Widgets;

use Modules\Lease\app\Models\Lease;
use Modules\Invoice\app\Models\Invoice;
use Modules\Property\app\Models\Property;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class CompanyDetailsOverview extends BaseWidget
{
    public $record;
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        // Get properties for this company
        $properties = Property::where('company_id', $this->record->id)
            ->select('id', 'created_at', 'updated_at')
            ->get();
        
        $propertyChart = $this->calculateDailyStats($properties, 'created_at');
        $propertyCount = $properties->count();
        $this->cards[] = Stat::make(__('Total Properties'), $propertyCount)
            ->color('primary')
            ->chart(array_reverse($propertyChart));

        // Get leases for this company
        $leases = Lease::where('company_id', $this->record->id)
            ->select('id', 'created_at', 'updated_at')
            ->get();
        
        $leaseChart = $this->calculateDailyStats($leases, 'created_at');
        $leaseCount = $leases->count();
        $this->cards[] = Stat::make(__('Total Leases'), $leaseCount)
            ->color('primary')
            ->chart(array_reverse($leaseChart));

        // Get invoices for this company
        $invoices = Invoice::where('company_id', $this->record->id)
            ->select('id', 'created_at', 'updated_at')
            ->get();
        
        $invoiceChart = $this->calculateDailyStats($invoices, 'created_at');
        $invoiceCount = $invoices->count();
        $this->cards[] = Stat::make(__('Total Invoices'), $invoiceCount)
            ->color('primary')
            ->chart(array_reverse($invoiceChart));

        return $this->cards;
    }
}