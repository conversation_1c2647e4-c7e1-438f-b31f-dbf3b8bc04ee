<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Pages;

use Modules\Company\app\Filament\Resources\CompanyResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components\Section;
use Filament\Forms;
use Modules\Company\app\Filament\Resources\CompanyResource\Widgets\CompanyDetailsOverview;

class ViewCompany extends ViewRecord
{
    protected static string $resource = CompanyResource::class;

    public function getTitle(): string
    {
        return __('View Company');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $company = $this->record;
        $user = $company->user;
        $userProfile = $user?->userProfile;

        // Add the id_type to the data so it's accessible in the form
        return array_merge($data, [
            'id_type' => $userProfile?->id_type ?? 'national_id', // Add this line
            'user' => [
                'name' => $user?->name,
                'email' => $user?->email,
                'lang' => $user?->lang,
                'theme' => $user?->theme,
                'theme_color' => $user?->theme_color,
                'profile_photo_path' => $user?->profile_photo_path,
                'active' => $user?->active,
                'email_verified' => $user?->email_verified,
                'userProfile' => [
                    'first_name' => $userProfile?->first_name,
                    'second_name' => $userProfile?->second_name,
                    'third_name' => $userProfile?->third_name,
                    'last_name' => $userProfile?->last_name,
                    'phone_number' => $userProfile?->phone_number,
                    'avatar_url' => $userProfile?->avatar_url,
                    'national_id' => $userProfile?->national_id,
                    'id_type' => $userProfile?->id_type, // Add this line
                    'country_of_issue' => $userProfile?->country_of_issue,
                    'birth_date' => $userProfile?->birth_date,
                    'bank_account' => $userProfile?->bank_account,
                ],
            ],
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\EditAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            CompanyDetailsOverview::class,
        ];
    }

    public function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Section::make(__('Company Information'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('phone')
                            ->label(__('Phone'))
                            ->disabled(),
                        Forms\Components\TextInput::make('email')
                            ->label(__('Email'))
                            ->disabled(),
                        Forms\Components\Textarea::make('address')
                            ->label(__('Address'))
                            ->disabled(),
                        Forms\Components\FileUpload::make('comp_unified_file')
                            ->label(__('Company Unified File'))
                            ->disabled()
                            ->directory('company-files'),
                        Forms\Components\FileUpload::make('logo')
                            ->label(__('Logo'))
                            ->disabled()
                            ->directory('company-logos')
                            ->image(),
                        Forms\Components\TextInput::make('comp_unified_number')
                            ->label(__('Company Unified Number'))
                            ->disabled(),
                        Forms\Components\TextInput::make('comp_cr_number')
                            ->label(__('Company CR Number'))
                            ->disabled(),
                        Forms\Components\TextInput::make('website_url')
                            ->label(__('Website link'))
                            ->disabled(),

                    ])->columns(2),

                Section::make(__('User Information'))
                    ->schema([
                        Forms\Components\TextInput::make('user.name')
                            ->label(__('Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('user.email')
                            ->label(__('Email'))
                            ->disabled(),
                    ])->columns(2),

                Section::make(__('User Profile Information'))
                    ->schema([
                        Forms\Components\TextInput::make('user.userProfile.first_name')
                            ->label(__('First Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('user.userProfile.second_name')
                            ->label(__('Second Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('user.userProfile.third_name')
                            ->label(__('Third Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('user.userProfile.last_name')
                            ->label(__('Last Name'))
                            ->disabled(),
                        Forms\Components\TextInput::make('user.userProfile.phone_number')
                            ->label(__('Phone Number'))
                            ->disabled(),
                        Forms\Components\FileUpload::make('user.userProfile.avatar_url')
                            ->label(__('Avatar link'))
                            ->disabled()
                            ->directory('avatars')
                            ->image(),

                        \Filament\Forms\Components\TextInput::make('user.userProfile.national_id')
                            ->label(function (Forms\Get $get): string {
                                $idType = $get('user.userProfile.id_type');
                                return match ($idType) {
                                    'national_id' => __('National ID'),
                                    'residency_permit' => __('Residency Permit Number'),
                                    'passport' => __('Passport Number'),
                                    'gcc_id' => __('GCC ID Number'),
                                    'other' => __('ID Number'),
                                    default => __('ID Number'),
                                };
                            })
                            ->disabled(),
                        \Filament\Forms\Components\TextInput::make('user.userProfile.country_of_issue')
                            ->label(__('Country Of Issue'))
                            ->disabled()
                            ->visible(function (Forms\Get $get): bool {
                                $idType = $get('user.userProfile.id_type');
                                return $idType !== 'national_id';
                            }),
                        Forms\Components\DatePicker::make('user.userProfile.birth_date')
                            ->label(__('Birth Date'))
                            ->disabled(),
                    ])->columns(2),
            ]);
    }
}
