<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Pages;

use App\Forms\Components\NationalIdSearchSection;
use Closure;
use Filament\Forms\Components\TextInput;
use Illuminate\Validation\Rule;
use Modules\Company\app\Filament\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Illuminate\Support\Facades\Hash;
use Modules\Company\app\Models\UserProfile;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use App\Shared\Helpers\FormComponents;
use Modules\Company\app\Models\Company;

class EditCompany extends EditRecord
{
    protected static string $resource = CompanyResource::class;

    public function getTitle(): string
    {
        return __('Edit Company');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $company = $this->record;
        $user = $company->user;
        $userProfile = $user?->userProfile;

        return array_merge($data, [
            'user' => [
                'name' => $user?->name,
                'email' => $user?->email,
                'lang' => $user?->lang,
                'theme' => $user?->theme,
                'theme_color' => $user?->theme_color,
                'profile_photo_path' => $user?->profile_photo_path,
                'active' => $user?->active,
                'email_verified' => $user?->email_verified,
                'userProfile' => [
                    'first_name' => $userProfile?->first_name,
                    'second_name' => $userProfile?->second_name,
                    'third_name' => $userProfile?->third_name,
                    'last_name' => $userProfile?->last_name,
                    'phone_number' => $userProfile?->phone_number,
                    'avatar_url' => $userProfile?->avatar_url,
                    'national_id' => $userProfile?->national_id,
                    'id_type' => $userProfile?->id_type ?? 'national_id',
                    'country_of_issue' => $userProfile?->country_of_issue,
                    'birth_date' => $userProfile?->birth_date,
                    'bank_account' => $userProfile?->bank_account,
                ],
            ],
        ]);

    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
        ];
    }

    public function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Company Information'))
                    ->description(__('Enter the company details'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('Company Name'))
                            ->placeholder(__('Enter company name'))
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),

                        TextInput::make('phone')
                            ->label(__('Phone Number'))
                            ->tel()
                            ->required()
                            ->prefix('+966')
                            ->placeholder('5xxxxxxxx')
                            ->rules([
                                'required',
                                'string',
                                'regex:/^5[0-9]{8}$/',
                                'size:9'
                            ])
                            ->extraInputAttributes([
                                'maxlength' => '9',
                                'pattern' => '5[0-9]*',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                            ])
                            ->unique(ignoreRecord: true)
                            ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                            ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                            ->validationMessages([
                                'required' => __(' phone is required'),
                                'regex' => __('Phone number must start with 5 and be 9 digits'),
                                'size' => __('Phone number must be exactly 9 digits'),
                                'unique' => __('This phone number is already taken'),
                            ]),

                        Forms\Components\TextInput::make('email')
                            ->label(__('Company Email'))
                            ->placeholder(__('<EMAIL>'))
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->validationMessages([
                                'required' => __('Company email is required'),
                                'email' => __('Please enter a valid email address'),
                                'unique' => __('This email is already registered'),
                            ]),

                        Forms\Components\Textarea::make('address')
                            ->label(__('Company Address'))
                            ->placeholder(__('Enter complete company address'))
                            ->maxLength(65535)
                            ->rows(3)
                            ->validationMessages([
                                'max' => __('Address is too long'),
                            ]),

                        Forms\Components\TextInput::make('comp_unified_number')
                            ->label(__('Unified Number'))
                            ->placeholder(__('Enter company unified number'))
                            ->maxLength(255)
                            ->regex('/^[A-Z0-9-]+$/')
                            ->helperText(__('Enter the official company registration number'))
                            ->validationMessages([
                                'regex' => __('Unified number can only contain uppercase letters, numbers and hyphens'),
                            ]),
                        Forms\Components\TextInput::make('comp_cr_number')
                            ->label(__('CR Number'))
                            ->required()
                            ->placeholder(__('Enter company CR Number'))
                            ->live()
                            ->rules([
                                'required',
                            ])
                            ->unique(table: 'companies',column: 'comp_cr_number', ignoreRecord: true)

                            ->validationMessages([
                                'required' => __('CR Number is required'),
                                'unique' => __('This CR Number is already taken'),
                            ]),

                        Forms\Components\TextInput::make('website_url')
                            ->label(__('Website URL'))
                            ->placeholder(__('https://www.example.com'))
                            ->url()
                            ->maxLength(255)
                            ->validationMessages([
                                'url' => __('Please enter a valid URL'),
                            ]),

                        Forms\Components\FileUpload::make('comp_unified_file')
                            ->label(__('Company Registration Document'))
                            ->directory('company-files')
                            ->acceptedFileTypes(['application/pdf'])
                            ->maxSize(5120)
                            ->helperText(__('Upload PDF file (max 5MB)'))
                            ->validationMessages([
                                'max' => __('File size must not exceed 5MB'),
                                'mimes' => __('File must be a PDF'),
                            ]),

                        Forms\Components\FileUpload::make('logo')
                            ->label(__('Company Logo'))
                            ->directory('company-logos')
                            ->image()
                            ->maxSize(2048)
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('1920')
                            ->imageResizeTargetHeight('1080')
                            ->helperText(__('Upload company logo (max 2MB, 16:9 ratio recommended)'))
                            ->validationMessages([
                                'image' => __('File must be an image'),
                                'max' => __('Image size must not exceed 2MB'),
                            ]),
                        Repeater::make('vals')
                            ->label(__('val'))
                            ->collapsed(false)
                            ->collapsible()
                            ->relationship('vals')
                            ->schema([
                                TextInput::make('value')
                                    ->label(__('License number'))
                                    ->placeholder(__('License number'))
                                    ->required(),
                                Hidden::make('morphable_type')
                                    ->default(Company::class),
                                Hidden::make('morphable_id')
                                    ->default(function (Get $get) {
                                        return $get('../../id');
                                    }),
                                DatePicker::make('start_date')
                                    ->label(__('Start Date'))
                                    ->required(),
                                DatePicker::make('end_date')
                                    ->label(__('End Date'))
                                    ->after('start_date')
                                    ->afterOrEqual(today()),
                                Toggle::make('active')
                                    ->label(__('Active'))
                                    ->default(true)
                            ])
                            ->defaultItems(0)
                            ->maxItems(1)
                            ->columns(2)
                            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                return array_merge($data, [
                                    'company_id' => auth()->user()->company_id,
                                ]);
                            }),
                    ])->columns(2),

                Forms\Components\Section::make(__('User Profile Information'))
                    ->description(__('Enter user details'))
                    ->schema([
                        Forms\Components\TextInput::make('user.email')
                            ->label(__('User Email'))
                            ->placeholder(__('<EMAIL>'))
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->rules([
                                Rule::unique('users', 'email')->ignore($this->record?->user?->id)
                            ])
                            ->validationMessages([
                                'required' => __('Email is required'),
                                'email' => __('Please enter a valid email address'),
                                'unique' => __('This email is already registered'),
                            ]),

                        Forms\Components\Hidden::make('user.name'),

                        Forms\Components\TextInput::make('user.userProfile.first_name')
                            ->label(__('First Name'))
                            ->placeholder(__('Enter first name'))
                            ->maxLength(255)
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $this->updateFullName($set, $get);
                            }),

                        Forms\Components\TextInput::make('user.userProfile.second_name')
                            ->label(__('Second Name'))
                            ->placeholder(__('Enter second name'))
                            ->maxLength(255)
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $this->updateFullName($set, $get);
                            }),

                        Forms\Components\TextInput::make('user.userProfile.third_name')
                            ->label(__('Third Name'))
                            ->placeholder(__('Enter third name'))
                            ->maxLength(255)
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $this->updateFullName($set, $get);
                            }),

                        Forms\Components\TextInput::make('user.userProfile.last_name')
                            ->label(__('Last Name'))
                            ->placeholder(__('Enter last name'))
                            ->required()
                            ->maxLength(255)
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $this->updateFullName($set, $get);
                            }),
                        Forms\Components\TextInput::make('user.userProfile.phone_number')
                            ->label(__('Phone Number'))
                            ->tel()
                            ->required()
                            ->prefix('+966')
                            ->placeholder('5xxxxxxxx')
                            ->rules([
                                'required',
                                'string',
                                'regex:/^5[0-9]{8}$/',
                                'size:9'
                            ])
//                            ->unique(ignoreRecord: true)
                            ->extraInputAttributes([
                                'maxlength' => '9',
                                'pattern' => '5[0-9]*',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                            ])
                            ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                            ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                            ->validationMessages([
                                'required' => __(' phone is required'),
                                'regex' => __('Phone number must start with 5 and be 9 digits'),
                                'size' => __('Phone number must be exactly 9 digits'),
                                'unique' => __('This phone number is already taken'),
                            ]),

                        FormComponents::idFieldSet(
                            fieldPrefix: 'user.userProfile.',
                            table: 'user_profiles',
                            ignoreRecord: true,
                            readonlyCondition: fn(): bool => NationalIdSearchSection::isReadOnly()
                        ),

                        Forms\Components\FileUpload::make('user.userProfile.avatar_url')
                            ->label(__('Profile Picture'))
                            ->directory('avatars')
                            ->image()
                            ->maxSize(1024)
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('300')
                            ->imageResizeTargetHeight('300')
                            ->helperText(__('Upload profile picture (max 1MB, square ratio)'))
                            ->validationMessages([
                                'image' => __('File must be an image'),
                                'max' => __('Image size must not exceed 1MB'),
                            ]),

                        Forms\Components\DatePicker::make('user.userProfile.birth_date')
                            ->label(__('Date of Birth'))
                            ->required()
                            ->before('today')
                            ->after('1900-01-01')
                            ->validationMessages([
                                'required' => __('Date of birth is required'),
                                'before' => __('Date of birth must be in the past'),
                                'after' => __('Date of birth must be after 1900'),
                            ]),

                        Forms\Components\Textarea::make('user.userProfile.bank_account')
                            ->label(__('Bank Account Details'))
                            ->placeholder(__('Enter bank account information'))
                            ->maxLength(65535)
                            ->rows(3)
                            ->validationMessages([
                                'max' => __('Bank account details are too long'),
                            ]),
                    ])->columns(2),
            ]);
    }

    protected function updateFullName($set, $get): void
    {
        $firstName = $get('user.userProfile.first_name') ?? '';
        $secondName = $get('user.userProfile.second_name') ?? '';
        $thirdName = $get('user.userProfile.third_name') ?? '';
        $lastName = $get('user.userProfile.last_name') ?? '';

        $fullName = trim(implode(' ', array_filter([
            $firstName,
            $secondName,
            $thirdName,
            $lastName
        ])));

        $set('user.name', $fullName);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (isset($data['user'])) {
            $userData = $data['user'];
            $userProfileData = $userData['userProfile'] ?? [];
            unset($userData['userProfile']);

            // Determine which country field to use based on ID type
            if (isset($data['standard_country_of_issue']) && in_array($userProfileData['id_type'] ?? null, ['residency_permit', 'passport', 'other'])) {
                $userProfileData['country_of_issue'] = $data['standard_country_of_issue'];
            } elseif (isset($data['gcc_country_of_issue']) && ($userProfileData['id_type'] ?? null) === 'gcc_id') {
                $userProfileData['country_of_issue'] = $data['gcc_country_of_issue'];
            }

            // Update user
            $this->record->user->update($userData);

            // Update user profile
            if ($userProfileData) {
                $this->record->user->userProfile->update($userProfileData);
            }

            unset($data['user']);
            unset($data['standard_country_of_issue']);
            unset($data['gcc_country_of_issue']);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
