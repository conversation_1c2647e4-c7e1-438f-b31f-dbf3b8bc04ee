<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Modules\Property\Enums\PropertyStatus;
use phpDocumentor\Reflection\Types\String_;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\HtmlString;

class PropertiesRelationManager extends RelationManager
{
    protected static string $relationship = 'properties';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('properties');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('properties');
    }
    public static function getModelLabel(): string
    {
        return __('properties');
    }
    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('Name')),
                TextColumn::make('address')
                    ->label(__('Address')),
                TextColumn::make('price')
                    ->label(__('Price'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                        );
                    }), 
                TextColumn::make('address')
                    ->label(__('Address')),
                TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->formatStateUsing(fn (PropertyStatus $state): string => __($state->label()))
                    ->color(fn (PropertyStatus $state): string => $state->getColor()),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
            ])
            ;
    }
}
