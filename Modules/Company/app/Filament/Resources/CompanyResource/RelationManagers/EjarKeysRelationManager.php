<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\EjarIntegration\app\Models\EjarCompanyKey;

class EjarKeysRelationManager extends RelationManager
{
    protected static string $relationship = 'ejarKeys';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('client_id')
                    ->label(__('Client ID'))
                    ->required()
                    ->placeholder(__('Enter Client ID'))
                    ->reactive(),

                TextInput::make('client_secret')
                    ->label(__('Client Secret'))
                    ->required()
                    ->placeholder(__('Enter Client Secret'))
                    ->reactive(),

                DateTimePicker::make('response_received_at')
                    ->label(__('Response Received At'))
                    ->native(false)
                    ->formatStateUsing(fn ($record) => $record?->response_received_at ?? now())
                    ->default(fn ($record) => $record?->response_received_at ?? now())
                    ->seconds(true)
                    ->displayFormat('Y-m-d H:i:s')
                    ->timezone('UTC')
                    ->placeholder(__('Select Date and Time'))
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('requestSender.name')
                    ->label(__('Request Sent By')),

                Tables\Columns\BooleanColumn::make('request_sent')
                    ->label(__('Request Sent'))
                    ->default(true),

                Tables\Columns\BooleanColumn::make('response_received')
                    ->state(function ($record): bool {
                        return !is_null($record->response_received_at);
                    })
                    ->label(__('Response Received')),

                Tables\Columns\TextColumn::make('client_id')
                    ->label(__('Client ID'))
                    ->badge(),

                Tables\Columns\TextColumn::make('client_secret')
                    ->label(__('Client Secret'))
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Request Sent at'))
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('response_received_at')
                    ->label(__('Response Received at'))
                    ->dateTime()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return auth()->user()->can('viewAny', EjarCompanyKey::class);
    }
}
