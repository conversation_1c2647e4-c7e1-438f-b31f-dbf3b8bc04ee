<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\RelationManagers\RelationManager;

class UsersRelationManager extends RelationManager
{
    protected static string $relationship = 'users';

//    protected static ?string $recordTitleAttribute = 'name';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('users');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('users');
    }

    public static function getModelLabel(): string
    {
        return __('users');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('userProfile.avatar_url')
                    ->label(__('Avatar'))
                    ->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('userProfile.phone_number')
                    ->label(__('Phone'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('userProfile.national_id')
                    ->label(__('National ID'))
                    ->badge()
                    ->searchable(),


                Tables\Columns\TextColumn::make('userProfile.birth_date')
                    ->label(__('Birth Date'))
                    ->date()
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
                            ->orderBy('user_profiles.birth_date', $direction);
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
            ])
            ->headerActions([
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

}
