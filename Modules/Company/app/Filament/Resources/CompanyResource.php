<?php

namespace Modules\Company\app\Filament\Resources;

use App\Enums\RoleEnum;
use App\Enums\SyncUserAccountSourceEnum;
use App\Forms\ChangePasswordForm;
use App\Forms\Components\NationalIdSearchSection;
use App\Forms\Components\UserCompanyInfoSection;
use App\Shared\Helpers\FormComponents;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\BankAccount\Forms\BankAccountForm;
use Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers\CompaniesRelationManager;
use Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers\PropertiesRelationManager;
use Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers\UsersRelationManager;
use Modules\Company\app\Models\Company;
use Modules\Company\app\Filament\Resources\CompanyResource\Pages;
use Illuminate\Database\Eloquent\Builder;
use Modules\Company\app\Filament\Resources\CompanyResource\RelationManagers\EjarKeysRelationManager;
use Modules\EjarIntegration\app\Models\EjarCompanyKey;
use Filament\Forms\Components\Select;
use App\Models\User;
use Modules\Company\app\Filament\Resources\CompanyResource\Widgets\CompanyListOverview;
use Modules\Company\app\Filament\Resources\CompanyResource\Widgets\CompanyDetailsOverview;

class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

//    protected static ?string $navigationIcon = 'heroicon-o-office-building';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        if(!auth()->user()->hasRole(RoleEnum::ADMIN)){
            $company_id = auth()->user()->company ? auth()->user()->company->id : auth()->user()->company_id;
            $query->where('id',$company_id);
        }
        return $query;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Company Information'))
                    ->schema([
                        Forms\Components\Hidden::make('user_id')
                            ->label(__('User'))
                            ->default(auth()->id()),
                        Forms\Components\TextInput::make('name')
                            ->label(__('Company Name'))
                            ->required()
                            ->maxLength(75)
                            ->minLength(1)
                            ->unique(ignoreRecord: true)
                            ->placeholder(__('Enter company name'))
                            ->live()
                            ->extraInputAttributes([
                                'maxlength' => '75',
                                'oninput' => "
                                    this.value = this.value.substring(0, 75);
                                    this.dispatchEvent(new Event('input'));
                                "
                            ])
                            ->validationMessages([
                                'required' => __('Company name is required'),
                                'max' => __('Company name must not exceed 75 characters'),
                                'min' => __('Company name is required'),
                            ]),

                        TextInput::make('phone')
                            ->label(__('Phone Number'))
                            ->tel()
                            ->required()
                            ->prefix('+966')
                            ->placeholder('5xxxxxxxx')
                            ->rules([
                                'required',
                                'string',
                                'regex:/^5[0-9]{8}$/',
                                'size:9'
                            ])
                            ->extraInputAttributes([
                                'maxlength' => '9',
                                'pattern' => '5[0-9]*',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                            ])
                            ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                            ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                            ->validationMessages([
                                'required' => __(' phone is required'),
                                'regex' => __('Phone number must start with 5 and be 9 digits'),
                                'size' => __('Phone number must be exactly 9 digits'),
                                'unique' => __('This phone number is already taken'),
                            ]),


                        Forms\Components\TextInput::make('email')
                            ->label(__('Company Email'))
                            ->email()
                            ->required()
                            ->placeholder(__('Company email address'))
                            ->validationMessages([
                                'required' => __('Company email is required'),
                                'email' => __('Please enter a valid email address'),
                            ]),
                        Forms\Components\Textarea::make('address')
                            ->label(__('Company Address'))
                            ->required()
                            ->maxLength(150)
                            ->minLength(1)
                            ->placeholder(__('Enter company address'))
                            ->rows(1)
                            ->live()
                            ->extraInputAttributes([
                                'maxlength' => '150',
                                'oninput' => "
            this.value = this.value.substring(0, 150);
            this.dispatchEvent(new Event('input'));
        "
                            ])
                            ->validationMessages([
                                'required' => __('Company address is required'),
                                'max' => __('Company address must not exceed 150 characters'),
                                'min' => __('Company address is required'),
                            ]),
                        Forms\Components\FileUpload::make('comp_unified_file')
                            ->label(__('Unified File'))
                            ->directory('company-files')
                            ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                            ->validationMessages([
                                'mimes' => __('File must be PDF, DOC or DOCX format'),
                            ]),
                        Forms\Components\TextInput::make('comp_unified_number')
                            ->label(__('Unified Number'))
                            ->required()
                            ->numeric()
                            ->placeholder(__('Just Start with 70xxxxxxxx'))
                            ->live()
                            ->extraInputAttributes([
                                'maxlength' => '10',
                                'minlength' => '10',
                                'pattern' => '70[0-9]{8}',
                                'oninput' => "
                                                this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                                if (this.value.length > 1 && !this.value.startsWith('70')) {
                                                    this.value = '70';
                                                }
                                                this.dispatchEvent(new Event('input'));
                                            "
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'digits:10',
                                'regex:/^70\d{8}$/',
                            ])
                            ->validationMessages([
                                'required' => __('Unified Number is required'),
                                'digits' => __('Unified Number must be exactly 10 digits'),
                                'numeric' => __('Unified Number must contain only numbers'),
                                'regex' => __('Unified Number must start with 70'),
                            ]),

                        Forms\Components\TextInput::make('comp_cr_number')
                            ->label(__('CR Number'))
                            ->required()
                            ->unique(table: 'companies',column: 'comp_cr_number', ignoreRecord: true)
                            ->placeholder(__('Enter company CR Number'))
                            ->live()
                            ->rules([
                                'required',
                            ])
                            ->validationMessages([
                                'required' => __('CR Number is required'),
                                'unique' => __('This CR Number is already taken'),
                            ]),

                        Forms\Components\TextInput::make('website_url')
                            ->label(__('Website URL'))
                            ->url()
                            ->placeholder(__('Company website URL'))
                            ->validationMessages([
                                'url' => __('Please enter a valid URL'),
                            ]),
                        Forms\Components\FileUpload::make('logo')
                            ->label(__('Company Logo'))
                            ->directory('company-logos')
                            ->image()
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'])
                            ->validationMessages([
                                'mimes' => __('Logo must be JPG, PNG, JPEG, WEBP or SVG format'),
                            ]),
                            Repeater::make('vals')
                            ->label(__('val'))
                            ->collapsed(false)
                            ->collapsible()
                            ->relationship('vals')
                            ->schema([
                                TextInput::make('value')
                                    ->label(__('License number'))
                                    ->placeholder(__('License number'))
                                    ->required(),
                                Hidden::make('morphable_type')
                                    ->default(Company::class),
                                Hidden::make('morphable_id')
                                    ->default(function (Get $get) {
                                        return $get('../../id');
                                    }),
                                DatePicker::make('start_date')
                                    ->label(__('Start Date'))
                                    ->required(),
                                DatePicker::make('end_date')
                                    ->label(__('End Date'))
                                    ->required()
                                    ->after('start_date')
                                    ->afterOrEqual(today()),
                                Toggle::make('active')
                                    ->label(__('Active'))
                                    ->default(true)
                            ])
                            ->defaultItems(0)
                            ->maxItems(1)
                            ->columns(2)
                            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                return array_merge($data, [
                                    'company_id' => auth()->user()->company_id,
                                ]);
                            }),
                    ])->columns(2),
                Toggle::make('is_existing_user')
                    ->label(__('Existing Owner'))
                    ->onIcon('heroicon-m-bolt')
                    ->offIcon('heroicon-m-user')
                    ->live()
                    ->hidden(fn() => auth()->user()->hasRole(RoleEnum::OWNER)),
                Hidden::make('owner_id')
                    ->default(function () {
                        if (auth()->user()->hasRole(RoleEnum::OWNER)) {
                            return auth()->user()->id;
                        }
                    })
                    ->dehydrated(fn() => auth()->user()->hasRole(RoleEnum::OWNER)),

                Forms\Components\Section::make(__('Owner Information'))
                    ->schema([
                        Select::make('owner_id')
                            ->label(__('Owner'))
                            ->placeholder(__('Select Owner'))
                            ->searchable(['national_id', 'name'])
                            ->native(false)
                            ->required()
                            ->getSearchResultsUsing(function (string $search,$get,$livewire) {
                                return User::with('userProfile')
                                ->role(RoleEnum::OWNER)
                                ->whereHas('userProfile', function ($query) use ($search) {
                                    $query->where('national_id', '=', "{$search}");
                                })
                                ->orWhere('name', '=', "{$search}")
                                ->where('users.active', true)
                                ->get()
                                ->mapWithKeys(function ($user) {
                                    return [$user->id => "{$user->name} - {$user->userProfile->national_id}"];
                                });
                            })
                            ->getOptionLabelUsing(function ($value) {
                                // Only query the database for the selected value
                                $user = User::with('userProfile')
                                ->role(RoleEnum::OWNER)
                                ->find($value);
                                if ($user) {
                                    return "{$user->name} - {$user->userProfile->national_id}";
                                }
                                return null;
                            })
                            ->debounce(500),
                    ])
                    ->visible(fn ($livewire) => $livewire->data['is_existing_user']),
                NationalIdSearchSection::make(__('Search User'), SyncUserAccountSourceEnum::ADD_USER_COMPANY->value , true)->icon('heroicon-o-identification')
                ->visible(fn ($livewire) => !$livewire->data['is_existing_user'] && !auth()->user()->hasRole(RoleEnum::OWNER)), //search user national id component
                Forms\Components\Section::make(__('User Profile Information'))
                    ->schema([
                        Forms\Components\Hidden::make('user.name'),
                        Forms\Components\TextInput::make('user.userProfile.first_name')
                            ->label(__('First Name'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->required()
                            ->maxLength(25)
                            ->placeholder(__('First Name'))
                            ->validationMessages([
                                'required' => __('First name is required'),
                                'max' => __('First name cannot exceed 25 characters'),
                            ])

                           ,
                        Forms\Components\TextInput::make('user.userProfile.second_name')
                            ->label(__('Second Name'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->maxLength(25)
                            ->placeholder(__('Second Name'))
                            ->validationMessages([
                                'max' => __('Second name cannot exceed 25 characters'),
                            ])
                           ,
                        Forms\Components\TextInput::make('user.userProfile.third_name')
                            ->label(__('Third Name'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->maxLength(25)
                            ->placeholder(__('Third Name'))
                            ->validationMessages([
                                'max' => __('Third name cannot exceed 25 characters'),
                            ])
                            ,
                        Forms\Components\TextInput::make('user.userProfile.last_name')
                            ->label(__('Last Name'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->required()
                            ->maxLength(25)
                            ->placeholder(__('Last Name'))
                            ->validationMessages([
                                'required' => __('Last name is required'),
                                'max' => __('Last name cannot exceed 25 characters'),
                            ]),
                        Forms\Components\TextInput::make('user.email')
                            ->label(__('Email Address'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->email()
                            ->required()
                            ->placeholder(__('Email Address'))
                            ->validationMessages([
                                'required' => __('Email is required'),
                                'email' => __('Please enter a valid email address'),
                                'unique' => __('This email is already registered'),
                            ])
                            ->unique('users', 'email'),

                        TextInput::make('user.userProfile.phone_number')
                            ->label(__('Phone Number'))
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->tel()
                            ->required()
                            ->prefix('+966')
                            ->placeholder('5xxxxxxxx')
                            ->rules([
                                'required',
                                'string',
                                'regex:/^5[0-9]{8}$/',
                                'size:9'
                            ])
                            ->extraInputAttributes([
                                'maxlength' => '9',
                                'pattern' => '5[0-9]*',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                            ])
                            ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                            ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                            ->validationMessages([
                                'required' => __(' phone is required'),
                                'regex' => __('Phone number must start with 5 and be 9 digits'),
                                'size' => __('Phone number must be exactly 9 digits'),
                                'unique' => __('This phone number is already taken'),
                            ]),


                        FormComponents::idFieldSet(
                            fieldPrefix: 'user.userProfile.',
                            table: 'user_profiles',
                            ignoreRecord: true,
                            readonlyCondition: fn(): bool => NationalIdSearchSection::isReadOnly()
                        ),


                        FormComponents::birthDateInput(__('Birth date'),'user.userProfile.birth_date')
                            ->readonly(fn(): bool => NationalIdSearchSection::isReadOnly())
                            ->helperText(__('Must be at least 18 years old')),
                    ])
                        ->visible(fn ($livewire) => !$livewire->data['is_existing_user'] && !auth()->user()->hasRole(RoleEnum::OWNER))
                        ->columns(2),

                UserCompanyInfoSection::make(__("User's Company Information"))
                ->visible(fn ($livewire) => !$livewire->data['is_existing_user'] && !auth()->user()->hasRole(RoleEnum::OWNER))
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('Phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable(),
                Tables\Columns\ImageColumn::make('logo')
                    ->label(__('Logo'))
                    ->circular(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            UsersRelationManager::class,
            PropertiesRelationManager::class,
            EjarKeysRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
            'view' => Pages\ViewCompany::route('/{record}'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            CompanyListOverview::class,
            CompanyDetailsOverview::class
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('filament-shield::filament-shield.nav.group');
    }

    public static function getNavigationLabel(): string
    {
        return __("companies");
    }

    public static function getBreadcrumb() : string
    {
        return __('companies');
    }
    public static function getModelLabel(): string
    {
        return __('Company');
    }

    public static function getPluralModelLabel(): string
    {
        return __('companies');
    }

}
