<?php

namespace Modules\Company\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Company\app\Filament\Resources\CompanyResource;

class CompanyPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Company';
    }

    public function getId(): string
    {
        return 'company';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                CompanyResource::class,
            ]);
    }
}
