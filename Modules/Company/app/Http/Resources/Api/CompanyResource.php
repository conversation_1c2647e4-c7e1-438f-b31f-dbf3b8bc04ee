<?php

namespace Modules\Company\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Property\app\Resources\Api\BrokerResource;
use Modules\Property\app\Resources\Api\ValResource;


class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id,
            "name" => $this->name ?? null,
            "phone" => $this->phone ?? null,
            "email" => $this->email ?? null,
            "address" => $this->address ?? null,
            "logo" => $this->logo ? asset('storage/' . $this->logo) : null,
            "comp_unified_file" => $this->comp_unified_file ? asset('storage/' . $this->comp_unified_file) : null,
            "comp_unified_number" => $this->comp_unified_number ?? null,
            "comp_cr_number" => $this->comp_cr_number ?? null,
            "website_url" => $this->website_url ?? null,
            "val" => new ValResource($this->val),
        ];

        return $data;
    }
}
