<?php

namespace Modules\Company\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Enums\RoleEnum;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('company::index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('company::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('company::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('company::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        //
    }

    public function switchCompany(Request $request)
    {
        $request->validate([
            'company_id' => ['required', 'exists:companies,id']
        ]);

        $user = Auth::user();
        
        if ($user->companies->contains($request->company_id) && auth()->user()->hasRole(RoleEnum::OWNER)) {
            $user->update(['company_id' => $request->company_id]);
            session()->put('userTenancy', auth()->user());
        }
    }
}
