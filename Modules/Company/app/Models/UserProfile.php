<?php

namespace Modules\Company\app\Models;

use App\Models\User;
use App\Models\UserAccountCredential;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProfile extends Model
{
    protected $fillable = [
        'user_id',
        'first_name',
        'second_name',
        'third_name',
        'last_name',
        'phone_number',
        'avatar_url',
        'national_id',
        'birth_date',
        'bank_account',
        'terms_accepted',
        'id_type',
        'country_of_issue'
    ];

    protected $casts = [
        'birth_date' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function userCredential()
    {
        return $this->belongsTo(UserAccountCredential::class, 'user_id', 'user_id');
    }
}
