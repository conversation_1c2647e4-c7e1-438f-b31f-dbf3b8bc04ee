<?php

    namespace Modules\Company\app\Models;

    use App\Models\User;
use App\Models\Val;
use Illuminate\Database\Eloquent\Factories\HasFactory;
    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Database\Eloquent\Relations\HasMany;
    use Modules\EjarIntegration\app\Models\EjarCompanyKey;
    use Modules\Property\app\Models\Property;
    use Spatie\MediaLibrary\HasMedia;
    use Spatie\MediaLibrary\InteractsWithMedia;

    // use Modules\Tenancy\Database\Factories\CompanyFactory;

    class Company extends Model implements HasMedia
    {
        use HasFactory, InteractsWithMedia;

        /**
         * The attributes that are mass assignable.
         */
        protected $guarded = ['id'];

        // protected static function newFactory(): CompanyFactory
        // {
        //     // return CompanyFactory::new();
        // }
        /**
         * Get the user that owns the company.
         */
        public function ejarKeys()
        {
            return $this->hasOne(EjarCompanyKey::class);
        }
        public function user()
        {
            return $this->belongsTo(User::class);
        }
        public function users(): HasMany
        {
            return $this->hasMany(User::class);
        }

        public function usersWithPivot()
        {
            return $this->belongsToMany(User::class)->withTimestamps();
        }

        public function properties()
        {
            return $this->hasMany(Property::class, 'company_id');
        }

        public function vals()
        {
            return $this->morphMany(Val::class, 'morphable');
        }

        public function val()
        {
            return $this->morphOne(Val::class, 'morphable')->where('active', 1);
        }
    }
