<?php

namespace Modules\Company\Services;

use App\Enums\RoleEnum;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Modules\Company\app\Models\Company;
use Modules\Company\app\Models\UserProfile;

class CompanyRegistrationService
{
    public function register(array $data): array
    {
        // Create user
        $user = $this->createUser($data);

        // Create company
        $company = $this->createCompany($data, $user);

        // Update user with company information
        $this->updateUserCompany($user, $company);

        // Create user profile
        $this->createUserProfile($data, $user);

        // Assign role
        $user->assignRole(RoleEnum::OWNER);

        return [
            'user' => $user,
            'company' => $company
        ];
    }

    protected function createUser(array $data): User
    {
        return User::create([
            'name' => $data['name'] ?? $data['user']['name'] ?? $data['user_name'],
            'email' => $data['email'] ?? $data['user']['email'] ?? $data['user_email'],
            'password' => Hash::make($data['password'] ?? $data['user']['password']),
        ]);
    }

    protected function createCompany(array $data, User $user): Company
    {
        $companyData = $data['company'] ?? $data;

        return Company::create([
            'name' => $companyData['name'] ?? $companyData['company_name'],
            'user_id' => $user->id,
            'phone' => $companyData['phone'] ?? $companyData['company_phone'],
            'email' => $companyData['email'] ?? $companyData['company_email'],
            'address' => $companyData['address'] ?? $companyData['company_address'],
            'comp_unified_file' => $companyData['comp_unified_file'],
            'comp_unified_number' => $companyData['comp_unified_number'],
            'website_url' => $companyData['website_url'],
            'logo' => $companyData['logo'] ?? $companyData['company_logo'] ?? null,
        ]);
    }

    protected function updateUserCompany(User $user, Company $company): void
    {
        $user->update([
            'company_id' => $company->id,
            'current_company_id' => $company->id,
        ]);
    }

    protected function createUserProfile(array $data, User $user): UserProfile
    {
        $profileData = $data['user_profile'] ?? $data;

        return UserProfile::create([
            'user_id' => $user->id,
            'first_name' => $profileData['first_name'],
            'second_name' => $profileData['second_name'],
            'third_name' => $profileData['third_name'],
            'last_name' => $profileData['last_name'],
            'phone_number' => $profileData['phone_number'],
            'national_id' => $profileData['national_id'],
            'birth_date' => $profileData['birth_date'],
            'bank_account' => $profileData['bank_account'],
            'avatar_url' => $profileData['avatar_url'],
        ]);
    }

}
