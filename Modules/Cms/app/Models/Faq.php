<?php

namespace Modules\Cms\app\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;

/**
 * @property integer $id
 * @property mixed $question
 * @property mixed $answer
 * @property boolean $is_active
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 */
class Faq extends BaseModel
{
    use SoftDeletes;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';
    protected $translatable=['answer','question'];

    /**
     * @var array
     */
    protected $fillable = ['question', 'answer', 'is_active', 'source', 'deleted_at', 'created_at', 'updated_at'];
}
