<?php

namespace Modules\Cms\app\Filament\Resources\ContactUsResource\Components;

use Filament\Forms\Components\Card;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;

class FormComponent
{
    public static function getForm(): array
    {
        return [
            Group::make([
                Section::make(__('Contact Details'))
                    ->schema([
                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->required()
                                            ->maxLength(255),
                                    ]),
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->required()
                                            ->maxLength(255),
                            ]),
                        ]),
                        Textarea::make('message')
                            ->label(__('Message'))
                            ->required()
                            ->rows(4),
                        TextInput::make('subject')
                            ->label(__('Subject'))
                            ->required()
                            ->maxLength(255),
                        TextInput::make('phone')
                            ->label(__('Phone'))
                            ->tel()
                            ->required()
                            ->maxLength(20),
                        TextInput::make('email')
                            ->label(__('Email'))
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Toggle::make('is_read')
                            ->label(__('Read'))
                            ->default(false),
                    ])->columnSpan(2),
            ])->columnSpan(2),
        ];
    }
}
