<?php

namespace Modules\Cms\app\Filament\Resources;

use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Str;
use Modules\Cms\app\Filament\Resources\FaqResource\Components\FormComponent;
use Modules\Cms\app\Filament\Resources\FaqResource\Pages\CreateFaq;
use Modules\Cms\app\Filament\Resources\FaqResource\Pages\EditFaq;
use Modules\Cms\app\Filament\Resources\FaqResource\Pages\ListFaqs;
use Modules\Cms\app\Filament\Resources\FaqResource\Pages\ViewFaq;
use Modules\Cms\app\Models\Faq;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\ToggleColumn;
use Modules\Cms\app\Filament\Resources\FaqResource\Components\TableComponent;

class FaqResource extends Resource
{
    protected static ?string $model = Faq::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationLabel = 'FAQs';

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListFaqs::route('/'),
            'create' => CreateFaq::route('/create'),
            'view' => ViewFaq::route('/{record}'),
            'edit' => EditFaq::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationGroup(): ?string
    {
        return __('CMS');
    }

    public static function getNavigationLabel(): string
    {
        return __("FAQs");
    }
    public static function getBreadcrumb(): string
    {
        return __("FAQs");
    }


    public static function getModelLabel(): string
    {
        return __('FAQs');
    }

    public static function getPluralModelLabel(): string
    {
        return __('FAQs');
    }

}
