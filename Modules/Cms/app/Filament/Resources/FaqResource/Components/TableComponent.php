<?php

namespace Modules\Cms\app\Filament\Resources\FaqResource\Components;

use App\Shared\Components\DateRangeFilter;
use App\Shared\Enums\GeneralStatusEnum;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Modules\Cms\Enums\CmsSourceTypeEnum;

class TableComponent
{
    public static function getTable($table): Tables\Table
    {
        return $table
            ->columns([
                TextColumn::make('question')
                    ->label(__('Question'))
                    ->limit(25)
                    ->tooltip(function (TextColumn $column): string {
                        return $column->getState();
                    })
                    ->searchable()
                    ->sortable(),
                TextColumn::make('answer')
                    ->label(__('Answer'))
                    ->limit(25)
                    ->tooltip(function (TextColumn $column): string {
                        return strip_tags($column->getState());
                    })
                    ->formatStateUsing(fn(string $state): string => Str::of(strip_tags($state))->limit(25))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('source')
                    ->label(__('Source'))
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => CmsSourceTypeEnum::getLabel($state))
                    ->sortable(),
                ToggleColumn::make('is_active')
                    ->label(__('Active'))
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('deleted_at')
                    ->label(__('Deleted at'))
                    ->since()
                    ->sortable()
                    ->tooltip(fn($record) => $record->deleted_at?->format('Y-m-d H:i:s'))
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('is_active')->options(GeneralStatusEnum::getOptions())->native(false) ->label(__('Is Active')),
                DateRangeFilter::make('created_at')->fromLabel(__('Created From'))
                    ->untilLabel(__('Created Until')),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
