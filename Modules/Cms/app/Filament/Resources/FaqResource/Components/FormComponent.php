<?php

namespace Modules\Cms\app\Filament\Resources\FaqResource\Components;

use Filament\Forms\Components\Card;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Modules\Cms\Enums\CmsSourceTypeEnum;

class FormComponent
{

    public static function getForm(): array
    {
        return [
            Group::make([
                Section::make(__('FAQ Details'))
                    ->label(__('FAQ Details'))
                    ->schema([
                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make('English')
                                    ->schema([
                                        TextInput::make('question.en')
                                            ->label(__('Question'))
                                            ->required()
                                            ->maxLength(255),
                                        RichEditor::make('answer.en')
                                            ->label(__('Answer'))
                                            ->required()
                                            ->maxLength(65535),
                                    ]),
                                Tabs\Tab::make('Arabic')
                                    ->schema([
                                        TextInput::make('question.ar')
                                            ->label(__('Question'))
                                            ->required()
                                            ->maxLength(255),
                                        RichEditor::make('answer.ar')
                                            ->label(__('Answer'))
                                            ->required()
                                            ->maxLength(65535),
                                    ]),
                            ]),
                    ])->columnSpan(2),
            ])->columnSpan(2),
            Group::make(
                [
                    Card::make()
                        ->schema([
                            Select::make('source')
                                ->label(__('Source'))
                                ->required()
                                ->options(CmsSourceTypeEnum::labels()),
                        ]),
                ]
            ),
            Group::make([
                Card::make()
                    ->schema([
                        Toggle::make('is_active')
                            ->label(__('Active'))
                            ->default(true),
                    ])
                    ->columnSpan(1),
            ])->columnSpan(1),
        ];
    }

}
