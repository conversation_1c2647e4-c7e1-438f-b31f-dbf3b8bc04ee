<?php

namespace Modules\Cms\app\Filament\Resources;

use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Str;
use Modules\Cms\app\Filament\CmsPlugin;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Pages;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Pages\CreateCmsPage;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Pages\EditCmsPage;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Pages\ListCmsPages;
use Modules\Cms\app\Models\CmsPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Components\FormComponent;
use Modules\Cms\app\Filament\Resources\CmsPageResource\Components\TableComponent;

class CmsPageResource extends Resource
{
    protected static ?string $model = CmsPage::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }


    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCmsPages::route('/'),
            'create' => CreateCmsPage::route('/create'),
            'edit' => EditCmsPage::route('/{record}/edit'),
        ];
    }
    public static function getNavigationGroup(): ?string
    {
        return __('CMS');
    }
    public static function getNavigationLabel(): string
    {
        return __("CMS Pages");
    }
    public static function getBreadcrumb() : string
    {
        return __('CMS Pages');
    }
    public static function getModelLabel(): string
    {
        return __('CMS Pages');
    }

    public static function getPluralModelLabel(): string
    {
        return __('CMS Pages');
    }
}
