<?php

namespace Modules\Cms\app\Filament\Resources\CmsPageResource\Components;

use App\Shared\Components\DateRangeFilter;
use App\Shared\Enums\GeneralStatusEnum;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Illuminate\Support\Str;
use Modules\Cms\Enums\CmsSourceTypeEnum;

class TableComponent
{
    public static function getTable( $table)
    {
        return $table
            ->columns([
               TextColumn::make('name')
                    ->label(__('Name')
                    )
                    ->sortable()
                    ->searchable(),
                TextColumn::make('content')
                    ->label(__('Content'))
                    ->limit(25)
                    ->tooltip(function (TextColumn $column): string {
                        return strip_tags($column->getState());
                    })
                    ->formatStateUsing(fn (string $state): string => Str::of(strip_tags($state))->limit(25))
                    ->searchable()
                    ->sortable(),
               TextColumn::make('source')
                    ->label(__('Source'))
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => CmsSourceTypeEnum::getLabel($state))
                    ->sortable(),
               ToggleColumn::make('is_active')
                    ->label(__('Active'))
                    ->sortable()
                    ->onColor('success')
                    ->offColor('danger'),
               TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
               TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
               TextColumn::make('deleted_at')
                    ->label(__('Deleted'))
                    ->since()
                    ->sortable()
                    ->tooltip(fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s'))
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
               TrashedFilter::make(),
                SelectFilter::make('is_active')
                    ->label(__('Status'))
                    ->options(GeneralStatusEnum::getOptions())
                    ->native(false),
                DateRangeFilter::make('created_at')
                    ->fromLabel(__('Created From'))
                    ->untilLabel(__('Created Until')),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
//                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    BulkAction::make('markAsRead')
                        ->label(__('Mark as Read'))
                        ->icon('heroicon-o-check')
                        ->action(fn (Collection $records) => $records->each->update(['is_read' => true])),
                ]),
            ])
            ;
    }
}
