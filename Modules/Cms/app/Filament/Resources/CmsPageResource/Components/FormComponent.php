<?php

namespace Modules\Cms\app\Filament\Resources\CmsPageResource\Components;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

class FormComponent
{
    public static function getForm(): array
    {
        return [
            Group::make([
                Section::make(__('Page Details'))
                    ->schema([
                        TextInput::make('page_key')->label(__('key'))->disabled(),
                        TextInput::make('source')->label(__('Source'))->disabled(),
                        Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        TextInput::make('name.en')
                                            ->label(__('Name'))
                                            ->required()
                                            ->maxLength(255),
                                        RichEditor::make('content.en')
                                            ->label(__('Content'))
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        TextInput::make('name.ar')
                                            ->label(__('Name'))
                                            ->required()
                                            ->maxLength(255),
                                        RichEditor::make('content.ar')
                                            ->label(__('Content'))

                                    ]),
                            ]),
                    ])->columnSpan(2),
            ])->columnSpan(2),

            Group::make([
                Card::make()
                    ->schema([
                        Toggle::make('is_active')
                            ->label(__('Active'))
                            ->default(true),
                    ])
                    ->columnSpan(1),
            ])->columnSpan(1),
        ];
    }
}
