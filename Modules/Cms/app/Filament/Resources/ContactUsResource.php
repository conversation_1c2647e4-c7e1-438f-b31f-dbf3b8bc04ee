<?php

namespace Modules\Cms\app\Filament\Resources;

use Filament\Tables\Columns\ToggleColumn;
//use Modules\Cms\app\Filament\Resources\ContactUsResource\Components\TableComponent;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Components\FormComponent;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Components\TableComponent;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Pages\CreateContactUs;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Pages\EditContactUs;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Pages\ListContactUs;
use Modules\Cms\app\Filament\Resources\ContactUsResource\Pages\ViewContactUs;
use Modules\Cms\app\Models\ContactUs;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
//use Modules\Cms\Filament\Resources\ContactUsResource\Components\TableComponent;

class ContactUsResource extends Resource
{
    protected static ?string $model = ContactUs::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Contact Messages';







    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());

    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListContactUs::route('/'),
            'create' => CreateContactUs::route('/create'),
//            'view' => ViewContactUs::route('/{record}'),
            'edit' => EditContactUs::route('/{record}/edit'),
        ];
    }
    public static function getNavigationGroup(): ?string
    {
        return __('CMS');
    }

    public static function getNavigationLabel(): string
    {
        return __("Contact Messages");
    }


    public static function getBreadcrumb(): string
    {
        return __("Contact Us");
    }


    public static function getModelLabel(): string
    {
        return __('Contact Us');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Contact Us');
    }
}
