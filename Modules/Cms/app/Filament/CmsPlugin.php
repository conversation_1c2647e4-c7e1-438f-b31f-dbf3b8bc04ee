<?php

namespace Modules\Cms\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Cms\app\Filament\Resources\CmsPageResource;
use Modules\Cms\app\Filament\Resources\ContactUsResource;
use Modules\Cms\app\Filament\Resources\FaqResource;

class CmsPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return 'Cms';
    }

    public function getId(): string
    {
        return 'cms';
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }
    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                CmsPageResource::class,
                ContactUsResource::class,
                FaqResource::class
            ]);
    }
}
