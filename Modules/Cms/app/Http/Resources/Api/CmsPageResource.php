<?php

namespace Modules\Cms\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CmsPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
            "id" => $this->id ?? null,
            "name" => $this->name ?? null,
            "page_key" => $this->page_key ?? null,
            "content" => $this->content ?? null,
        ];

        return $data;

    }
}
