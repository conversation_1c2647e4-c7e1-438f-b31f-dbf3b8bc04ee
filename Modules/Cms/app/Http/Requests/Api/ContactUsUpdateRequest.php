<?php

namespace Modules\Cms\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class ContactUsUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
                        'name' => 'sometimes|max:255|string',
            'subject' => 'nullable|max:255|string',
            'phone' => 'sometimes|max:255|min:12',
            'email' => 'nullable|max:255|string|email',
            'message' => 'sometimes',
            'is_read' => 'sometimes'
        ];
        return $rules;
    }


}
