<?php

namespace Modules\Cms\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class ContactUsStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'name' => 'required|max:255|string',
            'subject' => 'nullable|max:255|string',
            'phone' => 'required|max:255|min:11',
            'email' => 'nullable|max:255|string|email',
            'message' => 'required',
        ];
        return $rules;
    }

}
