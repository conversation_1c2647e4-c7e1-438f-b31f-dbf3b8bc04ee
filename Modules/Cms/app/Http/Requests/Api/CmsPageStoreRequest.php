<?php

namespace Modules\Cms\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class CmsPageStoreRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
                        'name' => 'required|max:255|string',
            'content' => 'required',
            'is_active' => 'required'
        ];
        return $rules;
    }

}
