<?php

namespace Modules\Cms\app\Http\Requests\Api;
use Illuminate\Foundation\Http\FormRequest;

class FaqUpdateRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
                        'question' => 'sometimes',
            'answer' => 'sometimes',
            'is_active' => 'sometimes'
        ];
        return $rules;
    }


}
