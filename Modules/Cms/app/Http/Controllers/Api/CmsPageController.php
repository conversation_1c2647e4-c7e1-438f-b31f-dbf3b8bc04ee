<?php

namespace Modules\Cms\app\Http\Controllers\Api;

use Illuminate\Http\Request;
use Modules\Cms\Enums\CmsSourceTypeEnum;
use Modules\Cms\Services\CmsPageService;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Cms\app\Http\Requests\Api\CmsPageStoreRequest;
use Modules\Cms\app\Http\Requests\Api\CmsPageUpdateRequest;
use Modules\Cms\app\Http\Resources\Api\CmsPageResource;

class CmsPageController extends ControllerAbstract
{


    protected string $jsonResourceClass = CmsPageResource::class;
    protected string $storeRequestClass = CmsPageStoreRequest::class;
    protected string $updateRequestClass = CmsPageUpdateRequest::class;


    public function __construct(CmsPageService $service)
    {
        $this->filter['source'] = CmsSourceTypeEnum::MOBILE;
        parent::__construct($service);
    }

    public function index(Request $request)
    {
        if ($request->has('page_key')) {
            $this->filter['page_key'] = $request->get('page_key');
        }

        return parent::index($request);
    }
}
