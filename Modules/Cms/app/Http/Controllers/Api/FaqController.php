<?php

namespace Modules\Cms\app\Http\Controllers\Api;

use Modules\Cms\Enums\CmsSourceTypeEnum;
use Modules\Cms\Services\FaqService;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Cms\app\Http\Requests\Api\FaqStoreRequest;
use Modules\Cms\app\Http\Requests\Api\FaqUpdateRequest;
use Modules\Cms\app\Http\Resources\Api\FaqResource;

class FaqController extends ControllerAbstract
{

    protected string $jsonResourceClass = FaqResource::class;
    protected string $storeRequestClass = FaqStoreRequest::class;
    protected string $updateRequestClass = FaqUpdateRequest::class;

    protected array $filter=["is_active"=>1];

    public function __construct(FaqService $service)
    {
        $this->filter['source'] = CmsSourceTypeEnum::MOBILE;
        parent::__construct($service);
    }

}
