<?php

namespace Modules\Cms\app\Http\Controllers\Api;

use Modules\Cms\Services\ContactUsService;
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;
use Modules\Cms\app\Http\Requests\Api\ContactUsStoreRequest;
use Modules\Cms\app\Http\Requests\Api\ContactUsUpdateRequest;
use Modules\Cms\app\Http\Resources\Api\ContactUsResource;

class ContactUsController extends ControllerAbstract
{


    protected string $jsonResourceClass = ContactUsResource::class;
    protected string $storeRequestClass = ContactUsStoreRequest::class;
    protected string $updateRequestClass = ContactUsUpdateRequest::class;


    public function __construct(ContactUsService $service)
    {
        parent::__construct($service);
    }


}
