<?php

namespace Modules\Cms\app\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Cms\app\Models\Faq;

class FaqController extends Controller
{

    public function show()
    {
        $faqs = Faq::where('is_active',true)->get();
        $locale = auth()->user()->lang ?? session()->get(key: 'locale',default: 'en');
        $contents = [];


        foreach($faqs as $faq){

            $questionData = json_decode($faq , true);

            $question = $questionData['question'][$locale]?? '';
            $answer = $questionData['answer'][$locale]?? '';

            $contents[] = [
                'id' => $faq->id,
                'question' => $question,
                'answer' => $answer,
            ];
        }

        return view('cms::faq.show',compact('contents'));
    }

}
