<?php

namespace Modules\Cms\app\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Cms\app\Models\CmsPage;

class CmsPageController extends Controller
{

    public function show($key)
    {
        $content = CmsPage::where('page_key', $key)->first();

        if (!$content) {
            abort(404, 'Page not found');
        }

        // Get the current locale
        $locale = auth()->user()->lang ?? request()->cookie('filament_language_switch_locale') ?? session()->get(key: 'locale',default: 'en');
        $contentData = json_decode($content , true);
        // Retrieve the localized content
        $content = $contentData['content'][$locale]?? '';
        $name = $contentData['name'][$locale]?? '';

        return view('cms::cmsPage.show', [
            'content' => $content,
            'name' => $name,
            'livewire' => null  // Add this line
        ]);
    }


}
