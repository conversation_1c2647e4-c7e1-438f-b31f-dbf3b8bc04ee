<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
            Schema::create('faqs', function (Blueprint $table) {
                $table->id();

                //QA Category
//                $table->foreignId('type_id')->nullable()->constrained('types')->onDelete('cascade');

                //QA / Answer
                $table->json('question');
                $table->json('answer');
                $table->boolean('is_active')->default(0);

                $table->softDeletes();
                $table->timestamps();
            });


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('questions');
    }
};
