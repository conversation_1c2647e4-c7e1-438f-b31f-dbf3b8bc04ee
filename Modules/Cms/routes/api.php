<?php

use Modules\Cms\app\Http\Controllers\Api\ContactUsController;
use Modules\Cms\app\Http\Controllers\Api\FaqController;
use Modules\Cms\app\Http\Controllers\Api\CmsPageController;
use Modules\Cms\app\Http\Controllers\Api\SettingController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::prefix('cms/')->group(function () {

    // Public routes
    Route::apiResource('contact_us', ContactUsController::class)->only('store');
    Route::apiResource('faq', FaqController::class)->only('index');
    Route::apiResource('cms_page', CmsPageController::class)->only(['index', 'show']);
    Route::get('setting', [SettingController::class, 'index']);

});
