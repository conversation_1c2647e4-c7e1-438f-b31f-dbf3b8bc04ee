<?php

namespace Modules\Cms\Enums;

enum CmsSourceTypeEnum
{
    const WEB = 'web';
    const MOBILE = 'mobile';


    public static function getSourceTypes(): array
    {
        return [
            self::WEB,
            self::MOBILE,
        ];
    }
    public static function labels(): array
    {
        return [
            self::WEB => __('web'),
            self::MOBILE => __('mobile'),
        ];
    }
    public static function getLabel($type): string
    {
        return self::labels()[$type] ?? '';
    }

}
