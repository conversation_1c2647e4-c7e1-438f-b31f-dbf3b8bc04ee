<?php

namespace Modules\Cms\Enums;

enum ReadStatusEnum : string
{
    case Read = 'read';
    case Unread = 'unread';

    public function label(): string
    {
        return match($this) {
            self::Read => __('Read'),
            self::Unread => __('Unread'),
        };
    }

    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }

    public function value(): bool
    {
        return match($this) {
            self::Read => true,
            self::Unread => false,
        };
    }

    public static function getOptions(): array
    {
        return [
            self::Read->value() => self::Read->label(),
            self::Unread->value() => self::Unread->label(),
        ];
    }
}
