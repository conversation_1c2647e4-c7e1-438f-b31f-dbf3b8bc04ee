<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'nafath' => [
        'live_app_id'    => env('LIVE_NAFATH_APP_ID', '48ce70ba'),
        'live_app_key'   => env('LIVE_NAFATH_APP_KEY', '1fda5f35606de8d07928a4fb79474ddb'),
        'stage_app_id'   => env('STAGE_NAFATH_APP_ID', '6d396af3'),
        'stage_app_key'  => env('STAGE_NAFATH_APP_KEY', 'be84b1844891dfb366bdf0e8eb99fb03'),
        'title'          => env('NAFATH_SERVICE_TITLE', 'DigitalServiceEnrollmentWithoutBio'),
        'production_url' => env('LIVE_NAFATH_URL', 'https://nafath.api.elm.sa'), 
        'staging_url'    => env('STAGE_NAFATH_URL', 'https://nafath.api.elm.sa/stg'),
        'stage'          => env('STAGE_MODE', 'stage'),
    ],
];
