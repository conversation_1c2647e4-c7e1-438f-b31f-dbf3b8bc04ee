![Screenshot](https://github.com/tomatophp/tomato-notifications/blob/master/art/screenshot.png)

# Tomato Notifications

🍅 Laravel Notifications Channel with GUI to send notifications with templates for [TomatoPHP](https://docs.tomatophp.com/) build with [<PERSON>p<PERSON>](https://splade.dev/)

we are build a full notification system for you with multi provider like mail, firebase, pusher, discord, slack, messagebird and open the way to add more all of this working on queue and have a lot of helpers to make it easy to use this notification on your system

## Installation

```bash
composer require tomatophp/tomato-notifications
```

## Support

you can join our discord server to get support [TomatoPHP](https://discord.gg/VZc8nBJ3ZU)

## Docs

you can check docs of this package on [Docs](https://docs.tomatophp.com/plugins/tomato-notifications)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Credits

- [Fady Mondy](https://github.com/3x1io)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
