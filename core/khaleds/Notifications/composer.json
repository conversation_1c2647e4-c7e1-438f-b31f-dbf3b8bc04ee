{"name": "tomatophp/tomato-notifications", "type": "library", "description": "Laravel Notifications Channel with GUI to send notifications with templates", "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "inertiajs", "v<PERSON><PERSON><PERSON>", "blade", "php8", "laravel", "notifications", "fcm", "discord", "slack"], "license": "MIT", "extra": {"laravel": {"providers": ["Khaleds\\Notifications\\NotificationsServiceProvider"]}}, "autoload": {"psr-4": {"TomatoPHP\\TomatoNotifications\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "laravel-notification-channels/fcm": "^3.1", "kreait/firebase-php": "^7.0"}}