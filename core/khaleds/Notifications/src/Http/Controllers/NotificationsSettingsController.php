<?php

namespace TomatoPHP\TomatoNotifications\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use TomatoPHP\TomatoNotifications\Http\Requests\Settings\NotificationsSettingsRequest;
use TomatoPHP\TomatoNotifications\Settings\NotificationsSettings;
use TomatoPHP\TomatoNotifications\Services\Setting;

class NotificationsSettingsController extends Setting
{
    public string $setting = NotificationsSettings::class;


    public function index(Request $request)
    {
        return $this->get(request: $request, view:'tomato-notifications::settings.notifications');
    }

    public function store(NotificationsSettingsRequest $request)
    {
        return $this->save(request: $request, redirect: "admin.settings.notifications.index", media:[]);
    }
}
