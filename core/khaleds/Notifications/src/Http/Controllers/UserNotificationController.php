<?php

namespace TomatoPHP\TomatoNotifications\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use ProtoneMedia\Splade\Facades\Toast;
use TomatoPHP\TomatoNotifications\Http\Requests\UserNotification\UserNotificationStoreRequest;
use TomatoPHP\TomatoNotifications\Http\Requests\UserNotification\UserNotificationUpdateRequest;
use TomatoPHP\TomatoNotifications\Models\NotificationsTemplate;
use TomatoPHP\TomatoNotifications\Models\UserNotification;
use TomatoPHP\TomatoNotifications\Services\SendNotification;
use TomatoPHP\TomatoNotifications\Tables\UserNotificationTable;
use TomatoPHP\TomatoPHP\Services\Tomato;

class UserNotificationController extends Controller
{
    /**
     * @param Request $request
     * @return View
     */
    public function index(Request $request): View
    {
        $s=  Tomato::index(
            request: $request,
            view: 'tomato-notifications::user-notifications.index',
            table: UserNotificationTable::class,
        );

        return $s;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function api(Request $request): JsonResponse
    {
        return Tomato::json(
            request: $request,
            model: UserNotification::class,
        );
    }


    /**
     * @param Request $request
     * @return mixed
     */
    public function get(Request $request, $model): mixed
    {
        $getModel = config('tomato-notifications.models')[$model];

        return $getModel::all()->pluck('name', 'id');
    }

    /**
     * @return View
     */
    public function create(): View
    {
        return Tomato::create(
            view: 'tomato-notifications::user-notifications.create',
            data: [
                "templates" => NotificationsTemplate::all()
            ]
        );
    }

    /**
     * @param UserNotificationStoreRequest $request
     * @return RedirectResponse
     */
    public function store(UserNotificationStoreRequest $request): RedirectResponse
    {
        $request->validated();

        $template = NotificationsTemplate::find($request->get('template_id'));

        if ($request->get('privacy') == "public"){

            $model = config('tomato-notifications.models')[$request->get('model_type')]::query();

            if (!is_null($request->get('tags')) )
                $model->whereHas('tags',function ($query) use ($request){
                    $query->whereIn('id',array_values($request->tags));
                });

//            if (!is_null($request->get('level')) )
//                $model->where('level',$request->level);

            $model=$model->with('tags')->get();
            foreach ($model as $item){

                $notification = new UserNotification();
                $notification->title = $template->title;
                $notification->template_id = $template->id;
                $notification->description = $template->body;
                $notification->type = $template->type;
                $notification->privacy = $request->get('privacy');
                $notification->model_id = $item->id;
                $notification->model_type = $item::class;
                $notification->icon = $template->icon;
                $notification->url = $template->url;
                $notification->created_by = auth()->user()->id;
                $notification->save();


                SendNotification::make($template->providers)
                    ->title($template->title)
                    ->template($template->key)
                    ->database(false)
                    ->privacy('private')
                    ->model($item::class)
                    ->id($item->id)
                    ->fire();
            }
        }else{

//            $notification = new UserNotification();
//            $notification->title = $template->title;
//            $notification->template_id = $template->id;
//            $notification->description = $template->body;
//            $notification->type = $template->type;
//            $notification->privacy = $request->get('privacy');
//            $notification->model_id = $item->id;
//            $notification->model_type = $item::class;
//            $notification->icon = $template->icon;
//            $notification->url = $template->url;
//            $notification->created_by = auth()->user()->id;
//            $notification->save();


            SendNotification::make($template->providers)
            ->title($template->title)
            ->template($template->key)
            ->database(true)
            ->privacy($request->get('privacy'))
            ->model(config('tomato-notifications.models')[$request->get('model_type')])
            ->id($request->get('model_id'))
            ->fire();

        }



        Toast::title(trans('tomato-notifications::global.notifications.success'))->success()->autoDismiss(2);
        return redirect()->route('admin.user-notifications.index');
    }

    /**
     * @param UserNotification $model
     * @return View
     */
    public function show(UserNotification $model): View
    {
        return Tomato::get(
            model: $model,
            view: 'tomato-notifications::user-notifications.show',
        );
    }

    /**
     * @param UserNotification $model
     * @return RedirectResponse
     */
    public function destroy(UserNotification $model): RedirectResponse
    {
        return Tomato::destroy(
            model: $model,
            message: 'UserNotification deleted successfully',
            redirect: 'admin.user-notifications.index',
        );
    }

    /**
     * @param UserNotification $model
     * @return RedirectResponse
     */
    public function resend(UserNotification $model):RedirectResponse
    {

        $template = NotificationsTemplate::find($model->template_id);

        $notification = new UserNotification();
        $notification->title = $template->title;
        $notification->template_id = $template->id;
        $notification->description = $template->body;
        $notification->type = $template->type;
        $notification->privacy = $model->privacy;
        $notification->model_id = $model->model_id;
        $notification->model_type = $model->model_type;
        $notification->icon = $template->icon;
        $notification->url = $template->url;
        $notification->created_by = auth()->user()->id;
        $notification->save();

        SendNotification::make($template->providers)
            ->title($template->title)
            ->template($template->key)
            ->database(false)
            ->privacy($model->privacy)
            ->model($model->model_type)
            ->id($model->model_id)
            ->fire();


        Toast::title(trans('tomato-notifications::global.notifications.success'))->success()->autoDismiss(2);
        return redirect()->route('admin.user-notifications.index');
    }

    public function sendCustom($request,$template){

    }
}
