<?php

namespace Khaleds\Notifications;

use Illuminate\Support\ServiceProvider;

class NotificationsServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //Register Migrations
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

        //Register Config file
        $this->mergeConfigFrom(__DIR__.'/../config/tomato-notifications.php', 'tomato-notifications');

        //Register views
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'tomato-notifications');

        //Register Langs
        $this->loadTranslationsFrom(__DIR__.'/../resources/lang', 'tomato-notifications');

        //Register Routes
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');

          //Publish Views
          $this->publishes([
            __DIR__.'/../resources/views' => resource_path('views/vendor/tomato-notifications'),
        ], 'tomato-notifications-views');

        //Publish Config
        $this->publishes([
            __DIR__.'/../config/tomato-notifications.php' => config_path('tomato-notifications.php'),
        ], 'tomato-notifications-config');

        //Publish Lang
        $this->publishes([
            __DIR__.'/../resources/lang' => app_path('lang/vendor/tomato-notifications'),
        ], 'tomato-notifications-lang');

        //Publish Migrations
        $this->publishes([
            __DIR__.'/../database/migrations' => database_path('migrations'),
        ], 'tomato-notifications-migrations');

        //Register install command


        $this->registerPermissions();
    }

    public function boot(): void
    {
        //Add Middleware Global to Routes web
    }

    /**
     * @return void
     */
    public function registerPermissions(): void
    {
        //Register Permission For Settings

    }
}
