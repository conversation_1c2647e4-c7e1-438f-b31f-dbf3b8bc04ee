<?php

namespace Khaleds\Notifications\Services;

use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Models\UserNotification;
use Khaleds\Notifications\Jobs\NotificationJop;
use Khaleds\Notifications\Services\Actions\FireEvent;
use Khaleds\Notifications\Services\Actions\LoadTemplate;
use Khaleds\Notifications\Services\Actions\SendToDatabase;
use Khaleds\Notifications\Services\Actions\SendToJob;
use Khaleds\Notifications\Services\Concerns\HasCreatedBy;
use Khaleds\Notifications\Services\Concerns\HasData;
use Khaleds\Notifications\Services\Concerns\HasFindBody;
use Khaleds\Notifications\Services\Concerns\HasFindTitle;
use Khaleds\Notifications\Services\Concerns\HasIcon;
use Khaleds\Notifications\Services\Concerns\HasId;
use Khaleds\Notifications\Services\Concerns\HasImage;
use Khaleds\Notifications\Services\Concerns\HasLang;
use Khaleds\Notifications\Services\Concerns\HasMessage;
use Khaleds\Notifications\Services\Concerns\HasModel;
use Khaleds\Notifications\Services\Concerns\HasPrivacy;
use Khaleds\Notifications\Services\Concerns\HasProviders;
use Khaleds\Notifications\Services\Concerns\HasReplaceBody;
use Khaleds\Notifications\Services\Concerns\HasReplaceTitle;
use Khaleds\Notifications\Services\Concerns\HasTemplate;
use Khaleds\Notifications\Services\Concerns\HasTemplateModel;
use Khaleds\Notifications\Services\Concerns\HasTitle;
use Khaleds\Notifications\Services\Concerns\HasType;
use Khaleds\Notifications\Services\Concerns\HasUrl;
use Khaleds\Notifications\Services\Concerns\HasUser;
use Khaleds\Notifications\Services\Concerns\IsDatabase;

class SendNotification
{
    use HasTitle;
    use HasMessage;
    use HasType;
    use HasProviders;
    use HasPrivacy;
    use HasUrl;
    use HasImage;
    use HasIcon;
    use HasModel;
    use HasTemplate;
    use HasFindTitle;
    use HasFindBody;
    use HasReplaceTitle;
    use HasReplaceBody;
    use HasId;
    use HasCreatedBy;
    use HasUser;
    use HasLang;
    use HasTemplateModel;
    use IsDatabase;

    /*
     * Actions
     */
    use FireEvent;
    use LoadTemplate;
    use SendToDatabase;
    use SendToJob;
    use HasData;
    /**
     * @param ?array $providers
     * @return static
     */
    public static function make(?array $providers): static
    {
        return (new static)->providers($providers);
    }
}
