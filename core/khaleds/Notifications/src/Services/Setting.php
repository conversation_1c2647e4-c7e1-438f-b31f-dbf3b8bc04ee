<?php

namespace Khaleds\Notifications\Services;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use ProtoneMedia\Splade\Facades\Toast;

class Setting extends Controller
{
    public string $setting;

    /**
     * @return mixed
     */
    public function loadSettings(): array
    {
        return app($this->setting)->toArray();
    }


    /**
     * @param Request $request
     * @param string $view
     * @return Factory|View|Application
     */
    public function get(Request $request, string $view): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        return view($view, [
            "settings" => $this->loadSettings()
        ]);
    }


    /**
     * @param FormRequest $request
     * @param string $redirect
     * @param array|null $media
     * @return RedirectResponse
     */
    public function save(FormRequest $request, string $redirect, array|null $media= null): \Illuminate\Http\RedirectResponse
    {

        $settings = \TomatoPHP\TomatoSettings\Models\Setting::whereIn('name', array_keys($request->all()))->get();

        foreach ($settings as $setting) {
            if (!$request->hasFile($setting->name)) {
                $setting->payload = $request->input($setting->name);
                $setting->save();
            }
            else{
                    $request->file($setting->name)->storeAs('public/settings', $setting->name .  '.'.$request->file($setting->name)->extension());
                $setting->name = url('storage/settings/'.$setting->name .'.'.$request->file($setting->name)->extension());
                $setting->save();

            }
        }




        Toast::title(trans('tomato-settings::global.message.success'))->success()->autoDismiss(2);
        return redirect()->route($redirect);
    }

}
