{"name": "khaleds/FilamentTranslations", "type": "library", "description": "Manage your translation with DB and cache, you can scan your languages tags like trans(), __(), and get the string inside and translate them use UI.", "keywords": ["php", "laravel", "template"], "license": "MIT", "autoload": {"psr-4": {"Khaleds\\FilamentTranslations\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Khaleds\\FilamentTranslations\\FilamentTranslationsServiceProvider"]}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1|^8.2", "filament/filament": "^3.2", "Khaleds/console-helpers": "^1.0", "filament/notifications": "^3.0.0", "tomatophp/filament-developer-gate": "^1.0", "maatwebsite/excel": "^3.1"}}