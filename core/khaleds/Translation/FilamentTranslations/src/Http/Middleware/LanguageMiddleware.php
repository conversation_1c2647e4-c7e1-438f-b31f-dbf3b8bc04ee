<?php

namespace Khaleds\FilamentTranslations\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!$request->user() && request()->cookie('filament_language_switch_locale')){
            app()->setLocale(request()->cookie('filament_language_switch_locale'));

            return $next($request);

        }

        if ($request->user()) {

            $local = $request->user()->lang;
            if ($local != request()->cookie('filament_language_switch_locale'))
                $local = request()->cookie('filament_language_switch_locale');


            if (is_null($local)){
                $local = App::getLocale();
            }

                app()->setLocale($local);

        }
        return $next($request);
    }
}
