<?php

namespace <PERSON>haleds\FilamentTranslations\Jobs;

use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Khaleds\FilamentTranslations\Models\Translation;
use Stichoza\GoogleTranslate\GoogleTranslate;
use Illuminate\Support\Facades\Log;


class ScanWithGoogleTranslate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Authenticatable $user,
        public string          $language = "en"
    )
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $translator = new GoogleTranslate($this->language);
        Translation::chunk(200, function (Collection $translations) use ($translator) {
            try {
                foreach ($translations as $translation) {
                    if (empty($translation->text[$this->language])) {
                        $textToTranslate = $translation->text['en'] ?? $translation['key'];

                        $translatedText = $translator->translate($textToTranslate);
                        Log::info("Translated '{$textToTranslate}' to '{$translatedText}' in {$this->language}");

                        $translation->setTranslation($this->language, $translatedText);

                        $translation->save();
                    }
                }
            } catch (\Exception $e) {
                Log::error('Google API Service Exception: ' . $e->getMessage());
            }
        });

        // Notify the user when the process is completed
        Notification::make()
            ->title(trans('filament-translations::translation.google_scan_notifications_done'))
            ->success()
            ->sendToDatabase($this->user);

        // Log that the process was completed
        Log::info('Translation process completed successfully for language: ' . $this->language);
    }
}
