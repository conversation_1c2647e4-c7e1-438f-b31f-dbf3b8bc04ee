{"name": "khaleds/FilamentTranslationComponent", "type": "library", "description": "Translation Component as a key/value to use it with Spatie Translatable FilamentPHP Plugin", "keywords": ["php", "laravel", "template"], "license": "MIT", "autoload": {"psr-4": {"Khaleds\\FilamentTranslationComponent\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Khaleds\\FilamentTranslationComponent\\FilamentTranslationComponentServiceProvider"]}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {}}