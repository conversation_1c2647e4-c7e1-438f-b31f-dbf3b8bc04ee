<?php

namespace <PERSON>haleds\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Khaleds\Shared\Helpers\HandleMediaCollection;

abstract class BaseModel extends Model implements HasMedia
{

    use HasTranslations,InteractsWithMedia,HandleMediaCollection;

    protected $translatable=['name','description'];

    protected array $allowedFilters=['id','name'];


    public function getAllowedFilters(){

        return $this->allowedFilters ;

    }
}
