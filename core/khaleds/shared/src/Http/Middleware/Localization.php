<?php

namespace <PERSON>haleds\Shared\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class Localization
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, \Closure $next)
    {

        if ($request->header('Accept-Language') && in_array($request->header('Accept-Language'),['ar','en'])) {
            App::setLocale($request->header('Accept-Language'));
        }

        return $next($request);
    }

}
