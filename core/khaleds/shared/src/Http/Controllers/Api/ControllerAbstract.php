<?php

namespace Khaleds\Shared\Http\Controllers\Api;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Khaleds\Shared\Helpers\ApiResponse;
use Khaleds\Shared\Services\ServiceInterface;
use Illuminate\Http\Request;


abstract class ControllerAbstract
{
    protected array $append=[];
    protected string $jsonResourceClass;
    protected string $storeRequestClass;
    protected string $updateRequestClass;


    protected array $filter = [];
    protected array $with = [];
    protected array $select = ['*'];

    public function __construct(protected ServiceInterface $service)
    {
        $this->setFilter();
    }

    private function setFilter(): void
    {
        $this->filter = array_merge(request()->input('search') ?? [],
            $this->filter
        );
    }
    //todo
    private function setAppends(){


    }
//todo make general expetion meadleware
    public function index(Request $request)
    {
        return ApiResponse::data($this->jsonResourceClass::collection($this->service->paginate($this->filter, $this->with, $this->select)));
    }


    public function show(int $id)
    {
        return ApiResponse::data($this->jsonResourceClass::make($this->service->findOrFail($id, $this->filter, $this->with)));
    }


    public function store(Request $request)
    {
        $request = app($this->storeRequestClass);

        return ApiResponse::data($this->jsonResourceClass::make($this->service->create($request->validated())));
    }

    public function update(Request $request, $id)
    {

        $request = app($this->updateRequestClass);

        $model = $this->service->findOrFail($id, $this->filter);

        $this->service->update($request->validated(), $model);

        return ApiResponse::data($this->jsonResourceClass::make($model->refresh()),ApiResponse::UPDATED);
    }

    public function destroy($id)
    {

        $model = $this->service->findOrFail($id, $this->filter);

        $this->service->delete($model);

        return ApiResponse::success(ApiResponse::DELETED);
    }



    // TODO: factory for response , hooks for in methods , events , interface

}
