<?php

namespace Khaleds\Shared\Http\Controllers\Web;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\View\View;
use App\Http\Controllers\Controller;

abstract class ControllerAbstract extends Controller
//    implements ControllerInterface
{

    protected array $append=[];
    protected FormRequest $storeRequest;
    protected FormRequest $updateRequest;
    abstract function getStoreRequest():FormRequest;
    abstract function getUpdateRequest():FormRequest;
    abstract function getViews():CrudViews;
    abstract function getModel():Model;
    abstract function getTable():string;
    //todo try to inject the value object
// todo : try to use parent return and edit on response
//todo self or this or static

    public function index(): View
    {
        return view($this->getViews()->indexView, array_merge([
            'table' => app($this->getTable()),
        ],$this->append));
    }

    public function json(): JsonResponse
    {
        //todo add filter
        return response()->json([
            'model' => ["data"=>$this->getModel()::select('id','name')->toArray()],
            'data' => $this->append
        ]);
    }


    public function create(): View
    {
        return view($this->getViews()->createView, $this->append);
    }

    public function store(FormRequest $request, string $model, string $message, string $redirect, bool $hasMedia=false, string $collection="", bool $multi = false): array
    {
        $request = app($this->getStoreRequest());
        // todo make it service
        $record = $model::create($request->validated());

        if($hasMedia){

            if($multi){
                if(count($request->get($collection))){
                    foreach ($request->{$collection} as $item) {
                        $record->addMedia($item)
                            ->preservingOriginal()
                            ->toMediaCollection($collection);
                    }
                }
            }
            else {
                if($request->hasFile($collection)){
                    $record->addMedia($request->{$collection})
                    ->preservingOriginal()
                    ->toMediaCollection($collection);
                }
            }
        }

        Toast::title($message)->success()->autoDismiss(2);
        return [
            "redirect" => redirect()->route($redirect),
            "record" => $record
        ];
    }



    public function get(Model $model, string $view, array $data=[], bool $hasMedia=false, string $collection="", bool $multi=false): View
    {
        if($hasMedia){
            if($multi){
                $model->{$collection} = $model->getMedia($collection)->map(function ($file) {
                    return $file->getUrl();
                });
            }
            else {
                $model->{$collection} = $model->getMedia($collection)->first() ? $model->getMedia($collection)->first()->getUrl() : null;
            }

            return view($view, array_merge([
                "model" => $model
            ], $data));
        }

        return view($view, array_merge([
            "model" => $model
        ], $data));
    }


    /**
     * @param FormRequest $request
     * @param Model $model
     * @param string $message
     * @param string $redirect
     * @param bool $hasMedia
     * @param string $collection
     * @param bool $multi
     * @return array
     */
    public function update(FormRequest $request, Model $model, string $message, string $redirect, bool $hasMedia=false, string $collection="", bool $multi = false): array
    {
        $request->validated();
        $model->update($request->all());

        if($hasMedia){
            if($request->{$collection} ){
                $model->clearMediaCollection($collection);
                if($multi){
                    if($request->has($collection) && count($request->get($collection))){
                        foreach ($request->{$collection} as $item) {
                            if(!is_string($item)){
                                if($item->getClientOriginalName() === 'blob'){
                                    $model->addMedia($item)
                                        ->usingFileName(strtolower(Str::random(10).'_'.$collection.'.'.$item->extension()))
                                        ->preservingOriginal()
                                        ->toMediaCollection($collection);
                                }
                                else {
                                    $model->addMedia($item)
                                        ->preservingOriginal()
                                        ->toMediaCollection($collection);
                                }
                            }
                        }
                    }
                }
                else {
                    if($request->has($collection)){
                        if($request->{$collection}->getClientOriginalName() === 'blob'){
                            $model->addMedia($request->{$collection})
                                ->usingFileName(strtolower(Str::random(10).'_'.$collection.'.'.$request->{$collection}->extension()))
                                ->preservingOriginal()
                                ->toMediaCollection($collection);
                        }
                        else {
                            $model->addMedia($request->{$collection})
                                ->preservingOriginal()
                                ->toMediaCollection($collection);
                        }
                    }
                }
            }
        }

        Toast::title($message)->success()->autoDismiss(2);
        return [
            "redirect" => redirect()->route($redirect),
            "record" => $model
        ];
    }

    /**
     * @param Model $model
     * @param string $message
     * @param string $redirect
     * @return RedirectResponse
     */
    public function destroy(Model $model, string $message, string $redirect): RedirectResponse
    {
        $model->delete();
        Toast::title($message)->success()->autoDismiss(2);
        return redirect()->route($redirect);
    }
}
