<?php
namespace Khaleds\Shared\Helpers;

trait HttpHelper
{

    private $timeout=30;
    private array $headers=[];
    private array $extra=[];

    public $response=['status'=>true,'message'=>'','data'=>[]];

    public function post($uri ,$data){

      $httpClient = new \GuzzleHttp\Client();

      try{
            $response = $httpClient->request('POST', $uri, array_merge([
                'body' => json_encode($data),
            ],$this->getBodyArray()));

            $this->response['data']=$response->getBody()->getContents();
      }
      catch(\Exception $e){

            $this->response['status']=false;
            $this->response['message']=$e->getMessage();

      }
      return $this->response;


    }
    public function get($uri ,$data){

        $httpClient = new \GuzzleHttp\Client();

        try{
            $url =  $uri;
            $count =0;
            if(!empty($data)){
                foreach($data as $key => $value){
                    if ($count == 0){
                        $url.= '?' .$key . '=' .$value;
                        $count++;
                    }else{
                        $url.= '&' .$key . '=' .$value;
                    }
                }
            }

            $response = $httpClient->request('GET', $url, $this->getBodyArray());
            $this->response['data']=$response->getBody()->getContents();

        }
        catch(\Exception $e){
            $this->response['status']=false;
            $this->response['message']=$e->getMessage();

        }
        return $this->response;


    }

    public function delete($uri, array $data){

        $httpClient = new \GuzzleHttp\Client();

        try{
            $response = $httpClient->request('DELETE', $uri, array_merge([
                'body' => json_encode($data),
            ],$this->getBodyArray()));

            $this->response['data']=$response->getBody()->getContents();
        }
        catch(\Exception $e){

            $this->response['status']=false;
            $this->response['message']=$e->getMessage();

        }
        return $this->response;


    }
    public function setHttpHeaders(array $headers)
    {
        $this->headers = $headers;
    }

    public function setHttpExtra(array $extra)
    {
        $this->extra = $extra;
    }

    private function getBodyArray():array{

        return array_merge([
            'headers' => array_merge([
                'Content-Type' => 'application/json',
            ],$this->headers),
            "connect_timeout"=>$this->timeout,
            'timeout'=>$this->timeout,
        ],$this->extra);
    }

    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
    }

}
