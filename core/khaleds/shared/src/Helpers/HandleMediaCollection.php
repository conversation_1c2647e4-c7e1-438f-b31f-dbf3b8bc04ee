<?php

namespace Khaleds\Shared\Helpers;

trait HandleMediaCollection
{
    public function getMediaImages(string $collection)
    {
        $mediaImages = $this->getMedia($collection);
        return $mediaImages && $mediaImages->count() ? $mediaImages : [];
    }

    public function getMediaImage(string $collection)
    {
        $mediaImage = $this->getMedia($collection)->first()?->geturl();
        return $mediaImage ?? null;
    }
}
