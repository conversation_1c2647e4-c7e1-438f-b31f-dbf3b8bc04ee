<?php

namespace Khaleds\Shared\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Spatie\QueryBuilder\Filters\Filter;

class TranslationSearchFilter implements Filter
{
//todo check power join for performance
    private string $table;

    private string $column;

    private string $relation;

    public function __invoke(Builder $query, $value, string $property)
    {
        $value = strtolower($value);
        return $query->where(function ($query) use ($property, $value) {
            $query->whereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT({$property}, '$.en'))) LIKE ?", ['%' .$value . '%'])
                ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT({$property}, '$.ar'))) LIKE ?", ['%' . $value . '%']);
        });

//        return $query->whereRaw("LOWER({$property}->'$.ar') like ?", ["%{$value}%"])
//            ->orWhereRaw("LOWER({$property}->'$.en') like ?", ["%{$value}%"]);
    }



}
