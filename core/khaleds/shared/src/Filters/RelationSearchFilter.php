<?php

namespace Khaleds\Shared\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Spatie\QueryBuilder\Filters\Filter;

class RelationSearchFilter implements Filter
{
//todo check power join for performance
    private string $table;

    private string $column;

    private string $relation;

    public function __invoke(Builder $query, $value, string $property)
    {
       $this->getQualifiedNames($property);

        $query->whereHas($this->relation, function (Builder $query) use ($value) {
            $query->where($this->table . "." .$this->column, $value);
        });
    }

    private function getQualifiedNames(string $property)
    {
        $elements = explode('.',$property);
        $this->relation = Str::beforeLast($property, '.');
        $this->column = Str::afterLast($property, '.');
        $this->table = Str::snake(Str::pluralStudly(class_basename($elements[count($elements) - 2])));

    }

}
