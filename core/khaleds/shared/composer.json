{"name": "khaleds/Shared", "type": "library", "description": "A Laravel package that provides an abstraction layer for managing and organizing code components such as controllers, models, resources, repositories, caching, services, and other files. It helps implement coding standards and best practices by separating concerns and promoting a modular architecture.", "keywords": ["laravel", "abstraction", "code-standards", "modular-architecture", "controllers", "models", "resources", "repositories", "caching", "services"], "license": "MIT", "autoload": {"psr-4": {"Khaleds\\Shared\\": "src/"}}, "autoload-dev": {"psr-4": {}}, "extra": {"laravel": {"providers": ["Khaleds\\Shared\\SharedServiceProvider"]}}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2"}}