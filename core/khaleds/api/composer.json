{"name": "khaleds/api", "type": "library", "description": "Full API CRUD Generator build on repository pattern", "keywords": ["php", "laravel", "template", "API", "CRUD Generator", "Tomato"], "license": "MIT", "autoload": {"psr-4": {"Khaleds\\Api\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Khaleds\\Api\\ApiServiceProvider"]}}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "Khaleds/console-helpers": "^1.0", "Khaleds/tomato-model-generator": "^1.0"}}