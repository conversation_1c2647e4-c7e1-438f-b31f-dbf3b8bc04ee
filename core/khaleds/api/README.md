![Screenshot](https://github.com/Khaleds/tomato-api/blob/master/art/screenshot.png)

# Tomato API

Full API CRUD Generator build on repository pattern

## Installation

```bash
composer require Khaleds/tomato-api
```

## Support

you can join our discord server to get support [<PERSON><PERSON><PERSON>](https://discord.gg/VZc8nBJ3ZU)

## Docs

you can check docs of this package on [Docs](https://docs.Khaleds.com/plugins/tomato-api)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## Credits

- [Fady Mondy](https://github.com/3x1io)
- [<PERSON>](https://github.com/Ahmed-<PERSON>banna-Git)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
