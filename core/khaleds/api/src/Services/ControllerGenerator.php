<?php

namespace Khaleds\Api\Services;


use Khaleds\Api\Services\Concerns\GenerateApiController;
use Khaleds\Api\Services\Concerns\GenerateRoutes;
use Khaleds\Shared\Helpers\StubHelper;

//todo isolate the commands one for create repo and one for service etc
class ControllerGenerator
{

    use GenerateApiController;
    use GenerateRoutes;


    use StubHelper;

    private string $stub;

    public function __construct(
        private string $name,
        private string | bool | null $module,
        private string $service,
        private string $storeRequest,
        private string $updateRequest,
        private string $jsonResource,

    )
    {

        $this->stub = __DIR__ .'/../../stubs/';
    }


    public function generate(): void
    {
        $this->generateController();
        $this->generateRoutes();

    }
}
