<?php

namespace Khaleds\Api\Services;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Khaleds\Api\Services\Concerns\GenerateFields;
use Khaleds\Api\Services\Concerns\GenerateModel;
use Khaleds\Api\Services\Concerns\GenerateName;
use Khaleds\Api\Services\Concerns\GenerateResource;
use Khaleds\Api\Services\Concerns\GenerateValidation;
use Khaleds\Shared\Helpers\StubHelper;

class ModelGenerator
{

    use GenerateModel;
    use GenerateResource;
    use GenerateValidation;
    use GenerateName;
    use GenerateFields;


    use StubHelper;

    private Connection $connection;
    private string $stub;
    private string $model;

    public function __construct(
        private string $table,
//        private string $name,
        private string | bool | null $module
    )
    {
        $connectionParams = [
            'dbname' => config('database.connections.mysql.database'),
            'user' => config('database.connections.mysql.username'),
            'password' => config('database.connections.mysql.password'),
            'host' => config('database.connections.mysql.host'),
            'driver' => 'pdo_mysql',
        ];

        $this->connection = DriverManager::getConnection($connectionParams);
        $this->stub = __DIR__ .'/../../stubs/';
        $this->model = $this->generateName(true, true, false);
    }

    public function generate(): void
    {
        $this->generateModel();
        $this->generateResource();
        $this->generateValidation();

    }
}
