<?php

namespace Khaleds\Api\Services\Concerns;

use Illuminate\Support\Str;

trait GenerateApiController
{


    private function generateController(): void
    {
        $repositoriesPath = $this->module ? module_path($this->module) . '/Http/Controllers/Api' : app_path('/Http/Controllers/Api');

        if (!file_exists($repositoriesPath . '/' . $this->name . 'Repository.php')) {
            $this->generateStubs(
                $this->stub . 'apiController.stub',
                $repositoriesPath . '/' . $this->name . 'Controller.php',
                [
                    "path" => $this->module ? 'Modules\\' . $this->module : 'App',
                    "name" => $this->name,
                    "module" => $this->module,
                    "storeRequest" => $this->storeRequest,
                    "updateRequest" => $this->updateRequest,
                    "jsonResource" => $this->jsonResource,
                    "service" => $this->service,

                ],
                [
                    $repositoriesPath
                ]
            );
        }
    }


}
