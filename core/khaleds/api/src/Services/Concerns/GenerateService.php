<?php

namespace Khaleds\Api\Services\Concerns;

use Illuminate\Support\Str;

trait GenerateService
{
   //todo refactor all this traits to be one with parameter or class with properties
    private function generateService(): void
    {
        $repositoriesPath = $this->module ? module_path($this->module) .'/Services' : app_path('/Services');
        $this->generateStubs(
            $this->stub .'service.stub',
            $repositoriesPath . '/'.$this->name.'Service.php',
            [
                "path" => $this->module ? 'Modules\\'.$this->module : 'App',
                "repository" => $this->repository,
                "repositoryPath" => $this->module ? 'Modules\\'.$this->module .'\Repositories\\' .$this->repository : 'App\\Models\\'.$this->repository, // todo this is a fixed
                "name" => $this->name
            ],
            [
                $repositoriesPath
            ]
        );
    }
}
