<?php

namespace Khaleds\Api\Services\Concerns;

use Illuminate\Support\Facades\Artisan;

trait GenerateModel
{
    // todo move all console helpers
    /**
     * @return void
     */
    public function generateModel(): void
    {
        //Check if model exists or not

        $command = "config:clear";

        if($this->module && !file_exists(module_path($this->module) . '/Models/'. $this->model . '.php')){
            $command = 'krlove:generate:model ' . $this->model . ' --table-name=' . $this->table . ' --output-path=' . module_path($this->module) . '/Models' . ' --namespace=' . "Modules" . "\\\\" . $this->module . "\\\\" . "Models";
        }
        else if(!$this->module && !file_exists(app_path("Models/{$this->model}.php"))){
            $command = 'krlove:generate:model ' . $this->model . ' --table-name=' . $this->table . ' --output-path=' . app_path('/Models') . ' --namespace=' . "\\App\\\\Models\\";
        }

        Artisan::call($command);
    }
}
