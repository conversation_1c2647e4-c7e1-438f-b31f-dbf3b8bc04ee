<?php

namespace Khaleds\Api\Services\Concerns;

use Illuminate\Support\Str;
use function Laravel\Prompts\confirm;
use function Laravel\Prompts\error;

trait GenerateRepository
{
    /**
     * @return void
     */
    private function generateRepository(): void
    {
        $repositoriesPath = $this->module ? module_path($this->module) . '/Repositories' : app_path('/Repositories');

        if (!file_exists($repositoriesPath . '/' . $this->name . 'Repository.php')) {
            $this->generateStubs(
                $this->stub . 'repository.stub',
                $repositoriesPath . '/' . $this->name . 'Repository.php',
                [
                    "path" => $this->module ? 'Modules\\' . $this->module : 'App',
                    "model" => $this->model,
                    "modelPath" => $this->module ? 'Modules\\' . $this->module . '\\Models' : 'App\\Models',
                    "table" => Str::studly(Str::lcfirst($this->table)),
                    "name" => $this->name
                ],
                [
                    $repositoriesPath
                ]
            );
        }
    }
}
