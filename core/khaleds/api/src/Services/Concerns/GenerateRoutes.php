<?php

namespace Khaleds\Api\Services\Concerns;

use Illuminate\Support\Str;

trait GenerateRoutes
{
    /**
     * @return void
     */
    private function generateRoutes(): void
    {
        $routePath = $this->module ?  module_path($this->module) .'/Routes/' : base_path('/routes') ;

        $this->generateStubs(
            $this->stub . "route.stub",
            $routePath . '/api.php',
            [
                "path" => $this->module ? 'Modules\\'.$this->module : 'App',
                "name" => Str::snake($this->name),
                "controller" => $this->name,
            ],
            [
                $routePath
            ],
            true
        );
    }
}
