<?php

namespace Khaleds\Api\Services;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Khaleds\Api\Services\Concerns\GenerateController;
use Khaleds\Api\Services\Concerns\GenerateFields;
use Khaleds\Api\Services\Concerns\GenerateInterface;
use Khaleds\Api\Services\Concerns\GenerateModel;
use Khaleds\Api\Services\Concerns\GenerateName;
use Khaleds\Api\Services\Concerns\GenerateRepository;
use Khaleds\Api\Services\Concerns\GenerateResource;
use Khaleds\Api\Services\Concerns\GenerateRoutes;
use Khaleds\Api\Services\Concerns\GenerateService;
use Khaleds\Api\Services\Concerns\GenerateValidation;
use Khaleds\Shared\Helpers\StubHelper;

//todo isolate the commands one for create repo and one for service etc
class ServiceGenerator
{
    use GenerateFields;
    use GenerateName;
    use GenerateInterface;
    use GenerateRepository;
    use GenerateValidation;
    use GenerateController;
    use GenerateRoutes;
    use GenerateResource;
    use GenerateModel;
    use GenerateService;

    use StubHelper;

    private Connection $connection;
    private string $stub;
    private string $model;

    public function __construct(
        private string $name,
        private string | bool | null $module,
        private string $repository

    )
    {
        $connectionParams = [
            'dbname' => config('database.connections.mysql.database'),
            'user' => config('database.connections.mysql.username'),
            'password' => config('database.connections.mysql.password'),
            'host' => config('database.connections.mysql.host'),
            'driver' => 'pdo_mysql',
        ];

        try {

            $this->connection = DriverManager::getConnection($connectionParams);
            $this->connection->getDatabasePlatform()->registerDoctrineTypeMapping('enum', 'string');
        }catch (\Exception $e){

        }

        $this->stub = __DIR__ .'/../../stubs/';
    }


    public function generate(): void
    {
        $this->generateService();

    }
}
