<?php

namespace Khaleds\Api\Console;

use Illuminate\Console\Command;

use Khaleds\Api\Services\RepositoryGenerator;
use function Laravel\Prompts\text;

class GenerateRepositoryCommand extends CommandAbstract
{


    protected $name = 'tomato:generate:repository';

    protected $description = 'Generate Repository extended from abstract ';

    public function handle()
    {


        $name = text(label: '🍅 Please input your repository name ',placeholder:"ex: User" );

        $this->askForTable();
        $this->askForModule();



        try {
            $newGenerator = new RepositoryGenerator($this->table,$name, $this->module);
            $newGenerator->generate();

        } catch (\Exception $e) {
            $this->error($e);
        }
        info('Repository Has Been Generated Success');
        return Command::SUCCESS;

    }



}
