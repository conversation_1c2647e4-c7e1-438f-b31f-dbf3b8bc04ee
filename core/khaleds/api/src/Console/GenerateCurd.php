<?php

namespace Khaleds\Api\Console;

use Illuminate\Console\Command;
use Khaleds\Api\Services\ApiGenerator;

class GenerateCurd  extends CommandAbstract
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:api';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Full Api Crud';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $this->askForModule();
        $this->askForTable();


        try {
            $newGenerator = new ApiGenerator($this->table, $this->module,);
            $newGenerator->generate();

        } catch (\Exception $e) {
            $this->error($e);
        }

        return Command::SUCCESS;

    }

}
