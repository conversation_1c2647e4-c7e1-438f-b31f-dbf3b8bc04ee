<?php

namespace Khaleds\Api\Console;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputOption;
use Khaleds\Api\Services\ModelGenerator;
use Khaleds\Api\Services\RepositoryGenerator;
use function Laravel\Prompts\text;

class GenerateModelCommand extends CommandAbstract
{


    protected $name = 'tomato:generate:model';

    protected $description = 'Generate Repository extended from abstract ';

    public function handle()
    {

        $this->askForTable();
        $this->askForModule();



        try {
            $newGenerator = new ModelGenerator($this->table, $this->module);
            $newGenerator->generate();

        } catch (\Exception $e) {
            $this->error($e);
        }
        info('Repository Has Been Generated Success');
        return Command::SUCCESS;

    }


}

