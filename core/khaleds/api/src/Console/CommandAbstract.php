<?php

namespace <PERSON>haleds\Api\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Khaleds\Shared\Helpers\CommandHelper;
use function Laravel\Prompts\confirm;
use function Laravel\Prompts\error;
use function Laravel\Prompts\search;
use function Laravel\Prompts\suggest;
use function Laravel\Prompts\text;

abstract class CommandAbstract extends Command
{
    use CommandHelper;

    protected string $table;
    protected string $module;

    abstract  public function handle();


    protected function askForTable()
    {
        $tables = collect(DB::select('SHOW TABLES'))->map(function ($item) {
            return $item->{'Tables_in_' . config('database.connections.mysql.database')};
        })->toArray();

        $this->table = search(
            label: 'Please input your table name you went to create CRUD?',
            options: fn(string $value) => strlen($value) > 0
                ? collect($tables)->filter(function ($item, $key) use ($value) {
                    return Str::contains($item, $value) ? (string)$item : null;
                })->toArray()
                : [],
            placeholder: "ex: users",
            scroll: 10
        );

        if (is_numeric($this->table)) {
            $this->table = $tables[$this->table];
        }


    }

    protected function askForModule()
    {
        $isModule = confirm('Do you went to use HMVC module?');

        if ($isModule) {
            if (class_exists(\Nwidart\Modules\Facades\Module::class)) {
                $modules = \Nwidart\Modules\Facades\Module::toCollection()->map(function ($item) {
                    return $item->getName();
                });
                $this->module = suggest(
                    label: 'Please input your module name?',
                    placeholder: 'Account',
                    options: fn(string $value) => strlen($value) > 0
                        ? collect($modules)->filter(function ($item, $key) use ($value) {
                            return Str::contains($item, $value) ? $item : null;
                        })->toArray()
                        : [],
                    validate: fn(string $value) => match (true) {
                        strlen($value) < 1 => "Sorry this filed is required!",
                        default => null
                    },
                    scroll: 10
                );
                $check = \Nwidart\Modules\Facades\Module::find($this->module);
                if (!$check) {
                    error('Module not found!');
                }

            } else {
                error('Sorry nwidart/laravel-modules not installed please install it first. do you when to install it?');
            }
        }
    }

}
