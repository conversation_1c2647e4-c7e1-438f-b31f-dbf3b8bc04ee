<?php

namespace Khaleds\Api\Console;

use Illuminate\Console\Command;

use Khaleds\Api\Services\RepositoryGenerator;
use Khaleds\Api\Services\ServiceGenerator;
use function Laravel\Prompts\text;

class GenerateServiceCommand extends CommandAbstract
{


    protected $name = 'tomato:generate:service';

    protected $description = 'Generate service extended from abstract ';

    public function handle()
    {


        $name = text(label: '🍅 Please input your service name ',placeholder:"ex: User" );
        $repository = text(label: '🍅 Please input your repository name ',placeholder:"ex: UserRepository" );

        $this->askForModule();



        try {
            $newGenerator = new ServiceGenerator($name, $this->module,$repository);
            $newGenerator->generate();

        } catch (\Exception $e) {
            $this->error($e);
        }
        info('Repository Has Been Generated Success');
        return Command::SUCCESS;

    }



}
