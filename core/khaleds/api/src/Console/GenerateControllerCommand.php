<?php

namespace Khaleds\Api\Console;

use Illuminate\Console\Command;

use Khaleds\Api\Services\ControllerGenerator;
use Khaleds\Api\Services\RepositoryGenerator;
use Khaleds\Api\Services\ServiceGenerator;
use function Laravel\Prompts\text;

class GenerateControllerCommand extends CommandAbstract
{


    protected $name = 'tomato:generate:api:controller';

    protected $description = 'Generate controller extended from abstract ';

    public function handle()
    {


        $name = text(label: '🍅 Please input your controller name ',placeholder:"ex: User" );
        $service = text(label: '🍅 Please input your Service name ',placeholder:"ex: UserService" );
        $storeRequest = text(label: '🍅 Please input your store request name ',placeholder:"ex: UserStoreRequest" );
        $updateRequest = text(label: '🍅 Please input your update request name ',placeholder:"ex: UserUpdateRequest" );
        $jsonResource = text(label: '🍅 Please input your json resource name ',placeholder:"ex: UserResource" );

        $this->askForModule();


        try {
            $newGenerator = new ControllerGenerator($name, $this->module,$service,$storeRequest,$updateRequest,$jsonResource);
            $newGenerator->generate();

        } catch (\Exception $e) {
            $this->error($e);
        }
        info('Repository Has Been Generated Success');
        return Command::SUCCESS;

    }



}
