<?php

namespace Khaleds\Api;

use Illuminate\Support\ServiceProvider;
use Khaleds\Api\Console\GenerateControllerCommand;
use Khaleds\Api\Console\GenerateCurd;
use Khaleds\Api\Console\GenerateModelCommand;
use Khaleds\Api\Console\GenerateRepositoryCommand;
use Khaleds\Api\Console\GenerateServiceCommand;


class ApiServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //Register generate command
        $this->commands([
            GenerateRepositoryCommand::class,
            GenerateServiceCommand::class,
            GenerateControllerCommand::class,
            GenerateModelCommand::class,
            GenerateCurd::class
        ]);



    }

    public function boot(): void
    {
        //you boot methods here
    }
}
