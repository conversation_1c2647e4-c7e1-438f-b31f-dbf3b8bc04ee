<?php

namespace {{ path }}\Http\Resources\Api;
use Illuminate\Http\Resources\Json\JsonResource;

class {{ model }}Resource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = [
             {{ cols }}
        ];

        return $data;

    }
}
