<?php

namespace {{ path }}\Http\Controllers\Api;

use Modules\{{ module }}\Http\Requests\Api\{{ storeRequest }};
use Modules\{{ module }}\Http\Requests\Api\{{ updateRequest }};
use Modules\{{ module }}\Http\Resources\Api\{{ jsonResource }};
use Modules\{{ module }}\Services\{{ service }};
use Khaleds\Shared\Http\Controllers\Api\ControllerAbstract;

class {{ name }}Controller extends ControllerAbstract
{


    protected string $jsonResourceClass = {{ jsonResource }}::class;
    protected string $storeRequestClass = {{ storeRequest }}::class;
    protected string $updateRequestClass = {{ updateRequest }}::class;


    public function __construct({{ service }} $service)
    {
        parent::__construct($service);
    }


}
