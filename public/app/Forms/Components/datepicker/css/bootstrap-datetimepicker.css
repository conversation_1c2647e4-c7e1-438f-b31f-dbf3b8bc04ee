/*!
 * Datetimepicker for Bootstrap 4
 * version : 4.17.47
 * modified by: b<PERSON><PERSON><PERSON>
 * https://github.com/E<PERSON>sdan/bootstrap-datetimepicker/
 */

@import url("https://fonts.googleapis.com/css?family=IBM Plex Sans Arabic:300,300i,400,400i,700,700i&display=swap");

:root{
    --font-size:13px;
    --container-width:255px;
    --text-color:#000000;
    --text-muted:#c1c1c1;
    --hover--bg-color:#e2e2e2;
    --weekend-bg-color:#f5f5f5;
    --disabled-color:#cccccc;
}

.bootstrap-datetimepicker-widget {
    @apply list-none;
}

.bootstrap-datetimepicker-widget.dropdown-menu-datepicker {
    @apply block my-1 p-0;
}

@media (min-width: 768px) { /* Phone */
    .bootstrap-datetimepicker-widget.dropdown-menu-datepicker.timepicker-sbs {
        @apply w-[38em];
    }
}

@media (min-width: 992px) { /* Tablet */
    .bootstrap-datetimepicker-widget.dropdown-menu-datepicker.timepicker-sbs {
        @apply w-[38em];
    }
}

@media (min-width: 1200px) { /* Desktop */
    .bootstrap-datetimepicker-widget.dropdown-menu-datepicker.timepicker-sbs {
        @apply w-[38em];
    }
}

.bootstrap-datetimepicker-widget .list-unstyled {
    @apply m-0;
}

.bootstrap-datetimepicker-widget a[data-action] {
    @apply py-1.5 px-0;
}

.bootstrap-datetimepicker-widget a[data-action]:active {
    @apply shadow-none;
}

.bootstrap-datetimepicker-widget .timepicker-hour,
.bootstrap-datetimepicker-widget .timepicker-minute,
.bootstrap-datetimepicker-widget .timepicker-second {
    @apply w-[54px] font-bold text-[1.2em] m-0;
}

.bootstrap-datetimepicker-widget button[data-action] {
    @apply p-1.5;
}

.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Increment Hours";
}

.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Increment Minutes";
}

.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Decrement Hours";
}

.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Decrement Minutes";
}

.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Show Hours";
}

.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Show Minutes";
}

.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Toggle AM/PM";
}

.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Clear the picker";
}

.bootstrap-datetimepicker-widget .btn[data-action="today"]::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Set the date to today";
}

.bootstrap-datetimepicker-widget .picker-switch {
    @apply text-center text-black;
    font-size: var(--font-size);
}

.bootstrap-datetimepicker-widget .picker-switch::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Toggle Date and Time Screens";
}

.bootstrap-datetimepicker-widget .picker-switch td {
    @apply p-0 m-0 h-auto;
    line-height: inherit;
}

.bootstrap-datetimepicker-widget .picker-switch td span {
    @apply leading-[2.5] h-[2.5em] w-auto;
}

.bootstrap-datetimepicker-widget table {
    @apply w-full m-0;
}

.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th {
    @apply text-center rounded;
}

.bootstrap-datetimepicker-widget table th {
    @apply h-5 leading-5 w-5;
}

.bootstrap-datetimepicker-widget table th.picker-switch {
    @apply w-[145px] dark:text-white text-black;
}

.bootstrap-datetimepicker-widget table th.disabled,
.bootstrap-datetimepicker-widget table th.disabled:hover {
    @apply bg-transparent cursor-not-allowed;
    color: var(--disabled-color) !important;
}

.bootstrap-datetimepicker-widget table th.prev::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Previous Month";
}

.bootstrap-datetimepicker-widget table th.next::after {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
    content: "Next Month";
}

.bootstrap-datetimepicker-widget table thead tr:first-child th {
    @apply cursor-pointer;
}

.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
    @apply bg-primary-500 text-white;
}

.bootstrap-datetimepicker-widget table td {
    @apply h-[54px] leading-[54px] w-[54px];
}

.bootstrap-datetimepicker-widget table td.cw {
    @apply text-[0.8em] h-5 leading-5 text-[#777777];
}

.bootstrap-datetimepicker-widget table td.day {
    @apply h-5 leading-5 w-5;
    font-size: var(--font-size);
}

.bootstrap-datetimepicker-widget table td.day:hover,
.bootstrap-datetimepicker-widget table td.hour:hover,
.bootstrap-datetimepicker-widget table td.minute:hover,
.bootstrap-datetimepicker-widget table td.second:hover {
    @apply bg-[#eeeeee] cursor-pointer;
}

.bootstrap-datetimepicker-widget table td.old,
.bootstrap-datetimepicker-widget table td.new {
    @apply text-[#777777];
}

.bootstrap-datetimepicker-widget table td.today {
    @apply relative;
}

.bootstrap-datetimepicker-widget table td.today:before {
    @apply content-[''] inline-block absolute bottom-1;
    border: solid transparent;
    border-width: 0 0 7px 7px;
    border-bottom-color: #337ab7;
    border-top-color: rgba(0, 0, 0, 0.2);
}

.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
    @apply bg-[#337ab7] text-white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.bootstrap-datetimepicker-widget table td.active.today:before {
    @apply border-b-white;
}

.bootstrap-datetimepicker-widget table td.disabled,
.bootstrap-datetimepicker-widget table td.disabled:hover {
    @apply bg-transparent cursor-not-allowed opacity-50;
}

.bootstrap-datetimepicker-widget table td span {
    @apply inline-block w-[54px] h-[54px] leading-[54px] my-0.5 mx-[1.5px] cursor-pointer rounded text-black dark:text-white;
}

.bootstrap-datetimepicker-widget table td span:hover {
    @apply text-primary-500 dark:text-primary-400;
}

.bootstrap-datetimepicker-widget table td span.active {
    @apply bg-[#337ab7] text-white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.bootstrap-datetimepicker-widget table td span.old {
    @apply text-[#777777];
}

.bootstrap-datetimepicker-widget table td span.disabled,
.bootstrap-datetimepicker-widget table td span.disabled:hover {
    @apply bg-transparent cursor-not-allowed opacity-50;
    color: var(--disabled-color) !important;
}

.bootstrap-datetimepicker-widget.usetwentyfour td.hour {
    @apply h-[27px] leading-[27px];
}

.bootstrap-datetimepicker-widget.wider {
    @apply w-[21em];
}

.bootstrap-datetimepicker-widget .datepicker-decades .decade {
    @apply leading-[1.8em] !important;
}

.input-group.date .input-group-addon {
    @apply cursor-pointer;
}

.sr-only {
    @apply absolute w-px h-px -m-px p-0 overflow-hidden border-0;
    clip: rect(0, 0, 0, 0);
}

.datepicker-months .month {
    @apply w-[55px] h-[45px] leading-[45px];
    font-size: var(--font-size);
}

.bootstrap-datetimepicker-widget table td.weekend {
    @apply rounded-none;
    background-color: var(--weekend-bg-color);
}

.bootstrap-datetimepicker-widget {
    @apply p-10;
}

.datepicker tbody tr > td.day {
    @apply w-7 h-7 p-0;
    color: var(--text-color);
}

.datepicker tbody tr > td.day.old {
    color: var(--text-muted);
}

.datepicker tbody tr > td.day.new {
    color: var(--text-muted);
}

.datepicker {
    @apply p-2.5;
    width: var(--container-width);
}

/*===================================================================================== Custom ========*/

.datepicker.datepicker-orient-top {
    @apply mt-2;
}

.datepicker table {
    @apply w-full;
}

.datepicker td,
.datepicker th {
    @apply w-[35px] h-[35px] rounded;
    font-weight: regular;
}

.datepicker thead th {
    @apply text-[#a7abc3];
}

.datepicker thead th.prev, 
.datepicker thead th.datepicker-switch, 
.datepicker thead th.next {
    @apply font-medium text-[#a7abc3];
}

.datepicker thead th.prev i, 
.datepicker thead th.datepicker-switch i, 
.datepicker thead th.next i {
    @apply text-[1.2rem] text-[#a7abc3];
}

.datepicker thead th.prev i:before, 
.datepicker thead th.datepicker-switch i:before, 
.datepicker thead th.next i:before {
    @apply leading-[0] align-middle;
}

.datepicker thead th.prev:hover, 
.datepicker thead th.datepicker-switch:hover, 
.datepicker thead th.next:hover {
    @apply bg-primary-500 !important;
}

.datepicker thead th.dow {
    @apply text-gray-500 dark:text-gray-400 font-medium;
    font-size: var(--font-size);
}

.datepicker tbody tr > td.day {
    @apply text-gray-950 dark:text-white;
}

.datepicker tbody tr > td.day:hover {
    @apply text-primary-500 dark:text-primary-400 bg-gray-100 dark:bg-white/5;
   
}

.datepicker tbody tr > td.day.selected, 
.datepicker tbody tr > td.day.selected:hover, 
.datepicker tbody tr > td.day.active, 
.datepicker tbody tr > td.day.active:hover {
    @apply bg-primary-500 text-white;
}

.datepicker tbody tr > td.day.today {
    @apply relative bg-[rgba(93,120,255,0.7)] text-white !important;
}

.datepicker tbody tr > td.day.today:before {
    @apply content-[''] inline-block absolute bottom-1 right-1;
    border: solid transparent;
    border-width: 0 0 7px 7px;
    border-bottom-color: #ffffff;
    border-top-color: #ebedf2;
}

.datepicker tbody tr > td.day.range {
    @apply bg-[#f7f8fa];
}

.datepicker tbody tr > td span.year,
.datepicker tbody tr > td span.hour,
.datepicker tbody tr > td span.minute,
.datepicker tbody tr > td span.month {
    @apply text-gray-950 dark:text-white;
    font-size: var(--font-size);
}

.datepicker tbody tr > td span.year {
    @apply w-[55px] h-[45px] leading-[45px];
}

.datepicker tbody tr > td span.year:hover,
.datepicker tbody tr > td span.hour:hover,
.datepicker tbody tr > td span.minute:hover,
.datepicker tbody tr > td span.month:hover {
    @apply text-primary-500 dark:text-primary-400 bg-gray-100 dark:bg-white/5;
}

.datepicker tbody tr > td span.year.focused, 
.datepicker tbody tr > td span.year.focused:hover, 
.datepicker tbody tr > td span.year.active:hover, 
.datepicker tbody tr > td span.year.active.focused:hover, 
.datepicker tbody tr > td span.year.active,
.datepicker tbody tr > td span.hour.focused,
.datepicker tbody tr > td span.hour.focused:hover,
.datepicker tbody tr > td span.hour.active:hover,
.datepicker tbody tr > td span.hour.active.focused:hover,
.datepicker tbody tr > td span.hour.active,
.datepicker tbody tr > td span.minute.focused,
.datepicker tbody tr > td span.minute.focused:hover,
.datepicker tbody tr > td span.minute.active:hover,
.datepicker tbody tr > td span.minute.active.focused:hover,
.datepicker tbody tr > td span.minute.active,
.datepicker tbody tr > td span.month.focused,
.datepicker tbody tr > td span.month.focused:hover,
.datepicker tbody tr > td span.month.active:hover,
.datepicker tbody tr > td span.month.active.focused:hover,
.datepicker tbody tr > td span.month.active {
    @apply bg-primary-500 text-white;
}

.datepicker tfoot tr > th.today, 
.datepicker tfoot tr > th.clear {
    @apply rounded font-medium;
}

.datepicker tfoot tr > th.today:hover, 
.datepicker tfoot tr > th.clear:hover {
    @apply bg-[#ebedf2];
}

.datepicker.datepicker-inline {
    @apply border border-[#ebedf2];
}

.bootstrap-datetimepicker-widget .picker-switch.footer-actions{
    @apply border-t border-gray-200 dark:border-gray-800
}

.bootstrap-datetimepicker-widget .picker-switch.footer-actions table tr td:not(:first-child){
    @apply ltr:border-l rtl:border-r border-gray-200 dark:border-gray-800;
}

.dropdown-menu-datepicker {
    @apply absolute top-full left-0 z-10 hidden float-left min-w-[10rem] py-2 mt-0.5 text-base text-gray-800 text-left bg-white bg-clip-padding rounded-lg p-4 shadow-lg ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10;
}